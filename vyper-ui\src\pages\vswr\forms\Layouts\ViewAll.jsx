import React, { useState } from "react";
import { makeStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import Button from "@material-ui/core/Button";

import GeneralInfo from "../Sections/GeneralInfo";
import RequestorInfo from "../Sections/RequestorInfo";
import DieInfo from "../Sections/DieInfo/DieInfo";
import Edit from "@material-ui/icons/Edit";

import BomInfo from "../Sections/BomInfo";
import ManufacturingInfo from "../Sections/ManufacturingInfo/ManufacturingInfo";
import { headerItems } from "../Sections/ManufacturingInfo/headerItems";
import { useHistory } from "react-router-dom";
import VerifyPushButton from "../VerifyPushButton";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(4),
  },
  parentGrid: {
    width: "80%",
  },
  paper: {
    width: "max-content",
    marginTop: "30px",
    padding: "20px",
  },
  secondaryPaper: {
    width: "max-content",
    marginTop: "30px",
    padding: "20px",
  },
  tabBar: {
    position: "sticky",
    top: "50px",
    zIndex: "999",
  },
  tab: {
    minWidth: "150px",
  },
  stickyDiv: {
    position: "sticky",
    marginTop: "10px",
    top: "110px",
    height: "40px",
    width: "100%",
    zIndex: "999",
    display: "flex",
    justifyContent: "space-between",
  },
  textField: {
    "&.MuiInputLabel-outlined": {
      "&.MuiInputLabel-shrink": {
        transform: "translate(14px, -12px) scale(.75)",
        color: "black",
      },
    },
  },
}));

const ViewAll = (props) => {
  const { defaultValues } = props;
  const {
    requestorInfo = {},
    deviceInfo = {},
    dieInfo = {},
    assemblyInfo = {},
    bomInfo = {},
    packingRequirements = {},
    packingMaterial = {},
    shippingInfo = {},
    traveler = {},
    comments = {},
  } = defaultValues;
  const classes = useStyles();
  const history = useHistory();
  const [generalInfo, setGeneralInfo] = useState(
    defaultValues.generalInfo || {}
  );

  const handleEdit = () => {
    history.push(`/vswr/edit/${generalInfo.vswrID}`);
  };

  return (
    <div className={classes.root}>
      <Grid>
        <div className={classes.stickyDiv}>
          <Button
            startIcon={<Edit />}
            variant="contained"
            color="primary"
            onClick={handleEdit}
          >
            Edit
          </Button>
          <VerifyPushButton
            generalInfo={generalInfo}
            setGeneralInfo={setGeneralInfo}
            requestorInfo={requestorInfo}
            deviceInfo={deviceInfo}
            bomInfo={bomInfo}
            dieInfo={dieInfo}
            traveler={traveler}
          />
        </div>
        <Grid
          container
          spacing={0}
          direction="column"
          alignItems="center"
          justifyContent="flex-start"
        >
          <Grid className={classes.parentGrid} item>
            <GeneralInfo
              formState={generalInfo}
              readOnly={true}
              classes={classes}
            />

            <RequestorInfo formState={requestorInfo} classes={classes} />

            {/* <DeviceInfo
                            formState={deviceInfo}
                            readOnly={true}
                            classes={classes}
                        /> */}

            <DieInfo dieData={dieInfo} readOnly={true} classes={classes} />

            {/* <AssemblyInfo
                            formState={assemblyInfo}
                            readOnly={true}
                            classes={classes}
                        /> */}

            <BomInfo bomData={bomInfo} readOnly={true} classes={classes} />

            {/* <PackingRequirements
                            formState={packingRequirements}
                            readOnly={true}
                            classes={classes}
                        /> */}

            {/* <PackingMaterial
                            packingMaterialData={packingMaterial}
                            classes={classes}
                        /> */}

            {/* <ShippingInfo
                            shippingData={shippingInfo}
                            readOnly={true}
                            classes={classes}
                        /> */}

            <ManufacturingInfo
              travelerInfo={traveler}
              headerItems={headerItems(deviceInfo) || {}}
              comments={comments || {}}
              readOnly={true}
              vbuildID={generalInfo.vbuildID}
              classes={classes}
            />
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};

export default ViewAll;
