import { DaoBase } from "src/component/fetch/DaoBase";

export class FlowPackConfigMapDao extends DaoBase {
  constructor(params) {
    super({
      name: "FlowPackConfigMapDao",
      url: "/vyper/v1/config/flowpackconfig",
      ...params,
    });
  }

  list() {
    return this.handleFetch("list", `/`, "GET");
  }

  search(page, size) {
    return this.handleFetch(
      "search",
      `/search?page=${page}&size=${size}`,
      "GET"
    );
  }

  getById(id) {
    return this.handleFetch("getFlowPackConfigById", `/${id}`, "GET");
  }

  create(flowPackConfigMap) {
    return this.handleFetch("create", ``, "POST", flowPackConfigMap);
  }

  update(flowPackConfigMap) {
    return this.handleFetch("update", ``, "PUT", flowPackConfigMap);
  }

  delete(id) {
    return this.handleFetch("delete", `/${id}`, "DELETE");
  }

  delete(flowid, opnid) {
    return this.handleFetch("delete", `/${flowid}/${opnid}`, "DELETE");
  }
}
