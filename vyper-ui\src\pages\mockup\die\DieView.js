import React from "react";
import { ViewDieInstance } from "./ViewDieInstance";

export const DieView = ({ instances, build }) => {
  return instances == null || instances.length === 0 ? (
    <div>click to select</div>
  ) : (
    <div>
      {instances.map((instance, position) => (
        <ViewDieInstance
          key={position}
          position={position}
          instance={instance}
          showHeader={instances?.length > 1}
          build={build}
        />
      ))}
    </div>
  );
};
