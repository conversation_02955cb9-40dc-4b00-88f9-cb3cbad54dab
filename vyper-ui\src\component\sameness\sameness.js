/**
 * Return true if all of the builds have the same device
 * @param builds
 * @returns {boolean}
 */
export const samenessDevice = (builds) => {
  return builds
    .map((build) => build.material.object.Material)
    .every((material, index, array) => material === array[0]);
};

/**
 * Return true if all of the builds have the same facility
 * @param builds
 * @returns {boolean}
 */
export const samenessFacility = (builds) => {
  return builds
    .map((build) => build.facility.object.PDBFacility)
    .every((facility, index, array) => facility === array[0]);
};

/**
 * Return true if all of the builds have the same package niche
 * @param builds
 * @returns {boolean}
 */
export const samenessPackageNiche = (builds) => {
  return builds
    .map((build) => build.packageNiche.name)
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same bom template
 * @param builds
 * @returns {boolean}
 */
export const samenessBomTemplate = (builds) => {
  return builds
    .map((build) => build.bomTemplate.object.name)
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same dies
 * @param builds
 * @returns {boolean}
 */
export const samenessDies = (builds) => {
  const buildToDieNamesCsv = (build) =>
    build.dies.dieInstances
      .flatMap((dieInstances) => dieInstances)
      .flatMap((dieInstance) => dieInstance.dies)
      .map((die) => die.object.name)
      .join(",");

  return builds
    .map((build) => buildToDieNamesCsv(build))
    .every((name, index, array) => name === array[0]);
};

/**
 * Returns true if all of the builds have the same component
 * @param builds
 * @param name
 * @returns {boolean}
 */
export const samenessComponents = (builds, name) => {
  const buildToComponentNamesCsv = (build) =>
    build.components
      .filter((component) => component.name === name)
      .flatMap((component) => component?.instances ?? [])
      .flatMap((instance) => instance?.priorities ?? [])
      .flatMap((priority) => priority?.object?.name ?? [])
      .join(",");

  return builds
    .map((build) => buildToComponentNamesCsv(build))
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same symbolization
 * @param builds
 * @returns {boolean}
 */
export const samenessSymbolization = (builds) => {
  return builds
    .map((build) =>
      build.symbolization?.symbols.map((symbol) => symbol.object.name).join(",")
    )
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same extended shelf life
 * @param builds
 * @returns {boolean}
 */
export const samenessEsl = (builds) => {
  return builds
    .map((build) => build.esl?.object?.value)
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same turnkey
 * @param builds
 * @returns {boolean}
 */
export const samenessTurnkey = (builds) => {
  return builds
    .map((build) => build.turnkey?.value)
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same pack configuration
 * @param builds
 * @returns {boolean}
 */
export const samenessPackConfig = (builds) => {
  return builds
    .map((build) => build.packConfig?.object?.value)
    .every((name, index, array) => name === array[0]);
};

/**
 * Return true if all of the builds have the same pack configuration
 * @param builds
 * @returns {boolean}
 */
export const samenessDryBake = (builds) => {
  return builds
    .map((build) => build.dryBake?.object?.value)
    .every((name, index, array) => name === array[0]);
};

export const samenessFillComponent = (builds) => {
  return true; // todo:
};

export const samenessFlow = (builds) => {
  return true; // todo:
};

export const samenessDifference = (builds) => {
  return true; // todo:
};

export const samenessBuildtype = (builds) => {
  return true; // todo:
};

export const samenessBackgrind = (builds) => {
  return true; // todo:
};

export const samenessWaferSawMethod = (builds) => {
  return true; // todo:
};
