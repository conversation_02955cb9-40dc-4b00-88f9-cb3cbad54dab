import { useContext, useEffect, useState } from "react";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { useArmarcApi } from "src/api/useArmarcApi";

export const useArmarcWireMetal = () => {
  const { armarcWiremetal } = useArmarcApi();
  const { open: openError } = useContext(ErrorDialogContext);
  const [armarcWireMetals, setArmarcWireMetals] = useState([]);

  useEffect(() => {
    armarcWiremetal()
      .then((json) => json.map((wm) => wm.wire_metal))
      .then(setArmarcWireMetals)
      .catch((error) => openError({ error, title: "Fetch ArmArc Wire Metal" }));
  }, []);

  return { armarcWireMetals };
};
