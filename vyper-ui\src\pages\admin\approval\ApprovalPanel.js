import IconButton from "@material-ui/core/IconButton";
import { makeStyles } from "@material-ui/core/styles";
import ComputerIcon from "@material-ui/icons/Computer";
import React, { useContext, useEffect, useState } from "react";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import {
  DataGrid,
  gridActionDeleteRecord,
  gridActionEditRecord,
} from "../../../component/universal";
import { noop } from "../../../component/vyper/noop";
import { DataModelsContext } from "../../../DataModel";
import { ApprovalOperationDialog } from "./ApprovalOperationDialog";

const useMyStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(3),
    minWidth: "40vw",
  },
}));

export const ApprovalPanel = () => {
  const { approvalOperationDao } = useContext(DataModelsContext);
  const { open: showConfirm } = useContext(ConfirmationDialogContext);

  const [approvalOperations, setApprovalOperations] = useState([]);
  const [open, setOpen] = useState(false);
  const [selectedAo, setSelectedAo] = useState();

  // load the approval operations
  useEffect(() => {
    refresh();
  }, []);

  const refresh = () => {
    approvalOperationDao
      .search(0, 2000)
      .then((json) => setApprovalOperations(json.content))
      .catch(noop);
  };

  const columns = [
    {
      title: "ID",
      field: "id",
      filtering: false,
    },
    { title: "Operation", field: "operation" },
    { title: "Group Text", field: "groupText" },
  ];

  const handleEdit = (event, ao) => {
    setSelectedAo(ao);
    setOpen(true);
  };

  const handleUpdate = (ao) => {
    approvalOperationDao
      .update(ao)
      .then(() => refresh())
      .catch(noop);
    setOpen(false);
  };

  const handleDelete = (event, ao) => {
    showConfirm({
      title: "Confirmation Delete",
      message: `Are you sure you want to delete the approval operation record: '${ao.operation}' / '${ao.groupText}'?`,
      onYes: () => {
        approvalOperationDao
          .delete(ao.id)
          .then(() => refresh())
          .catch(noop);
      },
    });

    setOpen(false);
  };

  const handleRefreshAtss = () => {
    approvalOperationDao
      .atssRefresh()
      .then(() => refresh())
      .catch(noop);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const classes = useMyStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Approval Operations"
        data={approvalOperations}
        columns={columns}
        actions={[
          gridActionEditRecord(handleEdit),
          gridActionDeleteRecord(handleDelete),
          {
            isFreeAction: true,
            icon: () => (
              <IconButton color="primary" size="small">
                <ComputerIcon />
              </IconButton>
            ),
            tooltip:
              "Fetch ATSS operations and create any missing Approval Operations.",
            onClick: handleRefreshAtss,
          },
        ]}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
        options={{
          search: false,
        }}
      />

      <ApprovalOperationDialog
        open={open}
        approvalOperation={selectedAo}
        onClose={handleClose}
        onUpdate={handleUpdate}
      />
    </div>
  );
};
