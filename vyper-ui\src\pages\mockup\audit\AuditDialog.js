import React, { useState } from "react";
import { Dialog, DialogTitle, Paper, Typography } from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import makeStyles from "@material-ui/core/styles/makeStyles";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineOppositeContent,
  TimelineSeparator,
} from "@material-ui/lab";
import LanguageIcon from "@material-ui/icons/Language";
import FiberNewIcon from "@material-ui/icons/FiberNew";
import EditIcon from "@material-ui/icons/Edit";
import AddIcon from "@material-ui/icons/Add";
import RemoveIcon from "@material-ui/icons/Remove";
import moment from "moment";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  paper: {
    padding: "6px 16px",
  },
  secondaryTail: {
    backgroundColor: theme.palette.secondary.main,
  },
}));

export const AuditDialog = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [audits, setAudits] = useState([]);

  const handleOpen = ({ buildNumber, audits }) => {
    setTitle(`${buildNumber} - Audit`);
    setAudits(audits);
    setOpen(true);
  };

  const handleClose = () => {
    setTitle("");
    setAudits([]);
    setOpen(false);
  };

  const classes = useStyles();

  const determineIcon = (activity) => {
    if (activity.startsWith("CREATE_")) return <FiberNewIcon />;
    if (activity.startsWith("CHANGE_")) return <EditIcon />;
    if (activity.startsWith("ADD_")) return <AddIcon />;
    if (activity.startsWith("REMOVE_")) return <RemoveIcon />;
    return <LanguageIcon />;
  };

  return (
    <AuditDialogContext.Provider
      value={{
        openDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="xl">
        <DialogTitle>{title}</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent>
          <DialogContentText>
            <Timeline align="alternate">
              {audits.map((audit) => (
                <TimelineItem>
                  <TimelineOppositeContent>
                    <Typography variant="body2" color="textSecondary">
                      {moment(audit.when).fromNow()}
                    </Typography>
                  </TimelineOppositeContent>
                  <TimelineSeparator>
                    <TimelineDot color="primary" variant="outlined">
                      {determineIcon(audit.activity)}
                    </TimelineDot>
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <Paper elevation={3} className={classes.paper}>
                      <Typography variant="h6" component="h1">
                        {audit.detail}
                      </Typography>
                      <Typography variant="caption">
                        {userInfo(audit)}
                      </Typography>
                    </Paper>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </DialogContentText>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </AuditDialogContext.Provider>
  );
};

const userInfo = (audit) => {
  if (externalUse) {
    return `${audit.username}`;
  }

  return `${audit.username} / ${audit.userid}`;
};

export const AuditDialogContext = React.createContext(null);
