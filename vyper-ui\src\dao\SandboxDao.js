import { DaoBase } from "src/component/fetch/DaoBase";

export class <PERSON><PERSON><PERSON>ao extends DaoBase {
  constructor(params) {
    super({ name: "SandboxDao", url: "/vyper/v1/sandbox", ...params });
  }

  pgs(device, plantCode, names) {
    let uri = `/pgs?device=${encodeURI(device)}&plantCode=${encodeURI(
      plantCode
    )}`;
    names.forEach((name) => (uri += `&names=${encodeURI(name)}`));
    return this.handleFetch("pgs", uri, "GET");
  }

  packageNiche(device, facilityAt) {
    return this.handleFetch(
      "packageNiche",
      `/packageniche?device=${encodeURI(device)}&facilityAt=${encodeURI(
        facilityAt
      )}`,
      "GET"
    );
  }

  bomTemplate(device, plantCode, packageNiche, shelfLife, dryPack) {
    let uri = `/bomtemplate?device=${device}&plantCode=${plantCode}&packageNiche=${packageNiche}&shelfLife=${shelfLife}&dryPack=${dryPack}`;
    return this.handleFetch("bomTemplate", uri, "GET");
  }
}
