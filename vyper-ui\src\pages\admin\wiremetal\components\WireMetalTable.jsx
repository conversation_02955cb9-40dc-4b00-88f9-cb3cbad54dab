import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@material-ui/core";
import TableHead from "@material-ui/core/TableHead";

export const WireMetalTable = ({
  title,
  items,
  selected,
  onChangeSelected,
}) => {
  const handleChange = (e, item) => {
    let newSelected;
    if (e.target.checked) {
      newSelected = [...selected, item];
    } else {
      newSelected = selected.filter((i) => i.id !== item.id);
    }
    onChangeSelected(newSelected);
  };

  if (items == null) {
    return null;
  }

  return (
    <div>
      <h4 style={{ marginBottom: 0 }}>{title}</h4>
      <TableContainer style={{ border: "1px solid #cccccc", height: "23rem" }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>&nbsp;</TableCell>
              <TableCell>ARMARC</TableCell>
              <TableCell>CAMS</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id} hover>
                <TableCell>
                  <input
                    type="checkbox"
                    name="item"
                    value={item}
                    checked={selected.includes(item)}
                    onChange={(e) => handleChange(e, item)}
                  />
                </TableCell>
                <TableCell>{item.armarcMetal}</TableCell>
                <TableCell>{item.camsMetal}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};
