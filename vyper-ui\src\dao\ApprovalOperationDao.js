import { DaoBase } from "src/component/fetch/DaoBase";

export class ApprovalOperationDao extends DaoBase {
  constructor(params) {
    super({
      name: "ApprovalOperationDao",
      url: "/vyper/v1/approvaloperation",
      ...params,
    });
  }

  list() {
    return this.handleFetch("list", `/`, "GET");
  }

  search(page, size) {
    return this.handleFetch(
      "search",
      `/search?page=${page}&size=${size}`,
      "GET"
    );
  }

  create(approvalOperation) {
    return this.handleFetch("create", ``, "POST", approvalOperation);
  }

  update(approvalOperation) {
    return this.handleFetch(
      "update",
      `/${approvalOperation.id}`,
      "POST",
      approvalOperation
    );
  }

  delete(id) {
    return this.handleFetch("delete", `/${id}`, "DELETE");
  }

  atssRefresh() {
    return this.handleFetch("atssRefresh", `/atss/refresh`, "POST");
  }
}
