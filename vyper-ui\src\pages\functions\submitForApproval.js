import { roleServiceGetGroups } from "../../component/api/roleService";
import { createTask, updateTask } from "../../component/api/taskService2";

/**
 * Handle clicking of the submit button.
 *
 * @param vyper The vyper object
 * @param build The build object
 * @param action The action - normally it is "submit"
 * @param authUser - the current user
 * @param buildDao - The BuildDao object
 * @return Promise<Build> A promise containing the updated build
 */
export const submitForApproval = (vyper, build, action, buildDao) => {
  // get the groups from role service

  return roleServiceGetGroups(vyper, build)
    .then((groups) => {
      if (groups.length === 0) {
        throw new Error("There are no approver groups defined.");
      } else {

        if(build.buildtype === "Minor Change"){
          groups = groups.filter(group => !group.name.includes("ASSY"));
        }
        return groups;
      }
    })
    .then((atApproverGroups) => {
      const isSubmit = action.toLowerCase() === "submit";
      const isDraft = build.state.toLowerCase() === "draft";
      const isRework = build.state.toLowerCase() === "rework";

      // build the task service data structures

      const atBranches = [
        {
          stateName: "at review",
          branchName: "at rework",
        },
        {
          stateName: "at review",
          branchName: "at approve",
        },
      ];
      const buBranches = [
        {
          stateName: "bu review",
          branchName: "bu rework",
        },
        {
          stateName: "bu review",
          branchName: "bu approve",
        },
      ];

      const buApproverAids = vyper.owners.map((owner) => owner.userid);
      const groupNames = atApproverGroups.map((grp) => grp.name).join(", ");
      const metaData = [
        {
          attrName: "device id",
          attrValue: build.material?.object?.Material,
        },
        { attrName: "description", attrValue: build.description },
        { attrName: "title", attrValue: vyper.title },
        { attrName: "groups", attrValue: groupNames },
        { attrName: "owners", attrValue: buApproverAids.join(",") },
      ];

      // if this is a submit, while in draft then we create a task
      if (isDraft && isSubmit) {
        const taskInfo = {
          taskName: build.buildNumber,
          ctxIdLabel: "buildNumber",
          ctxIdValue: build.buildNumber,
        };

        return createTask(
          metaData,
          taskInfo,
          atApproverGroups,
          buApproverAids,
          atBranches,
          buBranches
        );
      } else if (isRework && isSubmit) {
        const contextKey = `Vyper Approval Flow~buildNumber~${build.buildNumber}`;
        const taskChangesPayload = {
          ignoreCheck: true,
          branchName: "at rework",
          metaList: metaData,
          fnctList: [],
        };
        return updateTask(
          contextKey,
          taskChangesPayload,
          atApproverGroups,
          buApproverAids,
          atBranches,
          buBranches
        );
      } else {
        // no task created
        return Promise.resolve();
      }
    })
    .then((taskResponse) => {
      // notify vyper api about the workflow change.   Sending "BU_OWNER" as group and rejection reason as null to not break existing functionality
      return buildDao.changeWorkflow(
        vyper.vyperNumber,
        build.buildNumber,
        action.toLowerCase(),
        "BU_OWNER",
        null
      );
    });
};
