import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

export class OperationDao extends DaoBase {
  constructor(params = {}) {
    super({ name: "OperationDao", url: "/vyper/v1/vyper", ...params });
    this.operations = params.operations || [];
    this.setOperations = params.setOperations || noop;
  }

  list() {
    if (this.operations?.length > 0) {
      return Promise.resolve(this.operations);
    } else {
      return this.handleFetch("list", "/allOperations", "GET").then((json) => {
        this.setOperations(json);
        return this.operations;
      });
    }
  }
}
