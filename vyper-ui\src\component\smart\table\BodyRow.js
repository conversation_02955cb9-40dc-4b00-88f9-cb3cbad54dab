import React from "react";
import { TableCell, TableRow } from "@material-ui/core";
import { BodyButtons } from "./BodyButtons";

/**
 *
 * @param rowButtons
 * @param columns
 * @param row
 * @param rowIndex
 * @param content
 * @param visible
 * @returns {*}
 * @constructor
 */
export const BodyRow = ({
  rowButtons,
  columns,
  row,
  rowIndex,
  content,
  visible,
}) => {
  return (
    <TableRow hover={true}>
      <BodyButtons rowButtons={rowButtons} row={row} />

      {columns.map((column, columnIndex) => {
        if (!visible(column, columnIndex)) return null;

        return (
          <TableCell key={column.id}>
            {content(column, columnIndex, row, rowIndex)}
          </TableCell>
        );
      })}
    </TableRow>
  );
};
