import React, { useState } from "react";
import { BasicModalDialog, ConfirmDialog } from "./ModalDialog";
import {
  DataGrid,
  gridActionEditRecord,
  gridActionDeleteRecord,
} from "./DataGrid";
import { PrimaryButton, NeutralButton, DangerButton } from "./Buttons";
import { PlusIcon, SaveRecordIcon } from "./Icons";
import DialogActions from "@material-ui/core/DialogActions";
import * as Yup from "yup";
import { Formik, Form } from "formik";
import { Fields } from "./Form";
import { fetchPost, generateRandomTimestamp } from "../utils";

const apiCommentsUrl = "/api/v1/oss/comment/{context}/{dataId}";

export const Comments = ({
  title,
  url,
  size,
  handleClose,
  ossContext,
  ossDataId,
}) => {
  const [showAddComment, openAddDialog] = useState(false);
  const [showUpdateComment, openUpdateDialog] = useState(false);
  const [showDeleteComment, openDeleteDialog] = useState(false);
  const [timestamp, setTimestamp] = useState(null);
  const [record, setRecord] = useState(null);
  const columns = [
    { title: "Entry", field: "entry" },
    { title: "Entry Type", field: "entryType" },
    { title: "Logged By", field: "username" },
    { title: "Badge", field: "userId" },
    { title: "Last Update", field: "lastUpdateDTTM" },
  ];

  const toggleAddComment = () => {
    openAddDialog(!showAddComment);
  };

  const toggleUpdateComment = (event, data) => {
    setRecord(data);
    openUpdateDialog(!showUpdateComment);
  };

  const toggleDeleteComment = (event, data) => {
    setRecord(data);
    openDeleteDialog(!showDeleteComment);
  };

  return (
    <BasicModalDialog title={title} size={size}>
      <PrimaryButton
        icon={<PlusIcon />}
        label="Add Comment"
        handleClick={() => toggleAddComment()}
      />
      <div>&nbsp;</div>
      <DataGrid
        actions={[
          (rowData) => {
            if (rowData.entryType.toUpperCase() === "COMMENT") {
              return gridActionEditRecord(toggleUpdateComment);
            }
          },
          (rowData) => {
            if (rowData.entryType.toUpperCase() === "COMMENT") {
              return gridActionDeleteRecord(toggleDeleteComment);
            }
          },
        ]}
        columns={columns}
        timestamp={timestamp}
        url={url}
        maxBodyHeight="200px"
      />
      <DialogActions>
        <NeutralButton label="Close" handleClick={() => handleClose()} />
      </DialogActions>
      {showAddComment ? (
        <AddComment
          toggleAddComment={toggleAddComment}
          ossContext={ossContext}
          ossDataId={ossDataId}
        />
      ) : null}
      {showUpdateComment ? (
        <UpdateComment
          toggleUpdateComment={toggleUpdateComment}
          data={record}
          ossContext={ossContext}
          ossDataId={ossDataId}
        />
      ) : null}
      {showDeleteComment ? (
        <DeleteComment
          toggleDeleteComment={toggleDeleteComment}
          id={record.id}
          ossContext={ossContext}
          ossDataId={ossDataId}
        />
      ) : null}
    </BasicModalDialog>
  );
};

const AddComment = (properties) => {
  const [data, setData] = useState(initialValues);

  const initialValues = {
    entry: "",
  };

  const FieldComponents = [
    {
      name: "entry",
      type: "TEXT_MULTI",
      label: "Comment",
      required: true,
    },
  ];

  const validation = Yup.object().shape({
    entry: Yup.string().required("Required"),
  });

  const handleSave = (values) => {
    let data = {
      entry: values.entry,
      entryType: "Comment",
      ossDataId: properties.ossDataId,
      ossContext: properties.ossContext,
    };
    setTimeout(() => {
      const commentURL =
        apiCommentsUrl
          .replace("{context}", properties.ossContext)
          .replace("{dataId}", properties.ossDataId) +
        "?entry=" +
        encodeURIComponent(values.entry);
      fetchPost(commentURL, null, (_) => {
        alert("Success saving comments");
      });
      properties.toggleAddComment();
    }, 3000);
  };

  return (
    <BasicModalDialog title={"Add Comment"}>
      <React.Fragment>
        <Formik initialValues={initialValues} validationSchema={validation}>
          {(props) => (
            <Form>
              <Fields fields={FieldComponents} />
              <DialogActions>
                <DangerButton
                  icon={<SaveRecordIcon />}
                  label="Save"
                  handleClick={() => handleSave(props.values)}
                />
                <NeutralButton
                  label="Cancel"
                  handleClick={() => properties.toggleAddComment()}
                />
              </DialogActions>
            </Form>
          )}
        </Formik>
      </React.Fragment>
    </BasicModalDialog>
  );
};

const UpdateComment = (properties) => {
  const [data, setData] = useState(properties.data);

  const FieldComponents = [
    {
      name: "entry",
      type: "TEXT_MULTI",
      label: "Comment",
      required: true,
    },
  ];

  const validation = Yup.object().shape({
    entry: Yup.string().required("Required"),
  });

  const handleSave = (values) => {
    let data = {
      id: values.id,
      entry: values.entry,
    };

    setTimeout(() => {
      const commentURL =
        apiCommentsUrl
          .replace("{context}", properties.ossContext)
          .replace("{dataId}", properties.ossDataId) +
        "?entry=" +
        encodeURIComponent(values.entry);
      fetchPost(commentURL, null, (_) => {
        alert("Success saving comments");
      });
      properties.toggleUpdateComment();
    }, 3000);
  };

  return (
    <BasicModalDialog title={"Update Comment"}>
      <React.Fragment>
        <Formik initialValues={properties.data} validationSchema={validation}>
          {(props) => (
            <Form>
              <Fields fields={FieldComponents} />
              <DialogActions>
                <DangerButton
                  icon={<SaveRecordIcon />}
                  label="Save"
                  handleClick={() => handleSave(props.values)}
                />
                <NeutralButton
                  label="Cancel"
                  handleClick={() => properties.toggleUpdateComment()}
                />
              </DialogActions>
            </Form>
          )}
        </Formik>
      </React.Fragment>
    </BasicModalDialog>
  );
};

const DeleteComment = (properties) => {
  const handleDelete = () => {
    let data = {
      id: properties.id,
      ossDataId: properties.ossDataId,
      ossContext: properties.ossContext,
      //entry: values.entry,
    };
    /* placeholder */
    alert(JSON.stringify(data, null, 2));
    properties.toggleDeleteComment();
  };

  return (
    <ConfirmDialog
      handleNo={(_) => properties.toggleDeleteComment()}
      handleYes={(_) => handleDelete(properties.id)}
      title={`Delete Comment`}
    />
  );
};
