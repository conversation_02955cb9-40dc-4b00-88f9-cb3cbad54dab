import axios from "axios";
import { useQuery } from "react-query";
import { BASE_URL } from "../common/scswrAPI";
import useSnackbar from "../../../../hooks/Snackbar";

export let useSbeViewGetter = ({ swrId, uploadedBy }) => {
  let { enqueueErrorSnackbar } = useSnackbar();
  let params = { uploadedBy };
  if (swrId) {
    params.swrId = swrId;
  }
  let sbeViewGetter = useQuery(
    ["bp-diffs", "swrId", swrId],
    () => {
      return axios
        .get(`${BASE_URL}/bp-diffs`, { params })
        .then((res) => res.data);
    },
    {
      enabled: !!uploadedBy,
      onError: () => {
        enqueueErrorSnackbar("An error occured while trying to load data.");
      },
    }
  );

  return sbeViewGetter;
};
