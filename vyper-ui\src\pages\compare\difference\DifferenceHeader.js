import React from "react";
import { Table, TableBody, TableCell, TableRow } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  root: {},
  table: {
    backgroundColor: "#ccc",
    marginBottom: "2rem",
  },
  headerLabel: {
    fontWeight: "bold",
    paddingRight: 5,
  },
  headerValue: {
    paddingLeft: 5,
  },
});

export const DifferenceHeader = ({ items }) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Table className={classes.table}>
        <TableBody>
          <TableRow>
            {items.map((item) => (
              <React.Fragment key={item.label}>
                <TableCell align="right" className={classes.headerLabel}>
                  {item.label}
                </TableCell>
                <TableCell align="left" className={classes.headerValue}>
                  {item.value}
                </TableCell>
              </React.Fragment>
            ))}
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
