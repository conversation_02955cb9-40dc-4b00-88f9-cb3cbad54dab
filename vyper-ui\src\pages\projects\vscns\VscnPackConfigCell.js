import React, { useContext, useEffect, useState } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../../mockup/VyperLink";
import { HelperContext } from "src/component/helper/Helpers";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { QuestionDialogContext } from "../../../component/question/QuestionDialog";

export const VscnPackConfigCell = ({ vyper, build, vscn, onChange }) => {
  const { showQuestionDialog } = useContext(QuestionDialogContext);
  const { buildDao, vscnDao } = useContext(DataModelsContext);
  const [values, setValues] = useState([]);

  useEffect(() => {
    if (vyper == null || build == null) return;

    buildDao
      .listPackConfigs(vyper.vyperNumber, build.buildNumber)
      .then(setValues);
  }, [vyper, build]);

  const handleChangePackConfig = (value, vscn) => {
    return vscnDao
      .changePackConfig(vscn.vscnNumber, value)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (vscn) => {
    showQuestionDialog({
      type: "select",
      title: "Select the Pack Configuration",
      description: `Select the pack config value for this VSCN.`,
      value: vscn.packConfig?.object?.value,
      multiline: false,
      rows: 10,
      options: values,
      onSave: (value) => handleChangePackConfig(value, vscn),
    });
  };

  const { canEditPackConfig } = useContext(HelperContext);
  const canEdit = true; // need to define rule to edit

  if (vscn == null) return null;

  return (
    <DataCell source={vscn.packConfig?.source}>
      <VyperLink onClick={() => handleClick(vscn)} canEdit={canEdit}>
        {vscn.packConfig?.object?.value || "click to select"}
      </VyperLink>
    </DataCell>
  );
};
