import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Tooltip from "@material-ui/core/Tooltip";
import pgs160 from "./crown_160.png";
import { determineIcon } from "src/component/sourceicon/determineIcon";

const useStyles = makeStyles(() => ({
  root: {
    display: "inline",
    verticalAlign: "middle",
  },
  icon: {
    paddingRight: "0.25em",
    paddingTop: 2,
  },
  pgsIcon: {
    paddingRight: "0.5em",
    paddingTop: 2,
  },
  inline: {
    display: "inline",
  },
  disabled: {},
}));

/**
 *
 * @param source The source
 * @param heading Text that will appear as the prefix of the tool top
 * @param disabled
 * @returns {JSX.Element|null}
 * @constructor
 */
export const SourceIcon = ({
  source,
  heading,
  disabled = false,
  color,
  style,
}) => {
  const icon = determineIcon(source);

  const classes = style || useStyles();

  if (icon == null) {
    return null;
  }
  const title = heading == null ? icon.text : `${heading} ${icon.text}`;

  const TagName = icon.icon;

  const colorDisabled = "#B7B7B7";

  return (
    <div className={classes.root}>
      {icon.text === "PGS" ? (
        <Tooltip className={classes.inline} title={title} placement="top">
          <img
            className={classes.pgsIcon}
            height="18.35px"
            src={pgs160}
            alt="pgs"
          />
        </Tooltip>
      ) : (
        <Tooltip className={classes.inline} title={title} placement="top">
          <TagName
            className={classes.icon}
            htmlColor={disabled ? colorDisabled : color || icon.color}
            fontSize="small"
          ></TagName>
        </Tooltip>
      )}
    </div>
  );
};
