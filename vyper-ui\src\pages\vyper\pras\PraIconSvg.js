import PropTypes from "prop-types";
import React from "react";
import {
  choosePraIcon,
  choosePraIconStyle,
} from "src/pages/vyper/pras/praHelpers";

/**
 * Display the SVG based icons
 *
 * @param verifier
 * @param hover
 * @param onClick
 * @returns {JSX.Element}
 * @function
 */
export const PraIconSvg = ({ verifier, hover, onClick }) => {
  const base = {
    padding: 2,
    marginLeft: 1,
    marginRight: 1,
    cursor: "pointer",
  };

  const Icon = choosePraIcon(verifier.source);
  const style = choosePraIconStyle(verifier.status, base);
  return <Icon style={style} onClick={onClick} titleAccess={hover} />;
};

PraIconSvg.propTypes = {
  verifier: PropTypes.object.isRequired,
  hover: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
};
