import { useCallback } from "react";
import { useSnackbar } from "notistack";

const useCustomSnackbar = () => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const enqueueErrorSnackbar = useCallback(
    (message, options) => {
      enqueueSnackbar(message, { variant: "error", ...options });
    },
    [enqueueSnackbar]
  );
  const enqueueSuccessSnackbar = useCallback(
    (message, options) => {
      enqueueSnackbar(message, { variant: "success", ...options });
    },
    [enqueueSnackbar]
  );
  const enqueueWarningSnackbar = useCallback(
    (message, options) => {
      enqueueSnackbar(message, { variant: "warning", ...options });
    },
    [enqueueSnackbar]
  );

  return {
    enqueueErrorSnackbar,
    enqueueSuccessSnackbar,
    enqueueWarningSnackbar,
    closeSnackbar,
  };
};

export default useCustomSnackbar;
