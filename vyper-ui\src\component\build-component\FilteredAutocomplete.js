import { Input<PERSON>abel, MenuItem, Select, TextField } from "@material-ui/core";
import FormControl from "@material-ui/core/FormControl";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Autocomplete } from "@material-ui/lab";
import { createFilterOptions } from "@material-ui/lab/Autocomplete";
import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "../fetch/VyperFetch";
import config from "../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles({
  root: {},
  widgets: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "stretch",
  },
  helperText: {
    color: "#ccc",
  },
});

/**
 * Show a component that is a dropdown filter chooser, and a autocomplete input
 * Once a value is selected from the list fire event (this is an un-controlled component)
 *
 * @param mode either "queryBuild" or "querySelection" use the constants in ComponentDialog.js FILTER_MODE_BUILD or FILTER_MODE_SELECTED
 * @param title
 * @param componentMap
 * @param componentName
 * @param suffix
 * @param facility
 * @param pkgGroup
 * @param pkg
 * @param pin
 * @param supplierOrWire
 * @param onChange
 * @param buildNumber
 * @returns {JSX.Element}
 * @constructor
 */
export const FilteredAutocomplete = ({
  mode,
  title,
  componentMap,
  componentName,
  suffix,
  facility,
  pkgGroup,
  pkg,
  pin,
  supplierOrWire,
  onChange,
  buildNumber,
  allowParentChildDies,
  praNumber,
}) => {
  const { vget } = useContext(FetchContext);

  ///////////////////////////////////////////////////////////////////////////
  // determine the filter methods

  // filterMethods = [{value:"aaa", title: "bbb}, ...]
  const [filterMethods, setFilterMethods] = useState([]);

  const titles = {
    BOM: `ATSS BOM - ${facility} ${pin}${pkg}`,
    BOM_SUPPLIER: `ATSS BOM - ${facility} ${pin}${pkg} - supplier part number`,
    BOM_SUPPLIER_VALUE: `ATSS BOM ${facility} ${pin}${pkg} - ${supplierOrWire}`,
    BOM_WIRE: `ATSS BOM - ${facility} ${pin}${pkg} - base metal and diameter`,
    BOM_WIRE_VALUE: `ATSS BOM ${facility} ${pin}${pkg} - ${supplierOrWire}`,

    AT: `ATSS CAMS - ${facility}`,
    AT_SUPPLIER: `ATSS CAMS - ${facility} - supplier part number`,
    AT_SUPPLIER_VALUE: `ATSS CAMS ${facility} - ${supplierOrWire}`,
    AT_WIRE: `ATSS CAMS - ${facility} - base metal and diameter`,
    AT_WIRE_VALUE: `ATSS CAMS - ${facility} - ${supplierOrWire}`,

    CAMS: `ATSS CAMS - Global`,
    CAMS_SUPPLIER: `ATSS CAMS Global - supplier part number`,
    CAMS_SUPPLIER_VALUE: `ATSS CAMS - Global - ${supplierOrWire}`,
    CAMS_WIRE: `ATSS CAMS - Global - base metal and diameter`,
    CAMS_WIRE_VALUE: `ATSS CAMS - Global - ${supplierOrWire}`,

    CAMS_FACILITY_PINPKG: `ATSS CAMS - ${facility} ${pin}${pkg}`,

    ARMARC: `ARMARC / PAVV`,

    MSL: `Moisture Sensitivity Level`,
    RECENT: `Recently Updated Values`,
    SUFFIX: `Change Last 4 Digits`,
  };

  // load the filter method dropdown with valid values from the component map
  useEffect(() => {
    if (componentMap == null) {
      return;
    }

    if (allowParentChildDies === true) {
      filteredMethods = [
        { title: "Parent/Child Dies", value: "PARENT_CHILD_DIE" },
      ];
      setFilterMethods(filteredMethods);
      return;
    }

    if (componentMap[mode] == null) {
      return;
    }

    let filteredMethods = componentMap[mode]
      ?.split(",")
      .map((item) => ({ value: item, title: titles[item] }));

    if (externalUse) {
      filteredMethods = filteredMethods.filter(
        (filteredMethod) => !filteredMethod.title.includes("Global")
      );
    }

    setFilterMethods(filteredMethods);
  }, [componentMap, facility, pkg, pin, allowParentChildDies]);

  //////////////////////////////////////////////////////////////////////////////
  // save and change the selected filter method

  const [method, setMethod] = useState();

  // set the default method to the 1st filter method
  useEffect(() => {
    if (method != null || filterMethods == null || filterMethods.length === 0) {
      return;
    }
    setMethod(filterMethods[0].value);
  }, [filterMethods]);

  //////////////////////////////////////////////////////////////////////////////
  // refresh the choices when input data changes

  const [choices, setChoices] = useState([]);

  useEffect(() => {
    if (
      componentName == null ||
      method == null ||
      facility == null ||
      pkgGroup == null ||
      pkg == null ||
      pin == null
    ) {
      return;
    }

    setChoices([]);

    let updatedMethod = method;

    if (
      componentName === "MB Diagram" &&
      ["AT", "CAMS", "RECENT"].includes(method)
    ) {
      updatedMethod = updatedMethod + "_MB_DIAGRAM";
    }

    let url = `/vyper/v1/vyper/choices`;
    url += `?method=${updatedMethod}`;
    url += `&componentName=${componentName}`;
    url += `&suffix=${suffix}`;
    url += `&facility=${facility}`;
    url += `&pkgGroup=${pkgGroup}`;
    url += `&pkg=${pkg}`;
    url += `&pin=${pin}`;
    url += `&supplierOrWire=${supplierOrWire}`;
    url += `&buildNumber=${buildNumber}`;
    url += `&praNumber=${praNumber}`;

    vget(url, (c) => {
      // filter out nulls, because they cause the autocomplete control to crash
      setChoices(c.filter((choice) => choice != null));
    });
  }, [componentName, method, facility, pkgGroup, pkg, pin, supplierOrWire]);

  // when the method changes, store the method, and clear the choices
  const handleChangeMethod = (e) => {
    setMethod(e.target.value);
    setChoices([]);
  };

  const filterOptions = createFilterOptions({
    trim: true,
  });

  const classes = useStyles();

  const allowToolTip =
    componentMap?.allowFreeformTextForComponentChoice == null ||
    componentMap?.allowFreeformTextForComponentChoice === "YES";

  return (
    <div className={classes.root}>
      <div className={classes.widgets}>
        {title == null ? null : <h3>{title}</h3>}

        <FormControl variant="outlined">
          <InputLabel htmlFor="component-filter">Component Filter</InputLabel>
          <Select
            id="component-filter"
            value={method || ""}
            onChange={handleChangeMethod}
            label="Component Filter"
          >
            {filterMethods.map((filterMethod, n) => (
              <MenuItem key={n} value={filterMethod.value}>
                {filterMethod.title}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Autocomplete
          id="component-autocomplete"
          freeSolo={componentMap?.allowFreeformTextForComponentChoice === "YES"}
          options={choices}
          renderInput={(params) => (
            <TextField {...params} label="Search" variant="outlined" />
          )}
          onChange={(e, v) => onChange(v)}
          filterOptions={filterOptions}
        />
        <div className={classes.helperText}>
          There are {choices.length} values
        </div>
        {allowToolTip ? (
          <h3>Press ENTER to input value not already in list.</h3>
        ) : null}
      </div>
    </div>
  );
};

FilteredAutocomplete.propTypes = {
  mode: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  componentMap: PropTypes.object,
  componentName: PropTypes.string,
  suffix: PropTypes.string,
  facility: PropTypes.string,
  pkgGroup: PropTypes.string,
  pkg: PropTypes.string,
  pin: PropTypes.number,
  supplierOrWire: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  buildNumber: PropTypes.string,
  allowParentChildDies: PropTypes.bool,
  vscnNumber: PropTypes.string,
};
