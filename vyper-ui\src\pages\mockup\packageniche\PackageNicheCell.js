import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { hasFacility, hasMaterial } from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  highlight: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
});

export const PackageNicheCell = ({ vyper, build, packageNiche, onClick }) => {
  const { canEditPackageNiche } = useContext(HelperContext);
  const classes = useStyles();

  const canEdit = canEditPackageNiche(vyper, build);

  if (!hasMaterial(build) || !hasFacility(build)) return null;

  return (
    <DataCell source={packageNiche?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        {packageNiche.name || "click to select"}
      </VyperLink>
      {packageNiche.name == null && (
        <div className={classes.highlight}>Niche Not Selected</div>
      )}
    </DataCell>
  );
};
