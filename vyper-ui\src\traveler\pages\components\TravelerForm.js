import { Button, makeStyles, MenuItem, TextField } from "@material-ui/core";
import Autocomplete from "@material-ui/lab/Autocomplete";
import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { useLocalStorage } from "../../../component/hooks/useLocalStorage";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles(() => ({
  form: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    gap: "1em",
  },
  autocomplete: {
    display: "inherit",
    width: "16rem",
  },
}));

/**
 * @callback TravelerForm~onSelect - Called when the user chooses a number.
 * @type {string} type - The type of number. BUILD / PRA or VSCN
 * @type {string} number - The number selected
 * @type {string} variant - The traveler variant. VYPER or ATSS
 */

/**
 * Displays a form that lets the user autocomplete a build/pra/vscn number.
 * @param {number} [size] - The maximum number of results to return, defaults to 20
 * @param {number} [refreshInterval] - The delay between key presses and fetching data. defaults to 500ms
 * @param {TravelerForm~onSelect} onSelect - called when the user chooses a number
 * @return {JSX.Element}
 * @constructor
 */
export function TravelerForm({ size = 20, refreshInterval = 500, onSelect }) {
  const { vget } = useContext(FetchContext);
  const [type, setType] = useLocalStorage("traveler.type", "BUILD");
  const [variant, setVariant] = useLocalStorage("traveler.vyper", "VYPER");
  const [buildNumber, setBuildNumber] = useLocalStorage(
    "traveler.build_number",
    ""
  ); // VBUILD0111022-0008
  const [praNumber, setPraNumber] = useLocalStorage("traveler.pra_number", ""); // PRA0111022-0006
  const [vscnNumber, setVscnNumber] = useLocalStorage(
    "traveler.vscn_number",
    ""
  ); // VSC********-0012
  const [buildOptions, setBuildOptions] = useState([]);
  const [praOptions, setPraOptions] = useState([]);
  const [vscnOptions, setVscnOptions] = useState([]);
  const [form, setForm] = useState({ build: "", pra: "", vscn: "" });
  const [timer, setTimer] = useState();
  const classes = useStyles();

  // on mount, set the form
  useEffect(() => {
    setForm({ build: buildNumber, pra: praNumber, vscn: vscnNumber });
  }, []);

  // when user changes the type, clear the number
  function handleChangeType(e) {
    setType(e.target.value);
  }

  // user changed the variant
  function handleChangeVariant(e) {
    setVariant(e.target.value);
  }

  // the user type a character in an autocomplete
  function handleChange(e) {
    if (e != null) {
      setForm((oldForm) => ({ ...oldForm, [e.target.name]: e.target.value }));
    }
  }

  // user selected a build number from the drop down list
  function handleSelectBuild(e, v) {
    setBuildNumber(v);
    setForm({ ...form, build: v });
  }

  // user selected a pra number from the drop down list
  function handleSelectPra(e, v) {
    setPraNumber(v);
    setForm({ ...form, pra: v });
  }

  // user selected a vscn number from the drop down list
  function handleSelectVscn(e, v) {
    setVscnNumber(v);
    setForm({ ...form, vscn: v });
  }

  // when form.build changes, retrieve the updated list of options
  useEffect(() => {
    if (form.build == null || form.build === "") {
      return;
    }

    if (timer != null) {
      clearTimeout(timer);
    }

    const timerId = setTimeout(() => {
      vget(
        `/vyper/v1/autocomplete/buildNumber?search=${form.build.trim()}&size=${size}`,
        setBuildOptions
      );
    }, refreshInterval);

    setTimer(timerId);
  }, [form.build]);

  // when form.pra changes, retrieve the updated list of options
  useEffect(() => {
    if (form.pra == null || form.pra === "") {
      return;
    }

    if (timer != null) {
      clearTimeout(timer);
    }

    const timerId = setTimeout(() => {
      vget(
        `/vyper/v1/autocomplete/praNumber?search=${form.pra.trim()}&size=${size}`,
        setPraOptions
      );
    }, refreshInterval);

    setTimer(timerId);
  }, [form.pra]);

  // when form.vscn changes, retrieve the updated list of options
  useEffect(() => {
    if (form.vscn == null || form.vscn === "") {
      return;
    }

    if (timer != null) {
      clearTimeout(timer);
    }

    const timerId = setTimeout(() => {
      vget(
        `/vyper/v1/autocomplete/vscnNumber?search=${form.vscn.trim()}&size=${size}`,
        setVscnOptions
      );
    }, refreshInterval);

    setTimer(timerId);
  }, [form.vscn]);

  function handleClickShow() {
    switch (type) {
      case "BUILD":
        onSelect(type, buildNumber, variant);
        break;
      case "PRA":
        onSelect(type, praNumber, variant);
        break;
      case "VSCN":
        onSelect(type, vscnNumber, variant);
        break;
    }
  }

  // determine if the show button is enabled
  const enabled = type !== "" && variant !== "";

  return (
    <div>
      <h3>Traveler Select Form</h3>

      <form className={classes.form}>
        <TextField
          select
          variant="outlined"
          label="Type"
          value={type}
          onChange={handleChangeType}
        >
          <MenuItem value="BUILD">Build</MenuItem>
          <MenuItem value="PRA">PRA</MenuItem>
          {!externalUse && <MenuItem value="VSCN">VSCN</MenuItem>}
        </TextField>

        {type === "BUILD" && (
          <Autocomplete
            className={classes.autocomplete}
            inputValue={form.build}
            onInputChange={handleChange}
            value={buildNumber}
            onChange={handleSelectBuild}
            options={buildOptions}
            renderInput={(params) => (
              <TextField
                {...params}
                name="build"
                label="Build Number"
                variant="outlined"
              />
            )}
            defaultValue={buildNumber}
          />
        )}

        {type === "PRA" && (
          <Autocomplete
            className={classes.autocomplete}
            inputValue={form.pra}
            onInputChange={handleChange}
            value={praNumber}
            onChange={handleSelectPra}
            options={praOptions}
            renderInput={(params) => (
              <TextField
                {...params}
                name="pra"
                label="PRA Number"
                variant="outlined"
              />
            )}
            defaultValue={praNumber}
          />
        )}

        {type === "VSCN" && (
          <Autocomplete
            className={classes.autocomplete}
            inputValue={form.vscn}
            onInputChange={handleChange}
            value={vscnNumber}
            onChange={handleSelectVscn}
            options={vscnOptions}
            renderInput={(params) => (
              <TextField
                {...params}
                name="vscn"
                label="VSCN Number"
                variant="outlined"
              />
            )}
            defaultValue={vscnNumber}
          />
        )}

        <TextField
          select
          variant="outlined"
          label="Attributes"
          value={variant}
          onChange={handleChangeVariant}
        >
          <MenuItem value="VYPER">{type} Attributes</MenuItem>
          <MenuItem value="ATSS">ATSS Attributes</MenuItem>
        </TextField>

        {/* VBUILD/PRA/VSCN attributes, ATSS attributes */}

        <Button
          variant="contained"
          color="primary"
          disabled={!enabled}
          onClick={handleClickShow}
        >
          Show
        </Button>
      </form>
    </div>
  );
}

TravelerForm.propTypes = {
  size: PropTypes.number,
  refreshInterval: PropTypes.number,
  onSelect: PropTypes.func.isRequired,
};
