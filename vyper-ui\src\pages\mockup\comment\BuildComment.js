import React, { useContext } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { CommentsDialogContext } from "../../../component/comments/CommentsDialog";
import { VyperLink } from "../VyperLink";

export const BuildComment = ({ build, onChange }) => {
  const { open: openDialog } = useContext(CommentsDialogContext);

  const handleClick = () => {
    openDialog({
      comments: [...build.comments].reverse(),
      operations:
        build?.flow?.object?.operations.map((operation) => operation.name) ||
        [],
      onSave: onChange,
    });
  };

  return (
    <DataCell source={null}>
      <VyperLink onClick={handleClick}>
        {build.comments.length} comment(s)
      </VyperLink>
    </DataCell>
  );
};
