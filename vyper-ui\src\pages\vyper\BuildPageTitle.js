import React from "react";
import { IconButton, makeStyles } from "@material-ui/core";
import HelpIcon from "@material-ui/icons/Help";

const useStyles = makeStyles({
  bar: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
});

export const BuildPageTitle = ({ build, title }) => {
  const classes = useStyles();

  return (
    <h2 className={classes.bar}>
      <div>{`${build?.buildNumber} [${build?.state}] - ${title}`}</div>
      <div>
        <IconButton
          target="_blank"
          href="https://confluence.itg.ti.com/display/SimbaInfo/Select+Component+screen"
        >
          <HelpIcon />
        </IconButton>
      </div>
    </h2>
  );
};
