import React, { useState, useEffect } from "react";
import ViewAll from "./Layouts/ViewAll";
import { useParams } from "react-router-dom";

import { BASE_FETCH_DATA_URL } from "./FormConstants";

import SimpleSubmit from "./SimpleSubmit";

const ViewForm = () => {
  const [defaultValues, setDefaultValues] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isValid, setIsValid] = useState(true);

  const { vswrID } = useParams();

  const fetchVswr = (vswrID) => {
    setIsLoading(true);
    fetch(`${BASE_FETCH_DATA_URL}/fetchVswr/${vswrID}`)
      .then((response) => response.json())
      .then((json) => {
        setDefaultValues(json);
        setIsLoading(false);
        setIsValid(true);
      })
      .catch(() => {
        setIsLoading(false);
        setIsValid(false);
        setDefaultValues({});
      });
  };

  useEffect(() => {
    if (vswrID) {
      fetchVswr(vswrID);
    }
  }, []);

  if (Object.keys(defaultValues).length === 0) {
    return (
      <div>
        {isLoading && <strong>Loading...</strong>}
        {!isValid && (
          <strong>Entered SWR ID is invalid please check and try again</strong>
        )}
        {!vswrID && <SimpleSubmit label={"VSWR ID"} handleSubmit={fetchVswr} />}
      </div>
    );
  }
  return <ViewAll defaultValues={defaultValues} />;
};

export default ViewForm;
