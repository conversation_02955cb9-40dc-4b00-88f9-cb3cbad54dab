import React, { useState, useMemo, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import TextField from "@material-ui/core/TextField";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";

const RequestorInfo = (props) => {
  const { classes, formState } = props;

  const defaultFieldProps = useMemo(() => {
    return {
      variant: "outlined",
      style: { background: "#DCDCDC" },
      color: "secondary",
      disabled: true,
      required: true,
      fullWidth: true,
    };
  }, []);

  return (
    <Paper elevation={24} className={classes.secondaryPaper}>
      <Typography variant="h6"> Requestor Information </Typography>
      <Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.name}
              name={"name"}
              label="Name"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.email}
              name={"email"}
              label="Email"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.costCenter}
              name={"costCenter"}
              label="Cost Center"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.itssID}
              name={"itssID"}
              label="ITSS ID"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.phone}
              name={"phone"}
              label="Phone"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.group}
              name={"group"}
              label="Group"
            />
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};
export default RequestorInfo;
