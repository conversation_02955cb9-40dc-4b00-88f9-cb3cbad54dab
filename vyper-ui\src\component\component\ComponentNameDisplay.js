import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Tooltip from "@material-ui/core/Tooltip";
import { EngineeringIcon } from "src/component/icons/EngineeringIcon";
import clsx from "clsx";
import { ArmarcCheckMessage } from "src/component/build-component/ArmarcCheckMessage";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    whiteSpace: "normal",
  },
  error: {
    backgroundColor: "hsla(0, 100%, 75%, 1)",
  },
}));
//
export const ComponentNameDisplay = ({
  name,
  engineering,
  error,
  warnings = [],
  armarcCheckMessage,
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <span className={clsx({ [classes.error]: error })}>{name}</span>
      {engineering === "Y" ? (
        <Tooltip title="Engineering Component" placement="top-end" arrow>
          <span style={{ textDecoration: "none" }}>
            <EngineeringIcon fontSize="0.5em" />
          </span>
        </Tooltip>
      ) : null}
      {/*<ComponentPriorityWarning warnings={warnings} />*/}
      <ArmarcCheckMessage armarcCheckMessage={armarcCheckMessage} />
    </div>
  );
};
