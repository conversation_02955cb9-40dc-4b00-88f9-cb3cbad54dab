import { Field, Formik } from "formik";
import { ComponentMapHeader } from "src/pages/componentmap/ComponentMapHeader";
import Accordion from "@material-ui/core/Accordion";
import AccordionSummary from "@material-ui/core/AccordionSummary";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import Typography from "@material-ui/core/Typography";
import AccordionDetails from "@material-ui/core/AccordionDetails";
import Grid from "@material-ui/core/Grid";
import { TextField } from "formik-material-ui";
import { MenuItem } from "@material-ui/core";
import React from "react";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  field: {
    marginBottom: "1em",
  },
});

const translateMethods = [
  "",
  "REF_OBJECT",
  "ITEMS_ATTRS",
  "MB_DIAGRAM",
  "WIRE",
];

const yesNo = ["", "YES", "NO"];

const uniquenessValues = ["", "SINGLE_VALUE", "MULTIPLE_VALUE"];

export const ComponentMapForm = ({ componentMap, onNew, onSave }) => {
  const classes = useStyles();

  const handleValidate = (values) => {
    const errors = {};
    if (!values.name) {
      errors.name = "Required";
    }

    if (!values.atssComponentName) {
      errors.atssComponentName = "Required";
    }

    if (!values.componentUniqueness) {
      errors.componentUniqueness = "Required";
    }

    if (!values.allowFreeformTextForComponentChoice) {
      errors.allowFreeformTextForComponentChoice = "Required";
    }

    if (!values.componentChangeAlsoChangesSelection) {
      errors.componentChangeAlsoChangesSelection = "Required";
    }

    if (!values.selectionChangeAlsoChangesComponent) {
      errors.selectionChangeAlsoChangesComponent = "Required";
    }

    if (!values.allowMultipleInstances) {
      errors.allowMultipleInstances = "Required";
    }

    if (!values.allowMultiplePriorities) {
      errors.allowMultiplePriorities = "Required";
    }

    if (!values.requiredForSubmission) {
      errors.requiredForSubmission = "Required";
    }

    if (!values.displayOnPra) {
      errors.displayOnPra = "Required";
    }

    if (!values.praPgsValidator) {
      errors.praPgsValidator = "Required";
    }

    if (!values.praArmarcValidator) {
      errors.praArmarcValidator = "Required";
    }

    if (!values.praAtssGlobalValidator) {
      errors.praAtssGlobalValidator = "Required";
    }

    if (!values.praAtssAtValidator) {
      errors.praAtssAtValidator = "Required";
    }

    if (!values.praAtssBomValidator) {
      errors.praAtssBomValidator = "Required";
    }

    if (!values.praDieValidator) {
      errors.praDieValidator = "Required";
    }

    if (!values.querySuffix) {
      errors.querySuffix = "Required";
    }

    if (!values.valueShouldBe74) {
      errors.valueShouldBe74 = "Required";
    }

    if (!values.praDiagramValidator) {
      errors.praDiagramValidator = "Required";
    }

    if (!values.praBomQualifiedValidator) {
      errors.praBomQualifiedValidator = "Required";
    }

    if (!values.praPavvComponentValidator) {
      errors.praPavvComponentValidator = "Required";
    }

    return errors;
  };

  const handleSubmit = (values) => {
    // find the string fields, and trim them
    Object.entries(values).map(([key, value]) => {
      if ("string" === typeof value) {
        let v = value.trim();
        v = v === "" ? null : v;
        values[key] = v;
      }
    });

    // call save, then convert the component map into a form
    return onSave(values).then((map) => {
      return toForm(map);
    });
  };

  /**
   * Convert a component map into a form. convert null and undefined values to empty strings
   */
  const toForm = (map) => {
    const output = {};
    Object.entries(map).map(([key, value]) => {
      output[key] = value ? value : "";
    });
    return output;
  };

  if (componentMap == null) {
    return null;
  }

  return (
    <Formik
      initialValues={toForm(componentMap)}
      validate={handleValidate}
      onSubmit={handleSubmit}
      enableReinitialize={true}
    >
      {({ handleSubmit, values, dirty, handleReset, isValid }) => (
        <div>
          <form onSubmit={handleSubmit}>
            <ComponentMapHeader
              name={values.name}
              canNew={!dirty}
              canCancel={dirty}
              canSave={isValid && dirty}
              onNew={onNew}
              onCancel={handleReset}
            />

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Component Names</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="name"
                      type="text"
                      label="Vyper Component Name"
                      helperText="The is the name of the component in Vyper"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="atssComponentName"
                      type="text"
                      label="ATSS Component Name"
                      helperText="This is the name of the component in ATSS"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="engineeringAtssComponentName"
                      type="text"
                      label="Engineering ATSS Component Name"
                      helperText="enter the engineering component name that equates to this component"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>CAMs Queries</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="queryBuild"
                      type="text"
                      label="Query - Build"
                      helperText="These queries are used by the build page's component dialog"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="querySelected"
                      type="text"
                      label="Query - Selected"
                      helperText="These queries are used by the selection page's selection dialog"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="queryPra"
                      type="text"
                      label="Query - PRA"
                      helperText="These queries are used by the PRA page's selection dialog"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="querySuffix"
                      type="text"
                      label="Query - Suffix"
                      helperText="These queries are used by the PRA page's 7-4 suffix query"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>PGS Data Loader</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="pgsMethod"
                      label="PGS Method"
                      helperText="This is the method to use to translate PGS data to Vyper data"
                      disabled
                    >
                      {translateMethods.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="pgsAttrToAtssComponentValue"
                      type="text"
                      label="PGS Attr To Atss Component Value"
                      helperText="xxxx"
                      disabled
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="pgsAttrToDisplayNameTranslateMethod"
                      label="PGS Attr To Display Name Translate Method"
                      helperText="This is the method to use to translate PGS data to Vyper data"
                      disabled
                    >
                      {translateMethods.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="pgsAttrToDisplayName"
                      type="text"
                      label="PGS Attr To Display Name"
                      helperText="xxxx"
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="pgsItemsType"
                      type="text"
                      label="PGS Items Type"
                      helperText="xxxx"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="pgsItemsAttributeKey"
                      type="text"
                      label="PGS Items Attribute Key"
                      helperText="xxxx"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="pgsAttributeName"
                      type="text"
                      label="PGS Attribute Name"
                      helperText="xxxx"
                      disabled
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Component Dialogs</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="allowFreeformTextForComponentChoice"
                      label="Allow Freeform Text For Component Choice"
                      helperText="Can a user enter free-form text for the value?"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="componentUniqueness"
                      label="Component Uniqueness"
                      helperText="Can a user enter free-form text for the value?"
                    >
                      {uniquenessValues.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="allowMultipleInstances"
                      label="Allow Multiple Instances"
                      helperText="Can this component have multiple instances?"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="allowMultiplePriorities"
                      label="Allow Multiple Priorities"
                      helperText="Can this component have multiple priorities?"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Data Sync</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="componentChangeAlsoChangesSelection"
                      label="Component Change Also Changes Selection"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="selectionChangeAlsoChangesComponent"
                      label="Selection Change Also Changes Component"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>PRA Features</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="displayOnPra"
                      label="Display on PRA"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praPgsValidator"
                      label="PRA PGS Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praArmarcValidator"
                      label="PRA ArmArc Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praAtssGlobalValidator"
                      label="PRA ATSS Global Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praAtssAtValidator"
                      label="PRA ATSS A/T Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praAtssBomValidator"
                      label="PRA ATSS BOM Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praDieValidator"
                      label="PRA Die Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praDiagramValidator"
                      label="PRA MB Diagram Validator"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praBomQualifiedValidator"
                      label="PRA BOM Qualified Validator"
                      helperText="Check ATSS to determine if the component is qualified"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="praPavvComponentValidator"
                      label="PRA PAVV Component Validator"
                      helperText="Check that the mount, mold, flux, leadframe match PAVV / ARMARC"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="valueShouldBe74"
                      label="Value Should Be 7-4"
                      helperText="The value needs to be in the 7-4 component number format"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Others</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      select
                      variant="outlined"
                      name="requiredForSubmission"
                      label="Required For Submission"
                      helperText="xxx"
                    >
                      {yesNo.map((m) => (
                        <MenuItem key={m} value={m}>
                          {m || "N/A"}
                        </MenuItem>
                      ))}
                    </Field>
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="alternativeFillSource"
                      type="text"
                      label="Alternative Fill Source"
                      helperText="xxxx"
                      disabled
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Field
                      component={TextField}
                      fullWidth
                      className={classes.field}
                      variant="outlined"
                      name="scnIncludeInTraveler"
                      type="text"
                      label="SCN Include In Traveler"
                      helperText="xxxx"
                      disabled
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </form>
        </div>
      )}
    </Formik>
  );
};
