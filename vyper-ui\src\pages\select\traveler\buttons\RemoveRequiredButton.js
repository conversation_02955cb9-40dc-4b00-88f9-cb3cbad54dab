import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { IconButton, Tooltip, Zoom } from "@material-ui/core";
import DeleteIcon from "@material-ui/icons/Delete";

const useStyles = makeStyles({
  root: {
    display: "inline",
  },
});

export const RemoveRequiredButton = ({
  title,
  disabled,
  required,
  onClick,
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Tooltip
        title={title}
        placement="top"
        TransitionComponent={Zoom}
        arrow
        interactive
      >
        <span>
          {required ? (
            <IconButton disabled size="small">
              <DeleteIcon fontSize="small" />
            </IconButton>
          ) : (
            <IconButton
              disabled={disabled}
              color="primary"
              size="small"
              onClick={onClick}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          )}
        </span>
      </Tooltip>
    </div>
  );
};
