import { makeStyles } from "@material-ui/core/styles";
import React from "react";
import { DataGrid } from "../../component/universal";
import { separatedFormatDate } from "src/component/dateFormat";

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(3),
  },
}));

export const ArmArcPanel = () => {
  const columns = [
    { field: "jobId", title: "Job Id" },
    {
      field: "submitTime",
      title: "Submit Time",
      render: (rowData) => (
        <span>{separatedFormatDate(rowData.submitTime).date}</span>
      ),
    },
    {
      field: "lastChangeTime",
      title: "Last Change Time",
      render: (rowData) => (
        <span>{separatedFormatDate(rowData.lastChangeTime).date}</span>
      ),
    },
    { field: "queue", title: "Queue" },
    { field: "device", title: "Device" },
    { field: "requestType", title: "Request Type" },
    { field: "automotive", title: "Automotive" },
    { field: "dieStatus", title: "Die Status" },
    { field: "atSite", title: "At Site" },
    { field: "pkg", title: "Pkg" },
    { field: "packageType", title: "Package Type" },
    { field: "rulesFile", title: "Rules File" },
    { field: "submitter", title: "Submitter" },
    { field: "packageReviewer", title: "Package Reviewer" },
    { field: "atReviewer", title: "At Reviewer" },
    { field: "packageDisposition", title: "Package Disposition" },
    { field: "atDisposition", title: "At Disposition" },
    { field: "leadframe", title: "Leadframe" },
    { field: "lfPref", title: "Lf Pref" },
    { field: "pin", title: "Pin" },
    { field: "mbEdge", title: "Mb Edge" },
    { field: "wireDiameter", title: "Wire Diameter" },
    { field: "wireType", title: "Wire Type" },
    { field: "peSignoff", title: "Pe Signoff" },
    { field: "qaSignoff", title: "Qa Signoff" },
    { field: "packageGroup", title: "Package Group" },
    { field: "processTech", title: "Process Tech" },
    { field: "packageRequestID", title: "Package Request Id" },
    { field: "bomSet", title: "Bom Set" },
    { field: "mountCompound", title: "Mount Compound" },
    { field: "moldCompound", title: "Mold Compound" },
    { field: "msl", title: "Msl" },
    { field: "processStack", title: "Process Stack" },
    { field: "bumpStack", title: "Bump Stack" },
    { field: "bumpTech", title: "Bump Tech" },
    { field: "bumpType", title: "Bump Type" },
    { field: "siTech ", title: "Si Tech" },
  ];

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="ARM/ARC Jobs"
        url={`/vyper/v1/armarc/search`}
        actions={[]}
        columns={columns}
        pageable
        pageSize={20}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
      />
    </div>
  );
};
