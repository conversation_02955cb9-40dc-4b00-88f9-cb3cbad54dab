import React, { useContext } from "react";
import { BuildPageTitle } from "../../vyper/BuildPageTitle";
import { TravelerComplete } from "./TravelerComplete";
import { TravelerHeaderTable } from "./TravelerHeaderTable";
import { TravelerBodyTable } from "./TravelerBodyTable";
import { BackToBuildFormLink } from "../../../component/backbutton/BackToBuildFormLink";
import { DataModelsContext } from "src/DataModel";

export const VyperTravelerPage = ({ vyperNumber }) => {
  const { build } = useContext(DataModelsContext);

  return (
    <div>
      <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />

      <BuildPageTitle build={build} title="Traveler" />

      <TravelerComplete build={build} />

      <TravelerHeaderTable build={build} />

      <br />
      <br />
      <br />

      <TravelerBodyTable build={build} />
    </div>
  );
};
