import React from "react";
import config from "src/buildEnvironment";

export const columnDefs = [
  { field: "ATSITE", headerName: "A/T Site", width: 100 },
  {
    field: "SWR_ID",
    headerName: "SWR ID",
    cellRenderer: (params) => {
      let swrId = params.value;
      return (
        <a
          href={
            config.scswrBaseUrl +
            "/scswr/viewswr.do?swrId=" +
            swrId +
            "&action=viewSwr#top"
          }
          target="_blank"
        >
          {swrId}
        </a>
      );
    },
    width: 100,
  },
  { field: "TITLE", headerName: "Title", width: 100 },
  { field: "SWR_TYPE", headerName: "SWR Type", width: 100 },
  { field: "REQUESTOR_NAME", headerName: "Requestor Name", width: 100 },
  { field: "CURRENT_STATUS", headerName: "Current Status", width: 100 },
  { field: "SBE", headerName: "SBE", width: 100 },
  { field: "SBE1SITE", headerName: "SBE1 Site", width: 100 },
  { field: "DEVICE_NAME", headerName: "Device Name", width: 100 },
  { field: "FLOW_TYPE", headerName: "Flow Type", width: 100 },
  { field: "TRAVEL_STAT", headerName: "Traveler Status", width: 100 },
  { field: "ECCNREQ", headerName: "Redbull", width: 100 },
  { field: "PRIORITY", headerName: "Priority", width: 100 },
  { field: "PIN", headerName: "Pin", width: 100 },
  { field: "PKG", headerName: "Pkg", width: 100 },
  { field: "MARKET_CATEGORY", headerName: "Market Category", width: 100 },
  { field: "ISO_VALUE", headerName: "ISO", width: 100 },
  { field: "IS_MCM", headerName: "MCM", width: 100 },
  { field: "DIE_NAME", headerName: "Die Name", width: 100 },
  { field: "DIE_REV", headerName: "Die Rev", width: 100 },
  { field: "FAB_CODE", headerName: "Fab Code", width: 100 },
  { field: "DIE_LOT", headerName: "Die Lot/SAP Lot", width: 100 },
  { field: "WAFER_DIAMETER", headerName: "Wafer Diameter", width: 100 },
  { field: "WAFER_USE", headerName: "Which Wafer To Use", width: 100 },
  { field: "CHARGE", headerName: "IO Number", width: 100 },
  { field: "PO", headerName: "PO Number", width: 100 },
  { field: "BLD_QTY", headerName: "Build Qty", width: 100 },
  { field: "LEADFRAMES1", headerName: "Leadframe Substrate", width: 100 },
  { field: "LEADFRAME_CMT", headerName: "Leadframe Comment", width: 100 },
  { field: "WIRE", headerName: "Wire", width: 100 },
  { field: "WIRE_CMT", headerName: "Wire Comment", width: 100 },
  { field: "MOLD_COMPOUND1", headerName: "Mold Compound", width: 100 },
  { field: "MOLDCOMP_CMT", headerName: "Mold Compound Comment", width: 100 },
  { field: "MOUNT_COMPOUND1", headerName: "Mount Compound", width: 100 },
  { field: "MOUNTCOMP_CMT", headerName: "Mount Compound Comment", width: 100 },
  { field: "SOLDER", headerName: "Solderball", width: 100 },
  { field: "SOLDER_CMT", headerName: "Solderball Comment", width: 100 },
  { field: "LID1", headerName: "Lid", width: 100 },
  { field: "LID_CMT", headerName: "Lid Comment", width: 100 },
  { field: "CHIPCAP", headerName: "Chip Capacitor", width: 100 },
  { field: "TEST_CONFIG", headerName: "Tester Config", width: 100 },
  { field: "PACK_REQ", headerName: "Packing Material>", width: 100 },
  { field: "PACK_REQ_PN", headerName: "Reel Partnumber", width: 100 },
  { field: "PACK_REQ_PN_TAPE", headerName: "Tape Partnumber", width: 100 },
  { field: "PACK_REQ_PN_COVER", headerName: "Cover Partnumber", width: 100 },
  { field: "COMMENT_PAK", headerName: "Pack Requirement Comment", width: 100 },
  { field: "ECCNCODE", headerName: "eWaiver Number", width: 100 },
  { field: "STICKERTYPE", headerName: "Sticker Type", width: 100 },
  {
    field: "HOW_SHIP_REM",
    headerName: "Ship Partial (How Should Remaining FGs be packed)",
    width: 100,
  },
  { field: "ATTN", headerName: "Attention", width: 100 },
  { field: "PLANTCODE", headerName: "Plant", width: 100 },
  { field: "ADDRESS", headerName: "Address", width: 100 },
  { field: "QTY", headerName: "Qty", width: 100 },
  { field: "SHIPTYPE", headerName: "Ship Type", width: 100 },
  { field: "SHIP_DEVICE_NAME", headerName: "Ship Device Name", width: 100 },
  {
    field: "TARGET_MONTH",
    headerName: "SBE Shipped Paperwork Target Date (MONTH)",
    width: 100,
  },
  {
    field: "TARGET",
    headerName: "SBE Shipped Paperwork Target Date (DTTM)",
    width: 100,
  },
  {
    field: "UPDATED",
    headerName: "SBE Shipped Paperwork Updated Date",
    width: 100,
  },
  {
    field: "ACTUAL",
    headerName: "SBE Shipped Paperwork Actual Date",
    width: 100,
  },
  { field: "COMMENT_PURPOSE", headerName: "SWR Purpose", width: 100 },
  { field: "COMMENT_REQ", headerName: "SWR General Comments", width: 100 },
];
