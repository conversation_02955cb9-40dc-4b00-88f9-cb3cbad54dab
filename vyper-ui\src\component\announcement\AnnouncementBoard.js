import React, { useContext, useEffect, useState } from "react";
import { Alert, AlertTitle } from "@material-ui/lab";
import { AnnouncementDao } from "src/dao/AnnouncementDao";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Button from "@material-ui/core/Button";
import { AnnouncementDialog } from "src/component/announcement/AnnouncementDialog";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import {
  AlertDialogErrorHandler,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { SpinnerContext } from "src/component/Spinner";
import { noop } from "src/component/vyper/noop";

const useStyles = makeStyles((theme) => ({
  root: {},
  announcement: {
    marginBottom: theme.spacing(2),
  },
}));

export const AnnouncementBoard = () => {
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const [announcements, setAnnouncements] = useState([]);
  const [announcement, setAnnouncement] = useState({});
  const [show, setShow] = useState(false);

  // list of announcement ids that have been hidden
  const [hideIds, setHideIds] = useState([]);

  // list of announcement ids that have been marked as read.
  const [readIds, setReadIds] = useLocalStorage("announcements.readids", []);

  const announcementDao = new AnnouncementDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  // retrieve the active announcements
  useEffect(() => {
    announcementDao
      .active()
      .then((json) => setAnnouncements(json))
      .catch(noop);
  }, []);

  const handleRead = (announcement) => {
    setAnnouncement(announcement);
    setShow(true);
  };

  const handleClose = () => setShow(false);

  const handleHide = (announcement) => {
    setHideIds([...hideIds, announcement.id]);
  };

  const handleMarkAsRead = (announcement) => {
    setReadIds([...readIds, announcement.id]);
    setShow(false);
  };

  const unreadAnnouncements = () => {
    return announcements
      .filter((announcement) => !readIds.includes(announcement.id))
      .filter((announcement) => !hideIds.includes(announcement.id));
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      {unreadAnnouncements().map((announcement) => (
        <Alert
          key={announcement.id}
          className={classes.announcement}
          severity="error"
          action={
            <div>
              <Button
                color="inherit"
                size="small"
                onClick={() => handleHide(announcement)}
              >
                Hide
              </Button>
              <Button
                color="inherit"
                size="small"
                onClick={() => handleRead(announcement)}
              >
                Read
              </Button>
            </div>
          }
        >
          <AlertTitle>{announcement.teaser}</AlertTitle>
        </Alert>
      ))}

      {show && (
        <AnnouncementDialog
          announcement={announcement}
          onClose={handleClose}
          onMarkAsRead={handleMarkAsRead}
        />
      )}
    </div>
  );
};
