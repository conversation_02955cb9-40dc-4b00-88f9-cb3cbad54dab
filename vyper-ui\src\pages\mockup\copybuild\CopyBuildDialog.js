import {
  Button,
  Dialog,
  <PERSON><PERSON>A<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  makeStyles,
  MenuItem,
  TextField,
} from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React, { useState } from "react";
import { CopyBuildPreview } from "src/pages/mockup/copybuild/CopyBuildPreview";
import { Experimental, MinorChange, New } from "../buildtype/BuildTypes";

const useStyles = makeStyles((theme) => ({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
  },
  paper: {
    padding: "6px 16px",
  },
}));

// the list of selections in the copy type dropdown
const COPY_TO_NEW = "Copy to NEW VYPER Project (Create New VYPER Project)";
const COPY_TO_THIS =
  "Copy to this VYPER Project (Create new VBUILD in existing VYPER Project)";
const copyTypes = [COPY_TO_NEW, COPY_TO_THIS];

/**
 * Display the copy build dialog.
 *
 * @param children
 * @returns {JSX.Element}
 * @function
 */
export const CopyBuildDialog = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [build, setBuild] = useState({});
  const [copyType, setCopyType] = useState("");
  const [buildType, setBuildType] = useState("");
  const [description, setDescription] = useState("");

  // Functions to post to Api, coming from @VyperProjectPage.js
  const [onAddCopiedBuild, setOnAddCopiedBuild] = useState();
  const [onCreateVyperAndCopyBuild, setOnCreateVyperAndCopyBuild] = useState();

  // When Dialog is open
  const handleOpen = ({
    build,
    onAddCopiedBuild,
    onCreateVyperAndCopyBuild,
  }) => {
    setBuild(build);
    setOnAddCopiedBuild(() => onAddCopiedBuild);
    setOnCreateVyperAndCopyBuild(() => onCreateVyperAndCopyBuild);
    setCopyType("");
    setBuildType("");
    setDescription("");
    setOpen(true);
  };

  // When the dialog is closed
  const handleClose = () => {
    setOpen(false);
  };

  // when the copy is submitted
  const handleSubmit = () => {
    switch (copyType) {
      case COPY_TO_NEW:
        onCreateVyperAndCopyBuild(build.buildNumber, buildType, description);
        handleClose();
        break;

      case COPY_TO_THIS:
        onAddCopiedBuild(build.buildNumber, buildType, description);
        handleClose();
        break;

      default:
        break;
    }
  };

  const isDescriptionValid =
    description != null && description.trim().length > 0;
  const isMinorChangeAllowed = !(
    build?.state !== "FINAL_APPROVED" || build?.buildtype !== New
  );
  const showBuildTypeSelect = copyType !== "";
  const showDescription = buildType !== "";
  const showPreview = isDescriptionValid;
  const showSubmitButton = isDescriptionValid;

  const classes = useStyles();

  return (
    <CopyBuildDialogContext.Provider
      value={{
        openDialog: handleOpen,
      }}
    >
      <Dialog
        className={classes.root}
        open={open}
        onClose={handleClose}
        fullWidth
      >
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon htmlColor="#ffffff" />
        </IconButton>

        <DialogTitle className={classes.title}>Copy Build</DialogTitle>

        <DialogContent>
          <TextField
            className={classes.textField}
            variant="outlined"
            select
            label="Select the Copy Type"
            fullWidth
            value={copyType}
            onChange={(event) => setCopyType(event.target.value)}
          >
            {copyTypes.map((ct) => (
              <MenuItem key={ct} value={ct}>
                {ct}
              </MenuItem>
            ))}
          </TextField>

          <br />

          {showBuildTypeSelect && (
            <TextField
              variant="outlined"
              select
              label="Select Buildtype"
              fullWidth
              value={buildType}
              onChange={(event) => setBuildType(event.target.value)}
            >
              <MenuItem value={New}>{New}</MenuItem>
              <MenuItem value={Experimental}>{Experimental}</MenuItem>
              <MenuItem disabled={!isMinorChangeAllowed} value={MinorChange}>
                {MinorChange}
              </MenuItem>
            </TextField>
          )}

          <br />

          {showDescription && (
            <TextField
              id="description"
              label="Description"
              placeholder="Description"
              multiline
              fullWidth
              value={description}
              onChange={(event) => setDescription(event.target.value)}
              variant="outlined"
              error={!isDescriptionValid}
              helperText={!isDescriptionValid ? "Required" : " "}
            />
          )}

          <br />
          <br />

          {showPreview && (
            <CopyBuildPreview
              build={build}
              material={build.material.object.Material}
              facility={build.facility.object.PDBFacility}
              buildType={buildType}
              description={description}
              buildNumber={build.buildNumber}
            />
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Close
          </Button>

          <Button
            onClick={handleSubmit}
            variant="contained"
            color="primary"
            disabled={!showSubmitButton}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </CopyBuildDialogContext.Provider>
  );
};

CopyBuildDialog.propTypes = {
  children: PropTypes.node.isRequired,
};

export const CopyBuildDialogContext = React.createContext(null);
