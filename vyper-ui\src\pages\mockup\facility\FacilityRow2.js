import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../RowPrefix";
import { FacilityCell2 } from "./FacilityCell2";

export function FacilityRow2({ items, onGetFacilityObject }) {
  return (
    <TableRow hover>
      <RowPrefix help="facility" title="Facility" />
      {items.map((item, n) => (
        <TableCell key={n}>
          <FacilityCell2
            item={item}
            onGetFacilityObject={onGetFacilityObject}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

FacilityRow2.propTypes = {
  items: PropTypes.array.isRequired,
  onGetFacilityObject: PropTypes.func.isRequired,
};
