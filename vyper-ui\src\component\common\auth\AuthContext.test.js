import { isInRole } from "./AuthContext";

const appName = "Verify Preproduction Engineering Request (VYPER)";
const IT_ADMINS = "IT_ADMINS";
const USER = "USER";
const VYPER_FMX_ADMIN = "VYPER_FMX_ADMIN";

const user = {
  name: "Admin",
  uid: "Admin",
  roles: [
    {
      appName: appName,
      roleName: IT_ADMINS,
    },
    {
      appName: appName,
      roleName: VYPER_FMX_ADMIN,
    },
  ],
};

describe("AuthContext", () => {
  describe("isInRole", () => {
    describe("single allowed role", () => {
      test("allowed for IT_ADMINS", () => {
        expect(isInRole(user, IT_ADMINS)).toEqual(true);
      });

      test("allowed for VYPER_FMX_ADMIN", () => {
        expect(isInRole(user, VYPER_FMX_ADMIN)).toEqual(true);
      });

      test("denied for USER", () => {
        expect(isInRole(user, USER)).toEqual(false);
      });
    });

    describe("multiple allowed roles", () => {
      test("no matching roles", () => {
        expect(isInRole(user, ["ROLE1", "ROLE2"])).toEqual(false);
      });

      test("one matching roles", () => {
        expect(isInRole(user, [IT_ADMINS, "ROLE2"])).toEqual(true);
        expect(isInRole(user, ["ROLE1", IT_ADMINS])).toEqual(true);
      });

      test("multiple matching roles", () => {
        expect(isInRole(user, [IT_ADMINS, VYPER_FMX_ADMIN, "ROLE1"])).toEqual(
          true
        );
        expect(isInRole(user, [IT_ADMINS, "ROLE1", VYPER_FMX_ADMIN])).toEqual(
          true
        );
        expect(isInRole(user, ["ROLE1", IT_ADMINS, VYPER_FMX_ADMIN])).toEqual(
          true
        );
        expect(isInRole(user, ["ROLE1", "ROLE2", "ROLE3"])).toEqual(false);
      });
    });

    describe("handle bad data", () => {
      const badUser1 = { roles: [{ appName: appName, roleName: 1 }] };
      const badUser2 = { roles: [null] };

      describe("single value", () => {
        test("roles are converted to strings, then compared", () => {
          // noinspection JSCheckFunctionSignatures
          expect(isInRole(badUser1, 1)).toEqual(true);
        });

        test("nulls are handled without crashing", () => {
          expect(isInRole(badUser2, null)).toEqual(false);
        });
      });

      describe("array value", () => {
        test("roles are converted to strings, then compared", () => {
          // noinspection JSCheckFunctionSignatures
          expect(isInRole(badUser1, [1])).toEqual(true);
        });

        test("nulls are handled without crashing", () => {
          expect(isInRole(badUser2, [null])).toEqual(false);
        });
      });
    });
  });
});
