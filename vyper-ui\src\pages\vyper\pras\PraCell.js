import PropTypes from "prop-types";
import React, { useContext } from "react";
import {
  ComponentDialogContext,
  FILTER_MODE_PRA,
  FILTER_MODE_SELECTED,
  FILTER_MODE_SUFFIX,
  MODE_SINGLE,
} from "src/component/build-component/ComponentDialog";
import { DataCell } from "src/component/datacell/DataCell";
import { PraDisplay } from "src/pages/vyper/pras/PraDisplay";

/**
 * Display the values for a PRA component
 *
 * @param pra - The PRA object
 * @param name - The component name
 * @param onSave - Callback for saving component value updates
 * @returns {JSX.Element}
 * @function
 */
export const PraCell = ({ pra, name: oldName, onSave, canEdit }) => {
  const { openComponentDialog } = useContext(ComponentDialogContext);

  // is this a flux traveler or a mount compound traveler
  if (oldName !== "Mount Compound") {
    name = oldName;
  } else {
    // if we have flux components, change the name from Mount Compound to Flux
    const hasFluxInstances = pra.components
      .filter((c) => c.name === "Flux")
      .every((c) => c.instances.length > 0);
    name = hasFluxInstances ? "Flux" : "Mount Compound";
  }

  // get the verifiers for the component
  const verifiers = pra.verifiers.filter((verifier) => verifier.name === name);

  // get the component values (removing duplicates)
  const values = verifiers
    .map((verifier) => verifier.value)
    .filter((item, index, items) => items.indexOf(item) === index);

  // get the verifiers that match the value
  const valueVerifiers = (verifiers, value) =>
    verifiers.filter((verifier) => verifier.value === value);

  // get the engineering for the value
  const getEngineering = (verifiers, value) =>
    verifiers.find((verifier) => verifier.value === value)?.engineering || "N";

  // the user clicked the link for the value. show the dialog
  // noinspection JSUnusedLocalSymbols\
  const handleClick = (value, is74, isEngineering, shouldBy74) => {
    // get the supplier part number, or the wire's basemetal + diameter
    let supplierOrWire = pra.components
      .find((c) => c.name === name)
      ?.instances?.[0]?.priorities?.[0]?.object?.name?.replace(
        "Placeholder Supplier #: ",
        ""
      );

    if (supplierOrWire.includes("PLACEHOLDER")) {
      supplierOrWire = supplierOrWire?.replace(/placeholder:galileo:/gi, "");

      if (name === "Wire") {
        let wireOptions = supplierOrWire?.split(";");
        supplierOrWire = `${wireOptions[1]} ${wireOptions[0]}`;
      }
    }

    /*  determine the filter mode

        7-4     engineering shouldbe74 | filtermode
        f       f           f               FILTER_MODE_PRA
        f       f           t               FILTER_MODE_SELECTED
        f       t           f               FILTER_MODE_PRA
        f       t           t               FILTER_MODE_PRA
        t       f           f               FILTER_MODE_SUFFIX
        t       f           t               FILTER_MODE_SUFFIX
        t       t           f               FILTER_MODE_PRA
        t       t           t               FILTER_MODE_PRA
         */

    let filterMode;
    if (isEngineering) {
      filterMode = FILTER_MODE_PRA;
    } else if (!is74 && !isEngineering && shouldBy74) {
      filterMode = FILTER_MODE_SELECTED;
    } else {
      filterMode = FILTER_MODE_SUFFIX;
    }

    // show the component dialog
    openComponentDialog({
      buildNumber: pra.buildNumber,
      material: pra.material.object.Material,
      pin: pra.material.object.PackagePin,
      pkg: pra.material.object.PackageDesignator,
      pkgGroup: pra.material.object.PackageGroup,
      facility: pra.facility.object.PDBFacility,
      plantCode: pra.facility.object.PlantCode,
      component: {
        name: name,
        instances: [{ priorities: [{ object: { name: value } }] }],
      },
      suffix: value.substr(0, 7),
      enableEngineering: false,
      mode: MODE_SINGLE,
      filterMode: filterMode,
      supplierOrWire: supplierOrWire,
      onSave: (updatedComponent) => {
        const newValue =
          updatedComponent.instances[0].priorities[0].object.name;
        onSave(name, value, newValue, pra);
      },
    });
  };

  return (
    <DataCell source={null}>
      {values.map((value) => (
        <div key={value}>
          <PraDisplay
            pra={pra}
            name={name}
            value={value}
            verifiers={valueVerifiers(verifiers, value)}
            engineering={getEngineering(verifiers, value)}
            onClick={handleClick}
            canEdit={canEdit}
          />
        </div>
      ))}
    </DataCell>
  );
};

PraCell.propTypes = {
  pra: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
};
