import React, { useState, useEffect } from "react";
import { withStyles } from "@material-ui/core";
import Grid from "@material-ui/core/Grid";
import Paper from "@material-ui/core/Paper";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import Button from "@material-ui/core/Button";
import useSnackbar from "/src/hooks/Snackbar";

import CommentDialog from "../../comments/CommentDialog";
import RequestorInfo from "../Sections/RequestorInfo";
import GeneralInfo from "../Sections/GeneralInfo";
import DieInfo from "../Sections/DieInfo/DieInfo";
import ManufacturingInfo from "../Sections/ManufacturingInfo/ManufacturingInfo";
import { headerItems } from "../Sections/ManufacturingInfo/headerItems";
import { useHistory } from "react-router-dom";

import axios from "axios";

import { Comment } from "@material-ui/icons";

import {
  REQUIRED_GENERAL_INFO_FIELDS,
  REQUIRED_DIE_INFO_VALS,
} from "../RequiredFormFields";

import {
  BASE_FETCH_DATA_URL,
  BASE_VSCSWR_URL,
  findInvalidFields,
  isValidSubFields,
  DEFAULT_GENERAL_INFO_VALS,
  DEFAULT_REQUESTOR_INFO_VALS,
  DEFAULT_DEVICE_INFO_VALS,
  DEFAULT_ASSEMBLY_INFO_VALS,
  DEFAULT_PACKING_REQUIREMENTS_INFO_VALS,
} from "../FormConstants";

const styles = (theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(4),
  },
  paper: {
    marginTop: "30px",
    padding: "20px",
  },
  secondaryPaper: {
    marginTop: "10px",
    padding: "20px",
  },
  tabBar: {
    position: "sticky",
    top: "50px",
    zIndex: "999",
  },
  tab: {
    minWidth: "150px",
  },
  stickyDiv: {
    position: "sticky",
    marginTop: "10px",
    top: "110px",
    height: "40px",
    width: "100%",
    zIndex: "999",
    display: "flex",
    justifyContent: "space-between",
  },
  textField: {
    "&.MuiInputLabel-outlined": {
      "&.MuiInputLabel-shrink": {
        transform: "translate(14px, -12px) scale(.75)",
        color: "black",
      },
    },
  },
});

const fetchData = (uri, setFormState) => {
  axios.get(uri).then((response) => {
    setFormState(response.data);
  });
};

const FormSelect = (props) => {
  const { classes, defaultValues, saveData } = props;
  const history = useHistory();

  const [selectedTab, setSelectedTab] = useState("General Information");
  const [showComments, setShowComments] = useState(false);
  const [comments, setComments] = useState({});
  const [manufacturingOperation, setManufacturingOperation] =
    useState(selectedTab);

  const [generalInfoForm, setGeneralInfoForm] = useState(
    DEFAULT_GENERAL_INFO_VALS
  );
  const [requestorInfoForm, setRequestorInfoForm] = useState(
    DEFAULT_REQUESTOR_INFO_VALS
  );
  const [deviceInfoForm, setDeviceInfoForm] = useState(
    DEFAULT_DEVICE_INFO_VALS
  );
  const [dieData, setDieData] = useState([]);
  const [assemblyInfoForm, setAssemblyInfoForm] = useState(
    DEFAULT_ASSEMBLY_INFO_VALS
  );
  const [bomData, setBomData] = useState([]);
  const [packingRequirementsInfo, setPackingRequirementsInfo] = useState(
    DEFAULT_PACKING_REQUIREMENTS_INFO_VALS
  );
  const [packingMaterialData, setPackingMaterialData] = useState([]);
  const [shippingData, setShippingData] = useState([]);
  const [travelerInfo, setTravelerInfo] = useState({});

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { enqueueErrorSnackbar } = useSnackbar();

  useEffect(() => {
    fetchData(`${BASE_FETCH_DATA_URL}/requestorInfo`, setRequestorInfoForm);
    defaultValues.generalInfo && setGeneralInfoForm(defaultValues.generalInfo);
    defaultValues.bomData && setBomData(defaultValues.bomData);
    defaultValues.packingMaterialData &&
      setPackingMaterialData(defaultValues.packingMaterialData);
    defaultValues.traveler && setTravelerInfo(defaultValues.traveler);
    defaultValues.dieInfo && setDieData(defaultValues.dieInfo);
    defaultValues.deviceInfo && setDeviceInfoForm(defaultValues.deviceInfo);
  }, [defaultValues]);

  const existingScswrCallBack = (existingScswrData) => {
    setGeneralInfoForm((prevState) => {
      return {
        ...prevState,
        title: existingScswrData.title || "",
        purpose: existingScswrData.purpose || "",
        currentRequestor: existingScswrData.name || "",
        currentStatus: existingScswrData.currentStatus || "",
        purchaseOrder: existingScswrData.purchaseOrder || "",
        lineItem: existingScswrData.lineItem || "",
        io: existingScswrData.io || "",
        scswrMaterial: existingScswrData.sapMaterial || "",
        scswrPlant: existingScswrData.plantCode || "",
        scswrFacility: existingScswrData.atSite || "",
        scswrSpecDevice: existingScswrData.specDevice || "",
      };
    });
  };

  const handleExistingScswrInfo = (existingScswrID) => {
    if (existingScswrID.length === 11) {
      fetchData(
        `${BASE_VSCSWR_URL}/isScswrStatusBlocked/${existingScswrID}`,
        (isStatusBlocked) => {
          if (isStatusBlocked !== "") {
            enqueueErrorSnackbar(isStatusBlocked);
          } else {
            fetchData(
              `${BASE_VSCSWR_URL}/fetchExistingScswr/${existingScswrID}`,
              existingScswrCallBack
            );
          }
        }
      );
    } else {
      existingScswrCallBack({});
    }
  };

  const handleCommentClick = (operation) => {
    setManufacturingOperation(operation);
    setShowComments(true);
  };

  const handleCommentClose = () => {
    setShowComments(false);
    setManufacturingOperation(selectedTab);
  };

  const handleTabChange = (event, val) => {
    setManufacturingOperation(val);
    setSelectedTab(val);
  };

  const submitCallback = (response) => {
    setIsSubmitting(false);
    if (response.status === 200) {
      response.json().then((json) => {
        history.push(`/vswr/view/${json.vswrID}`);
        setGeneralInfoForm(json);
      });
      setSelectedTab("General Information");
    } else {
      enqueueErrorSnackbar(
        "SWR failed to submit. If issue persist please contact IT"
      );
    }
  };

  const canSave = () => {
    const message = "Please complete the following fields before submitting\n";
    let invalidFields = [];

    invalidFields = findInvalidFields(
      generalInfoForm,
      REQUIRED_GENERAL_INFO_FIELDS
    );
    if (invalidFields.length !== 0) {
      setSelectedTab("General Information");
      enqueueErrorSnackbar(`${message} ${invalidFields.join(", ")}`);
      return false;
    }

    if (
      generalInfoForm?.existingScswrID != null &&
      generalInfoForm?.existingScswrID?.length !== 0 &&
      generalInfoForm?.existingScswrID?.length !== 11
    ) {
      setSelectedTab("General Information");
      enqueueErrorSnackbar("Invalid Existing SCSWR ID");
      return false;
    }

    if (generalInfoForm?.existingScswrID?.length === 11) {
      const formatter = new Intl.ListFormat("en", {
        style: "long",
        type: "conjunction",
      });
      let fields = [];
      if (generalInfoForm.scswrPlant != generalInfoForm.plant) {
        fields.push("plant");
      }
      // if(generalInfoForm.scswrFacility != generalInfoForm.facility){
      //     fields.push('facility');
      // }
      if (generalInfoForm.scswrMaterial != generalInfoForm.vbuildMaterial) {
        fields.push("material");
      }
      if (generalInfoForm.scswrSpecDevice != generalInfoForm.specDevice) {
        fields.push("spec device");
      }
      if (fields.length > 0) {
        setSelectedTab("General Information");
        enqueueErrorSnackbar(
          `Fields ${formatter.format(
            fields
          )} do not match with the existing SCSWR`
        );
        return false;
      }
    }

    // invalidFields = findInvalidFields(deviceInfoForm, REQUIRED_DEVICE_INFO_FIELDS,);
    // if(invalidFields.length !== 0){
    //     setSelectedTab('Device Information');
    //     enqueueErrorSnackbar(`${message} ${invalidFields.join(', ')}`);
    //     return;
    // }
    /*
    if (!isValidSubFields(dieData, REQUIRED_DIE_INFO_VALS)) {
      setSelectedTab("Die Information");
      enqueueErrorSnackbar(
        "At least one die must contain a die lot entry before submitting"
      );
      return false;
    } */

    // invalidFields = findInvalidFields(packingRequirementsInfo, REQUIRED_PACKING_REQUIREMENTS_FIELDS);
    // if(invalidFields.length !== 0){
    //     setSelectedTab('Packing Information');
    //     enqueueErrorSnackbar(`${message} ${invalidFields.join(', ')}`);
    //     return;
    // }

    // if(shippingData.length === 0 ){
    //     setSelectedTab('Shipping Information');
    //     enqueueErrorSnackbar('At least one Shipping entry must be entered.');
    //     return;
    // }

    return true;
  };

  const handleSave = () => {
    if (!canSave()) {
      return;
    }

    setIsSubmitting(true);

    const submitData = {
      generalInfo: generalInfoForm,
      requestorInfo: requestorInfoForm,
      deviceInfo: deviceInfoForm,
      dieInfo: dieData,
      assemblyInfo: assemblyInfoForm,
      bomInfo: bomData,
      packingRequirements: packingRequirementsInfo,
      packingMaterial: packingMaterialData,
      shippingInfo: shippingData,
      comments: comments,
    };

    saveData(submitData, submitCallback);
  };

  const informationFieldTabs = {
    "General Information": (
      <Grid item>
        <GeneralInfo
          formState={generalInfoForm}
          setFormState={setGeneralInfoForm}
          handleExistingScswrInfo={handleExistingScswrInfo}
          classes={classes}
        />
        <RequestorInfo
          formState={requestorInfoForm}
          setFormState={setRequestorInfoForm}
          classes={classes}
        />
      </Grid>
    ),
    //  "Device Information": (
    //     <DeviceInfo
    //         formState={deviceInfoForm}
    //         setFormState={setDeviceInfoForm}
    //         classes={classes}
    //     />
    // ),
    "Die Information": (
      <DieInfo dieData={dieData} setDieData={setDieData} classes={classes} />
    ),
    //  "Assembly Information": (
    //     <Grid item style={{width:'100%'}}>
    //         <AssemblyInfo
    //             formState={assemblyInfoForm}
    //             setFormState={setAssemblyInfoForm}
    //             classes={classes}
    //             />
    //         <BomInfo
    //             bomData={bomData}
    //             forecastInfo={forecastInfo}
    //             classes={classes}
    //             />
    //     </Grid>
    // ),
    //  "Symbol Information": (
    //     <SymbolInfo
    //         symbolData={symbolData}
    //         classes={classes}
    //     />
    // ),
    // "Packing Information": (
    //    <Grid item>
    //        <PackingRequirements
    //            formState={packingRequirementsInfo}
    //            setFormState={setPackingRequirementsInfo}
    //            classes={classes}
    //        />
    //        <PackingMaterial
    //            packingMaterialData={packingMaterialData}
    //            classes={classes}
    //        />
    //    </Grid>
    // ),
    // "Shipping Information": (
    //     <ShippingInfo
    //         shippingData={shippingData}
    //         setShippingData={setShippingData}
    //         classes={classes}
    //     />
    // ),
    "Manufacturing Information": (
      <ManufacturingInfo
        travelerInfo={travelerInfo}
        headerItems={headerItems(deviceInfoForm) || []}
        handleCommentClick={handleCommentClick}
        comments={comments}
        vbuildID={generalInfoForm.vbuildID}
        classes={classes}
      />
    ),
  };

  return (
    <div className={classes.root}>
      <CommentDialog
        showComments={showComments}
        handleClose={handleCommentClose}
        comments={comments}
        defaultSelected={manufacturingOperation}
        setComments={setComments}
      />
      <Grid item lg={12} sm={12} xl={12} xs={12}>
        <Paper className={classes.tabBar}>
          <Tabs
            value={selectedTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            {Object.keys(informationFieldTabs).map((tab, i) => (
              <Tab className={classes.tab} key={i} value={tab} label={tab} />
            ))}
          </Tabs>
        </Paper>
        <div className={classes.stickyDiv}>
          <Button
            disabled={isSubmitting}
            variant="contained"
            color="primary"
            onClick={handleSave}
          >
            Save
          </Button>
          {selectedTab === "Manufacturing Information" && (
            <Button
              variant="contained"
              color="secondary"
              onClick={() => setShowComments(true)}
            >
              <Comment />
              Comments
            </Button>
          )}
        </div>

        <Grid
          container
          spacing={0}
          direction="column"
          alignItems="center"
          justifyContent="flex-start"
        >
          {informationFieldTabs[selectedTab]}
        </Grid>
      </Grid>
    </div>
  );
};

export default withStyles(styles)(FormSelect);
