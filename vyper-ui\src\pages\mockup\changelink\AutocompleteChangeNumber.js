import React, { useEffect, useState } from "react";
import { Autocomplete } from "@material-ui/lab";
import TextField from "@material-ui/core/TextField";
import CircularProgress from "@material-ui/core/CircularProgress";

export const AutocompleteChangeNumber = ({ changeNumber, onChangeNumber, disabled }) => {
  const [open, setOpen] = React.useState(false);
  const [options, setOptions] = React.useState([]);

  const [search, setSearch] = React.useState(changeNumber);
  const handleInputChange = (e, v) => setSearch(v);
  const [loading, setLoading] = useState(false);

  const handleChangNumber = (e, v) => onChangeNumber(v);

  useEffect(() => {
    if (search == null || search === "") {
      return undefined;
    }

    setLoading(true);

    fetch(
      `/vyper/v1/changelink/change/autocomplete/changeNumber?search=${search}`
    )
      .then((response) => response.json())
      .then((json) => setOptions(json))
      .catch((e) => console.log(e))
      .finally(() => setLoading(false));
  }, [search]);

  React.useEffect(() => {
    if (!open) {
      setOptions([]);
    }
  }, [open]);

  return (
    <Autocomplete
      disabled={disabled}
      open={open}
      onOpen={() => setOpen(true)}
      onClose={() => setOpen(false)}
      renderInput={(params) => (
        <TextField
          {...params}
          label="CMS ChangeNumber"
          variant="outlined"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {loading ? (
                  <CircularProgress color="inherit" size={20} />
                ) : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
          }}
        />
      )}
      options={options}
      loading={loading}
      inputValue={search}
      onInputChange={handleInputChange}
      value={changeNumber}
      onChange={handleChangNumber}
      style={{ minWidth: "20em" }}
    />
  );
};
