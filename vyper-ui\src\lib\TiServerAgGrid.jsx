import React, {
  useState,
  forwardRef,
  use<PERSON>allback,
  useMemo,
  useEffect,
} from "react";
import "./TiAgGrid.css";

import { AgGridReact } from "ag-grid-react"; // the AG Grid React Component
import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS
import TiPagination from "./TiPagination";
import { saveAs } from "file-saver";

const defaultcreateURLParamsFunc = (pageSize, pageNum, sort, filters) => {
  let sortString = sort.map((item) => {
    return `${item.colId},${item.sort}`;
  });

  let filterString = Object.keys(filters).map((key) => {
    return `${key}|${filters[key].type}|${filters[key].filter}`;
  });

  const params = {
    page: pageNum,
    size: pageSize,
    sort: sortString.toString(),
    filter: filterString.toString(),
  };

  const query = new URLSearchParams(params);
  return `?${query.toString()}`;
};

const defaultFilterColDef = {
  suppressMenu: true,
  sortable: true,
  floatingFilter: true,
  filter: "agTextColumnFilter",
  filterParams: {
    suppressAndOrCondition: true,
  },
};
/**
 * @component
 * @param {object & AgGridReact} props Component props.
 * @param {object} ref Ref props.
 *
 * @typedef {object} urlObject
 * @example
 * {
 *      url: <string>,
 *      options: {method, headers, body, ...}
 * }
 *
 * @property {string | urlObject} prop.url String or urlObject to api endpoint.
 * @property {object} [prop.style] Styling object for container.
 * @property {string} [prop.classNames] Additional class names for container, defaulted to ag-theme-alpine.
 *
 * @typedef {object} filterModel
 * @example
 * {
 *      <colDef>: {
 *          filterType: <string> 'text' | 'number',
 *          type: <string> 'contains' | 'equals' | 'lessThan' | ... ,
 *          filter: 'value'
 *      }
 * }
 *
 * @property {object} [prop.initialFilterModel] Initial filter state for grid to use.
 * @property {(filterModel)=>void} [prop.getFilterModel] Callback function for retrieving current filter state
 *
 * @typedef {object} sortModel
 * @example
 *
 * {
 * state: [
 *   { colId: 'country', sort: 'asc', sortIndex: 0 },
 *   { colId: 'sport', sort: 'asc', sortIndex: 1 },
 *   ],
 *   defaultState: { sort: null },
 * }
 *
 * @property {sortModel} [prop.initialSortModel] Initial sort state for grid to use.
 * @property {(sortModel)=>void} [prop.getSortModel] Callback function for retrieving current sort state
 * @property {boolean} [prop.useDefaultFilter] Enable default filter.
 * @property {(number, number, sortModel, filterModel)=>query string} [prop.createURLParamsFunc] Function to create url search query.
 * @property {boolean} [prop.suppressPaginationPanel] Suppress default pagination ui and functions.
 * @property {number} [prop.defaultPageSize] Default page size on initial render.
 * @property {list<number>} [prop.paginationSelectOptions] List of page sizes for pagination.
 * @property {(gridRef)=>void} [prop.onPaginationChanged] Callback function on pagination changes.
 * @property {(gridRef)=>void} [prop.onGridReady] Callback function on grid ready.
 *
 * @typedef {object} dataSource
 * @example
 * {
 *      rowCount: <number>,
 *      getRows: <function> (params)=>void
 * }
 * @property {dataSource} [prop.dataSource]
 * @property {object} [prop.defaultColDef]
 */
const TiServerAgGrid = forwardRef((props, ref) => {
  const {
    url,
    style = {},
    classNames = "ag-theme-alpine",
    initialFilterModel = {},
    getFilterModel = null,
    initialSortModel = {},
    getSortModel = null,
    useDefaultFilter = true,
    createURLParamsFunc = defaultcreateURLParamsFunc,
    suppressPaginationPanel = false,
    defaultPageSize = null,
    paginationSelectOptions = [],
    onPaginationChanged: userOnPaginationChanged = null,
    onGridReady: userOnGridReady = null,
  } = props;
  const [gridRef, setGridRef] = useState(null);
  const [pageSize, setPageSize] = useState(
    defaultPageSize ||
      (paginationSelectOptions.length > 0 && paginationSelectOptions[0]) ||
      20
  );
  const [paginationVals, setPaginationVals] = useState(null);

  //fetches based of larges select option available
  const cacheBlockSize = useMemo(() => {
    return Math.max(...paginationSelectOptions, 100);
  }, [paginationSelectOptions]);

  // set default filter column defs if user needs filter
  const defaultColDef = useMemo(() => {
    if (useDefaultFilter) {
      return {
        ...defaultFilterColDef,
        ...props.defaultColDef,
      };
    }
    return { ...props.defaultColDef };
  }, [props.defaultColDef, useDefaultFilter]);

  // user data source to be used over default
  const dataSource = useMemo(() => {
    let fetchUrl = url;
    let options = {};
    if (typeof url === "object") {
      fetchUrl = url.url;
      options = url.options;
    }

    return (
      props.dataSource || {
        rowCount: undefined,
        getRows: (dsParams) => {
          //calculate current cache block based of current row displayed
          const currentCacheBlock = dsParams.startRow / cacheBlockSize;
          const urlParams =
            createURLParamsFunc(
              cacheBlockSize,
              currentCacheBlock,
              dsParams.sortModel,
              dsParams.filterModel
            ) || "";
          fetch(`${fetchUrl}${urlParams}`, options)
            .then((resp) => resp.json())
            .then((data) => {
              dsParams.successCallback(data.content, data.totalElements);
            })
            .catch((e) => {
              dsParams.failCallback();
              console.error(e);
            });
        },
      }
    );
  }, [url, props.dataSource, cacheBlockSize, createURLParamsFunc]);

  useEffect(() => {
    if (gridRef) {
      if (Object.keys(initialFilterModel).length > 0) {
        gridRef.api.setFilterModel(initialFilterModel);
      }
    }
  }, [gridRef, initialFilterModel]);

  useEffect(() => {
    if (gridRef) {
      if (Object.keys(initialSortModel).length > 0) {
        gridRef.columnApi.applyColumnState(initialSortModel);
      }
    }
  }, [gridRef, initialSortModel]);

  const setPageVals = useCallback(
    (gridRef) => {
      if (suppressPaginationPanel) {
        return;
      }
      let paginationVals = {};

      const currentPage = gridRef.api.paginationGetCurrentPage();
      const rowCount = gridRef.api.paginationGetRowCount();
      const pageSize = gridRef.api.paginationGetPageSize();
      const totalPages = gridRef.api.paginationGetTotalPages();

      paginationVals["firstRow"] = currentPage * pageSize + 1;
      paginationVals["lastRow"] = Math.min(
        rowCount,
        pageSize * (currentPage + 1)
      );
      paginationVals["totalRows"] = rowCount;
      paginationVals["currPage"] = currentPage + 1;
      paginationVals["totalPages"] = totalPages;

      paginationVals["onFirstPageClick"] = () => {
        gridRef.api.paginationGoToFirstPage();
      };
      paginationVals["onPrevPageClick"] = () => {
        gridRef.api.paginationGoToPreviousPage();
      };
      paginationVals["onNextPageClick"] = () => {
        gridRef.api.paginationGoToNextPage();
      };
      paginationVals["onLastPageClick"] = () => {
        gridRef.api.paginationGoToLastPage();
      };

      setPaginationVals(paginationVals);
    },
    [suppressPaginationPanel]
  );

  const onGridReady = useCallback(
    (params) => {
      //sets user callback if passed;
      if (typeof userOnGridReady === "function") {
        userOnGridReady(params);
      }

      //sets data source
      params.api.setDatasource(dataSource);

      //sets ref hook
      setGridRef(params);

      //initializes custom pagination
      setPageVals(params);
    },
    [userOnGridReady, dataSource, setPageVals]
  );

  const onPageSelectChange = (evt) => {
    gridRef.api.paginationSetPageSize(evt.target.value);
    setPageSize(evt.target.value);
  };

  const onPaginationChanged = (gridRef) => {
    if (typeof userOnPaginationChanged === "function") {
      userOnPaginationChanged(gridRef);
    }
    if (typeof getFilterModel === "function") {
      getFilterModel(gridRef.api.getFilterModel());
    }
    if (typeof getSortModel === "function") {
      getSortModel(() => {
        let colState = gridRef.columnApi.getColumnState();
        let sortState = colState
          .filter((s) => {
            return s.sort != null;
          })
          .map((s) => {
            return { colId: s.colId, sort: s.sort, sortIndex: s.sortIndex };
          });
        return sortState;
      });
    }
    setPageVals(gridRef);
  };

  return (
    <div style={style} className={`ti-server-ag-grid ${classNames}`}>
      <AgGridReact
        ref={ref}
        maxBlocksInCache={1}
        enableCellTextSelection={true}
        cacheBlockSize={cacheBlockSize}
        paginationPageSize={pageSize}
        onPaginationChanged={onPaginationChanged}
        suppressPaginationPanel={true}
        rowModelType={"infinite"}
        pagination={true}
        {...props}
        defaultColDef={defaultColDef}
        onGridReady={onGridReady}
      />
      {!suppressPaginationPanel && paginationVals !== null && (
        <TiPagination
          selectOptions={paginationSelectOptions}
          onSelectChange={onPageSelectChange}
          firstRow={paginationVals.firstRow}
          lastRow={paginationVals.lastRow}
          totalRows={paginationVals.totalRows}
          currPage={paginationVals.currPage}
          totalPages={paginationVals.totalPages}
          onFirstPageClick={paginationVals.onFirstPageClick}
          onPrevPageClick={paginationVals.onPrevPageClick}
          onNextPageClick={paginationVals.onNextPageClick}
          onLastPageClick={paginationVals.onLastPageClick}
          defaultPageSize={defaultPageSize}
        />
      )}
    </div>
  );
});
export default TiServerAgGrid;

/**
 * Export grid data as Excel.
 * @param {GridApi} api Grid API.
 * @param {ColumnApi} columnApi Column API.
 * @param {object} options Export options.
 * @param {(cell: exceljs.Cell, rowNode: RowNode, colDef: ColDef) => void} options.cellProcessor Cell processor function.
 * @param {(column: Column) => boolean} options.columnFilter Column filter function.
 * @param {string} options.fileName Output file name.
 * @param {(rowNode: RowNode) => boolean} options.rowFilter Row filter function.
 * @param {string} options.tabName Output tab name.
 */
export const exportGridAsExcel = (api, columnApi, options = {}) => {
  import("exceljs").then((exceljs) => {
    console.log("exceljs=", exceljs);
    const { cellProcessor, columnFilter, fileName, rowFilter, tabName } =
      options;
    const wb = new exceljs.Workbook();
    const ws = wb.addWorksheet(tabName ?? "Data");
    const excelContentType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    const ignoreRenderers = ["actionCellRenderer"];

    /** @type {{[name: string]: number}} */
    const colIndex = {};
    /** @type {Array<ColDef>} */
    const colDefs = [];
    const columns = columnApi.getAllGridColumns().filter((column) => {
      const colDef = column.getColDef();
      if (ignoreRenderers.includes(colDef.cellRenderer)) {
        return false;
      }
      if (columnFilter) {
        return columnFilter(column);
      } else {
        return column.isVisible();
      }
    });
    columns.forEach((column, index) => {
      const colDef = column.getColDef();
      const name = colDef.headerName;
      colIndex[name] = index + 1;
      colDefs[index + 1] = colDef;
      ws.getColumn(index + 1).width = (colDef.width ?? 120) / 8;
    });

    const EXCEL_CLASS_MAP = {
      Header: {
        bold: true,
        bgColor: "FFCC0000",
        fgColor: "FFFFFFFF",
      },
      Link: {
        fgColor: "FF118899",
      },
      GreyCell: {
        bgColor: "FFC4C4C4",
      },
    };

    const applyCellStyle = (cell, classes) => {
      if (classes == null) {
        classes = [];
      }
      if (!Array.isArray(classes)) {
        classes = [classes];
      }
      classes.forEach((cls) => {
        const mappedStyle = EXCEL_CLASS_MAP[cls];
        if (mappedStyle) {
          if (mappedStyle.bgColor) {
            if (cell.fill == null) {
              cell.fill = {};
            }
            cell.fill.type = "pattern";
            cell.fill.pattern = "solid";
            cell.fill.fgColor = { argb: mappedStyle.bgColor };
          }
          if (mappedStyle.fgColor || mappedStyle.bold) {
            if (cell.font == null) {
              cell.font = {};
            }
            if (mappedStyle.fgColor) {
              cell.font.color = { argb: mappedStyle.fgColor };
            }
            if (mappedStyle.bold) {
              cell.font.bold = true;
            }
          }
        }
      });
    };

    const headerCells = columns.map((column) => column.getColDef().headerName);
    const headerRow = ws.addRow(headerCells);
    headerRow.eachCell((cell, colNumber) => {
      applyCellStyle(cell, ["Header"]);
      const colDef = colDefs[colNumber];
      if (colDef.headerTooltip != null) {
        cell.note = colDef.headerTooltip;
      }
    });

    api.forEachNode((node) => {
      if (rowFilter && !rowFilter(node)) {
        return;
      }
      const data = node.data;
      headerRow.eachCell((cell, colNumber) => {
        const colDef = colDefs[colNumber];
        if (colDef.headerComponent === "linkCellRenderer") {
          const params = colDef.headerComponentParams;
          const links =
            typeof params.links === "function"
              ? params.links({ data })
              : params.links;
          if (links && !Array.isArray(links)) {
            if (links.label !== undefined) {
              applyCellStyle(cell, ["Link"]);
              cell.value = {
                text: links.label,
                hyperlink: links.url,
                tooltip: links.url,
              };
              cell.alignment = {
                horizontal: "left",
                vertical: "top",
                wrapText: true,
              };
            }
          }
        }
      });

      let cells = columns.map((column) => api.getValue(column, node));

      for (var i = 0; i < cells.length; i++) {
        if (typeof cells[i] === "undefined") {
          cells[i] = "";
        }
      }
      const row = ws.addRow(cells);
      row.eachCell((cell, colNumber) => {
        const colDef = colDefs[colNumber];
        if (typeof colDef.valueFormatter === "function") {
          cell.value = colDef.valueFormatter({
            api: api,
            colDef: colDef,
            column: columns[colNumber - 1],
            columnApi: columnApi,
            context: null,
            data: data,
            node: node,
            value: cell.value,
          });
        }
        if (colDef.linkCellRendererParams !== undefined) {
          /** @type {LinkCellRendererProps} */
          const params = colDef.linkCellRendererParams;
          const links =
            typeof params.links === "function"
              ? params.links({ data })
              : params.links;
          if (links && !Array.isArray(links)) {
            if (links.label !== undefined) {
              applyCellStyle(cell, ["Link"]);
              cell.value = {
                text: links.label,
                hyperlink: links.url,
                tooltip: links.url,
              };
              cell.alignment = {
                horizontal: "left",
                vertical: "top",
                wrapText: colDef.wrapText,
              };
            }
          }
        }

        cell.alignment = {
          horizontal: "left",
          vertical: "top",
          wrapText: colDef.wrapText,
        };
        if (cellProcessor) {
          cellProcessor(cell, node, colDef);
        }
      });
    });

    wb.xlsx
      .writeBuffer()
      .then((buffer) => {
        var blob = new Blob([buffer], {
          type: excelContentType,
        });
        let fn = fileName ?? "Export.xlsx";
        saveAs(blob, fn);
      })
      .catch((error) => {
        console.error(error);
      });
  });
};
