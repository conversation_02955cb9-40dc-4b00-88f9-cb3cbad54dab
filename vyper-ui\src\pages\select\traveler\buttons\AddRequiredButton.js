import makeStyles from "@material-ui/core/styles/makeStyles";
import { IconButton, Tooltip, Zoom } from "@material-ui/core";
import AddIcon from "@material-ui/icons/Add";
import React from "react";

export const AddRequiredButton = ({ title, disabled, onClick }) => {
  const useStyles = makeStyles({
    root: {
      display: "inline",
    },
  });

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Tooltip
        title={title}
        placement="top"
        TransitionComponent={Zoom}
        arrow
        interactive
      >
        <span>
          <IconButton
            disabled={disabled}
            color="primary"
            size="small"
            onClick={onClick}
          >
            <AddIcon fontSize="small" />
          </IconButton>
        </span>
      </Tooltip>
    </div>
  );
};
