import React from "react";
import { Button, TableCell, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

export const BodyButtons = ({ rowButtons, row }) => {
  const classes = makeStyles((theme) => ({
    Button: {
      marginRight: theme.spacing(1),
    },
  }))();

  return rowButtons.length > 0 ? (
    <TableCell size="small" variant="body" wrap="nowrap">
      <Typography noWrap={true}>
        {rowButtons.map((btn, n) => (
          <Button
            key={n}
            className={classes.Button}
            size={btn.size}
            variant={btn.variant || "text"}
            color={btn.color || "primary"}
            startIcon={btn.icon}
            onClick={() => btn.onClick && btn.onClick(row, btn)}
          >
            {btn.title}
          </Button>
        ))}
      </Typography>
    </TableCell>
  ) : null;
};
