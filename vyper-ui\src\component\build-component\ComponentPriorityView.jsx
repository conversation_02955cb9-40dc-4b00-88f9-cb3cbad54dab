import { ComponentNameDisplay } from "src/component/component/ComponentNameDisplay";
import React from "react";

export const ComponentPriorityView = ({ priority }) => {
  //
  // determine the text for the priority

  const determineText = (priority) => {
    if (priority.object.PartNumber) {
      return `${priority.object.name} (${priority.object.PartNumber})`;
    }

    if (priority.object.revision) {
      return `${priority.object.name} ${priority.object.revision}`;
    }

    if (priority.object.Revision) {
      return `${priority.object.name} ${priority.object.Revision}`;
    }

    return priority.object.name;
  };

  return (
    <ComponentNameDisplay
      name={determineText(priority)}
      engineering={priority.engineering}
      warnings={priority.warnings}
      armarcCheckMessage={priority.armarcCheckMessage}
    />
  );
};
