import React from "react";
import TextField from "@material-ui/core/TextField";
import MenuItem from "@material-ui/core/MenuItem";
import { useTravelerStatus } from "src/component/atss/hooks/useTravelerStatus";

export const AtssTravelerStatusSelect = ({
  specDevice = "",
  facilityAt = "",
  status = "",
  onChange,
  showSelectedText,
}) => {
  const { statuses } = useTravelerStatus(specDevice, facilityAt);

  const enabled = !!specDevice && !!facilityAt;

  return (
    <TextField
      disabled={!enabled}
      select
      label="Status"
      variant="outlined"
      value={status}
      onChange={(e) => onChange(e.target.value)}
      InputLabelProps={{ shrink: true }}
      style={{ minWidth: "10em" }}
      helperText={
        showSelectedText
          ? !status
            ? "not selected"
            : `selected: ${status}`
          : ""
      }
      size="small"
    >
      {statuses.map((status) => (
        <MenuItem key={status} value={status}>
          {status}
        </MenuItem>
      ))}
      {status === "" && <MenuItem value="" />}
    </TextField>
  );
};
