import React, { useContext, useEffect, useState } from "react";
import config from "../../../buildEnvironment";
import clsx from "clsx";
import PropTypes from "prop-types";
import {
  AppBar,
  Chip,
  Hidden,
  IconButton,
  InputBase,
  List,
  ListItem,
  ListItemText,
  Menu,
  MenuItem,
  Toolbar,
  Tooltip,
  Typography,
} from "@material-ui/core";
import { alpha } from "@material-ui/core/styles";
import useScrollTrigger from "@material-ui/core/useScrollTrigger";
import AccountCircle from "@material-ui/icons/AccountCircle";
import AppsIcon from "@material-ui/icons/Apps";
import ArrowDropDownIcon from "@material-ui/icons/ArrowDropDown";
import HelpIcon from "@material-ui/icons/Help";
import MenuIcon from "@material-ui/icons/Menu";
import SearchIcon from "@material-ui/icons/Search";
import { AppToolbarUtil } from "@ti/simba-common-util";
import { AuthContext } from "../auth/AuthContext";
import tiLogo from "./ti-logo-white.svg";
import makeStyles from "@material-ui/core/styles/makeStyles";

const ElevationScroll = (props) => {
  const { children, window } = props;
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 0,
    target: window ? window() : undefined,
  });

  return React.cloneElement(children, {
    elevation: trigger ? 4 : 0,
  });
};

const useStyles = makeStyles((theme) => ({
  flexGrow: {
    flexGrow: 1,
  },
  appNameButton: {
    padding: "0px",
  },
  cipFloating: {
    position: "absolute",
    top: "34px",
    left: "60px",
    fontSize: "7pt",
  },
  horizontalList: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    paddingTop: "0px",
    paddingBottom: "0px",
    marginLeft: "4px",
    marginRight: "4px",
  },
  horizontalListItem: {
    padding: "2px 8px",
  },
  search: {
    position: "relative",
    borderRadius: theme.shape.borderRadius,
    backgroundColor: alpha(theme.palette.common.white, 0.15),
    "&:hover": {
      backgroundColor: alpha(theme.palette.common.white, 0.25),
    },
    padding: "2px 8px",
  },
  searchInput: {
    padding: "0px 4px",
  },
  inputRoot: {
    color: "inherit",
  },
  inputInput: {
    padding: "0px",
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("md")]: {
      width: "18ch",
    },
  },
}));

const nbSpace = "\u00A0";

const Topbar = (props) => {
  const { className, onSidebarOpen, history, isAutoDesktop, ...rest } = props;
  const classes = useStyles();
  const {
    templateAppName,
    apiGatewayLogoutUrl,
    templateCip,
    templateEnvMessage,
    templateShowIcon,
  } = config;
  const { isAuthenticated } = useContext(AuthContext);
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuOpen, setMenuOpen] = useState(null);
  const [searchType, setSearchType] = useState(null);
  const [searchText, setSearchText] = useState("");
  const iconSize = "25px";
  const isLgUp = isAutoDesktop ? true : false;

  /**
   * @type {[import("@ti/simba-common-util").AppToolbarConfig]}
   */
  const [toolbarConfig, setToolbarConfig] = useState(
    AppToolbarUtil.getAppToolbarConfig()
  );

  const handleMenu = (event) => {
    const target = event.currentTarget;
    if (target.getAttribute("aria-label")) {
      setAnchorEl(target);
      setMenuOpen(target.getAttribute("aria-label"));
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
    setMenuOpen(null);
  };

  /**
   * @param {import("@ti/simba-common-util").AppMenuItem} item
   * @param {React.MouseEvent} event
   */
  const handleItem = (item, event) => {
    handleClose();
    if (item && item.onClick && event) {
      item.onClick(event);
    }
    if (item.href && !event.isDefaultPrevented()) {
      const path = item.href;
      history.push(path);
    }
  };

  const handleHome = () => {
    history.push(toolbarConfig.basePath);
  };

  const handleProfile = () => {
    setAnchorEl(null);
    history.push("/profile");
  };

  const handleLogout = () => {
    window.location.href = apiGatewayLogoutUrl;
  };

  const handleSearch = (event) => {
    if (toolbarConfig.search.onSearch) {
      /**
       * @type {import("@ti/simba-common-util").AppSearchParam}
       */
      const param = { type: searchType, value: searchText };
      toolbarConfig.search.onSearch(event, param);
    }
  };

  const handleSearchText = (event) => {
    setSearchText(event.target.value);
  };

  const handleSearchType = (event) => {
    if (event.target) {
      setSearchType(event.target.getAttribute("aria-label"));
    }
    handleClose();
  };

  /**
   * Create a top-level list item
   * @param {import("@ti/simba-common-util").AppMenuItem} item
   */
  const createListItem = (item) => {
    const hasChildren = item.children && item.children.length > 0;
    /**
     * @type {React.CSSProperties}
     */
    const customStyle = item.style ? item.style : undefined;
    if (hasChildren) {
      return (
        <ListItem
          button
          aria-label={item.label}
          key={item.label}
          onClick={handleMenu}
          className={classes.horizontalListItem}
          style={customStyle}
        >
          {item.icon ? item.icon : null}
          <Hidden smDown>
            <ListItemText
              primary={item.label}
              primaryTypographyProps={{ noWrap: true }}
            />
          </Hidden>
          <ArrowDropDownIcon />
        </ListItem>
      );
    } else {
      return (
        <ListItem
          button
          aria-label={item.label}
          key={item.label}
          onClick={handleItem.bind(this, item)}
          className={classes.horizontalListItem}
          style={customStyle}
          to={item.href ? item.href : undefined}
        >
          {item.icon ? item.icon : null}
          <Hidden smDown>
            <ListItemText
              primary={item.label}
              primaryTypographyProps={{ noWrap: true }}
            />
          </Hidden>
        </ListItem>
      );
    }
  };

  /**
   * Create a menu item
   * @param {import("@ti/simba-common-util").AppMenuItem} item
   */
  const createMenuItem = (item) => {
    /**
     * @type {React.CSSProperties}
     */
    const customStyle = item.style ? item.style : undefined;
    return (
      <MenuItem
        key={item.label}
        aria-label={item.label}
        dense={true}
        to={item.href ? item.href : undefined}
        onClick={handleItem.bind(this, item)}
      >
        {item.icon ? item.icon : null}
        {item.label}
      </MenuItem>
    );
  };

  /**
   * Create a top-level menu
   * @param {import("@ti/simba-common-util").AppMenuItem} item
   */
  const createMenu = (item) => {
    return (
      <Menu
        key={item.label}
        anchorEl={anchorEl}
        getContentAnchorEl={null}
        keepMounted
        open={Boolean(anchorEl) && menuOpen === item.label}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        {item.children &&
          item.children.length > 0 &&
          item.children.map((child) => createMenuItem(child))}
      </Menu>
    );
  };

  /**
   * Create search type drop-down
   * @param {import("@ti/simba-common-util").AppSearchConfig} search
   */
  const createSearchType = (search) => {
    return (
      <>
        <ListItem
          button
          aria-label="Search Type"
          key="Search Type"
          onClick={handleMenu}
          className={classes.horizontalListItem}
        >
          <ListItemText
            primary={searchType}
            primaryTypographyProps={{ noWrap: true }}
          />
          <ArrowDropDownIcon />
        </ListItem>
        <Menu
          anchorEl={anchorEl}
          getContentAnchorEl={null}
          keepMounted
          open={Boolean(anchorEl) && menuOpen === "Search Type"}
          onClose={handleClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "left",
          }}
        >
          {search.searchTypes.map((item, index) => (
            <MenuItem
              key={index}
              aria-label={item}
              value={item}
              onClick={handleSearchType}
            >
              {item}
            </MenuItem>
          ))}
        </Menu>
      </>
    );
  };

  useEffect(() => {
    const subscription =
      AppToolbarUtil.getAppToolbarConfig$().subscribe(setToolbarConfig);
    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    if (toolbarConfig) {
      if (searchType == null && toolbarConfig.search) {
        const search = toolbarConfig.search;
        if (search.searchType != null) {
          setSearchType(search.searchType);
        } else if (search.searchTypes && search.searchTypes.length > 0) {
          setSearchType(search.searchTypes[0]);
        }
      }
    }
  }, [searchType, toolbarConfig]);

  const renderMap = {
    appMenu: () => {
      return (
        <Hidden lgUp={isLgUp}>
          <IconButton color="inherit" onClick={onSidebarOpen}>
            <MenuIcon />
          </IconButton>
        </Hidden>
      );
    },
    appSwitcher: () => {
      return (
        <Tooltip
          key="appSwitcher"
          title="Switch App (not available in playground mode)"
        >
          <IconButton color="inherit">
            <AppsIcon />
          </IconButton>
        </Tooltip>
      );
    },
    appIcon: () => {
      return (
        <>
          {/* opinionated - render if templateShowIcon is true */}
          {templateShowIcon && (
            <>
              {nbSpace}
              <img
                src={tiLogo}
                width={iconSize}
                height={iconSize}
                alt={templateAppName}
              />
            </>
          )}
        </>
      );
    },
    appName: () => {
      return (
        <React.Fragment key="appName">
          {/* opinionated - render if templateAppName exists */}
          {templateAppName && (
            <Typography variant="h6" color="inherit" noWrap>
              {nbSpace}
              {templateAppName}
            </Typography>
          )}
        </React.Fragment>
      );
    },
    appNameNew: () => {
      return (
        <React.Fragment key="appNameNew">
          {/* opinionated - render if templateAppName exists */}
          {templateAppName && (
            <Button
              color="inherit"
              className={classes.appNameButton}
              onClick={handleHome}
            >
              <Typography variant="h6" color="inherit" noWrap>
                {templateAppName}
              </Typography>
            </Button>
          )}
        </React.Fragment>
      );
    },
    flexGrow: () => {
      return <div key="flexGrow" className={classes.flexGrow} />;
    },
    appToolbarLeftMenu: () => {
      return (
        <React.Fragment key="appToolbarLeftMenu">
          {toolbarConfig && toolbarConfig.leftMenu && (
            <>
              <List className={classes.horizontalList}>
                {toolbarConfig.leftMenu.map((item) => createListItem(item))}
              </List>
              {toolbarConfig.leftMenu.map((item) => createMenu(item))}
            </>
          )}
        </React.Fragment>
      );
    },
    appToolbarSearch: () => {
      return (
        <React.Fragment key="appToolbarSearch">
          {toolbarConfig &&
            toolbarConfig.search &&
            toolbarConfig.search.enabled && (
              <div className={classes.search}>
                <div className={classes.menuBar}>
                  <List className={classes.horizontalList}>
                    {toolbarConfig.search.searchTypes &&
                      toolbarConfig.search.searchTypes.length > 0 && (
                        <ListItem
                          key="SearchType"
                          className={classes.searchInput}
                        >
                          {createSearchType(toolbarConfig.search)}
                        </ListItem>
                      )}
                    <ListItem key="SearchText" className={classes.searchInput}>
                      <InputBase
                        placeholder="Search"
                        classes={{
                          root: classes.inputRoot,
                          input: classes.inputInput,
                        }}
                        inputProps={{ "aria-label": "search" }}
                        onChange={handleSearchText}
                      />
                    </ListItem>
                    <Tooltip title="Search">
                      <IconButton
                        aria-label="Search"
                        onClick={handleSearch}
                        color="inherit"
                      >
                        <SearchIcon />
                      </IconButton>
                    </Tooltip>
                  </List>
                </div>
              </div>
            )}
        </React.Fragment>
      );
    },
    appToolbarRightMenu: () => {
      return (
        <React.Fragment key="appToolbarRightMenu">
          {toolbarConfig && toolbarConfig.rightMenu && (
            <>
              <List className={classes.horizontalList}>
                {toolbarConfig.rightMenu.map((item) => createListItem(item))}
              </List>
              {toolbarConfig.rightMenu.map((item) => createMenu(item))}
            </>
          )}
        </React.Fragment>
      );
    },
    envMessage: () => {
      return (
        <React.Fragment key="envMessage">
          {/* opinionated - render if templateEnvMessage exists */}
          {templateEnvMessage && (
            <>
              {nbSpace}
              <Chip label={templateEnvMessage} color="secondary" size="small" />
            </>
          )}
        </React.Fragment>
      );
    },
    cip: () => {
      return (
        <React.Fragment key="cip">
          {/* opinionated - render if templateCip exists */}
          {templateCip && (
            <Typography variant="caption" color="inherit" noWrap>
              {nbSpace}
              {templateCip}
            </Typography>
          )}
        </React.Fragment>
      );
    },
    cipFloating: () => {
      return (
        <React.Fragment key="cipFloating">
          {/* opinionated - render if templateCip exists */}
          {templateCip && (
            <Typography
              variant="caption"
              color="inherit"
              noWrap
              className={classes.cipFloating}
            >
              {templateCip}
            </Typography>
          )}
        </React.Fragment>
      );
    },
    help: () => {
      return (
        <Tooltip key="help" title="Help">
          <IconButton aria-label="Help" color="inherit">
            <HelpIcon />
          </IconButton>
        </Tooltip>
      );
    },
    profile: () => {
      return (
        <React.Fragment key="profile">
          {/* opinionated - render if authenticated */}
          {isAuthenticated() && (
            <div>
              <Tooltip title="User Profile">
                <IconButton
                  aria-label="profile of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleMenu}
                  color="inherit"
                >
                  <AccountCircle />
                </IconButton>
              </Tooltip>
              <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                getContentAnchorEl={null}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                keepMounted
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                open={
                  Boolean(anchorEl) && menuOpen === "profile of current user"
                }
                onClose={handleClose}
              >
                <MenuItem onClick={handleProfile}>Profile</MenuItem>
                <MenuItem onClick={handleLogout}>Logout</MenuItem>
              </Menu>
            </div>
          )}
        </React.Fragment>
      );
    },
  };

  const oldToolbarLayout = [
    "appMenu",
    "appIcon",
    "appName",
    "flexGrow",
    "appToolbarSearch",
    "envMessage",
    "cip",
    "help",
    "profile",
  ];
  const newToolbarLayout = [
    "appSwitcher",
    "appName",
    "cipFloating",
    "envMessage",
    "appToolbarLeftMenu",
    "flexGrow",
    "appToolbarSearch",
    "appToolbarRightMenu",
    "help",
    "profile",
  ];
  const toolbarLayout = toolbarConfig.disableSideBar
    ? newToolbarLayout
    : oldToolbarLayout;

  return (
    <ElevationScroll {...props}>
      <AppBar {...rest} className={clsx(classes.root, className)}>
        <Toolbar variant="dense">
          {toolbarLayout
            .filter((item) => renderMap[item])
            .map((item) => renderMap[item]())}
        </Toolbar>
      </AppBar>
    </ElevationScroll>
  );
};

Topbar.propTypes = {
  className: PropTypes.string,
  onSidebarOpen: PropTypes.func,
  history: PropTypes.object,
  isAutoDesktop: PropTypes.bool,
};

export default Topbar;
