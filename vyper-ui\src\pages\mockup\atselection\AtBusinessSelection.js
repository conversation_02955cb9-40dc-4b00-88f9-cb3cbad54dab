import makeStyles from "@material-ui/core/styles/makeStyles";
import { ComponentNameDisplay } from "src/component/component/ComponentNameDisplay";
import React from "react";

export const AtBusinessSelection = ({ build, selection }) => {
  const styles = makeStyles(() => ({
    priority: {
      display: "flex",
      justifyContent: "flex-start",
      alignItems: "center",
    },
  }));

  const classes = styles();

  let items = [];

  switch (selection.name) {
    case "CUST1":
    case "CUST2":
    case "CUST3":
    case "CUST4":
    case "CUST5":
    case "CUST6":
    case "CUST7":
    case "CUST8":
    case "CUST9":
      break;

    case "Die":
      break;

    case "Topside Symbol":
      break;

    default:
      // components

      const c = build.components.find((c) => c.name === selection.name);
      if (c != null) {
        c.instances.forEach((i) =>
          i.priorities.forEach((p) =>
            items.push({ name: p.object.name, engineering: p.engineering })
          )
        );
      }
  }

  return (
    <div>
      {items.map((item, n) => (
        <ComponentNameDisplay key={n} {...item} />
      ))}
    </div>
  );
};
