import { useContext, useEffect, useState } from "react";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { useWireMetalApi } from "src/api/useWireMetalApi";

export const useWireMetal = () => {
  const { open: openError } = useContext(ErrorDialogContext);
  const [wireMetals, setWireMetals] = useState([]);
  const { search } = useWireMetalApi();

  useEffect(() => {
    search(0, 1000)
      .then((json) => json.page.content)
      .then(setWireMetals)
      .catch((error) => openError({ error, title: "Fetch Wire Metals" }));
  }, []);

  return { wireMetals, setWireMetals };
};
