import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import DialogActions from "@material-ui/core/DialogActions";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Typography from "@material-ui/core/Typography";
import AddIcon from "@material-ui/icons/Add";
import CloseIcon from "@material-ui/icons/Close";
import KeyboardArrowDownIcon from "@material-ui/icons/KeyboardArrowDown";
import KeyboardArrowRightIcon from "@material-ui/icons/KeyboardArrowRight";
import KeyboardArrowUpIcon from "@material-ui/icons/KeyboardArrowUp";
import produce from "immer";
import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import { SelectedValue } from "src/component/build-component/selected/SelectedValue";
import { AlertDialogContext } from "../alert/AlertDialog";
import { ComponentMapContext } from "../componentmap/ComponentMap";
import { FetchContext } from "../fetch/VyperFetch";
import Instructions from "../instructions/Instructions";
import { Actions } from "./actions/Actions";
import { BomFor } from "./BomFor";
import { FilteredAutocomplete } from "./FilteredAutocomplete";
import { EngineeringDialog } from "./selected/EngineeringDialog";
import { Selected } from "./selected/Selected";
import { Experimental } from "../../pages/mockup/buildtype/BuildTypes";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialog: {
    display: "flex",
    justifyContent: "space-evenly",
    alignItems: "start",
    padding: "0 1rem",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  highlight: {
    marginTop: "1em",
    padding: 7,
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));

// MODE_COMPONENT = use instances and priorities to store the value (the component object)
// MODE_SINGLE = store a single value (string)
export const MODE_COMPONENT = "COMPONENT";
export const MODE_SINGLE = "SINGLE";

// determines which filter queries to use in the filter dropdown
export const FILTER_MODE_BUILD = "queryBuild";
export const FILTER_MODE_SELECTED = "querySelected";
export const FILTER_MODE_PRA = "queryPra";
export const FILTER_MODE_SUFFIX = "querySuffix";

/**
 * Displays a dialog that allows for editing of component values
 *
 * @param children
 * @returns {JSX.Element}
 * @constructor
 */
export const ComponentDialog = ({ children }) => {
  const { vget } = useContext(FetchContext);
  const { findComponentMapByName } = useContext(ComponentMapContext);
  const { open: openAlert } = useContext(AlertDialogContext);

  const [buildNumber, setBuildNumber] = useState();
  const [material, setMaterial] = useState();
  const [pin, setPin] = useState();
  const [pkg, setPkg] = useState();
  const [pkgGroup, setPkgGroup] = useState();
  const [facility, setFacility] = useState();
  const [plantCode, setPlantCode] = useState();
  const [component, setComponent] = useState({});
  const [suffix, setSuffix] = useState();
  const [onSave, setOnSave] = useState();
  const [enableEngineering, setEnableEngineering] = useState();

  const [mode, setMode] = useState("COMPONENT");
  const [open, setOpen] = useState(false);
  const [filterMode, setFilterMode] = useState(FILTER_MODE_BUILD);
  const [supplierOrWire, setSupplierOrWire] = useState();
  const [currentAvailableValue, setCurrentAvailableValue] = useState();
  const [currentSelectValue, setCurrentSelectValue] = useState();
  const [openEngineering, setOpenEngineering] = useState(false);
  const [pgsComponent, setPgsComponent] = useState();
  const [revertToPgs, setRevertToPgs] = useState(false);
  const [buildType, setBuildType] = useState();

  // when the dialog is opened, fetch the pgs component data
  // for use by the revert action
  useEffect(() => {
    if (
      !open ||
      plantCode == null ||
      material == null ||
      component == null ||
      buildNumber == null
    ) {
      return;
    }

    // build the url
    let url = `/vyper/v1/vyper/pgscomponents`;
    url += `?plantCode=${encodeURI(plantCode)}`;
    url += `&material=${encodeURI(material)}`;
    url += `&names=${encodeURI(component.name)}`;
    url += `&buildNumber=${encodeURI(buildNumber)}`;

    // fetch a build, with the pgs data loaded
    vget(url, (json) => {
      // find the component. if it has instances, then set it as the pgs component
      const pgsComponent = json.build.components.find(
        (c) => c.name === component.name
      );
      if (pgsComponent != null && pgsComponent.instances.length !== 0) {
        setPgsComponent(pgsComponent);
      } else {
        setPgsComponent(undefined);
      }
    });
  }, [open, plantCode, material, component, buildNumber]);

  /**
   * open the dialog, and store the values passed in
   * @param buildNumber
   * @param material
   * @param pin
   * @param pkg
   * @param pkgGroup
   * @param facility
   * @param plantCode
   * @param component
   * @param suffix
   * @param enableEngineering
   * @param mode
   * @param onSave
   * @param filterMode
   * @param supplierOrWire
   */
  //
  const handleOpen = ({
    buildNumber,
    material,
    pin,
    pkg,
    pkgGroup,
    facility,
    plantCode,
    component,
    suffix,
    enableEngineering,
    mode,
    onSave,
    filterMode = "queryBuild",
    buildType,
    supplierOrWire,
  }) => {
    setBuildNumber(buildNumber);
    setMaterial(material);
    setPin(pin);
    setPkg(pkg);
    setPkgGroup(pkgGroup);
    setFacility(facility);
    setPlantCode(plantCode);
    setComponent(component);
    setSuffix(suffix);
    setEnableEngineering(enableEngineering);
    setMode(mode);
    setOnSave(() => onSave);
    setFilterMode(filterMode);
    setSupplierOrWire(supplierOrWire);
    setOpen(true);
    setBuildType(buildType);
  };

  const handleClose = () => {
    setCurrentAvailableValue(undefined);
    setCurrentSelectValue(null);
    setOpenEngineering(false);
    setOpen(false);
  };

  const handleSave = () => {
    onSave(component, revertToPgs);
    handleClose();
  };

  // get the component map for this component
  const componentMap = findComponentMapByName(component?.name);
  // manage the available value
  const handleChangeCurrentAvailableValue = (value) =>
    setCurrentAvailableValue(value);

  // manage the selected value
  const handleChangeSelectValue = (value) => setCurrentSelectValue(value);

  // remove a component priority
  const handleRemovePriority = (instanceIndex, priorityIndex) => {
    // delete the priority.
    // if priorities is now empty, delete the instance
    setComponent(
      produce(component, (draft) => {
        draft.instances[instanceIndex].priorities.splice(priorityIndex, 1);

        // if we only had 1 priority before, then we have zero now,
        // and we can delete the instance
        if (0 === draft.instances[instanceIndex].priorities.length) {
          draft.instances.splice(instanceIndex, 1);
        }
      })
    );
  };

  // move a priority to a new index
  const handleChangePriority = (instanceIndex, from, to) => {
    // make the from the lower value

    if (from > to) {
      const temp = from;
      from = to;
      to = temp;
    }

    setComponent(
      produce(component, (draft) => {
        draft.instances[instanceIndex].priorities = [
          ...component.instances[instanceIndex].priorities.slice(0, from),
          component.instances[instanceIndex].priorities[to],
          ...component.instances[instanceIndex].priorities.slice(from + 1, to),
          component.instances[instanceIndex].priorities[from],
          ...component.instances[instanceIndex].priorities.slice(to + 1),
        ];
      })
    );
  };

  // create the priority object, with handling for special components
  const makePriorityObject = (name, value) => {
    if (name === "MB Diagram" && value.includes(" ")) {
      const [number, revision] = value.split(" ");
      return { name: number, Revision: revision };
    } else {
      return { name: value };
    }
  };

  // replace the selected components with the available component
  const enableReplaceInstance = currentAvailableValue != null;

  const handleReplaceInstance = () => {
    setComponent(
      produce(component, (draft) => {
        draft.instances = [
          {
            priorities: [
              {
                object: makePriorityObject(
                  component.name,
                  currentAvailableValue
                ),
                engineering: "N",
              },
            ],
          },
        ];
      })
    );
  };

  // handle adding a new component
  const enableAddInstance =
    currentAvailableValue != null &&
    (componentMap.allowMultipleInstances === "YES" ||
      componentMap.allowMultipleInstances == null);

  const handleAddInstance = () => {
    setComponent(
      produce(component, (draft) => {
        draft.instances.push({
          priorities: [
            {
              object: makePriorityObject(component.name, currentAvailableValue),
              engineering: "N",
            },
          ],
        });
      })
    );
  };

  // handle adding a priority

  const enableAddPriority =
    currentAvailableValue != null &&
    currentSelectValue != null &&
    (componentMap.allowMultiplePriorities === "YES" ||
      componentMap.allowMultiplePriorities == null);

  const handleAddPriority = () => {
    const exists = component.instances[currentSelectValue].priorities.some(
      (p) => p.object.name === currentAvailableValue
    );
    if (exists) {
      openAlert({
        title: "Duplicate Component Value",
        message: "You can't add the same component value twice.",
      });
    } else {
      setComponent(
        produce(component, (draft) => {
          draft.instances[currentSelectValue].priorities.push({
            object: { name: currentAvailableValue },
            engineering: "N",
          });
        })
      );
    }
  };

  // handle moving instances up and down

  const enableMoveUp = currentSelectValue != null && currentSelectValue > 0;
  const enableMoveDown =
    currentSelectValue != null &&
    currentSelectValue < component.instances.length - 1;

  const handleMoveUp = () => {
    move(currentSelectValue - 1, currentSelectValue);
    setCurrentSelectValue(currentSelectValue - 1);
  };

  const handleMoveDown = () => {
    move(currentSelectValue, currentSelectValue + 1);
    setCurrentSelectValue(currentSelectValue + 1);
  };

  const move = (index1, index2) => {
    setComponent(
      produce(component, (draft) => {
        draft.instances = [
          ...component.instances.slice(0, index1),
          component.instances[index2],
          component.instances[index1],
          ...component.instances.slice(index2 + 1),
        ];
      })
    );
  };
  // TODO: FIX
  const handleRevertToPGS = () => {
    setComponent(
      produce(component, (draft) => {
        draft.instances = pgsComponent.instances;
        draft.source = pgsComponent.source;
      })
    );
    setRevertToPgs(true);
  };

  const pgsValueHasChanged = (component) =>
    JSON.stringify(component) === JSON.stringify(pgsComponent);

  // handle the add engineering component dialog

  const handleAddEngineering = () => setOpenEngineering(true);
  const handleCloseEngineering = () => setOpenEngineering(false);
  const handleSaveEngineering = (value) => {
    // if an instance is selected, add the eng comp as a priority
    // if an instance is not selected, add a new instance

    setComponent(
      produce(component, (draft) => {
        if (currentSelectValue == null) {
          draft.instances.push({
            priorities: [
              {
                object: {
                  name: value,
                },
                engineering: "Y",
              },
            ],
          });
        } else {
          draft.instances[currentSelectValue].priorities.push({
            object: {
              name: value,
            },
            engineering: "Y",
          });
        }
      })
    );

    setOpenEngineering(false);
  };

  // determine the valid filter methods

  const isBom = componentMap?.querySelected?.includes("BOM");

  const canHaveMultipleInstances =
    componentMap?.allowMultipleInstances == null ||
    componentMap?.allowMultipleInstances === "YES";
  const canHaveMultiplePriorities =
    componentMap?.allowMultiplePriorities == null ||
    componentMap?.allowMultiplePriorities === "YES";

  const actions = [];
  actions.push({
    text: `Replace with <<value>>`,
    icon: <KeyboardArrowRightIcon />,
    disabled: !enableReplaceInstance,
    onClick: handleReplaceInstance,
  });

  mode === MODE_COMPONENT &&
    actions.push({
      text: `Add Instance of <<value>>`,
      icon: <KeyboardArrowRightIcon />,
      disabled: !(enableAddInstance && canHaveMultipleInstances),
      onClick: handleAddInstance,
    });

  mode === MODE_COMPONENT &&
    actions.push({
      text: `Add Priority of <<value>> `,
      icon: <KeyboardArrowRightIcon />,
      disabled: !(enableAddPriority && canHaveMultiplePriorities),
      onClick: handleAddPriority,
    });

  mode === MODE_COMPONENT &&
    actions.push({
      text: `Move Up`,
      icon: <KeyboardArrowUpIcon />,
      disabled: !enableMoveUp,
      onClick: handleMoveUp,
    });

  mode === MODE_COMPONENT &&
    actions.push({
      text: `Move Down`,
      icon: <KeyboardArrowDownIcon />,
      disabled: !enableMoveDown,
      onClick: handleMoveDown,
    });

  enableEngineering &&
    componentMap?.engineeringAtssComponentName != null &&
    (component?.name === "MB Diagram" ? buildType === Experimental : true) &&
    actions.push({
      text: `Add Engineering`,
      icon: <AddIcon />,
      disabled: false,
      onClick: handleAddEngineering,
    });

  const classes = useStyles();

  return (
    <ComponentDialogContext.Provider
      value={{
        openComponentDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="lg">
        <DialogTitle
          classes={{
            root: classes.title,
          }}
        >
          Component: {component?.name}
        </DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        {isBom ? <BomFor pin={pin} pkg={pkg} facility={facility} /> : null}

        <div className={classes.dialog}>
          <div>
            <FilteredAutocomplete
              mode={filterMode}
              title="Available Components"
              componentMap={componentMap}
              componentName={component?.name}
              suffix={suffix}
              facility={facility}
              pkg={pkg}
              pin={pin}
              pkgGroup={pkgGroup}
              onChange={handleChangeCurrentAvailableValue}
              buildNumber={buildNumber}
              supplierOrWire={supplierOrWire}
            />

            {!canHaveMultipleInstances && mode === MODE_COMPONENT && (
              <div className={classes.highlight}>
                {componentMap?.name} can have only 1 instance.
              </div>
            )}

            {!canHaveMultiplePriorities && mode === MODE_COMPONENT && (
              <div className={classes.highlight}>
                {componentMap?.name} can have only 1 priority.
              </div>
            )}

            <div hidden={buildNumber}>
              <Button
                color="primary"
                variant="contained"
                onClick={handleRevertToPGS}
              >
                Revert To PGS
              </Button>
              {pgsValueHasChanged() ? (
                <Typography>PGS values have been updated!</Typography>
              ) : (
                <></>
              )}
            </div>
          </div>

          <Actions actions={actions} value={currentAvailableValue} />

          {mode === MODE_COMPONENT && (
            <Selected
              mode={mode}
              component={component}
              selectedInstanceRow={currentSelectValue}
              onSelect={handleChangeSelectValue}
              onRemovePriority={handleRemovePriority}
              onChangePriority={handleChangePriority}
            />
          )}

          {mode === MODE_SINGLE && (
            <SelectedValue
              name={component.name}
              value={component.instances[0].priorities[0].object.name}
            />
          )}

          <EngineeringDialog
            open={openEngineering}
            onClose={handleCloseEngineering}
            onSave={handleSaveEngineering}
          />
        </div>

        <br />
        <br />

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>

        <Instructions
          show={false}
          title="Instructions"
          instructions={[
            "Select a value from the component filter. This will change the values that are allowed for you to choose, such as BOM only, or A/T site only, or all of ATSS.",
            "Start typing in the autocomplete box. A list will open with matching selections. Choose a value from the list.",
            "If your value is not found in the list, type the complete value, then press enter, which will allow it to be added (not all components allow this).",
            "Clicking the Replace button will remove any selected values, and put the value you chose in the list.",
            "Clicking the Add button will create a new Instance and add your value to the instance.",
            "Clicking the Add Priority button will add another priority value to the list. You must select an instance first.",
            "If you select an instance, and have more than 1 instances, then you can adjust the values by Clicking the up or down buttons.",
            "Some components allow for engineering components. You can click the engineering button, and a dialog will appear, that allows you to enter the description of the material.",
          ]}
        />
      </Dialog>

      {children}
    </ComponentDialogContext.Provider>
  );
};

export const ComponentDialogContext = React.createContext(null);

ComponentDialog.propTypes = {
  children: PropTypes.any,
};
