import { useContext, useEffect, useState } from "react";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { useAtssApi } from "src/api/useAtssApi";

export const useCamsWireMetal = () => {
  const { atssCansWiremetal } = useAtssApi();
  const { open: openError } = useContext(ErrorDialogContext);
  const [camsWireMetals, setCamsWireMetals] = useState([]);

  useEffect(() => {
    atssCansWiremetal()
      .then((json) => json.map((wm) => wm.wire_metal))
      .then(setCamsWireMetals)
      .catch((error) => openError({ error, title: "Fetch CAMS Wire Metal" }));
  }, []);

  return { camsWireMetals };
};
