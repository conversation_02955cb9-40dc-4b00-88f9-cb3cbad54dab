import PropTypes from "prop-types";
import React from "react";

/**
 * Returns the traveler's title
 * @param {string} number - the traveler's number. Ex: the buildNumber, praNumber or vscnNumber.
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerTitle({ number }) {
  return (
    <>
      {" ".repeat(27) + number + " Device Specification"}
      <br />
    </>
  );
}

TravelerTitle.propTypes = {
  number: PropTypes.string.isRequired,
};
