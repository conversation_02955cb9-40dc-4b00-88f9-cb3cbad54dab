import React, { useMemo } from "react";

const TooltipComponent = (props) => {
  const data = useMemo(() => props.value, []);
  if (data.constructor.name === "Array") {
    return (
      <div
        className="custom-tooltip"
        style={{ backgroundColor: props.color || "white" }}
      >
        {<pre>{data.map((para) => para).join("\n")}</pre>}
      </div>
    );
  } else if (typeof data === "string") {
    return (
      <div
        className="custom-tooltip"
        style={{ backgroundColor: props.color || "white" }}
      >
        <pre>{data}</pre>
      </div>
    );
  }else {
    return <div></div>;
  }
};

export default TooltipComponent;
