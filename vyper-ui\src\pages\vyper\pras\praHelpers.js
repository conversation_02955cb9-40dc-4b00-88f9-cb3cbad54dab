import AttachMoneyIcon from "@material-ui/icons/AttachMoney";
import BuildIcon from "@material-ui/icons/Build";
import FireplaceIcon from "@material-ui/icons/Fireplace";
import LibraryBooksIcon from "@material-ui/icons/LibraryBooks";
import MemoryIcon from "@material-ui/icons/Memory";
import VpnLockIcon from "@material-ui/icons/VpnLock";
import GradientIcon from "@material-ui/icons/Gradient";
import BrokenImageIcon from "@material-ui/icons/BrokenImage";
import CheckIcon from "@material-ui/icons/Check";
import {
  InsertCommentRounded,
  ImportContactsRounded,
  ChangeHistoryRounded,
  MapOutlined,
} from "@material-ui/icons";
import PlaylistAddCheckIcon from "@material-ui/icons/PlaylistAddCheck";

/**
 * Given a verifier source, return the SVG icon.
 *
 * @param source
 * @returns object
 */
export const choosePraIcon = (source) => {
  switch (source) {
    case "SOURCE_PGS":
      return AttachMoneyIcon;
    case "SOURCE_ARMARC":
      return BuildIcon;
    case "SOURCE_PAVV_COMPONENT":
      return PlaylistAddCheckIcon;
    case "SOURCE_ATSS_GLOBAL":
      return VpnLockIcon;
    case "SOURCE_ATSS_AT":
      return LibraryBooksIcon;
    case "SOURCE_ATSS_BOM":
      return FireplaceIcon;
    case "SOURCE_DIE":
      return MemoryIcon;
    case "SOURCE_DIAGRAM":
      return GradientIcon;
    case "SOURCE_ATSS_QUALIFIED":
      return CheckIcon;
    case "SOURCE_TSM":
      return InsertCommentRounded;
    case "SOURCE_SCS":
      return ImportContactsRounded;
    case "SOURCE_CHANGELINK":
      return ChangeHistoryRounded;
    case "SOURCE_SCS_MATERIAL_DIE":
      return MapOutlined;
    default:
      return BrokenImageIcon;
  }
};

/**
 * Given a verifier status and base styles, return an object of base styles plus the icons for the status.
 *
 * @param status
 * @param baseStyles
 * @returns {{backgroundColor: string, color: string}}
 */
export const choosePraIconStyle = (status, baseStyles = {}) => {
  switch (status) {
    case "NOT_VERIFIED":
      return { ...baseStyles, color: "#FFFFFF", backgroundColor: "#CC0000" };
    case "PARTIALLY_VERIFIED":
      return { ...baseStyles, color: "#FFFFFF", backgroundColor: "#2222FF" };
    case "FULLY_VERIFIED":
      return { ...baseStyles, color: "#FFFFFF", backgroundColor: "#007700" };
  }
};
