// vscn state
const STATE_NEW = "VSCN_DRAFT";
const STATE_VSCN_AT_REVIEW = "VSCN_AT_REVIEW";

const editableFieldsWithVerifiers = ["Die", "Material", "ChangeNumber"];

export const hasMaterial = (vscn) => {
  return vscn.material.object?.Material != null;
};

export const hasFacility = (vscn) => {
  return vscn.facility.object?.PDBFacility != null;
};

export const stateIsNew = (vscn) => {
  return vscn.state === STATE_NEW;
};

export const currentUserIsOwner = (vyper, authUser) => {
  if (!vyper) return false;
  return vyper.owners.some((usr) => usr.userid === authUser.uid);
};

export const testIsSet = (vscn) => {
  return vscn.test.content != null;
};

export const symbolizationIsSet = (vscn) => {
  return vscn.symbolization.symbols[0]?.object?.name != null;
};

export const packConfigIsSet = (vscn) => {
  return vscn.packConfig.object.value != null;
};

export const vscnSubmitBlockers = (vscn, vyper, authUser) => {
  const blockers = [];
  if (!stateIsNew(vscn)) {
    blockers.push("State is not new or reject.");
  }
  if (!currentUserIsOwner(vyper, authUser)) {
    blockers.push("You are not an owner.");
  }
  if (!hasMaterial(vscn)) {
    blockers.push("The material has not been set.");
  }
  if (!hasFacility(vscn)) {
    blockers.push("The facility has not been set.");
  }
  if (!testIsSet(vscn)) {
    blockers.push("Test information has not been uploaded.");
  }

  vscn.components
    .filter(
      (c) =>
        c.instances?.[0]?.priorities?.[0]?.object?.name == null ||
        c.instances?.[0]?.priorities?.[0]?.object?.name === ""
    )
    .forEach((c) => blockers.push(`Component ${c.name} has not been set.`));

  if (!symbolizationIsSet(vscn)) {
    blockers.push("The symbolization has not been set.");
  }
  if (!packConfigIsSet(vscn)) {
    blockers.push("The pack config has not been set.");
  }

  // custX

  const custNames = [
    "CUST1",
    "CUST2",
    "CUST3",
    "CUST4",
    "CUST5",
    "CUST6",
    "CUST7",
    "CUST8",
  ];

  vscn.components
    .filter((c) => custNames.includes(c.name))
    .filter((c) => {
      const object = c.instances?.[0]?.priorities?.[0]?.object;
      const isSet = object?.name != null || object?.name !== "";
      const isBlank =
        (object?.name == null || object?.name !== "") &&
        object?.ignoreBlank === "Y";
      const isValid = isSet || isBlank;
      return !isValid;
    })
    .map((c) => `${c.name} has not been set.`)
    .forEach((text) => blockers.push(text));

  // ecat
  vscn.components
    .filter((c) => "ECAT" === c.name)
    .filter(
      (c) =>
        c.instances?.[0]?.priorities?.[0]?.object?.name == null ||
        c.instances?.[0]?.priorities?.[0]?.object?.name === ""
    )
    .map((c) => `${c.name} has not been set.`)
    .forEach((text) => blockers.push(text));

  vscn.verifiers
    .filter(
      (verifier) =>
        verifier.name?.includes("Test Program") ||
        editableFieldsWithVerifiers.includes(verifier.name)
    )
    .filter(
      (verifier) =>
        verifier.status != "PARTIALLY_VERIFIED" &&
        verifier.status != "FULLY_VERIFIED" &&
        verifier.status != null
    )
    .map(
      (verifier) =>
        `${verifier.name} ${verifier.value} is not approved at source ${verifier.source}`
    )
    .forEach((text) => blockers.push(text));

  vscn.traveler.operations
    .flatMap((operation) => operation.components)
    .filter((component) => !component.value)
    .map(
      (component) =>
        `${component.name} value in Pack Selection Screen has not been set`
    )
    .forEach((text) => blockers.push(text));

  vscn.test?.travelerOperations
    ?.flatMap((travelerOperation) => travelerOperation?.components)
    .filter((component) => component.name.includes("Legacy"))
    .map(
      (component) =>
        `Legacy Test Component ${component.name} is not a valid component to use`
    )
    .forEach((text) => blockers.push(text));

  return blockers;
};
