import React, { useContext } from "react";
import { AtSelectionDialogContext } from "src/pages/mockup/atselection/AtSelectionDialog";
import { ComponentNameDisplay } from "src/component/component/ComponentNameDisplay";
import { TableRow } from "@material-ui/core";
import TableCell from "@material-ui/core/TableCell";
import { AtBusinessSelection } from "src/pages/mockup/atselection/AtBusinessSelection";
import { SourceIcon } from "src/component/sourceicon/SourceIcon";
import { VyperLink } from "src/pages/mockup/VyperLink";

export const AtSelectionRow = ({ selection, build, onSave }) => {
  const { showSelectionDialog } = useContext(AtSelectionDialogContext);

  const handleClick = () => {
    showSelectionDialog({
      build: build,
      selection: selection,
      name: selection.name,
      selectionItems: selection.items || [],
      onSave: onSave,
      facilityAt: build.facility.object.PDBFacility,
      pin: build.material.object.PackagePin,
      pkg: build.material.object.PackageDesignator,
      userValues: getUserValues()?.join(", ") || null,
      userValuesArray: getUserValues(),
    });
  };

  const getUserValues = () => {
    let userValues = [];

    // add components
    const c = build.components.find((c) => c.name === selection.name);
    if (c != null) {
      c.instances.forEach((i) =>
        i.priorities.forEach((p) => userValues.push(p.object.name))
      );
    }

    // add dies
    if (selection.name === "Die") {
      const d = build.dies.dieInstances.forEach((i) =>
        i.dies.forEach((d) => userValues.push(d.name))
      );
    }

    // add topside symbol
    if (selection.name === "Topside Symbol") {
      let value = undefined;
      build.symbolization.symbols.forEach((s) => {
        if (s.object.name != null) {
          userValues.push(value);
        }
      });
    }

    // add cust values
    for (let n = 1; n < 10; ++n) {
      const name = `CUST${n}`;
      if (selection.name === name) {
        build.symbolization.customs.forEach((c) => {
          if (name === c.object.name) {
            userValues.push(c.object.value);
          }
        });
      }
    }

    return userValues.length === 0 ? null : userValues;
  };

  const items = selection.items.map((item, n) => (
    <ComponentNameDisplay
      key={n}
      name={item.value}
      engineering={item.engineering}
    />
  ));

  return (
    <TableRow hover>
      <TableCell>{selection.name}</TableCell>
      <TableCell>
        <AtBusinessSelection build={build} selection={selection} />
      </TableCell>
      <TableCell>
        <SourceIcon source={selection.source} />
      </TableCell>
      <TableCell>
        <VyperLink onClick={handleClick}>
          {items.length === 0 ? "click to select" : items}
        </VyperLink>
      </TableCell>
    </TableRow>
  );
};
