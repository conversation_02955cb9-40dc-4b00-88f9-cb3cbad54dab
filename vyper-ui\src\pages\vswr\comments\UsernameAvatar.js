import React from "react";
import { Avatar, Tooltip } from "@material-ui/core";

function stringToColor(string) {
  let hash = 0;
  let i;

  /* eslint-disable no-bitwise */
  for (i = 0; i < string.length; i += 1) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }

  let color = "#";

  for (i = 0; i < 3; i += 1) {
    const value = (hash >> (i * 8)) & 0xff;
    color += `00${value.toString(16)}`.substr(-2);
  }
  /* eslint-enable no-bitwise */
  return color;
}

function stringAvatar(name) {
  name = `${name.split(" ")[0][0]}${name.split(" ")[1][0]}`;
  return name;
}

export const UsernameAvatar = ({ userName }) => {
  return (
    <Tooltip title={userName} placement="top" arrow>
      <Avatar style={{ backgroundColor: stringToColor(userName) }}>
        {stringAvatar(userName)}
      </Avatar>
    </Tooltip>
  );
};
