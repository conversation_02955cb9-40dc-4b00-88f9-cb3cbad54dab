import moment from "moment";

/**
 * Returns a plain text version of the traveler generated for the SCN submission.
 * @param {object} json - the json returned from the api
 * @param {string} buildNumber - The build number
 * @returns {string} - The plain text traveler
 */
export function toPlainText(json, buildNumber) {
  let traveler = title(json, buildNumber);
  traveler += "\n";
  traveler += headers(json);
  traveler += "\n\n";
  traveler += body(json);
  return traveler;
}

/**
 * Format the traveler's title
 * @param {object} json - the json returned from the api
 * @param {string} buildNumber - The build number
 * @returns {string} - The formatted title
 */
function title(json, buildNumber) {
  return " ".repeat(27) + buildNumber + " Device Specification\n";
}

/**
 * Format the traveler's header section
 * @param {object} json - the json returned from the api
 * @returns {string} - The formatted header
 */
function headers(json) {
  let traveler = headerRow(
    "Current Date / Time",
    moment(json.header.currentDateTime).format("MM/DD/YYYY, h:mm:ss A"),
    "Pin",
    json.header.pins
  );
  traveler += headerRow(
    "Old Material",
    json.header.oldMaterial,
    "PKG",
    json.header.pkg
  );
  traveler += headerRow(
    "Spec Device",
    json.header.specDevice,
    "Package Group",
    json.header.packageGroup
  );
  traveler += headerRow("A/T", json.header.at, "SPQ", json.header.spq);
  traveler += headerRow(
    "Revision",
    json.header.revision,
    "MOQ",
    json.header.moq
  );
  traveler += headerRow(
    "SBE",
    json.header.sbe,
    "Industry Sector",
    json.header.industrySector
  );
  traveler += headerRow(
    "SBE-1",
    json.header.sbe1,
    "Customer",
    json.header.customer
  );
  traveler += headerRow(
    "SBE-2",
    json.header.sbe2,
    "Cust Part Name",
    json.header.custPartName
  );
  traveler += headerRow(
    "SAP Material",
    json.header.sapMaterial,
    "Cust Print Name",
    json.header.custPrintName
  );
  traveler += headerRow(
    "SAP Base Material",
    json.header.sapBaseName,
    "Cust Print Revision",
    json.header.custPrintRevision
  );
  traveler += headerRow(
    "Niche",
    json.header.niche,
    "Type 4",
    json.header.type4
  );
  traveler += headerRow("PRESP", json.header.presp);
  return traveler;
}

/**
 * Format the traveler's header row
 * @param {string} title1 - the left column's title
 * @param {string} value1 - the left column's data
 * @param {string} [title2=undefined] - the right column's title
 * @param {string} [value2=undefined] - the right column's data
 * @returns {string} - The formatted header row
 */
function headerRow(title1, value1, title2 = undefined, value2 = undefined) {
  let traveler =
    title1.padStart(19, " ") +
    ": " +
    (value1 == null ? "" : value1).padEnd(31, " ") +
    "  ";

  if (title2 != null) {
    traveler +=
      title2.padStart(20, " ") + ": " + (value2 == null ? "" : value2) + " \n";
  }

  return traveler;
}

/**
 * Format the traveler's body
 * @param {object} json - the json returned from the api
 * @returns {string} - The formatted body
 */
function body(json) {
  return json.subFlows.map((sf) => subFlow(sf)).join("\n");
}

/**
 * Format the traveler's subflow
 * @param {object} sf - the subflow
 * @returns {string} - The formatted body
 */
function subFlow(sf) {
  return sf.operations.map((op) => operation(op)).join("\n");
}

/**
 * Format the traveler's operation
 * @param {object} op - the operation
 * @returns {string} - The formatted body
 */
function operation(op) {
  let traveler = op.name + "\n";
  traveler += op.components.map((comp) => component(comp)).join("");
  return traveler;
}

/**
 * Format the traveler's component
 * @param {object} comp - the component
 * @returns {string} - The formatted body
 */
function component(comp) {
  let traveler = ("   " + comp.name).padEnd(32, " ");
  traveler += ": ";
  traveler += (comp.value || "").padEnd(52, " ");
  traveler += comp.priority.toString().padStart(2, "0");
  traveler += "\n";
  traveler += comp.attributes.map((attr) => attribute(attr)).join("");
  traveler += paragraph(comp.paragraph);
  return traveler;
}

/**
 * Format the traveler's attribute
 * @param {object} attr - the attribute
 * @returns {string} - The formatted body
 */
function attribute(attr) {
  let traveler = ("      " + attr.name).padEnd(32, " ");
  traveler += ": ";
  traveler += attr.value || "";
  traveler += "\n";
  return traveler;
}

/**
 * Format the traveler's paragraph text
 * @param {object} text - the paragraph text
 * @returns {string} - The formatted body
 */
function paragraph(text) {
  return text == null
    ? ""
    : text
        .split("\n")
        .map((line) => "     " + line + "\n")
        .join("");
}
