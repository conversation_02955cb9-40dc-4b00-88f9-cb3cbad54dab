import React from "react";
import ListIcon from "@material-ui/icons/List";
import LibraryBooksIcon from "@material-ui/icons/LibraryBooks";
import SubtitlesIcon from "@material-ui/icons/Subtitles";
import ListAltIcon from "@material-ui/icons/ListAlt";
import CreateIcon from "@material-ui/icons/Create";
import InfoIcon from "@material-ui/icons/Info";
import ViewIcon from "@material-ui/icons/Visibility";
import PermIdentityIcon from "@material-ui/icons/PermIdentity";
import {
  VSWR_ROLE_GROUPS,
  ADMINISTRATIVE_LEVEL,
} from "../component/common/auth";
const { SBE, AT, BTH } = VSWR_ROLE_GROUPS;
const { ADMIN } = ADMINISTRATIVE_LEVEL;
import config from "../../src/buildEnvironment";
const { externalUse, showSwrMenu } = config;

function addBuildToVswrCreate() {
  const urlPath = window.location.pathname;
  const vbuildMatch = urlPath.match("VBUILD[0-9]{7}-[0-9]{4}");
  let vbuildID = "";
  if (vbuildMatch != null) {
    vbuildID += `/${vbuildMatch[0]}`;
  }
  location.href = config.basePath + `/vswr/create${vbuildID}`;
}

let leftMenu = [
  {
    label: "Data Sources",
    href: "/datasources",
    icon: <LibraryBooksIcon />,
  },
  {
    label: "Reports",
    href: "/reports",
    icon: <ListAltIcon />,
    internal: true,
  },
  {
    label: "Admin",
    href: "/admin",
    icon: <ListIcon />,
  },
  {
    label: "Roles",
    href: "/ATApprovalAdminPage",
    icon: <PermIdentityIcon />,
  },
  {
    label: "Symbol Maintenance",
    href: "/topSymbol",
    icon: <SubtitlesIcon />,
  },
  {
    label: "About",
    href: "/about",
    icon: <InfoIcon />,
    internal: true,
  },
  {
    label: "VSWR",
    href: "#",
    icon: <LibraryBooksIcon />,
    children: [
      {
        label: "Create VSWR",
        onClick: addBuildToVswrCreate,
        icon: <CreateIcon />,
      },
      {
        label: "View VSWR",
        href: "/vswr/view",
        icon: <ViewIcon />,
      },
    ],
  },
];

if (externalUse) {
  leftMenu = leftMenu.filter((item) => item.internal);
}

// https://confluence.itg.ti.com/display/PLUE/Simba+Common+Header
export let appToolbarConfig = ({ authUser, basePath }) => {
  let appToolbarCfg = {
    basePath: basePath,
    disableSideBar: true,
    helpPageURL: "https://confluence.itg.ti.com/display/SimbaInfo/VYPER+Help",
    leftMenu,
    rightMenu: [],
    search: {
      enabled: false,
    },
  };

  if (showSwrMenu) {
    let swrSubMenu = generateVswrSubMenu(authUser);
    if (swrSubMenu.length !== 0 && !externalUse) {
      appToolbarCfg.leftMenu.push({
        label: "SWR Batch Processing",
        href: "#",
        icon: <LibraryBooksIcon />,
        children: swrSubMenu,
      });
    }
  }

  return appToolbarCfg;
};

let generateVswrSubMenu = (authUser) => {
  let userVswrSecurityRole = authUser?.vswrSecurity?.roleCategory;
  let userVswrAdministrativeLevel = authUser?.vswrSecurity?.administrativeLevel;
  let hasSbeAccess = userVswrSecurityRole === SBE;
  let hasAtAccess = userVswrSecurityRole === AT;
  let hasBthAccess = userVswrSecurityRole === BTH;
  let hasAdminAccess = userVswrAdministrativeLevel === ADMIN;
  let subMenu = [];
  if (hasSbeAccess || hasBthAccess || hasAdminAccess) {
    subMenu.push({
      label: "List SWRS",
      href: "/batch/listswrs",
      icon: <CreateIcon />,
    });
  }
  if (hasSbeAccess || hasBthAccess || hasAdminAccess) {
    subMenu.push({
      label: "Update SWRS",
      href: "/batch/updateswrs",
      icon: <CreateIcon />,
    });
  }
  if (hasSbeAccess || hasBthAccess || hasAdminAccess) {
    subMenu.push({
      label: "Forecast SBE View",
      href: "/batch/forecastsbeview",
      icon: <ViewIcon />,
    });
  }
  if (hasAtAccess || hasBthAccess || hasAdminAccess) {
    subMenu.push({
      label: "Forecast AT View",
      href: "/batch/forecastatview",
      icon: <ViewIcon />,
    });
  }

  return subMenu;
};
