import { makeStyles, MenuItem, TextField } from "@material-ui/core";
import moment from "moment";
import PropTypes from "prop-types";
import React, { useContext } from "react";
import { ApprovalOperationContext } from "../../../component/approvaloperation/ApprovalOperation";
import { AuthContext } from "../../../component/common";
import { dateFormat } from "../../../component/dateFormat";
import { New } from "../../mockup/buildtype/BuildTypes";
import { validateOperation } from "./ValidatedOperation";

const TYPE_CONVERT = {
  UNAPPROVED: "M&B Not Approved",
  PRODUCTION: "M&B Approved for Production",
  ENGINEERING: "M&B Approved for Engineering Evaluation",
  TECH_FEASIBILITY: "<PERSON>&<PERSON> Approved for Tech Feasibility",
};

const useStyles = makeStyles(() => ({
  select: {
    minWidth: 400,
  },
}));

/**
 * @callback DiagramApproval~onSelect
 * @param {object} build - The build
 * @param {string} value - the component value
 * @param {string} type - the selected approval type
 */

/**
 * Displays the A/T approval dropdown list for MB Diagram components.
 *
 * @param {object} build - The build
 * @param {object} operation - The current operation
 * @param {object} component - The component
 * @param {string} component.name - The component's name
 * @param {object[]} component.instances - The component's instances
 * @param value - The component's value
 * @param {DiagramApproval~onSelect} onSelect - Callback when the user selects a different value
 * @returns {JSX.Element}
 * @constructor
 */
export const DiagramApproval = ({
  build,
  operation,
  component,
  value,
  onSelect,
}) => {
  const { authUser } = useContext(AuthContext);
  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );

  // display this component when:
  // 1, build type is new, and
  // 2. component name is mb diagram, and
  // 3. component value is legacy format
  // legacy means the length != 8 digits, or it has non-digits in the value
  if (
    build.buildtype !== New ||
    component.name !== "MB Diagram" ||
    value.match(/^[0-9]{8}$/)
  ) {
    return null;
  }

  // use the validate operation, and get the enabled and checked values.
  // this tells us that the current user is a/t for the operation's approval group.
  // which means, we show the edit component, instead of the view component
  // and once checked, the value can't be changed.
  const { enabled } = validateOperation({
    buildState: build.state,
    buildFacility: build.facility.object.PDBFacility,
    sbe: build.material.object.SBE,
    sbe1: build.material.object.SBE1,
    operation: operation,
    authUser,
    validatedOperation: build.validatedOperations.find(
      (vo) => vo.operation === operation.name
    ),
    findApprovalOperationByOperation: findApprovalOperationByOperation,
  });

  // if the diagram exists in other operations, and those operations
  // are checked, then this one becomes view-only.

  let mbOperationChecked = false;

  build.traveler.operations.forEach((op) => {
    op.components.forEach((cp) => {
      if (cp.name === "MB Diagram" || cp.value === value) {
        const { checked } = validateOperation({
          buildState: build.state,
          buildFacility: build.facility.object.PDBFacility,
          sbe: build.material.object.SBE,
          sbe1: build.material.object.SBE1,
          operation: op,
          authUser,
          validatedOperation: build.validatedOperations.find(
            (vo) => vo.operation === op.name
          ),
          findApprovalOperationByOperation: findApprovalOperationByOperation,
        });

        if (checked) {
          mbOperationChecked = true;
        }
      }
    });
  });

  return (
    <>
      {!mbOperationChecked && enabled ? (
        <DiagramApprovalEdit
          build={build}
          component={component}
          value={value}
          onSelect={onSelect}
        />
      ) : (
        <DiagramApprovalView build={build} value={value} />
      )}
    </>
  );
};

DiagramApproval.propTypes = {
  build: PropTypes.object.isRequired,
  operation: PropTypes.object.isRequired,
  component: PropTypes.object.isRequired,
  value: PropTypes.string,
  onSelect: PropTypes.func.isRequired,
};

/**
 * Show a display-only version of the MB Diagram's approval type.
 *
 * @param {object} build - The build
 * @param {string} value - The MB Diagram's number
 * @returns {JSX.Element}
 * @constructor
 */
const DiagramApprovalView = ({ build, value }) => {
  const approval = build.diagramApprovals[value];

  let message = null;
  let popup = null;
  if (approval != null) {
    const date = moment(approval.approver.when).format(dateFormat);

    message = `${TYPE_CONVERT[approval.type || "UNAPPROVED"]}`;
    popup = `Approved by ${approval.approver.user.username} at ${date}`;
  }

  return (
    <>
      &nbsp;&nbsp;<span title={popup}>{message}</span>
    </>
  );
};

DiagramApprovalView.propTypes = {
  build: PropTypes.object.isRequired,
  value: PropTypes.string,
};

/**
 * Show a editable version of the MB Diagram's approval type.
 *
 * @param {object} build - The build
 * @param {string} value - The MB Diagram's number
 * @param {DiagramApproval~onSelect} onSelect - callback when the value changes
 * @returns {JSX.Element}
 * @constructor
 */

const DiagramApprovalEdit = ({ value, build, onSelect }) => {
  const type = build.diagramApprovals[value]?.type;

  const handleChangeApproval = (e) => onSelect(build, value, e.target.value);

  const classes = useStyles();

  return (
    <>
      &nbsp; &nbsp;
      <TextField
        className={classes.select}
        select
        variant="outlined"
        label={`MB Diagram ${value} Approval`}
        value={type || ""}
        onChange={handleChangeApproval}
      >
        <MenuItem value={null}>{TYPE_CONVERT["UNAPPROVED"]}</MenuItem>
        <MenuItem value={"PRODUCTION"}>{TYPE_CONVERT["PRODUCTION"]}</MenuItem>
        <MenuItem value={"ENGINEERING"}>{TYPE_CONVERT["ENGINEERING"]}</MenuItem>
        <MenuItem value={"TECH_FEASIBILITY"}>
          {TYPE_CONVERT["TECH_FEASIBILITY"]}
        </MenuItem>
      </TextField>
    </>
  );
};

DiagramApprovalEdit.propTypes = {
  build: PropTypes.object.isRequired,
  value: PropTypes.string,
  onSelect: PropTypes.func.isRequired,
};
