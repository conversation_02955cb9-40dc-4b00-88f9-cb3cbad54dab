import {
  Button,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React from "react";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "white",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    padding: theme.spacing(1),
  },
  required: {
    color: "red",
    fontSize: "1.5rem",
  },
}));

/**
 * called when the user clicks cancel on the ApprovalDialog.
 * @callback ApprovalBlockersDialog~onClose
 */

/**
 * Displays the reasons why an approval can't be approved.
 * @param {boolean} open - hide or show the dialog
 * @param {string[]} reasons - The reasons why the approval can't be done
 * @param {ApprovalBlockersDialog~onClose} onClose - Called when the user clicks the close button.
 * @returns {JSX.Element}
 * @constructor
 */
export function ApprovalBlockersDialog({ open, reasons, onClose }) {
  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className={classes.title}>
        Can't Perform the Action: AT Approve
      </DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <div>You are unable to perform that action. Here are the reasons:</div>

        <br />

        <List>
          {reasons.map((reason) => (
            <ListItem key={reason}>
              <ListItemIcon>
                <ArrowRightAltIcon color="primary" />
              </ListItemIcon>
              <ListItemText>{reason}</ListItemText>
            </ListItem>
          ))}
        </List>
      </DialogContent>

      <DialogActions>
        <Button variant="contained" color="primary" onClick={onClose}>
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
}

ApprovalBlockersDialog.propType = {
  open: PropTypes.bool.isRequired,
  blockReasons: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
};
