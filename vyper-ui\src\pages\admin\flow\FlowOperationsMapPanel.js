import React, { useState, useContext, useEffect } from "react";
import { DataGrid } from "../../../component/universal";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { logError } from "../../functions/logError";
import { FlowOperationsMapDao } from "../../../dao/FlowOperationsMapDao";
import { DeviceFlowMapDao } from "../../../dao/DeviceFlowMapDao";
import {
  AlertDialogErrorHandler,
  NoopLoadingHandler,
} from "../../../component/fetch/DaoBase";
import {
  gridActionEditRecord,
  gridActionDeleteRecord,
} from "../../../component/universal";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import { FlowRowMapDialog } from "./FlowRowMapDialog";
import AddBoxIcon from "@material-ui/icons/AddBox";

export function FlowOperationsMapPanel() {
  const [data, setData] = useState([]);
  const { open: openAlert } = useContext(AlertDialogContext);
  const [show, setShow] = useState(false);
  const [rowData, setRowData] = useState({
    flowName: undefined,
    opnName: undefined,
    opnSeq: undefined,
    opnDesc: undefined,
  });
  const [mffFlows, setMffFlows] = useState();
  const [defaultRows, setDefaultRows] = useState();
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  // const { showOperationsDialog } = useContext(FlowOperationsDialogContext);

  const flowOperationsMapDao = new FlowOperationsMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new NoopLoadingHandler(),
  });

  const deviveFlowMapDao = new DeviceFlowMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new NoopLoadingHandler(),
  });

  useEffect(() => {
    getDeviceFlowData().catch(logError);
    // refresh().catch(logError);
  }, []);

  useEffect(() => {}, [rowData]);

  const getDeviceFlowData = () => {
    return deviveFlowMapDao
      .list(0, 1000)
      .then((json) => {
        const defaultFlow = json.filter((flowData) => {
          return flowData.default;
        })[0].flowName;

        setMffFlows(json.map((flowData) => flowData.flowName));
        refresh(defaultFlow).catch(logError);
      })
      .catch(logError);
  };

  const refresh = (defaultFlow) => {
    return flowOperationsMapDao
      .list(0, 1000)
      .then((json) => {
        console.log({ json });
        setData(json);
        console.log({ defaultFlow });
        setDefaultRows(
          json
            .filter((flowRowData) => flowRowData.flowName === defaultFlow)
            .map((flowRowData) => flowRowData.opnName)
        );
      })
      .catch(logError);
  };

  const columns = [
    {
      field: "flowName",
      title: "FLOW NAME",
      validate: (rowData) => {
        return rowData.flowName === "" ? "flow name cannot be blank" : "";
      },
    },
    {
      field: "opnName",
      title: "ROW NAME",
      validate: (rowData) => {
        return rowData.opnName === "" ? "operation name cannot be blank" : "";
      },
    },
    {
      field: "opnSeq",
      title: "ROW SEQ",
      validate: (rowData) => {
        return rowData.opnSeq === ""
          ? "operation description cannot be blank"
          : "";
      },
    },
    {
      field: "opnDesc",
      title: "ROW DESC",
      validate: (rowData) => {
        return rowData.opnDesc === ""
          ? "operation description cannot be blank"
          : "";
      },
    },
  ];

  function handleRowAdd() {
    setRowData({
      flowName: undefined,
      opnName: undefined,
      opnSeq: undefined,
      opnDesc: undefined,
    });
    setShow(true);
  }

  function handleRowUpdate(newData) {
    return new Promise((resolve, reject) => {
      flowOperationsMapDao
        .update(newData)
        .then((flowRowMap) => {
          setData(
            data.map((tnm) => (tnm.id === flowRowMap.id ? flowRowMap : tnm))
          );
          resolve();
        })
        .catch(() => reject());
    });
  }

  function handleRowDelete(oldData) {
    return new Promise((resolve, reject) => {
      flowOperationsMapDao
        .delete(oldData.id)
        .then(() => {
          setData(data.filter((tnm) => tnm.id !== oldData.id));
          resolve();
        })
        .catch(() => reject());
    });
  }

  const handleSave = () => {
    console.log({ rowData });
    setShow(false);
    if (rowData.id == null) {
      console.log({ id: rowData.id });

      return new Promise((resolve, reject) => {
        flowOperationsMapDao
          .create(rowData)
          .then((data) => {
            setData(data);
            resolve();
          })
          .catch(() => reject());
      });
    } else {
      console.log({ id: rowData.id });
      //   handleRowDelete(rowData)
    }
  };

  const handleEdit = (event, row) => {
    setRowData(row);
    setShow(true);
  };

  const handleDelete = (e, row) => {
    openConfirmation({
      title: "Delete Flow Row Map",
      message: `Are you sure you want to delete flow row map # ${row.flowName} & ${row.opnName}?`,
      yesText: "Yes",
      noText: "No",
      onYes: () => {
        console.log({ row });
      },
    });
  };

  const handleChangeRowMap = (e, field) => {
    console.log({ e, rowData });
    const newRowData = rowData;
    newRowData[`${field}`] = e.target.value;
    console.log({ newRowData });
    setRowData(newRowData);
  };

  return (
    <div>
      <DataGrid
        title="Flow Rows Map"
        columns={columns}
        data={data}
        editable={true}
        actions={[
          gridActionEditRecord(handleEdit),
          gridActionDeleteRecord(handleDelete),
          {
            icon: () => <AddBoxIcon />,
            isFreeAction: true,
            onClick: handleRowAdd,
          },
        ]}
        options={{
          search: false,
          pageSize: 20,
          pageSizeOptions: [5, 10, 20, 50, 100, 200],
        }}
      />
      {show && (
        <FlowRowMapDialog
          rowData={rowData}
          show={true}
          onClose={() => setShow(false)}
          onSave={handleSave}
          mffFlows={mffFlows}
          defaultRows={defaultRows}
          handleChange={handleChangeRowMap}
        />
      )}
    </div>
  );
}
