import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";

export const DryBakeCell = ({ vyper, build, onClick }) => {
  const { canEditDryPack } = useContext(HelperContext);
  const canEdit = canEditDryPack(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Dry Bake")
  )
    return null;

  return (
    <DataCell source={build.dryBake?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        {build.dryBake?.object?.value || "click to select"}
      </VyperLink>
    </DataCell>
  );
};
