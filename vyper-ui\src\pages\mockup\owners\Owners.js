import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext } from "react";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";
import { HelperContext } from "src/component/helper/Helpers";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { AddOwner } from "src/pages/mockup/owners/AddOwner";
import { SelectUserDialogContext } from "src/pages/mockup/owners/SelectUserDialog";
import { Owner } from "./Owner";

const useStyles = makeStyles(() => ({
  root: {},
}));

/**
 * Display the owner chips and handle adding and removing them.
 *
 * @returns {JSX.Element}
 * @function
 */
export const Owners = () => {
  const { vyperDao, vyper } = useContext(DataModelsContext);
  const { handleOpen: showSelectUserDialog } = useContext(
    SelectUserDialogContext
  );
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { canEditOwners } = useContext(HelperContext);

  if (!vyper) return null;

  const canEdit = canEditOwners(vyper);

  const handleAdd = () => {
    showSelectUserDialog({
      onSave: (data) => {
        vyperDao
          .addOwner(vyper.vyperNumber, data.userid, data.username)
          .catch(noop);
      },
    });
  };

  const handleRemove = (user) => {
    openConfirmation({
      title: "Remove Owner",
      message: `Are you sure you want to remove ${user.username}?`,
      yesText: "Yes",
      noText: "No",
      onYes: () => {
        vyperDao.removeOwner(vyper.vyperNumber, user.userid).catch(noop);
      },
    });
  };

  // you can remove only if there is more than 1 owner
  // aka- can't remove the last owner
  const canRemove = vyper.owners.length > 1 && canEdit;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      {vyper.owners?.map((owner, n) => (
        <Owner
          key={n}
          owner={owner}
          canRemove={canRemove}
          onRemove={handleRemove}
          canEdit={canEdit}
        />
      ))}

      <AddOwner canEdit={canEdit} onAdd={handleAdd} />
    </div>
  );
};

Owners.propTypes = {};
