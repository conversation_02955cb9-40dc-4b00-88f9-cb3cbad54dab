import React from "react";
import { TableRow } from "@material-ui/core";
import { HeaderButt<PERSON> } from "./HeaderButtons";
import { HeaderCell } from "./HeaderCell";

/**
 *
 * @param rowButtons
 * @param rowButtonsTitle
 * @param columns
 * @param pageable
 * @param filters
 * @param header
 * @param onSortClick
 * @param onFilterChange
 * @param overrides
 * @param visible
 * @returns {*}
 * @constructor
 */
export const HeaderRow = ({
  rowButtons,
  rowButtonsTitle,
  columns,
  pageable,
  filters,
  header,
  onSortClick,
  onFilterChange,
  overrides,
  visible,
}) => {
  return (
    <TableRow hover>
      <HeaderButtons
        rowButtons={rowButtons}
        rowButtonsTitle={rowButtonsTitle}
      />

      {columns.map((column, columnIndex) => (
        <HeaderCell
          key={column.id}
          column={column}
          columnIndex={columnIndex}
          pageable={pageable}
          filter={filters[column.id]}
          header={header(column, columnIndex)}
          onSortClick={() => onSortClick(column)}
          onFilterChange={onFilterChange}
          overrides={overrides}
          visible={visible}
        />
      ))}
    </TableRow>
  );
};
