import React from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import DragIndicatorIcon from "@material-ui/icons/DragIndicator";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import { Operation } from "./Operation";
import { AddRequiredButton } from "./buttons/AddRequiredButton";

const useStyles = makeStyles({
  dragHandle: {
    display: "inline-flex",
    alignItems: "center",
    cursor: "grab",
    color: "#666",
    marginRight: "8px",
    "&:hover": {
      color: "#333",
    },
    "&:active": {
      cursor: "grabbing",
    },
  },
  operationWithDrag: {
    display: "flex",
    alignItems: "flex-start",
  },
  operationContent: {
    flex: 1,
  },
  draggingItem: {
    backgroundColor: "#f0f8ff",
    borderRadius: "4px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  }
});

export const Body = ({
  vyper,
  build,
  options,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  onSelectDiagramApproval,
  unApprovedGroups,
  dragDropChanges,
  onOperationReorder,
  onComponentReorder,
}) => {
  const determineValidatedOperation = (operation) => {
    return build.validatedOperations.find(
      (vo) => vo.operation === operation.name
    );
  };

  // Use drag drop changes if available, otherwise use build operations
  const operationsToRender = dragDropChanges?.operations || build.traveler.operations;

  const isDragDropEnabled = options.dragdrop;

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    // Check if it's an operation reorder (droppableId starts with "operations")
    if (result.source.droppableId === "operations" && result.destination.droppableId === "operations") {
      onOperationReorder(result);
    } else {
      // It's a component reorder
      onComponentReorder(result);
    }
  };

  const classes = useStyles();

  const renderOperations = () => {
    return operationsToRender.map((operation, n) =>
      operation.name === "TEST" ? null : (
        <Draggable
          key={operation.name}
          draggableId={`operation-${operation.name}`}
          index={n}
          isDragDisabled={!isDragDropEnabled}
        >
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.draggableProps}
              style={provided.draggableProps.style}
              className={clsx(
                classes.operationWithDrag,
                snapshot.isDragging && classes.draggingItem
              )}
            >
              <div className={classes.operationWithDrag}>
                {isDragDropEnabled && (
                  <div {...provided.dragHandleProps} className={classes.dragHandle}>
                    <DragIndicatorIcon fontSize="small" />
                  </div>
                )}
                <div className={classes.operationContent}>
                  <Operation
                    key={n}
                    vyper={vyper}
                    build={build}
                    operation={operation}
                    operationIndex={n}
                    options={options}
                    onEditOperation={(o) => onEditOperation(o, n)}
                    onAddOperation={(o) => onAddOperation(o, n)}
                    onRemoveOperation={(o) => onRemoveOperation(o, n)}
                    onEditComponentName={(o, c, cPos) =>
                      onEditComponentName(o, c, n, cPos)
                    }
                    onEditComponentValue={(o, c, cPos) =>
                      onEditComponentValue(o, c, n, cPos)
                    }
                    onAddComponent={(o, c, cPos) => onAddComponent(o, c, n, cPos)}
                    onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, n, cPos)}
                    validatedOperation={determineValidatedOperation(operation)}
                    onClickValidate={onClickValidate}
                    onCommentOperation={onCommentOperation}
                    approvedGroups={approvedGroups}
                    onUndoDelete={(o) => onUndoDelete(o)}
                    reworkedTraveler={build?.reworkedTraveler}
                    onSelectDiagramApproval={onSelectDiagramApproval}
                    unApprovedGroups={unApprovedGroups}
                    isDragDropEnabled={isDragDropEnabled}
                    onComponentReorder={onComponentReorder}
                  />
                </div>
              </div>
            </div>
          )}
        </Draggable>
      )
    );
  };

  return (
    <div>
      {options.editbutton ? (
        <AddRequiredButton
          title="Add Operation"
          onClick={() => onAddOperation(null)}
        />
      ) : null}

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="operations" type="operation" isDropDisabled={!isDragDropEnabled}>
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {renderOperations()}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};