import React, { useContext, useEffect, useState } from "react";
import { useWireMetal } from "src/pages/admin/wiremetal/hooks/useWireMetal";
import { useArmarcWireMetal } from "src/pages/admin/wiremetal/hooks/useArmarcWireMetal";
import { useCamsWireMetal } from "src/pages/admin/wiremetal/hooks/useCamsWireMetal";
import { Box, Button } from "@material-ui/core";
import { CheckboxList } from "src/pages/admin/wiremetal/components/CheckboxList";
import { useWireMetalApi } from "src/api/useWireMetalApi";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { WireMetalTable } from "src/pages/admin/wiremetal/components/WireMetalTable";

export const WireMetalPanel = () => {
  // 1. get all armarc metals
  // 2. get all cams metals
  // 3. get all wire metal maps

  const { open: openError } = useContext(ErrorDialogContext);
  const { link, unlink } = useWireMetalApi();
  const { wireMetals, setWireMetals } = useWireMetal();
  const { armarcWireMetals } = useArmarcWireMetal();
  const { camsWireMetals } = useCamsWireMetal();
  const [wireMetalValues, setWireMetalValues] = useState([]);
  const [armarcValues, setArmarcValues] = useState([]);
  const [camsValues, setCamsValues] = useState([]);
  const [displayCamsWireMetals, setDisplayCamsWireMetals] = useState([]);

  // create the cams displayed values, which is the <NO WIRE> value + cams wire metals
  useEffect(() => {
    setDisplayCamsWireMetals(["<NO WIRE>", ...camsWireMetals]);
  }, [camsWireMetals]);

  const handleClickLink = () => {
    return link(armarcValues, camsValues)
      .then((json) => setWireMetals((old) => [...old, ...json]))
      .then(() => setArmarcValues([]))
      .then(() => setCamsValues([]))
      .catch((error) => openError({ error, title: "Link Wire Metals" }));
  };

  const handleClickUnlink = () => {
    return unlink(wireMetalValues.map((wm) => wm.id))
      .then(() =>
        setWireMetals((old) =>
          old.filter((wm) => !wireMetalValues.includes(wm))
        )
      )
      .then(() => setWireMetalValues([]))
      .catch((error) => openError({ error, title: "UnLink Wire Metals" }));
  };

  if (!wireMetals || !armarcWireMetals || !camsWireMetals) {
    return null;
  }

  const canAdd = armarcValues.length > 0 && camsValues.length > 0;
  const canRemove = wireMetalValues.length > 0;

  console.log("canRemove", canRemove);

  return (
    <div>
      <h3>MAP ARMARC Wire Type to CAMS wire Type</h3>
      <p>
        The PRA PAVV verifier will use these values to match armarc and cams
        wires.
      </p>

      <div>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            gap: "1em",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "flex-start",
              alignItems: "flex-end",
            }}
          >
            <div style={{ width: "100%" }}>
              <CheckboxList
                title="ARMARC Wire Types"
                items={armarcWireMetals}
                selected={armarcValues}
                onChangeSelected={setArmarcValues}
              />
            </div>

            <div style={{ width: "100%" }}>
              <CheckboxList
                title="CAMS Wire Types"
                items={displayCamsWireMetals}
                selected={camsValues}
                onChangeSelected={setCamsValues}
              />
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                disabled={!canAdd}
                onClick={handleClickLink}
              >
                Link
              </Button>
            </div>
          </div>

          <div>
            <div>
              <WireMetalTable
                title="Linked Wire Types"
                items={wireMetals}
                selected={wireMetalValues}
                onChangeSelected={setWireMetalValues}
              />
            </div>
            <div style={{ marginTop: "1rem" }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                disabled={!canRemove}
                onClick={handleClickUnlink}
              >
                UnLink
              </Button>
            </div>
          </div>
        </Box>
      </div>
    </div>
  );
};
