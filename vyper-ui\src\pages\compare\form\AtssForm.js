import {
  <PERSON><PERSON>,
  FormControlLabel,
  FormGroup,
  makeStyles,
  TextField,
} from "@material-ui/core";
import MenuItem from "@material-ui/core/MenuItem";
import React, { useContext, useState } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { BuildNumberAutocomplete } from "../../../autocomplete/components/BuildNumberAutocomplete";
import { useLocalStorage } from "../../../component/hooks/useLocalStorage";
import { MaterialAutocomplete } from "../../../autocomplete/components/MaterialAutocomplete";
import { Facility } from "../../../component/newBuild/Facility";
import { Difference } from "../difference/Difference";
import { DifferenceHeader } from "../difference/DifferenceHeader";

const useStyles = makeStyles((theme) => ({
  root: {},
  form: {
    marginBottom: theme.spacing(3),
  },
  widget: {
    marginRight: "3rem",
    width: "16rem",
  },
}));

export const AtssForm = () => {
  const { compareDao } = useContext(DataModelsContext);

  const [buildNumber, setBuildNumber] = useState();
  const [material, setMaterial] = useState({ Material: "" }); // {Material:"", SBE:"", ...}
  const [facilities, setFacilities] = useState([]); // {Material:"", SBE:"", ...}
  const [facility, setFacility] = useState({ PDBFacility: "" }); // { PDBFacility:"", PlantCode:0000, ...}
  const [status, setStatus] = useLocalStorage("compare.atss.status");

  const [header, setHeader] = useState([]);
  const [labels, setLabels] = useState({ left: "Left", right: "Right" });
  const [differences, setDifferences] = useState();

  const statuses = ["ACTIVE", "WORKING"];

  // when the user selects a material in the material autocomplete, save the attributes and facilities.
  const handleSelect = ({ facilities, materialAttributes }) => {
    setMaterial(materialAttributes);
    setFacilities(facilities);
    setFacility("");
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    setHeader([
      { label: "Device:", value: material.Material },
      { label: "ATSS Facility:", value: facility.PDBFacility },
      { label: "ATSS Status:", value: status },
      { label: "Vyper Build Number:", value: buildNumber },
    ]);

    setLabels({ left: "Vyper #", right: "ATSS #" });

    compareDao
      .atss(buildNumber, material.Material, facility.PDBFacility, status)
      .then((json) => setDifferences(json.differences))
      .catch(noop);
  };

  console.log("##### facilities", facilities);

  const enableButton =
    buildNumber != null &&
    material?.Material != null &&
    facility != null &&
    status != null;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <form className={classes.form} onSubmit={handleSubmit}>
        <FormGroup row>
          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                id="build-atss-build"
                variant="outlined"
                aria-describedby="build-text"
                defaultNumber={buildNumber}
                onSelect={(number) => setBuildNumber(number)}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <MaterialAutocomplete
                label="Device"
                fullWidth
                variant="outlined"
                defaultMaterial={material}
                onSelect={handleSelect}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <Facility
                facilities={facilities}
                facility={facility}
                onChange={(e) => {
                  console.log("####", e);
                  setFacility(e);
                }}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <TextField
                variant="outlined"
                className={classes.autocomplete}
                select
                label="ATSS Status"
                value={status || ""}
                onChange={(e) => setStatus(e.target.value)}
              >
                {statuses.map((s) => (
                  <MenuItem key={s} value={s}>
                    {s}
                  </MenuItem>
                ))}
              </TextField>
            }
          />

          <FormControlLabel
            label=""
            control={
              <Button
                type="submit"
                color="primary"
                variant="contained"
                disabled={!enableButton}
              >
                Compare
              </Button>
            }
          />
        </FormGroup>
      </form>

      <DifferenceHeader items={header} />

      <Difference
        label1={labels.left}
        label2={labels.right}
        differences={differences}
      />
    </div>
  );
};
