import React from "react";
import { ComponentPriorityView } from "src/component/build-component/ComponentPriorityView";

export const ComponentInstanceView = ({ instance }) => {
  if (instance.priorities.length === 0) {
    return <div>click to select</div>;
  }

  /**
   * Display : Name (Part number ) or Name
   */
  return (
    <div>
      {instance.priorities.map((priority, n) => (
        <ComponentPriorityView key={n} priority={priority} />
      ))}
    </div>
  );
};
