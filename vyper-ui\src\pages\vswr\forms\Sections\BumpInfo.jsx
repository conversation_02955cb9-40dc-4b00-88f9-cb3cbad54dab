import React, { useState } from "react";
import Grid from "@material-ui/core/Grid";
import TextField from "@material-ui/core/TextField";

import {
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@material-ui/core";

const BumpInfo = (props) => {
  const { classes, defaultFormVals = {} } = props;
  const [bumpReq, setBumpReq] = useState("no");
  const [backCoatReq, setBackCoatReq] = useState("");

  return (
    <div className={classes.card}>
      {"Disabled until further VYPER support"}
      <div className={classes.formHeader}>{"Bump Information"}</div>

      <Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <FormControl color={"secondary"} component="fieldset">
              <FormLabel component="legend">Bump Required</FormLabel>
              <RadioGroup
                row
                name="bumpReq"
                value={bumpReq}
                onChange={(e) => setBumpReq(e.target.value)}
              >
                <FormControlLabel
                  value={"no"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="No"
                />
                <FormControlLabel
                  disabled
                  value={"yes"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="Yes"
                />
              </RadioGroup>
            </FormControl>
          </Grid>
        </Grid>
        {bumpReq === "yes" && (
          <>
            <Grid container spacing={1}>
              <Grid item xs>
                <TextField
                  color="secondary"
                  label="Bump Mask"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs>
                <TextField
                  color="secondary"
                  label="Bump Material"
                  variant="outlined"
                />
              </Grid>
            </Grid>

            <Grid container spacing={1}>
              <Grid item xs>
                <TextField
                  color="secondary"
                  label="Repassive Type"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs>
                <TextField
                  color="secondary"
                  label="Ground Pin Map"
                  variant="outlined"
                />
              </Grid>
            </Grid>

            <Grid container spacing={1}>
              <Grid item xs>
                <FormControl fullWidth color={"secondary"} component="fieldset">
                  <FormLabel component="legend">
                    Backside Coating Required?
                  </FormLabel>
                  <RadioGroup
                    row
                    name="bumpReq"
                    value={backCoatReq}
                    onChange={(e) => setBackCoatReq(e.target.value)}
                  >
                    <FormControlLabel
                      value={"yes"}
                      labelPlacement="end"
                      control={<Radio />}
                      label="Yes"
                    />
                    <FormControlLabel
                      value={"no"}
                      labelPlacement="end"
                      control={<Radio />}
                      label="No"
                    />
                  </RadioGroup>
                </FormControl>
              </Grid>
            </Grid>

            <Grid container spacing={1}>
              <Grid item xs>
                <TextField
                  fullWidth
                  color="secondary"
                  label="Backside Coating Thickness"
                  variant="outlined"
                />
              </Grid>
              <Grid item justifyContent="left">
                <FormControl color={"secondary"} component="fieldset">
                  <RadioGroup
                    row
                    name="bumpReq"
                    value={backCoatReq}
                    onChange={(e) => setBackCoatReq(e.target.value)}
                  >
                    <FormControlLabel
                      value={"mils"}
                      labelPlacement="end"
                      control={<Radio />}
                      label="mils"
                    />
                    <FormControlLabel
                      value={"microns"}
                      labelPlacement="end"
                      control={<Radio />}
                      label="microns"
                    />
                  </RadioGroup>
                </FormControl>
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs>
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  color="secondary"
                  label="Comment"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </>
        )}
      </Grid>
    </div>
  );
};
export default BumpInfo;
