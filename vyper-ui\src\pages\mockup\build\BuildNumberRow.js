import { TableCell, TableRow } from "@material-ui/core";
import React, { useContext, useState } from "react";
import { useHistory } from "react-router-dom";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import { HelperContext } from "../../../component/helper/Helpers";
import { uriEncode } from "../../../component/utils";
import { noop } from "../../../component/vyper/noop";
import { DataModelsContext } from "../../../DataModel";
import { AuditDialogContext } from "../audit/AuditDialog";
import { CopyBuildDialogContext } from "../copybuild/CopyBuildDialog";
import { RowPrefix } from "../RowPrefix";
import { NumberItem } from "./NumberItem";
import { logError } from "src/pages/functions/logError";
import { createTask } from "src/component/api/taskService2";
import {
  ApprovalOperationContext,
  determineGroupFromApprovalOperation,
} from "../../../component/approvaloperation/ApprovalOperation";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import config from "../../../../src/buildEnvironment";
import { useWarningApi } from "src/api/useWarningApi";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { ArmArcCheckerDialog } from "src/pages/mockup/armarc_checker/ArmArcCheckerDialog";

const { externalUse } = config;

/**
 * @typedef {object} ExtGroupUser
 * @property {string} name
 * @property {string} internalFlag
 * @property {string} uid
 * @property {string} eid
 */

/**
 * @typedef {object} ExtGroup
 * @property {string} name
 * @property {string} role
 * @property {boolean} roleGroup
 * @property {Array<ExtGroupUser>} users
 */

/**
 * @typedef {object} ExtGroups
 * @property {string} plantName
 * @property {number} plantId
 * @property {string} plantName
 * @property {Array<ExtGroup>} groups
 */

export const BuildNumberRow = ({
  vyper,
  vyperNumber,
  builds,
  onChange,
  buildsWithPras = [],
}) => {
  const { warningRefresh } = useWarningApi();
  const { open: openError } = useContext(ErrorDialogContext);

  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );
  const { vget } = useContext(FetchContext);

  const { openDialog: openAudit } = useContext(AuditDialogContext);
  const { open: confirmationDialogOpen } = useContext(
    ConfirmationDialogContext
  );
  const { auditDao, buildDao, praDao } = useContext(DataModelsContext);
  const { openDialog: openCopyBuildDialog } = useContext(
    CopyBuildDialogContext
  );
  const { open: openAlert } = useContext(AlertDialogContext);
  const { canEditOwners, canAddPra } = useContext(HelperContext);

  const history = useHistory();

  // can the current user edit this vyper?
  const canEdit = canEditOwners(vyper);

  // get the audits, then show the dialog
  const handleClickAudit = (build) => {
    auditDao
      .findAllByVyperNumber(vyperNumber)
      .then((json) =>
        json.filter(
          (a) => a.buildNumber == null || a.buildNumber === build.buildNumber
        )
      )
      .then((json) =>
        openAudit({ buildNumber: build.buildNumber, audits: json })
      )
      .catch(noop);
  };

  /**
   * confirm, then send the refresh PGS post
   * @param build
   */
  const handleClickRefreshPgs = (build) => {
    confirmationDialogOpen({
      message:
        "This will update any components with PGS as the source to the current PGS values.",
      onYes: () => {
        buildDao
          .refreshPgs(vyperNumber, build.buildNumber)
          .then((json) => {
            onChange(json);

            // convert the /n separated lastChange into an array of divs

            let message;
            if (json.pgs.lastChange === "" || json.pgs.lastChange == null) {
              message = "No changes.";
            } else {
              message = json.pgs.lastChange
                .split("\n")
                .map((item) => <div key={item}>{item}</div>);
            }

            openAlert({ title: "PGS Changes", message: message });
          })
          .catch(console.log);
      },
    });
  };

  /**
   * confirm, then send the refresh BOM post
   * @param build
   */
  const handleClickRefreshBomTemplate = (build) => {
    confirmationDialogOpen({
      message:
        "Are you sure you want to refresh the Bill of Process Template? This will overwrite any changes you have made.",
      onYes: () => {
        buildDao
          .refreshBomTemplate(vyperNumber, build.buildNumber)
          .then((json) => onChange(json))
          .catch(noop);
      },
    });
  };

  const handleClickRefreshFlow = (build) => {
    confirmationDialogOpen({
      message:
        "Are you sure you want to refresh the flow? This may overwrite any changes you have made.",
      onYes: () => {
        buildDao
          .refreshFlow(vyperNumber, build.buildNumber)
          .then((json) => onChange(json))
          .catch(noop);
      },
    });
  };

  const handleClickAddPra = (build) => {
    const facility = build.facility.object.PDBFacility;

    vget(`/vyper/v1/emas/plant/roles/${facility}`, (extGroups) => {
      praDao
        .addPra(vyperNumber, build.buildNumber)
        .then((newPra) => {
          //get groups from component operations
          let atApproverGroups = Object.keys(newPra.validatedComponents)
            .map((key) => newPra.validatedComponents[key].parentOperation)
            .map((operation) => {
              const ao = findApprovalOperationByOperation(operation);
              return determineGroupFromApprovalOperation(ao, facility);
            });

          atApproverGroups = new Set(atApproverGroups);
          const buApproverAids = vyper.owners.map((owner) => owner.userid);

          //creating task data
          const metaData = [
            {
              attrName: "device id",
              attrValue: newPra.material?.object?.Material,
            },
            { attrName: "description", attrValue: newPra.description },
            {
              attrName: "groups",
              attrValue: atApproverGroups.map((grp) => grp).join(", "),
            },
            { attrName: "owners", attrValue: buApproverAids.join(",") },
          ];
          const taskInfo = {
            taskName: newPra.praNumber,
            ctxIdLabel: "praNumber",
            ctxIdValue: newPra.praNumber,
          };

          if (extGroups.plantId && extGroups.groups) {
            // External site, need to transform approver groups
            const newGroups = extGroups.groups.filter(
              (item) => atApproverGroups.has(item.name) && item.users.length
            );
            atApproverGroups = newGroups;
          }

          return createTask(
            metaData,
            taskInfo,
            atApproverGroups,
            buApproverAids
          );
        })
        .then(handleGoToPra)
        .catch(logError);
    });
  };

  // Opens the Copy Build Dialog
  const handleClickCopyBuild = (build) => {
    openCopyBuildDialog({
      build: build,
      onAddCopiedBuild: (srcBuildNumber, buildtype, description) => {
        return buildDao
          .addCopiedBuild(vyperNumber, srcBuildNumber, buildtype, description)
          .then(onChange)
          .catch(noop);
      },

      onCreateVyperAndCopyBuild: (srcBuildNumber, buildtype, description) => {
        return buildDao
          .createVyperAndCopyBuild(
            vyperNumber,
            srcBuildNumber,
            buildtype,
            description
          )
          .then((json) => {
            // creates a new vyper, so we navigate to it's page
            history.push(`/projects/${json.vyperNumber}`);
          })
          .catch(noop);
      },
    });
  };

  const handleGoToPra = () => {
    history.push(`/projects/${vyperNumber}/pras`);
  };
  // TODOX: Change From hardCoded link
  const handleGoToVSWR = (vbuild) => {
    window.open(`/vyper/vswr/create/${vbuild}`, "_blank");
  };

  const handleRefreshWarnings = (build) => {
    warningRefresh(vyperNumber, build.buildNumber)
      .then(onChange)
      .catch((error) => openError({ error, title: "Refresh Warnings" }));
  };

  const [openChecker, setOpenChecker] = useState(false);
  const [checkerBuild, setCheckerBuild] = useState();

  const handleArmarc = (build) => {
    buildDao
      .checkArmarc(vyperNumber, build.buildNumber)
      .then((build) => {
        onChange(build);
        setCheckerBuild(build);
        setOpenChecker(true);
      })
      .catch((error) => openError({ error, title: "Refresh Armarc" }));
  };

  // Function will create a new vyper and copy the srcBuild to it
  const handleMenu = (build, action) => {
    switch (action) {
      case "Select Components":
        history.push(
          `/projects/${vyperNumber}/builds/${build.buildNumber}/selection`
        );
        break;

      case "Build Process Summary":
        history.push(
          `/projects/${vyperNumber}/builds/${build.buildNumber}/summary`
        );
        break;

      case "Traveler":
        history.push(
          `/projects/${vyperNumber}/builds/${build.buildNumber}/traveler`
        );
        break;

      case "Traveler Plain":
        history.push(
          `/projects/${vyperNumber}/builds/${build.buildNumber}/plain`
        );
        break;

      case "CycleTime Report":
        history.push(
          `/reports?label=${uriEncode("Build Workflow Age")}&buildNumber=${
            build.buildNumber
          }`
        );
        break;

      case "Audit":
        handleClickAudit(build);
        break;

      case "Approval History Analysis":
        history.push(
          `/projects/${vyperNumber}/builds/${build.buildNumber}/approvalsHistory`
        );
        break;

      case "Refresh PGS":
        handleClickRefreshPgs(build, false);
        break;

      case "Refresh Bill of Process Template":
        handleClickRefreshBomTemplate(build, false);
        break;

      case "Refresh Flow":
        handleClickRefreshFlow(build, false);
        break;

      case "Open Copy Build Dialog":
        handleClickCopyBuild(build);
        break;

      case "Add PRA":
        handleClickAddPra(build);
        break;

      case "Go To PRA":
        handleGoToPra();
        break;

      case "Go To VSWR":
        handleGoToVSWR(build.buildNumber);
        break;

      case "Refresh Warnings":
        handleRefreshWarnings(build);
        break;

      case "armarc":
        handleArmarc(build);
        break;
    }
  };

  const buildConfig = (build) => {
    // does the build have pras?
    const hasPra = buildsWithPras.includes(build.buildNumber);

    // can a pra be created?
    const canPra = canAddPra(build, hasPra);

    const canVswr = build.state === "FINAL_APPROVED";

    let enableItem;
    if(build.buildtype === "Minor Change"){
      enableItem = false;
    }
    else{
      enableItem = true;
    }

    let menuItems = [
      {
        id: "Select Components",
        title: "Select Components",
        enabled: true,
        internal: true,
      },
      {
        id: "Build Process Summary",
        title: "Build Process Summary",
        enabled: true,
        internal: true,
      },
      { id: "Traveler", title: "Traveler", enabled: true, internal: true },
      {
        id: "Traveler Plain",
        title: "Traveler Plain",
        enabled: true,
        internal: true,
      },
      {
        id: "CycleTime Report",
        title: "CycleTime Report",
        enabled: true,
        internal: true,
      },
      { id: "Audit", title: "Audit", enabled: true, internal: true },
      {
        id: "Approval History Analysis",
        title: "Approval History Analysis",
        enabled: true,
        internal: true,
      },
      { id: "Refresh PGS", 
        title: "Refresh PGS", 
        enabled: canEdit && enableItem },
      {
        id: "Refresh Bill of Process Template",
        title: "Refresh Bill of Process Template",
        enabled: canEdit && enableItem,
      },
      { id: "Refresh Flow", 
        title: "Refresh Flow", 
        enabled: canEdit && enableItem },
      { id: "Refresh Warnings", 
        title: "Refresh Warnings", 
        enabled: false },
      {
        id: "Open Copy Build Dialog",
        title: "Open Copy Build Dialog",
        enabled: true,
      },
      { id: "Add PRA", title: "Add PRA", enabled: canPra },
      { id: "Go To PRA", title: "Go To PRA", enabled: hasPra, internal: true },
      { id: "Go To VSWR", title: "Go To VSWR", enabled: canVswr },
      { id: "armarc", title: "Check ARM:ARC validation", enabled: true },
    ];

    if (externalUse) {
      menuItems = menuItems.filter((menuItem) => menuItem.internal);
    }

    return {
      number: build.buildNumber,
      state: build.state,
      menuItems,
    };
  };

  return (
    <TableRow hover>
      <RowPrefix help="build" title="Build Number" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <NumberItem
            config={buildConfig(build)}
            onMenu={(action) => handleMenu(build, action)}
          />
        </TableCell>
      ))}
      <ArmArcCheckerDialog
        open={openChecker}
        onClose={() => setOpenChecker(false)}
        build={checkerBuild}
        button1Text="Close"
        onButton1={() => setOpenChecker(false)}
      />
    </TableRow>
  );
};
