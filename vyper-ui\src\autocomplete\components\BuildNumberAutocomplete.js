import React, { useContext, useEffect, useState } from "react";
import Autocomplete, {
  createFilterOptions,
} from "@material-ui/lab/Autocomplete";
import TextField from "@material-ui/core/TextField";
import { makeStyles } from "@material-ui/core";
import { FetchContext } from "../../component/fetch/VyperFetch";

const useStyles = makeStyles({
  autocomplete: {
    display: "inherit",
    width: "16rem",
  },
});

/**
 *
 * @param label
 * @param defaultNumber
 * @param onSelect
 * @param max
 * @param refreshInterval
 * @param variant
 * @param rest
 * @returns {JSX.Element}
 * @constructor
 */
export const BuildNumberAutocomplete = ({
  label = "Build Number",
  defaultNumber,
  onSelect,
  max = 10,
  refreshInterval = 500,
  variant,
  ...rest
}) => {
  const { vget } = useContext(FetchContext);
  const [number, setNumber] = useState(defaultNumber);
  const [options, setOptions] = useState([]);

  /**
   * Storage for the timer id
   */
  const [timer, setTimer] = useState();

  /**
   * When number changes, start a timer which will refresh the options list
   */
  useEffect(() => {
    if (number == null || number === "") return;
    if (timer != null) clearTimeout(timer);
    setTimer(window.setTimeout(() => refresh(), refreshInterval));
  }, [number]);

  /**
   * If the number is valid, refresh the options
   */
  const refresh = () => {
    if (number == null || number === "") return;
    vget(
      `/vyper/v1/autocomplete/buildNumber?search=${number.trim()}&max=${max}`,
      setOptions
    );
  };

  const filterOptions = createFilterOptions({
    trim: true,
  });

  const classes = useStyles();

  return (
    <Autocomplete
      {...rest}
      className={classes.autocomplete}
      inputValue={number || ""}
      onInputChange={(e, v) => setNumber(v)}
      value={defaultNumber || ""}
      onChange={(e, number) => onSelect(number)}
      getOptionLabel={(o) => o}
      options={options}
      renderInput={(params) => (
        <TextField {...params} label={label} variant={variant} />
      )}
      filterOptions={filterOptions}
    />
  );
};
