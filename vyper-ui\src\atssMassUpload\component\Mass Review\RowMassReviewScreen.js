import React, {
  useRef,
  useCallback,
  useMemo,
  useState,
  useEffect,
} from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS
import "./styles.css";
import { Button } from "@material-ui/core";
import getColumnDefinition, {
  combineColumnDefinition,
  prepareAgGridColumnsFromColumnMap,
  flattenTravelerData,
  getGroupNamesFromColumnDefs,
} from "./utility";
import TooltipComponent from "./TooltipComponent";

const useStyles = makeStyles({
  root: {},
  buttonBar:{
    display: "flex",
    gap: "0.1rem",
    margin: "0.1rem 0"
  }
});

const RowMassReviewScreen = (props) => {
  const { unifiedView, travelerData, comparisionConfig } = props;
  const gridRef = useRef();
  const containerStyle = useMemo(() => ({ width: "100%", height: "100%" }), []);
  const gridStyle = useMemo(() => ({ height: "100%", width: "100%" }), []);
  const [gridApi, setGridApi] = useState(null);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const referenceTraveler = useRef([]);
  const projectDeviceTraveler = useRef([]);
  const unifiedViewColumns = useRef([]);
  const nonUnifiedViewColumns = useRef([]);
  const rowData = useRef([]);
  const [columnDefs, setColumnDefs] = useState([]);

  const autoSizeAll = useCallback(
    (skipHeader) => {
      if (!gridColumnApi) return;
      const allColumnIds = [];
      gridColumnApi.getAllColumns().forEach((column) => {
        allColumnIds.push(column.getId());
      });
      gridColumnApi.autoSizeColumns(allColumnIds, skipHeader);
    },
    [gridColumnApi]
  );

  useEffect(() => {
    setColumnDefs(
      unifiedView ? unifiedViewColumns.current : nonUnifiedViewColumns.current
    );
  }, [unifiedView]);

  const defaultColDef = useMemo(() => {
    return {
      resizable: true,
      autoHeight: true,
      tooltipComponent: TooltipComponent,
    };
  }, []);

  useEffect(() => {
    referenceTraveler.current = {
      ...travelerData["referenceTraveler"],
      isReference: true,
    };
    projectDeviceTraveler.current = travelerData["projectDeviceTraveler"];
    nonUnifiedViewColumns.current = getColumnDefinition(
      referenceTraveler.current,
      comparisionConfig
    );
    setColumnDefs(nonUnifiedViewColumns.current);
    let columnsMap = new Map();
    for (let i = 0; i < projectDeviceTraveler.current.length; ++i) {
      const map = combineColumnDefinition(
        nonUnifiedViewColumns.current,
        getColumnDefinition(
          projectDeviceTraveler.current[i],
          comparisionConfig
        ),
        columnsMap
      );
      columnsMap = map;
    }
    unifiedViewColumns.current = prepareAgGridColumnsFromColumnMap(columnsMap);

    rowData.current = flattenTravelerData([
      referenceTraveler.current,
      ...projectDeviceTraveler.current,
    ]);
  }, [travelerData]);

  useEffect(() => {
    if (gridApi && gridColumnApi) {
      gridApi.setColumnDefs(columnDefs);
      gridColumnApi.autoSizeAllColumns();
      gridApi.refreshHeader();
    }
  }, [gridApi, gridColumnApi, columnDefs]);

  const onGridReady = useCallback((params) => {
    setGridApi(params.api);
    setGridColumnApi(params.columnApi);
    autoSizeAll(true);
  }, []);

  const expandAll = useCallback(
    (expand) => {
      const groupNames = getGroupNamesFromColumnDefs(gridApi.getColumnDefs());
      groupNames.forEach((groupId) => {
        gridRef.current.columnApi.setColumnGroupOpened(groupId, expand);
      });
      autoSizeAll(true);
    },
    [gridApi]
  );

  const classes = useStyles();
  return (
    <div className={classes.root}>
      <div style={containerStyle}>
        <div className="test-container">
          <div className={classes.buttonBar}>
            <Button variant="contained" color="primary" onClick={() => expandAll(true)}>Expand All</Button>
            <Button variant="contained" color="primary" onClick={() => expandAll(false)}>Contract All</Button>
          </div>
          <div style={gridStyle} className="ag-theme-alpine">
            <AgGridReact
              ref={gridRef}
              rowData={rowData.current}
              defaultColDef={defaultColDef}
              onGridReady={onGridReady}
              tooltipShowDelay={0}
              tooltipHideDelay={2000}
              gridOptions={{
                rowHeight: 25,
                autoSizePadding: 6,
                getRowStyle: (params) => {
                  if (
                    params.data["specDevice"] ===
                    rowData.current[0]["specDevice"]
                  ) {
                    return {
                      background: "#B8E2F2",
                    };
                  }
                },
                context: {
                  referenceData: rowData.current[0],
                },
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RowMassReviewScreen;
