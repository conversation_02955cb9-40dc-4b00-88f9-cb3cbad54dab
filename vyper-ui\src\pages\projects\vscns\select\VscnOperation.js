import makeStyles from "@material-ui/core/styles/makeStyles";
import React from "react";
import { SourceIcon } from "../../../../component/sourceicon/SourceIcon";
import { VscnComponent } from "./VscnComponent";

const useStyles = makeStyles({
  icon: {
    fontSize: "0.75em",
    "&:hover": {
      backgroundColor: "#ccc",
    },
  },
  operationComment: {
    fontSize: "1.5rem",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
    display: "flex",
    justifyContent: "center",
    border: "1px solid red",
  },
  operationText: {
    paddingTop: "0.5em",
    paddingBottom: "0.5em",
  },
  centered: {
    display: "flex",
    whiteSpace: "nowrap",
  },
  deleted: {
    textDecoration: "line-through",
  },
  leftMargin: {
    marginLeft: "10px",
  },
});

export const VscnOperation = ({
  vyper,
  build,
  options,
  operation,
  onEditComponentValue,
  onSelectDiagramApproval,
}) => {
  const classes = useStyles();

  // used to track the component, and determine the priority
  const priorityMap = {};

  return (
    <div>
      <br />

      <SourceIcon
        disabled={operation.engineeringDeleted}
        source={operation.source}
        heading="The operation was added by"
      />

      <span className={classes.leftMargin}>{operation.name}</span>

      <br />

      {operation.components.map((component, n) => {
        let priority;
        if (component.name in priorityMap) {
          priorityMap[component.name]++;
          priority = priorityMap[component.name];
        } else {
          priorityMap[component.name] = 1;
          priority = 1;
        }

        return (
          <VscnComponent
            key={n}
            vyper={vyper}
            build={build}
            disabled={false}
            operation={operation}
            component={component}
            options={options}
            onEditValue={(o, c) => onEditComponentValue(o, c, n)}
            priority={priority}
            onSelectDiagramApproval={onSelectDiagramApproval}
          />
        );
      })}
    </div>
  );
};
