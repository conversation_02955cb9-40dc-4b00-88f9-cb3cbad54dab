import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import {
  hasFacility,
  hasMaterial,
  hasPackageNiche,
} from "src/pages/vyper/FormStatus";
import { Link } from "react-router-dom";
import { DataCell } from "src/component/datacell/DataCell";
import { convertBuildNumbertoVyperNumber } from "src/component/helper/convertBuildNumbertoVyperNumber";

const useStyles = makeStyles(() => ({
  link: {
    textDecoration: "none",
    color: "rgb(85, 26, 139)",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
      color: "red",
    },
  },
}));

export const FlowCell = ({ build, flow }) => {
  if (!hasMaterial(build) || !hasFacility(build) || !hasPackageNiche(build))
    return null;

  const classes = useStyles();

  const buildNumber = build?.buildNumber;
  const vyperNumber = convertBuildNumbertoVyperNumber(buildNumber);

  return (
    <DataCell source={flow.source}>
      <Link
        className={classes.link}
        to={`/projects/${vyperNumber}/builds/${buildNumber}/flow`}
      >
        {flow.object.name}
      </Link>
    </DataCell>
  );
};
