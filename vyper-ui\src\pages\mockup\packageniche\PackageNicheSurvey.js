import { Card, CardMedia } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext, useEffect } from "react";
import { AuthContext } from "src/component/common";
import buildEnvironment from "src/buildEnvironment";

const useStyles = makeStyles((theme) => ({
  iframeContainer: {
    width: "90vw",
    height: "85vh",
    border: "none",
    marginLeft: theme.spacing(3),
    marginRight: theme.spacing(3),
    marginBottom: theme.spacing(2),
  },
  iframeCard: {
    height: "100%",
    width: "100%",
  },
}));

const PackageNicheSurvey = ({ onPkgNicheSelected, pkgGroup }) => {
  const { authUser } = useContext(AuthContext);

  const classes = useStyles();
  let pkgNicheSurveyURL = buildEnvironment.pkgNicheSurveyURL;
  if (pkgGroup) {
    pkgNicheSurveyURL += "&pkgGroup=" + pkgGroup;
  }
  pkgNicheSurveyURL += "&contextUser=" + authUser.uid;

  useEffect(() => {
    window.addEventListener(
      "message",
      function (event) {
        if (event.origin !== "https://pkgapp.itg.ti.com") return;
        onPkgNicheSelected(event.data?.pkgNicheDetails[0]?.pkgNiche);
      },
      false
    );
  }, []);

  return (
    <>
      <Card fullwidth={1} className={classes.iframeContainer} id="iframeCard">
        <CardMedia
          component="iframe"
          src={pkgNicheSurveyURL}
          className={classes.iframeCard}
        />
      </Card>
    </>
  );
};

export default PackageNicheSurvey;
