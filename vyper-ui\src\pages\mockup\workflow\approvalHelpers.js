import {
  fetchTaskAssignments,
  updateTaskAssignment,
} from "../../../component/api/taskService2";

export const TASK_UPDATE_APPROVE = "Approve";
export const TASK_UPDATE_REWORK = "Rework";

/**
 * @namespace ApprovalHelper
 */

/**
 * Retrieve the list of tasks from task service
 * @param {string} buildNumber - the build number
 * @param {UseAuth~AuthUser} authUser - the current user
 * @param {function} setTasks - the function to call, with the assignment task records
 * @returns {Promise<TaskService~TaskAssignmentResponse>}
 */
export function retrieveTaskApprovals(buildNumber, authUser, setTasks) {
  // exit if we don't have a current user
  if (authUser.uid === "") {
    return Promise.resolve([]);
  }

  return fetchTaskAssignments(
    `Vyper Approval Flow~buildNumber~${buildNumber}`
  ).then((taskAssignments) => {
    setTasks(taskAssignments.value || []);
    return taskAssignments;
  });
}

/**
 * @typedef {object} ApprovalHelper~ProcessTasks
 * @property {TaskService~TaskAssignment[]} taskAssignments - the user's unapproved task assignment records.
 * @property {string|null} users - the current userid if they can approve, else null.
 * @property {ApprovalHelper~GroupObject[]} approvedGroups - the groups that have approved the build.
 * @property {string[]} unApprovedGroups - the groups that have not approved the build.
 * @property {ApprovalHelper~GroupObject[]} unApprovedGroupsAsObjects - the groups that have not approved the build
 * @property {string[]} allGroups - all of the groups assigned to the build
 * @property {boolean} isApprover - true if the current user can approve this build
 * @property {boolean} isFrozen
 */

/**
 * process the Task Assignment records
 * @param {TaskService~TaskAssignment[]} tasks - the task assignment records
 * @param {UseAuth~AuthUser} authUser - the current user
 * @returns {ApprovalHelper~ProcessTasks}
 */
export function processTasks(tasks, authUser) {
  const taskAssignments = determineTaskAssignments(tasks, authUser);
  const users = determineUsers(taskAssignments);
  const unApprovedGroups = determineUnapprovedGroups(taskAssignments);
  const isFrozen = determineIsFrozen(unApprovedGroups);
  const unApprovedGroupsAsObjects =
    determineUnapprovedGroupsAsObjects(unApprovedGroups);
  const isApprover = determineIsApprover(authUser, users);
  const approvedGroups = determineApprovedGroups(tasks);
  const allGroups = determineAllGroups(tasks, authUser);

  return {
    taskAssignments,
    users,
    approvedGroups,
    unApprovedGroups,
    unApprovedGroupsAsObjects,
    allGroups,
    isApprover,
    isFrozen,
  };
}

/**
 * filter the tasks down to the records that match the current user, the current iteration, and not approved
 * @param {TaskService~TaskAssignment[]} tasks - task assignment records for current user that are not approved
 * @param {UseAuth~AuthUser} authUser - the current user
 * @return {TaskService~TaskAssignment[]}
 */
function determineTaskAssignments(tasks, authUser) {
  // noinspection JSValidateTypes
  return tasks
    .filter((task) => task.current)
    .filter((task) => !task.complete)
    .filter((task) => !!task.availBranchNames)
    .filter((task) => task.userId === authUser.uid.toLowerCase());
}

/**
 * get the unique list of groups that haven't approved yet
 * @param {TaskService~TaskAssignment[]} taskAssignments - all of the task assignment records
 * @return {string[]}
 */
function determineUnapprovedGroups(taskAssignments) {
  return [...new Set(taskAssignments.map((task) => task.fnctName))];
}

/**
 * determine if the current build is frozen (the current user has no groups to approve)
 * @param {string[]} unApprovedGroups - the unapproved groups
 * @type {boolean}
 */
function determineIsFrozen(unApprovedGroups) {
  return unApprovedGroups.length === 0;
}

/**
 * @typedef ApprovalHelper~GroupObject
 * @property {string} groupName - the name of the group
 * @property {string} groupText - the text of the group
 */
/**
 * Return the unapproved groups as objects
 *
 * @param {string[]} unApprovedGroups - the list of unapproved groups
 * @returns {ApprovalHelper~GroupObject[]}
 */
function determineUnapprovedGroupsAsObjects(unApprovedGroups) {
  return unApprovedGroups.map((group) => ({
    groupName: group,
    groupText: group,
  }));
}

/**
 * return the current user id if they have not approved, and the group has not approved the build.
 * @param {TaskService~TaskAssignment[]} taskAssignments
 * @returns {string|null}
 */
function determineUsers(taskAssignments) {
  return taskAssignments?.[0]?.userId;
}

/**
 * determine if the current user approved this task
 * @param {UseAuth~AuthUser} authUser - the current user
 * @param {string|undefined} users - the user who approved the task, or undefined
 * @returns {boolean} - true if the current is an approver of this task
 */
function determineIsApprover(authUser, users) {
  return authUser.uid.toLowerCase() === users;
}

/**
 * get the list of groups that have approved
 * @param {TaskService~TaskAssignment[]} tasks - the list of tasks assignments for the build.
 * @return {ApprovalHelper~GroupObject[]} array of {group, username, date} of the approved, completed tasks.
 */
function determineApprovedGroups(tasks) {
  // noinspection JSValidateTypes
  return tasks
    .filter((task) => task.complete && task.current)
    .filter((task) => task.branchName.includes("at approve"))
    .map((task) => ({
      group: task.fnctName,
      username: task.userName,
      date: task.modifiedDttm,
    }));
}

/**
 * get the complete list of groups for the current user.
 * @param {TaskService~TaskAssignment[]} tasks - task assignment records for current user that are not approved
 * @param {UseAuth~AuthUser} authUser - the current user
 * @return {string[]} - array of group names.
 */
function determineAllGroups(tasks, authUser) {
  return [
    ...new Set(
      tasks
        .filter((task) => task.userId === authUser.uid.toLowerCase())
        .filter((task) => task.current)
        .map((task) => task.fnctName)
    ),
  ];
}

/**
 * Approve the task
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function approveTask(tasks, authUser, comment, group) {
  return approveOrRejectTheTask(
    tasks,
    authUser,
    TASK_UPDATE_APPROVE,
    comment,
    "",
    group
  );
}

/**
 * Reject the task
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} reason - The rejection reason, or null
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function rejectTask(tasks, authUser, comment, reason, group) {
  return approveOrRejectTheTask(
    tasks,
    authUser,
    TASK_UPDATE_REWORK,
    comment,
    reason,
    group
  );
}

/**
 * Update the current task (Approve or Rework)
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} action - Approve or Reject
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} reason - The rejection reason, or null
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function approveOrRejectTheTask(
  tasks,
  authUser,
  action,
  comment,
  reason,
  group
) {
  // find the record for the current user and group
  // get the taskUuid and asmtUuid
  const assignment = tasks.find(
    (assignment) =>
      assignment.fnctName === group &&
      assignment.userId.toLowerCase() === authUser.uid.toLowerCase() &&
      assignment.current &&
      !assignment.complete
  );

  const branchName = assignment.availBranchNames.find((branch) =>
    branch.toLowerCase().includes(action.toLowerCase())
  );

  if (!branchName) {
    throw new Error(`Branch not found! for action ${action}`);
  }

  // build the payload
  const taskChangesPayload = {
    branchName: branchName,
    commentText: JSON.stringify({
      reason: reason,
      comment: comment,
    }),
  };

  // update the task
  return updateTaskAssignment(
    assignment.taskUuid,
    assignment.asmtUuid,
    taskChangesPayload
  );
}
