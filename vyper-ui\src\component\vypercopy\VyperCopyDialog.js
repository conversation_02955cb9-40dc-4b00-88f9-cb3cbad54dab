/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState } from "react";
import TextField from "@material-ui/core/TextField";
import Button from "@material-ui/core/Button";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { FetchContext } from "../fetch/VyperFetch";
import Grid from "@material-ui/core/Grid";
import MenuItem from "@material-ui/core/MenuItem";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Dialog from "@material-ui/core/Dialog";
import { DialogActions } from "@material-ui/core";
import DialogContent from "@material-ui/core/DialogContent";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  autocomplete: {
    minWidth: "13rem",
  },
  dialog: {
    padding: "3rem",
    margin: "3rem",
  },
  grid: {
    marginRight: "2em",
    marginBottom: "1em",
  },
}));

export const VyperCopyDialog = ({ open, onClose, onSave, build }) => {
  const { vget } = useContext(FetchContext);

  const [facilities, setFacilities] = useState([]);
  const [materials, setMaterials] = useState([]);
  const [vyperBuilds, setVyperBuilds] = useState([]);
  const [facility, setFacility] = useState(build.atss.object.facilityAt);
  const [material, setMaterial] = useState(build.atss.object.material);
  const [srcBuildNumber, setSrcBuildNumber] = useState();
  const [disabled, setDisabled] = useState(true);

  const handleClose = () => {
    changeLevel("all");
    onClose();
  };

  const handleSave = () => onSave(srcBuildNumber);

  const changeLevel = (level) => {
    switch (level) {
      case "all":
        setFacilities([]);
        setMaterials([]);
        setVyperBuilds([]);
        break;

      case "facility":
        setMaterials([]);
        setVyperBuilds([]);
        break;

      case "material":
        setVyperBuilds([]);
        break;
    }
  };

  //On mount, get the list of facilities
  useEffect(() => {
    vget(`/vyper/v1/vyper/findAllDistinctFacilities`, setFacilities);
  }, []);

  // If list of facilities have changed, try to select the default facility.
  // In this case, the default facility is the current build's facility.
  useEffect(() => {
    if (facilities.length === 0 || build == null) return;

    facilities.map((facility) => {
      if (facility === build?.facility?.object?.PDBFacility) {
        setFacility(facility);
      }
    });
  }, [facilities, build]);

  // Whenever facility changes, retrieve the list of materials.
  useEffect(() => {
    if (!facility) return;
    changeLevel("facility");
    vget(
      `/vyper/v1/vyper/findAllDistinctMaterialsByFacilty?facility=${encodeURIComponent(
        facility
      )}`,
      setMaterials
    );
  }, [facility]);

  // Whenever the list of materials changes, select our material if it's in the list.
  useEffect(() => {
    if (materials.length === 0 || build == null) return;
    materials.map((material) => {
      if (material === build?.material?.object?.Material) {
        setMaterial(material);
      }
    });
  }, [materials, build]);

  // When material or facility changes, retrieve list of builds.
  useEffect(() => {
    if (facility == null || material == null) return;
    changeLevel("material");
    const encURLParams = `facility=${encodeURIComponent(
      facility
    )}&material=${encodeURIComponent(material)}&flow=${encodeURIComponent(
      build?.buildFlow?.flowName
    )}`;
    vget(
      `/vyper/v1/vyper/findAllVyperBuildsByMaterialAndFacility?${encURLParams}`,
      setVyperBuilds
    );
  }, [material, facility]);

  // Whenever the list of builds changes, select our build if it's in the list.
  useEffect(() => {
    if (vyperBuilds.length === 0 || build == null) return;
    vyperBuilds.map((vyperBuild) => {
      if (vyperBuild === build?.buildNumber) {
        setSrcBuildNumber(vyperBuild);
      }
    });
  }, [vyperBuilds, build]);

  // If no facility, or material, or build selected, disable the copy button.
  useEffect(() => {
    setDisabled(facility == null || material == null || srcBuildNumber == null);
  }, [facility, material, srcBuildNumber]);

  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg">
      <DialogTitle className={classes.title}>Copy From Vyper</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent className={classes.form}>
        <Grid container>
          <Grid item xs={6} className={classes.grid}>
            <TextField
              variant="outlined"
              fullWidth
              select
              label="Facility"
              value={facility || ""}
              onChange={(e) => setFacility(e.target.value)}
            >
              {facilities.map((facility2) => (
                <MenuItem
                  key={facility2}
                  value={facility2}
                  selected={facility2 === facility}
                >
                  {facility2}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={6} className={classes.grid}>
            <TextField
              variant="outlined"
              fullWidth
              select
              label="Device"
              value={material || ""}
              onChange={(e) => setMaterial(e.target.value)}
            >
              {materials.map((material2) => (
                <MenuItem
                  key={material2}
                  value={material2}
                  selected={material2 === material}
                >
                  {material2}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={6} className={classes.grid}>
            <TextField
              variant="outlined"
              fullWidth
              select
              label="Vyper Build to copy from"
              value={srcBuildNumber || ""}
              onChange={(e) => setSrcBuildNumber(e.target.value)}
            >
              {vyperBuilds
                .filter((b) => build?.buildNumber === b)
                .map((build_number) => (
                  <MenuItem
                    key={build_number}
                    value={build_number}
                    selected={srcBuildNumber === build_number}
                  >
                    {build_number}
                  </MenuItem>
                ))}
            </TextField>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleSave}
          color="primary"
          variant="contained"
          disabled={disabled}
        >
          Copy
        </Button>
      </DialogActions>
    </Dialog>
  );
};
