import { Checkbox, FormControlLabel } from "@material-ui/core";
import React from "react";
import PropTypes from "prop-types";

/**
 * Display the PIM Setup cell for a single item
 * @param {*} item - The item to display
 * @param {PimCell~PimSetup} pimSetup - The PIM setup object
 * @param {PimCell~onChangeValidated} onChangeValidated - user changed the validated state
 * @return {JSX.Element}
 * @constructor
 */
export function PimValidation({ item, pimSetup, onChangeValidated }) {
  function handleChangeValidated(e) {
    onChangeValidated(item, e.target.checked);
  }

  let label;
  if (pimSetup.validated.state) {
    label =
      "Validated by " +
      pimSetup.validated.user.username +
      "/" +
      pimSetup.validated.user.userid;
  } else {
    label = "Check to validate PIM setup is complete";
  }

  return (
    <div>
      <FormControlLabel
        control={
          <Checkbox
            checked={pimSetup.validated.state}
            onChange={handleChangeValidated}
            name="checkedA"
          />
        }
        label={label}
      />
    </div>
  );
}

PimValidation.propTypes = {
  item: PropTypes.any.isRequired,
  pimSetup: PropTypes.object.isRequired,
  onChangeValidated: PropTypes.func.isRequired,
};
