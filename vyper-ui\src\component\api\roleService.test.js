import {
  isBasicGroup,
  isDevelopmentTemplateAndNoTest,
  isScpAndHasEngineeringOperations,
  isTestGroup,
} from "./roleService";

describe("role service tests", () => {
  test("isBasicGroup", () => {
    expect(isBasicGroup("XXXXX_FMX_ASSY_BOND", "FMX")).toBe(false);
    expect(isBasicGroup("VYPER_XXX_ASSY_BOND", "FMX")).toBe(false);
    expect(isBasicGroup("VYPER_FMX_ASSY_TEST", "FMX")).toBe(false);
    expect(isBasicGroup("VYPER_FMX_ASSY_SCP", "FMX")).toBe(false);
    expect(isBasicGroup("VYPER_FMX_ASSY_BOND", "XXX")).toBe(false);

    expect(isBasicGroup("VYPER_FMX_ASSY_BOND", "FMX")).toBe(true);
  });

  test("isScpAndHasEngineeringOperations", () => {
    expect(isScpAndHasEngineeringOperations("XXXXX_FMX_SCP", "FMX", true)).toBe(
      false
    );
    expect(isScpAndHasEngineeringOperations("VYPER_XXX_SCP", "FMX", true)).toBe(
      false
    );
    expect(isScpAndHasEngineeringOperations("VYPER_FMX_XXX", "FMX", true)).toBe(
      false
    );
    expect(isScpAndHasEngineeringOperations("VYPER_FMX_SCP", "XXX", true)).toBe(
      false
    );
    expect(
      isScpAndHasEngineeringOperations("VYPER_FMX_SCP", "FMX", false)
    ).toBe(false);

    expect(isScpAndHasEngineeringOperations("VYPER_FMX_SCP", "FMX", true)).toBe(
      true
    );
  });

  test("isTestGroup", () => {
    expect(
      isTestGroup("XXXXX_FMX_TEST_ASC", "FMX", "TURNKEY", "ASC", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_XXX_TEST_ASC", "FMX", "TURNKEY", "ASC", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_FMX_XXXX_ASC", "FMX", "TURNKEY", "ASC", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_FMX_TEST_XXX", "FMX", "TURNKEY", "ASC", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_FMX_TEST_ASC", "XXX", "TURNKEY", "ASC", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_FMX_TEST_ASC", "FMX", "NON-TKY", "ASC", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_FMX_TEST_ASC", "FMX", "TURNKEY", "XXX", "LAMPS")
    ).toBe(false);
    expect(
      isTestGroup("VYPER_FMX_TEST_ASC", "FMX", "TURNKEY", "XXX", "XXXXX")
    ).toBe(false);

    expect(
      isTestGroup("VYPER_FMX_TEST_ASC", "FMX", "TURNKEY", "ASC", "XXXXX")
    ).toBe(true); // match on sbe only
    expect(
      isTestGroup("VYPER_FMX_TEST_ASC", "FMX", "TURNKEY", "ASC", "LAMPS")
    ).toBe(true); // match on sbe1
  });

  describe("SBE much match exactly", () => {
    test("partial match fails", () => {
      expect(
        isTestGroup("VYPER_FMX_TEST_BSR", "FMX", "TURNKEY", "ASC", "SR")
      ).toBe(false); // match on sbe1 exactly fails
    });

    test("exact match passes", () => {
      expect(
        isTestGroup("VYPER_FMX_TEST_SR", "FMX", "TURNKEY", "ASC", "SR")
      ).toBe(true); // match on sbe1 exactly
    });
  });

  test("isDevelopmentTemplateAndNoTest", () => {
    expect(isDevelopmentTemplateAndNoTest("XXXXX_FMX_SCP", "FMX", true)).toBe(
      false
    );
    expect(isDevelopmentTemplateAndNoTest("VYPER_XXX_SCP", "FMX", true)).toBe(
      false
    );
    expect(isDevelopmentTemplateAndNoTest("VYPER_FMX_XXX", "FMX", true)).toBe(
      false
    );
    expect(isDevelopmentTemplateAndNoTest("VYPER_FMX_SCP", "XXX", true)).toBe(
      false
    );
    expect(isDevelopmentTemplateAndNoTest("VYPER_FMX_SCP", "FMX", false)).toBe(
      false
    );

    expect(isDevelopmentTemplateAndNoTest("VYPER_FMX_SCP", "FMX", true)).toBe(
      true
    );
  });
});
