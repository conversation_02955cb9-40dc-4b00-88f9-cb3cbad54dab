import React from "react";
import { makeStyles } from "@material-ui/styles";
import { HorizontalTabs } from "src/component/tab/HorizontalTabs";
import { Exclusions } from "src/pages/admin/componentbasename/Exclusions";
import { BaseNames } from "src/pages/admin/componentbasename/BaseNames";

const useStyles = makeStyles({
  root: {},
});

export const ComponentBaseNameAdmin = () => {
  const classes = useStyles();

  const tabs = [
    {
      label: "Base Names",
      control: <BaseNames />,
    },
    {
      label: "Exclusions",
      control: <Exclusions />,
    },
  ];

  return (
    <div className={classes.root}>
      <h3>Component Base Names</h3>

      <HorizontalTabs storageKey="admin.componentbasename" tabs={tabs} />
    </div>
  );
};
