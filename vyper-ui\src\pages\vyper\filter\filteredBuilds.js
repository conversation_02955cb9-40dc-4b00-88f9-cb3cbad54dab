/**
 * Filter and sort the builds
 * @param builds Array of builds
 * @param currentFilters The filters
 * @returns {*[]} The processed array of builds
 */
export const filterTheBuilds = (builds, currentFilters) => {
  // if builds is invalid or empty, return empty list
  if (builds == null || currentFilters == null || builds.length === 0) {
    return [];
  }

  // make a copy of the builds
  let newBuilds = [...builds];

  // filter the build numbers
  // if there are any build numbers, then keep only those builds
  if (currentFilters.buildNumbers?.length > 0) {
    newBuilds = currentFilters.buildNumbers
      .map((o) => o.buildNumber)
      .map((buildNumber) =>
        newBuilds.find((b) => b.buildNumber === buildNumber)
      );
  }

  // filter the build type
  if (currentFilters.buildtype !== "All Buildtypes") {
    newBuilds = newBuilds.filter(
      (b) => b.buildtype === currentFilters.buildtype
    );
  }

  // filter the build state
  if (currentFilters.buildState !== "All Build States") {
    newBuilds = newBuilds.filter((b) => b.state === currentFilters.buildState);
  }

  // filter the facilities
  if (currentFilters.facility !== "All Facilities") {
    newBuilds = newBuilds.filter(
      (b) => b.facility.object?.PDBFacility === currentFilters.facility
    );
  }

  // filter the materials
  if (currentFilters.material !== "All Materials") {
    newBuilds = newBuilds.filter(
      (b) => b?.material?.object?.Material === currentFilters.material
    );
  }

  // update the order
  switch (currentFilters.order) {
    case "Newest -> Oldest":
      newBuilds = [...newBuilds].sort((a, b) => {
        if (a.buildNumber < b.buildNumber) return 1;
        else if (a.buildNumber > b.buildNumber) return -1;
        else return 0;
      });
      break;
    case "Oldest -> Newest":
      newBuilds = [...newBuilds].sort((a, b) => {
        if (a.buildNumber < b.buildNumber) return -1;
        else if (a.buildNumber > b.buildNumber) return 1;
        else return 0;
      });
      break;
  }

  return newBuilds;
};
