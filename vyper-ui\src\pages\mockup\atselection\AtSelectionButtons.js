import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import React from "react";

export const AtSelectionButtons = ({ onClose, onSave }) => {
  return (
    <DialogActions>
      <Button onClick={onClose} variant="outlined" color="primary">
        Cancel
      </Button>

      <Button onClick={onSave} color="primary" variant="contained">
        Save
      </Button>
    </DialogActions>
  );
};
