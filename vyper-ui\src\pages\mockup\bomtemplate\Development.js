import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles(() => ({
  development: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));
export const Development = ({ bomTemplate }) => {
  const classes = useStyles();

  if (!bomTemplate.object.isDevelopment) return null;

  return (
    <div className={classes.development}>*** DEVELOPMENT TEMPLATE ***</div>
  );
};
