import Grid from "@material-ui/core/Grid";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import { useHistory, useParams } from "react-router-dom";
import { DataModelsContext } from "src/DataModel";
import { filterPras } from "src/pages/vyper/filter/filteredPras";
import { PraRow } from "src/pages/vyper/pras/PraRow";
import { fetchAssignments } from "../../../component/api/taskService2";
import {
  ApprovalOperationContext,
  determineGroupFromApprovalOperation,
} from "../../../component/approvaloperation/ApprovalOperation";
import { logError } from "../../functions/logError";
import { AuditDialog2 } from "../../mockup/audit/AuditDialog2";
import { BuildNumberRow2 } from "../../mockup/build/BuildNumberRow2";
import { BuildCommentRow } from "../../mockup/comment/BuildCommentRow";
import { DescriptionRow } from "../../mockup/description/DescriptionRow";
import { FacilityRow } from "../../mockup/facility/FacilityRow";
import { MaterialRow } from "../../mockup/material/MaterialRow";
import { Owners } from "../../mockup/owners/Owners";
import ReferenceRow from "../../mockup/reference/ReferenceRow";
import { VyperHeader } from "../../mockup/vyper/VyperHeader";
import { PraWorkFlowRow } from "../../mockup/workflow/PraWorkflow/PraWorkFlowRow";
import VyperPraModeSwitch from "./VyperPraModeSwitch";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles((theme) => ({
  root: {},
  table: {
    width: "auto",
  },
  ProjectBox: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "stretch",
    padding: "0.5rem",
    marginBottom: "1rem",
    borderRadius: "1rem",
    paddingLeft: "1rem",
  },
  header: {
    marginTop: theme.spacing(3),
    fontWeight: "bold",
  },
  actions: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-evenly",
    alignItems: "flex-start",
  },
}));

const praHelpLink =
  "https://confluence.itg.ti.com/pages/viewpage.action?pageId=630627109";

/**
 * Display the list of PRA objects
 *
 * @return {JSX.Element|null}
 * @constructor
 */
export const VyperPraFormPage = () => {
  const { vyperNumber, praNumber } = useParams();

  const initialRender = useRef(true);
  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );
  const { vyper, vyperDao, praDao, vscnDao, auditDao } =
    useContext(DataModelsContext);

  const [pras, setPras] = useState([]);
  const [taskAssignments, setTaskAssignments] = useState([]);
  const [openAudit, setOpenAudit] = useState(false);
  const [auditTitle, setAuditTitle] = useState("");
  const [audits, setAudits] = useState([]);

  const history = useHistory();
  const classes = useStyles();

  let menuItems = [
    {
      id: "Refresh PRA Verifiers",
      title: "Refresh PRA Verifiers",
      enabled: true,
      internal: true,
    },
    {
      id: "Refresh Die Attributes",
      title: "Refresh Die Attributes",
      enabled: true,
    },
    {
      id: "Refresh MB Diagrams",
      title: "Refresh MB Diagrams",
      enabled: true,
    },
    {
      id: "VIEW_VSCN",
      title: "View VSCNs",
      enabled: true,
    },
    {
      id: "ADD_VSCN",
      title: "Add VSCN",
      enabled: true,
    },
    {
      id: "AUDIT",
      title: "Audit",
      enabled: true,
      internal: true,
    },
    {
      id: "TRAVELER",
      title: "Show Traveler",
      enabled: true,
      internal: true,
    },
  ];

  if (externalUse) {
    menuItems = menuItems.filter((menuItem) => menuItem.internal);
  }

  /**
   * When the PRA changes, update the list of filtered pras
   * @type {unknown}
   */
  const filteredPras = useMemo(() => {
    return filterPras(pras, {}) || [];
  }, [pras]);

  /**
   * ?????
   * @type {unknown}
   */
  const reqPrasCheckedGroups = useMemo(() => {
    if (!filteredPras) {
      return [];
    }
    let reqPrasCheckedGroups = [];
    filteredPras.forEach((pra) => {
      const reqCheckedGroups = {};
      Object.keys(pra.validatedComponents).forEach((component) => {
        const operation = pra.validatedComponents[component].parentOperation;
        const ao = findApprovalOperationByOperation(operation);
        const facility = pra.facility.object.PDBFacility;
        const group = determineGroupFromApprovalOperation(ao, facility);
        if (group == null) {
          return;
        }
        if (group in reqCheckedGroups) {
          reqCheckedGroups[group].push(component);
        } else {
          reqCheckedGroups[group] = [component];
        }
      });
      reqPrasCheckedGroups.push(reqCheckedGroups);
    });
    return reqPrasCheckedGroups;
  }, [filteredPras, findApprovalOperationByOperation]);

  /**
   * Fetch the tasks when the filtered pras change.
   */
  useEffect(() => {
    //doest run on initial render only on filtered pra changes
    if (initialRender.current) {
      initialRender.current = false;
      return;
    }
    if (filteredPras.length === 0) {
      return;
    }
    //Returns task service OBJ {@count:number, value:[taskAssignments]}
    fetchAssignments(`&taskName=${filteredPras[0].praNumber.split("-")[0]}*`)
      .then((assignments) => {
        if (!assignments?.value) {
          return [];
        }
        return assignments?.value;
      })
      .then(setTaskAssignments);
  }, [filteredPras]);

  /**
   * When the vyper number changes, load the pras
   */
  useEffect(() => {
    reloadPra();
  }, [vyperNumber]);

  /**
   * Load either a single PRA, or a list of PRAS by vyper number
   */
  function reloadPra() {
    if (praNumber == null) {
      praDao
        .findAllByVyperNumber(vyperNumber)
        .then((json) => setPras(json))
        .catch(logError);
    } else {
      praDao
        .findByPraNumber(praNumber)
        .then((json) => setPras([json]))
        .catch(logError);
    }
  }

  /**
   * The user changed the title of the Vyper
   * @param {string} title - The new title
   * @return {*}
   */
  function handleChangeTitle(title) {
    return vyperDao.updateTitle(vyper.vyperNumber, title).catch(logError);
  }

  /**
   * Return the configuration object for the build number row
   * @param {Pra} pra - The vscn object
   * @return {BuildNumberCell2~config}
   */
  function handleGetConfigBuildNumberRow(pra) {
    return {
      number: pra.praNumber,
      state: pra.state,
      menuItems,
    };
  }

  /**
   * User clicked a menu item.
   * @param {Pra} pra - The pra object
   * @param {string} action - The id of the clicked menu item
   */
  function handleClickBuildNumberRowMenu(pra, action) {
    switch (action.toUpperCase()) {
      case "REFRESH PRA VERIFIERS":
        handleRefreshPraVerifiers(pra.vyperNumber, pra.praNumber);
        break;

      case "REFRESH DIE ATTRIBUTES":
        handleClickRefreshDieAttributes(pra.vyperNumber, pra.praNumber);
        break;

      case "REFRESH MB DIAGRAMS":
        handleClickRefreshMbDiagrams(pra.vyperNumber, pra.praNumber);
        break;

      case "ADD_VSCN":
        handleClickAddVscn(pra);
        break;

      case "VIEW_VSCN":
        handleClickViewVscns(pra);
        break;

      case "AUDIT":
        showAuditDialog(pra);
        break;

      case "TRAVELER":
        history.push(
          `/traveler/${pra.praNumber}/?type=PRA&variant=VYPER&hideForm`
        );
        break;
    }
  }

  /**
   * User clicked the refresh pra verifiers item.
   * @param {string} vyperNumber - The vyper number
   * @param {string} praNumber - The pra number
   */
  function handleRefreshPraVerifiers(vyperNumber, praNumber) {
    praDao
      .refreshPra(vyperNumber, praNumber)
      .then((pra) => handleChangePra(pra))
      .catch(logError);
  }

  /**
   * User clicked the refresh die attributes item.
   * @param {string} vyperNumber - The vyper number
   * @param {string} praNumber - The pra number
   */
  function handleClickRefreshDieAttributes(vyperNumber, praNumber) {
    praDao
      .refreshPraDieAttributes(vyperNumber, praNumber)
      .then((pra) => handleChangePra(pra))
      .catch(logError);
  }

  /**
   * User clicked the refresh MB diagrams item.
   * @param {string} vyperNumber - The vyper number
   * @param {string} praNumber - The pra number
   */
  function handleClickRefreshMbDiagrams(vyperNumber, praNumber) {
    praDao
      .refreshPraMbDiagrams(vyperNumber, praNumber)
      .then((pra) => handleChangePra(pra))
      .catch(logError);
  }

  /**
   * User clicked the view VSCNs item.
   * @param {Pra} pra - The pra object
   */
  function handleClickViewVscns(pra) {
    history.push(`/projects/${vyper.vyperNumber}/vscns/${pra.praNumber}`);
  }

  /**
   * User clicked the add VSCN item.
   * @param {Pra} pra - The pra object
   */
  function handleClickAddVscn(pra) {
    vscnDao
      .create(pra.vyperNumber, pra.buildNumber, pra.praNumber)
      .then((vscn) =>
        history.push(`/projects/${vyper.vyperNumber}/vscns/${vscn.vscnNumber}`)
      )
      .catch(logError);
  }

  /**
   * Display the audit dialog. fetch the audits, then sort in reverse order
   * @param {Pra} pra - The vscn object
   */
  function showAuditDialog(pra) {
    auditDao
      .findAllByPraNumber(pra.praNumber)
      .then((audits) => {
        setAudits(audits);
        setAuditTitle(`Audit Log: ${pra.praNumber}`);
        setOpenAudit(true);
      })
      .catch(logError);
  }

  /**
   * Close the audit dialog
   */
  function handleCloseAuditDialog() {
    setAudits([]);
    setAuditTitle("");
    setOpenAudit(false);
  }

  /**
   * the user changes the description of the PRA
   * @param {string }description - the new description
   * @param {Pra} pra - The PRA object
   * @return {Promise<T>}
   */
  const handleDescriptionChange = (description, pra) => {
    praDao
      .changeDescription(vyper.vyperNumber, pra.praNumber, description)
      .then(handleChangePra)
      .catch(logError);
  };

  function handleReferenceClick(pra) {
    let path = `/vyper/projects/${pra.vyperNumber}/builds/${pra.buildNumber}`;
    // Open Link on new tab
    window.open(path);
  }

  function handleAddComment(comment, pra) {
    return praDao
      .addComment(
        vyper.vyperNumber,
        pra.praNumber,
        comment.who.username,
        comment.who.userid,
        comment.when,
        comment.text,
        comment.operation
      )
      .then(handleChangePra)
      .catch(logError);
  }

  function handleChangePra(updatedPra) {
    const pos = pras.findIndex((pra) => pra.praNumber === updatedPra.praNumber);

    if (-1 !== pos) {
      setPras([...pras.slice(0, pos), updatedPra, ...pras.slice(pos + 1)]);
    }
    return reloadPra();
  }

  function handleDeletePra(deletedPra) {
    setPras(pras.filter((pra) => pra.praNumber !== deletedPra.praNumber));
  }

  if (vyper == null || pras == null) {
    return null;
  }

  const showSameness = false;

  // determine the components to display
  // and sort them in the correct order

  const order = {
    Die: 10,
    Leadframe: 20,
    "MB Diagram": 30,
    "Mount Compound": 40,
    Wire: 50,
    "Mold Compound": 60,
  };

  const componentNames = pras
    .flatMap((pra) => pra.components)
    .map((component) => component.name)
    .filter((item, index, items) => items.indexOf(item) === index)
    .sort((a, b) => {
      let index1 = order[a] || 999;
      let index2 = order[b] || 999;

      if (index1 < index2) {
        return -1;
      } else if (index1 > index2) {
        return 1;
      } else {
        return 0;
      }
    });

  return (
    <div className={classes.root}>
      <h1>
        <VyperHeader
          vyper={vyper}
          onChangeTitle={handleChangeTitle}
          helpLink={praHelpLink}
        />
      </h1>

      <Grid container className={classes.ProjectBox + " header-box"}>
        <Grid item xs={4}>
          <div className={classes.header}>Owners</div>
          <Owners />
        </Grid>
        <Grid item xs={8}>
          <div className={classes.actions}>
            <div>
              <VyperPraModeSwitch praMode={true} vyperNumber={vyperNumber} />
            </div>
          </div>
        </Grid>
      </Grid>

      {filteredPras && (
        <Table className={classes.table} size="small">
          <TableBody>
            <BuildNumberRow2
              items={filteredPras}
              help="pra"
              title="PRA Number"
              onGetConfig={handleGetConfigBuildNumberRow}
              onClickMenu={handleClickBuildNumberRowMenu}
            />

            <PraWorkFlowRow
              vyper={vyper}
              pras={filteredPras}
              onChangePra={handleChangePra}
              onDeletePra={handleDeletePra}
              reqPrasCheckedGroups={reqPrasCheckedGroups}
              taskAssignments={taskAssignments}
            />
            <ReferenceRow
              vyper={vyper}
              pras={filteredPras}
              onClick={handleReferenceClick}
            />
            <DescriptionRow
              vyper={vyper}
              builds={filteredPras}
              handleRowChange={handleDescriptionChange}
              showSameness={showSameness}
            />
            <BuildCommentRow
              vyper={vyper}
              builds={filteredPras}
              handleAddComment={handleAddComment}
              showSameness={showSameness}
            />
            <MaterialRow
              vyper={vyper}
              builds={filteredPras}
              onChange={handleChangePra}
              showSameness={showSameness}
            />
            <FacilityRow
              vyper={vyper}
              builds={filteredPras}
              onChange={handleChangePra}
              showSameness={showSameness}
            />
            {componentNames.map((name) => (
              <PraRow
                key={name}
                name={name}
                vyper={vyper}
                pras={filteredPras}
                onSave={handleChangePra}
                showSameness={showSameness}
                reqPrasCheckedGroups={reqPrasCheckedGroups}
                taskAssignments={taskAssignments}
              />
            ))}
          </TableBody>
        </Table>
      )}

      <AuditDialog2
        open={openAudit}
        audits={audits}
        title={auditTitle}
        onClose={handleCloseAuditDialog}
      />
    </div>
  );
};
