import React from "react";
import { TextField } from "@material-ui/core";
import { Autocomplete } from "@material-ui/lab";

export const SymbolName = ({ name = "", onChange, options, buildType }) => {

  const isMinorChange = buildType === "Minor Change";

  return (
    <Autocomplete
      fullWidth
      value={name}
      onChange={(e, v) => !isMinorChange && onChange(v)}
      options={!name ? options : [...new Set([...options, name])]}
      handleHomeEndKeys={false}
      disableClearable={isMinorChange}
      disabled={isMinorChange}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          label="Symbol Name"
          error={!name || name.length === 0}
          InputLabelProps={{ shrink: true }}
          disabled={isMinorChange}
        />
      )}
    />
  );
};
