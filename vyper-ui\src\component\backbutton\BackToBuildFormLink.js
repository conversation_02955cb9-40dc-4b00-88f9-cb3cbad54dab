import React from "react";
import { NavLink } from "react-router-dom";
import makeStyles from "@material-ui/core/styles/makeStyles";
import config from "../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles({
  root: {
    marginBottom: "1rem",
    display: "flex",
    justifyContent: "space-between",
    position: "fixed",
    width: "95%",
    top: "48px",
    backgroundColor: "white",
    paddingTop: "20px",
    zIndex: 4,
  },
  active: {
    color: "red",
  },
});

export const BackToBuildFormLink = ({ vyperNumber, build }) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <NavLink
        exact
        to={`/projects/${vyperNumber}`}
        activeClassName={classes.active}
      >
        {vyperNumber}
      </NavLink>
      <NavLink
        exact
        to={`/projects/${vyperNumber}/builds/${build?.buildNumber}`}
        activeClassName={classes.active}
      >
        {build?.buildNumber}
      </NavLink>
      <NavLink
        to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/selection`}
        activeClassName={classes.active}
      >
        Selection
      </NavLink>
      {/*<NavLink to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/summary`} activeClassName={classes.active}>Summary</NavLink>*/}
      {/*<NavLink to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/traveler`} activeClassName={classes.active}>Traveler</NavLink>*/}
      <NavLink
        to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/plain`}
        activeClassName={classes.active}
      >
        Traveler
      </NavLink>
      {!externalUse && (
        <NavLink
          to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/bomtemplate`}
          activeClassName={classes.active}
        >
          Bill of Process Template
        </NavLink>
      )}
      <NavLink
        to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/flow`}
        activeClassName={classes.active}
      >
        Flow
      </NavLink>
      <NavLink
        to={`/projects/${vyperNumber}/builds/${build?.buildNumber}/approvalsHistory`}
        activeClassName={classes.active}
      >
        Approval History Analysis
      </NavLink>
    </div>
  );
};
