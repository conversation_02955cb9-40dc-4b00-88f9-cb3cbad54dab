import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Button from "@material-ui/core/Button";

const useStyles = makeStyles({
  header: {
    marginLeft: "1rem",
    marginRight: "1rem",
  },
  root: {
    marginLeft: "1rem",
    marginRight: "1rem",
    display: "flex",
    flexDirection: "column",
  },

  button: {
    marginTop: ".5rem",
  },
});

/**
 * Display a list of buttons
 *
 * @param actions
 * @param value
 * @returns {JSX.Element}
 * @constructor
 */
export const Actions = ({ actions, value }) => {
  const classes = useStyles();

  return (
    <div>
      <h3 className={classes.header}>Action</h3>

      <div className={classes.root}>
        {actions.map((action, n) => (
          <Button
            key={n}
            className={classes.button}
            variant="contained"
            color="primary"
            onClick={action.onClick}
            disabled={action.disabled}
          >
            {action.icon} {action.text.replaceAll("<<value>>", value || "")}
          </Button>
        ))}
      </div>
    </div>
  );
};
