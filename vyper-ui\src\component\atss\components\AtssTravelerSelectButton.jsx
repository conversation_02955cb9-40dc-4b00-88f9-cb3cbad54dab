import { Button } from "@material-ui/core";
import React from "react";

export const AtssTravelerSelectButton = ({
  specDevice = "",
  facilityAt = "",
  status = "",
  onClick,
}) => {
  const enabled = !!specDevice && !!facilityAt && !!status;
  return (
    <Button
      variant="contained"
      color="primary"
      disabled={!enabled}
      onClick={onClick}
    >
      Select
    </Button>
  );
};
