import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";

const ReferenceCell = ({ vyper, pra, onClick }) => {
  return (
    <DataCell source={"VYPER"}>
      <VyperLink onClick={() => onClick(pra)} canEdit={true}>
        {pra?.buildNumber || "click to select"}
      </VyperLink>
    </DataCell>
  );
};

export default ReferenceCell;
