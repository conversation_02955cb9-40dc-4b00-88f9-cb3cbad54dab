import React, { useEffect, useState } from "react";
import DataGrid from "../../../component/universal/DataGrid/DataGrid";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  root: {},
});

export const BaseNames = ({}) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    fetch(`/vyper/v1/basename/basenames`)
      .then((response) => response.json())
      .then((json) => setData(json));
  }, []);

  const classes = useStyles();

  const columns = [
    { field: "componentName", title: "Component Name" },
    { field: "baseName", title: "Base Name" },
  ];

  return (
    <div className={classes.root}>
      <p>
        Note: Component Names are grouped by Base Name, so that component values
        can be used if there isn't an exact name match. this data is stored in
        the component_base_name table.
      </p>

      <DataGrid title="Base Names" data={data} columns={columns} />
    </div>
  );
};
