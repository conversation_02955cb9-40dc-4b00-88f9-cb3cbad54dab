import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { Highlight } from "../../../component/highlight/Highlight";
import { HelperContext } from "src/component/helper/Helpers";

const useStyles = makeStyles({
  highlight: {
    marginTop: "1em",
    marginBottom: "1em",
    padding: 7,
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
  multiBuild: {
    color: "white",
    backgroundColor: "#118899",
    padding: "5px",
    borderRadius: "5px",
    marginTop: "5px",
    marginBottom: "5px",
  },
});

///////////////////////////////////////////////////////////////////////////////////////////////////

export const MaterialCell = ({ vyper, build, onClick }) => {
  const { canEditMaterial } = useContext(HelperContext);
  const canEdit = canEditMaterial(vyper, build);

  const isMcm = build.material?.object?.MCMChipCount > 1;

  return (
    <DataCell source={build.material?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        <Material
          material={build.material?.object?.Material}
          oldMaterial={build.material?.object?.OldMaterial}
          multiBuild={build.multiBuild?.multiBuild}
          specDevice={build.multiBuild?.specDevice}
        />
      </VyperLink>
      {isMcm ? (
        <Highlight>
          The device is MCM. Vyper does not fully support MCM devices.
        </Highlight>
      ) : null}
    </DataCell>
  );
};

///////////////////////////////////////////////////////////////////////////////////////////////////

const Material = ({ material, oldMaterial, multiBuild, specDevice }) => {
  const classes = useStyles();

  if (material == null) {
    return "click to select";
  }

  return (
    <span>
      {multiBuild ? (
        <span className={classes.multiBuild}>
          Multi-Build
          <br />
        </span>
      ) : null}
      Device: {material}
      <br />
      Spec Device: {specDevice || oldMaterial}
    </span>
  );
};
