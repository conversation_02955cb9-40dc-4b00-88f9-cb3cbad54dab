import React, { useEffect, useState } from "react";
import { NavLink } from "react-router-dom";
import PropTypes from "prop-types";
import { List, ListItem, ListItemIcon, ListItemText } from "@material-ui/core";
import ExpandLessIcon from "@material-ui/icons/ExpandLess";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { makeStyles } from "@material-ui/styles";
import { AppToolbarUtil } from "@ti/simba-common-util";

const useStyles = makeStyles((theme) => ({
  root: {},
  nested: {
    paddingLeft: theme.spacing(6),
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  item: {
    display: "flex",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  itemIcon: {
    minWidth: "32px",
    color: "inherit",
  },
  active: {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
    "& $icon": {
      color: theme.palette.primary.main,
    },
  },
}));

const SidebarNav = (props) => {
  const { className, ...rest } = props;

  const classes = useStyles();

  /**
   * @type {[import("@ti/simba-common-util").AppToolbarConfig]}
   */
  const [toolbarConfig, setToolbarConfig] = useState(
    AppToolbarUtil.getAppToolbarConfig()
  );
  const [menuOpen, setMenuOpen] = useState(null);

  useEffect(() => {
    const subscription =
      AppToolbarUtil.getAppToolbarConfig$().subscribe(setToolbarConfig);
    return () => subscription.unsubscribe();
  }, []);

  const handleMenu = (event) => {
    const target = event.currentTarget;
    if (target.ariaLabel) {
      // Toggle menu open/close
      setMenuOpen(menuOpen === target.ariaLabel ? null : target.ariaLabel);
    }
  };

  /**
   * Create a top-level list item
   * @param {import("@ti/simba-common-util").AppMenuItem} item
   */
  const createListItem = (item, className) => {
    const hasChildren = item.children && item.children.length > 0;
    if (hasChildren) {
      return (
        <>
          <ListItem
            button
            aria-label={item.label}
            key={item.label}
            onClick={handleMenu}
            className={classes.item}
          >
            {item.icon && (
              <ListItemIcon className={classes.itemIcon}>
                {item.icon}
              </ListItemIcon>
            )}
            <ListItemText
              primary={item.label}
              primaryTypographyProps={{ noWrap: true }}
            />
            {menuOpen === item.label ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </ListItem>
          {menuOpen === item.label && (
            <List component="div" disablePadding>
              {item.children.map((child) =>
                createListItem(child, classes.nested)
              )}
            </List>
          )}
        </>
      );
    } else {
      return (
        <ListItem
          button
          activeClassName={classes.active}
          aria-label={item.label}
          key={item.label}
          className={className || classes.item}
          component={item.href ? NavLink : undefined}
          to={item.href ? item.href : undefined}
          onClick={item.onClick ? item.onClick : undefined}
        >
          {item.icon && (
            <ListItemIcon className={classes.itemIcon}>
              {item.icon}
            </ListItemIcon>
          )}
          <ListItemText
            primary={item.label}
            primaryTypographyProps={{ noWrap: true }}
          />
        </ListItem>
      );
    }
  };

  return (
    <>
      {toolbarConfig && toolbarConfig.leftMenu && (
        <List>
          {toolbarConfig.leftMenu.map((item) => createListItem(item))}
        </List>
      )}
      {toolbarConfig && toolbarConfig.rightMenu && (
        <List>
          {toolbarConfig.rightMenu.map((item) => createListItem(item))}
        </List>
      )}
    </>
  );
};

SidebarNav.propTypes = {
  className: PropTypes.string,
};

export default SidebarNav;
