import React from "react";
import { OverrideAdminPanel } from "./override/OverrideAdminPanel";
import { PgsSandbox } from "./sandbox/pgs/PgsSandbox";
import { PackageNicheSandbox } from "./sandbox/pkgniche/PackageNicheSandbox";
import { BomTemplateSandbox } from "./sandbox/bomtemplate/BomTemplateSandbox";
import { ATApprovalAdminPage } from "./ATApprovalAdminPage";
import { ApprovalPanel } from "./approval/ApprovalPanel";
import { VerticalTabs } from "../../component/tab/VerticalTabs";
import { ProjectType } from "./projecttype/ProjectType";
import { AnnouncementAdmin } from "../../component/announcement/admin/AnnouncementAdmin";
import { ComponentMapPanel } from "src/pages/componentmap/ComponentMapPanel";
import { ComponentBaseNameAdmin } from "src/pages/admin/componentbasename/ComponentBaseNameAdmin";
import { TravelerNameMapPanel } from "./traveler-name-map/TravelerNameMapPanel";
import { AtssTravelerViewer } from "./traveler/AtssTravelerViewer";
import { ExtApprovalAdminPage } from "./ExtApprovalAdminPage";
import { DeviceFlowsMapPanel } from "./flow/DeviceFlowsMapPanel";
import { FlowOperationsConfig } from "./flow/FlowOperationsConfig";
import { FlowPackConfig } from "./flow/FlowPackConfig";
import { WireMetalPanel } from "src/pages/admin/wiremetal/WireMetalPanel";
import { WaferSawMethodPanel } from "src/pages/admin/wafersawmethod/WaferSawMethodPanel";

export const AdminIndexPage = () => {
  const tabs = [
    {
      label: "PGS Sandbox",
      control: <PgsSandbox />,
    },
    {
      label: "Package Niche Sandbox",
      control: <PackageNicheSandbox />,
    },
    {
      label: "Bill of Process Template Sandbox",
      control: <BomTemplateSandbox />,
    },
    {
      label: "Component Map",
      control: <ComponentMapPanel />,
    },
    {
      label: "Approval Operations",
      control: <ApprovalPanel />,
    },
    {
      label: "AT Admin Approval Page",
      control: <ATApprovalAdminPage />,
    },
    {
      label: "External Approval Page",
      control: <ExtApprovalAdminPage />,
    },
    {
      label: "Project Types",
      control: <ProjectType />,
    },
    {
      label: "Manage Announcements",
      control: <AnnouncementAdmin />,
    },
    {
      label: "Component Base Name",
      control: <ComponentBaseNameAdmin />,
    },
    {
      label: "Overrides",
      control: <OverrideAdminPanel />,
    },
    {
      label: "ATSS Traveler",
      control: <AtssTravelerViewer />,
    },
    {
      label: "Traveler Name Map",
      control: <TravelerNameMapPanel />,
    },
    {
      label: "Manage Device Flows",
      control: <DeviceFlowsMapPanel />,
    },
    {
      label: "Manage Flow Rows",
      control: <FlowOperationsConfig />,
    },
    {
      label: "Manage Flow PackConfigs",
      control: <FlowPackConfig />,
    },
    {
      label: "Wire Metal Map",
      control: <WireMetalPanel />,
    },
    {
      label: "Wafer Saw Methods",
      control: <WaferSawMethodPanel />,
    },
  ];

  return (
    <div>
      <VerticalTabs storageKey="admin.tab" tabs={tabs} />
    </div>
  );
};
