import TableFooter from "@material-ui/core/TableFooter";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import Button from "@material-ui/core/Button";
import React from "react";

export const DieFooter = ({ onAddDie }) => {
  return (
    <TableFooter>
      <TableRow hover>
        <TableCell>
          <Button variant="contained" color="primary" onClick={onAddDie}>
            + Die
          </Button>
        </TableCell>
      </TableRow>
    </TableFooter>
  );
};
