import React from "react";
import HeaderItem from "./HeaderItem";
import { leftPad, rightPad } from "../traveler/Padding";

const HeaderRow = ({ name1, value1, name2, value2 }) => {
  return (
    <>
      <HeaderItem
        name={leftPad(name1, 20)}
        value={rightPad(value1 || "", 33)}
      />
      {name2 == null ? null : (
        <HeaderItem name={leftPad(name2, 20)} value={value2 || ""} />
      )}
      <span>{"\n"}</span>
    </>
  );
};

export default HeaderRow;
