import PropTypes from "prop-types";
import React from "react";

/**
 * returns the paragraph text rows for the traveler
 * @param {string} paragraph - The paragraph text, with each line separated with an "\n"
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerParagraph({ paragraph }) {
  return (
    <>
      {paragraph.split("\n").map((line, n) => (
        <span key={n}>
          {"".padStart(5, " ") + line}
          <br />
        </span>
      ))}
    </>
  );
}

TravelerParagraph.propTypes = {
  paragraph: PropTypes.string.isRequired,
};
