import React, { useState, useEffect } from "react";
import FormSelect from "./Layouts/FormSelect";
import { useParams } from "react-router-dom";

import "../../../lib/TiAgGrid.css";
import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS
import SimpleSubmit from "./SimpleSubmit";

import { BASE_FETCH_DATA_URL, BASE_POST_DATA_URL } from "./FormConstants";

const postData = (data, callBack) => {
  fetch(`${BASE_POST_DATA_URL}/save`, {
    headers: {
      "Content-Type": "application/json",
    },
    method: "POST",
    body: JSON.stringify(data),
  }).then(callBack);
};

const CreateForm = () => {
  const { vbuildID } = useParams();
  const [defaultValues, setDefaultValues] = useState({});

  const [isLoading, setIsLoading] = useState(false);
  const [isValidID, setIsValidID] = useState(true);
  const [isFinalApproved, setIsFinalApproved] = useState(true);

  const fetchVBuild = (vbuildID) => {
    setIsLoading(true);
    setIsValidID(false);
    setIsFinalApproved(false);

    fetch(
      `${BASE_FETCH_DATA_URL}/fetchData/${vbuildID}?attributesToFetch=dieInfo,deviceInfo,generalInfo,traveler,bomData`
    )
      .then((response) => response.json())
      .then(setDefaultValues)
      .catch(() => {
        setDefaultValues({});
      });
  };

  useEffect(() => {
    if (vbuildID) {
      fetchVBuild(vbuildID);
    }
  }, []);

  useEffect(() => {
    if (defaultValues && defaultValues !== undefined) {
      setIsLoading(false);
      if (!defaultValues.validVBuild) {
        return;
      }
      setIsValidID(true);
      if (defaultValues.state.toUpperCase() !== "FINAL_APPROVED") {
        return;
      }
      setIsFinalApproved(true);
    }
  }, [defaultValues]);

  if (
    Object.keys(defaultValues).length !== 0 &&
    !isLoading &&
    isValidID &&
    isFinalApproved
  ) {
    return <FormSelect defaultValues={defaultValues} saveData={postData} />;
  }
  return (
    <>
      {isLoading && <strong>Loading...</strong>}
      {!isValidID && <strong>Vyper Build is not a valid ID.</strong>}
      {isValidID && !isFinalApproved && (
        <strong>Vyper Build is not currently in Final Approved state.</strong>
      )}
      {!vbuildID && (
        <SimpleSubmit label={"VBuild ID"} handleSubmit={fetchVBuild} />
      )}
    </>
  );
};

export default CreateForm;
