import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

export class EslDao extends DaoBase {
  constructor(params) {
    super({ name: "EslDao", url: "/vyper/v1/esl", ...params });
    this.esls = params.esls || [];
    this.setEsls = params.setEsls || noop;
  }

  list() {
    if (this.esls?.length > 0) {
      return Promise.resolve(this.esls);
    } else {
      return this.handleFetch("list", `/search?size=1000`, "GET").then(
        (json) => {
          this.setEsls(json.page.content);
          return this.esls;
        }
      );
    }
  }
}
