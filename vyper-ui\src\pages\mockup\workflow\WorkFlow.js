import React, { useContext, useEffect, useState } from "react";
import { PollForStateChanged } from "src/component/PollForStateChanged";
import { SpinnerContext } from "src/component/Spinner";
import { DataModelsContext } from "src/DataModel";
import { ApprovalOperationContext } from "../../../component/approvaloperation/ApprovalOperation";
import { AuthContext } from "../../../component/common/auth";
import { logError } from "../../functions/logError";
import { ApprovalBlockersDialog } from "./ApprovalBlockersDialog";
import { ApprovalDialog } from "./ApprovalDialog";
import {
  approveTask,
  processTasks,
  retrieveTaskApprovals,
} from "./approvalHelpers";
import { determineAtApproveBlockers } from "./blockers";
import { ReworkDialog } from "./ReworkDialog";
import TaskStatusDialog from "./TaskStatusDialog";
import { WorkFlowButton } from "./WorkFlowButton";
import { submitBlockers } from "../../vyper/FormStatus";
import { ReworkBuDialog } from "src/pages/mockup/workflow/ReworkBuDialog";
import { ArmArcCheckerDialog } from "src/pages/mockup/armarc_checker/ArmArcCheckerDialog";
import { getMessages } from "src/pages/mockup/armarc_checker/armarcChecker";

// button labels
// needs to match com.ti.specteam.vyper.actions.ChangeWorkflowAction
const buttonSubmit = "Submit";
const buttonDelete = "Delete";
const buttonRework = "Rework";
const buttonBuRework = "BU Rework";
const buttonCancel = "Cancel";
const buttonAtApprove = "AT Approve";
const buttonBuApprove = "BU Approve";
const buttonRestore = "Restore";
const buttonShowStatus = "Show Status";

// build state
// needs to match com.ti.specteam.vyper.build.model.BuildState.java
const stateNew = "DRAFT";
const stateAtReviewChange = "AT_REVIEW_CHANGE";
const stateBuReviewChange = "BU_REVIEW_CHANGE";
const stateFinalApproved = "FINAL_APPROVED";
const stateRework = "REWORK";
const stateCanceled = "CANCELED";

export const WorkFlow = ({ build, vyper, onWorkflow, onReload, onChange }) => {
  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);
  const { vyperDao, buildDao } = useContext(DataModelsContext);
  const { authUser } = useContext(AuthContext);
  const [tasks, setTasks] = useState([]);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showBlockingDialog, setShowBlockingDialog] = useState(false);
  const [blockingReasons, setBlockingReasons] = useState([]);
  const [showReworkDialog, setShowReworkDialog] = useState(false);
  const [showReworkBuDialog, setShowReworkBuDialog] = useState(false);
  const [runStateChange, setRunStateChange] = useState(false);
  const [isTaskStatusDialogOpen, setIsTaskStatusDialogOpen] = useState(false);
  const [openChecker, setOpenChecker] = useState(false);

  const handleStateChanged = () => {
    onReload();
    setRunStateChange(false);
  };

  /**
   * If the build changes, get the approvals
   */
  useEffect(() => {
    if (authUser.uid === "") {
      return;
    }

    fetchTaskApprovals().catch(logError);
  }, [authUser, build]);

  /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

  const fetchTaskApprovals = () => {
    return retrieveTaskApprovals(build.buildNumber, authUser, setTasks);
  };

  const {
    unApprovedGroups,
    isFrozen,
    unApprovedGroupsAsObjects,
    users,
    isApprover,
  } = processTasks(tasks, authUser);

  /**
   * The user clicked the approve button in the options row
   */
  const handleClickApprove = () => setShowApprovalDialog(true);

  /**
   * The user clicked the close button in the approvals dialog
   */
  const handleApproveClose = () => setShowApprovalDialog(false);

  /**
   * The user clicked the approve button in the approval dialog.
   * @param {string} group - The group that is approving
   * @param {string} [comment] - The approval comment (optional)
   */
  function handleApproveBuild(group, comment) {
    setShowApprovalDialog(false);

    // should we block the approval?
    const blockers = determineAtApproveBlockers(
      group,
      isFrozen,
      unApprovedGroupsAsObjects,
      build,
      authUser,
      findApprovalOperationByOperation
    );

    // if we have blockers, show the dialog and exit
    if (blockers.length > 0) {
      setBlockingReasons(blockers);
      setShowBlockingDialog(true);
      return;
    }

    // update the task
    approveTask(tasks, authUser, comment, group)
      .then(() => {
        // refresh the build, vyper and tasks
        const promise1 = buildDao.notifyAtGroupApproved(
          vyper.vyperNumber,
          build.buildNumber,
          group
        );
        const promise2 = fetchTaskApprovals();
        const promise3 = vyperDao.findByVyperNumber(vyper.vyperNumber);
        return Promise.all([promise1, promise2, promise3]);
      })
      .then(([newBuild]) => {
        // use onChange to change the build. It makes sure the parent handles it correctly
        onChange(newBuild);
        return newBuild;
      })
      .then(() => {
        // poll for approval status changes
        setRunStateChange(true);
      })
      .catch(logError);
  }

  /**
   * User clicked the close button on the ApprovalBlockersDialog.
   */
  const handleCloseBlockingDialog = () => setShowBlockingDialog(false);

  /**
   * User clicked the rework button.
   */
  const handleClickRework = () => setShowReworkDialog(true);

  /**
   * User clicked the rework bu button.
   */
  const handleClickReworkBu = () => setShowReworkBuDialog(true);

  /**
   * The user clicked the close button in the approvals dialog
   */
  const handleReworkClose = () => setShowReworkDialog(false);

  /**
   * User clicked rework in the rework dialog.
   */
  const handleReworkBuild = (group, reason, comment) => {
    setShowReworkDialog(false);
    onWorkflow(buttonRework, vyper, build, {
      tasks,
      authUser,
      group,
      reason,
      comment,
    }).then(() => fetchTaskApprovals());
  };

  /**
   * user clicked rework in the bu rework dialog
   */
  const handleReworkBuBuild = (reason) => {
    setShowReworkBuDialog(false);
    onWorkflow(buttonBuRework, vyper, build, {
      tasks,
      authUser,
      group: null,
      reason,
      comment: reason,
    }).then(() => fetchTaskApprovals());
  };

  /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

  const handleOpenStatus = () => {
    setIsTaskStatusDialogOpen(true);
  };

  // determine which buttons to display
  const determineButtons = (state) => {
    const buttons = [];

    // isOwner = true if the current user an owner of the vyper
    const isOwner = vyper.owners.some(
      (usr) => usr.userid === authUser.uid.toLowerCase()
    );

    switch (state) {
      case stateNew:
      case "NEW":
        // current user must be an owner
        if (isOwner) {
          buttons.push(buttonSubmit);
          buttons.push(buttonDelete);
        }

        break;

      case stateAtReviewChange:
        if (isApprover) {
          if (!isFrozen) {
            buttons.push(buttonAtApprove);
            buttons.push(buttonRework);
            buttons.push(buttonShowStatus);
          } else {
            buttons.push(buttonShowStatus);
          }
        } else {
          buttons.push(buttonShowStatus);
        }
        break;

      case stateBuReviewChange:
        const currentUserIsAnOwner = vyper.owners.some(
          (owner) => owner.userid === authUser.uid.toLowerCase()
        );
        if (currentUserIsAnOwner) {
          buttons.push(buttonBuApprove);
          buttons.push(buttonRework);
          buttons.push(buttonCancel);
        }
        break;

      case stateFinalApproved:
        break;

      case stateRework:
        buttons.push(buttonSubmit);
        buttons.push(buttonCancel);
        if (authUser.uid.toLowerCase() === users) {
          if (!isFrozen) {
            buttons.push(buttonAtApprove);
          }
        }
        break;

      case stateCanceled:
        buttons.push(buttonRestore);
        break;
    }

    return buttons;
  };

  const stateIsNew = build.state === stateNew || build.state === "NEW";

  const stateIsRework = build.state === stateRework;

  const stateIsAtReviewChange = build.state === stateAtReviewChange;

  const stateIsBuReviewChange = build.state === stateBuReviewChange;

  const stateIsCanceled = build.state === stateCanceled;

  const currentUserIsOwner = vyper.owners.some(
    (usr) => usr.userid === authUser.uid
  );

  // noinspection JSUnresolvedVariable
  const currentUserIsAt = authUser?.roles?.some((role) =>
    role.groupName?.includes(
      `_${build.facility.object.PDBFacility}_`.toUpperCase()
    )
  );

  ///////////////////////////////////////////////////////////////////////////////////
  // submit button

  const submitVisible = stateIsNew || stateIsRework;

  const handleSubmit = () => {
    const messages = getMessages(build);
    if (messages) {
      setOpenChecker(true);
    } else {
      handleSubmit2();
    }
  };

  const handleSubmit2 = () => {
    showSpinner();
    onWorkflow(buttonSubmit, vyper, build, {})
      .then(() => {
        return fetchTaskApprovals();
      })
      .finally(() => {
        hideSpinner();
      });
  };

  ///////////////////////////////////////////////////////////////////////////////////
  // delete button

  const deleteVisible = stateIsNew;

  const deleteBlockers = () => {
    const blockers = [];
    if (!stateIsNew) {
      blockers.push("State is not new.");
    }
    if (!currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }
    return blockers;
  };

  const handleDelete = () => {
    onWorkflow(buttonDelete, vyper, build, {});
  };

  ///////////////////////////////////////////////////////////////////////////////////
  // a/t approve button

  const currentUseIsAnApproverForTheTask = authUser.uid.toLowerCase() === users;

  const atApproveVisible =
    (stateIsAtReviewChange || stateIsRework) &&
    !isFrozen &&
    currentUseIsAnApproverForTheTask;

  const handleAtApprove = () => {
    if (isFrozen) {
      return;
    }
    handleClickApprove(() => {
      onWorkflow(buttonAtApprove, vyper, build, {});
      determineButtons(build.state);
      onReload();
    });
  };

  ///////////////////////////////////////////////////////////////////////////////////
  //reject button

  const reworkButton =
    ((build.state === "AT_REVIEW_CHANGE" || build.state === "REWORK") &&
      !isFrozen &&
      isApprover) ||
    build.state === "BU_REVIEW_CHANGE";

  const reworkBlockers = () => {
    const blockers = [];
    if (stateIsAtReviewChange && !(currentUserIsOwner || currentUserIsAt)) {
      blockers.push("You are not an owner, or a member of the A/T.");
    }
    if (stateIsBuReviewChange && !currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }
    return blockers;
  };

  ///////////////////////////////////////////////////////////////////////////////////
  // bu reject button

  const reworkBuButton = build.state === "AT_REVIEW_CHANGE";

  const reworkBuBlockers = () => {
    const blockers = [];
    if (stateIsBuReviewChange && !currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }
    return blockers;
  };

  ///////////////////////////////////////////////////////////////////////////////////
  // show status button

  const showStatusVisible = !stateIsNew;

  ///////////////////////////////////////////////////////////////////////////////////
  // cancel button

  const cancelVisible = stateIsBuReviewChange || stateIsRework;

  const cancelBlockers = () => {
    const blockers = [];
    if (!(stateIsBuReviewChange || stateIsRework)) {
      blockers.push("State is not B/U review change or rework.");
    }
    if (!currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }
    return blockers;
  };

  const handleCancel = () => {
    onWorkflow(buttonCancel, vyper, build, {});
  };

  ///////////////////////////////////////////////////////////////////////////////////
  // restore button

  const restoreVisible = stateIsCanceled;

  const restoreBlockers = () => {
    const blockers = [];
    if (!stateIsCanceled) {
      blockers.push("State is not canceled.");
    }
    if (!currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }
    return blockers;
  };

  const handleRestore = () => {
    onWorkflow(buttonRestore, vyper, build, {});
  };

  ///////////////////////////////////////////////////////////////////////////////////
  // bu approve button

  const buApproveVisible = stateIsBuReviewChange;

  const buApproveBlockers = () => {
    const blockers = [];
    if (!stateIsBuReviewChange) {
      blockers.push("State is not B/U Review Change.");
    }
    if (!currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }
    return blockers;
  };

  const handleBuApprove = () => {
    approveTask(tasks, authUser, "", "BU_APPROVERS").then(() => {
      onWorkflow(buttonBuApprove, vyper, build, {});
    });
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "flex-start",
        alignItems: "flex-start",
        flexWrap: "wrap",
        gap: "0.5em",
      }}
    >
      <TaskStatusDialog
        contextKey={`Vyper Approval Flow~buildNumber~${build.buildNumber}`}
        open={isTaskStatusDialogOpen}
        handleClose={() => setIsTaskStatusDialogOpen(false)}
      />
      <WorkFlowButton
        visible={submitVisible}
        blockers={submitBlockers(build, vyper, authUser)}
        value={buttonSubmit}
        onClick={handleSubmit}
      />
      <WorkFlowButton
        visible={deleteVisible}
        blockers={deleteBlockers()}
        value={buttonDelete}
        onClick={handleDelete}
      />
      <WorkFlowButton
        visible={atApproveVisible}
        blockers={[]}
        value={buttonAtApprove}
        onClick={handleAtApprove}
      />
      <WorkFlowButton
        visible={reworkButton}
        blockers={reworkBlockers()}
        value={buttonRework}
        onClick={handleClickRework}
      />
      <WorkFlowButton
        visible={reworkBuButton}
        blockers={reworkBuBlockers()}
        value={buttonBuRework}
        onClick={handleClickReworkBu}
      />
      <WorkFlowButton
        visible={showStatusVisible}
        blockers={[]}
        value={buttonShowStatus}
        onClick={handleOpenStatus}
      />
      <WorkFlowButton
        visible={cancelVisible}
        blockers={cancelBlockers()}
        value={buttonCancel}
        onClick={handleCancel}
      />
      <WorkFlowButton
        visible={restoreVisible}
        blockers={restoreBlockers()}
        value={buttonRestore}
        onClick={handleRestore}
      />
      <WorkFlowButton
        visible={buApproveVisible}
        blockers={buApproveBlockers()}
        value={buttonBuApprove}
        onClick={handleBuApprove}
      />
      <PollForStateChanged
        run={runStateChange}
        buildNumber={build.buildNumber}
        onStatusChanged={handleStateChanged}
      />
      <ApprovalDialog
        open={showApprovalDialog}
        approvalGroups={unApprovedGroups}
        onApprove={handleApproveBuild}
        onClose={handleApproveClose}
      />
      <ReworkDialog
        open={showReworkDialog}
        reworkGroups={unApprovedGroups}
        onRework={handleReworkBuild}
        onClose={handleReworkClose}
        buildState={build.state}
      />
      <ReworkBuDialog
        open={showReworkBuDialog}
        onClose={() => setShowReworkBuDialog(false)}
        onRework={handleReworkBuBuild}
      />
      <ApprovalBlockersDialog
        open={showBlockingDialog}
        reasons={blockingReasons}
        onClose={handleCloseBlockingDialog}
      />

      <ArmArcCheckerDialog
        open={openChecker}
        onClose={() => setOpenChecker(false)}
        build={build}
        button1Text="Cancel"
        onButton1={() => setOpenChecker(false)}
        button2Text="Continue"
        onButton2={() => {
          setOpenChecker(false);
          handleSubmit2();
        }}
      />
    </div>
  );
};
