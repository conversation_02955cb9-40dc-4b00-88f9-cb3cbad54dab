import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles(() => ({
  development: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));

export const HasBomTemplate = ({ bomTemplate, build }) => {
  const hasGlobal = bomTemplate.object.name?.includes("(GLOBAL)");
  const hasBom = bomTemplate.object.name?.includes("(BOM)");
  const hasBomTemplate = hasGlobal && hasBom;
  const hasTemplateType = build?.templateSource?.templateType != null;
  const classes = useStyles();

  if (hasBomTemplate || hasTemplateType) return null;
  return (
    <div className={classes.development}>
      The Bill of Process Template is incomplete.
    </div>
  );
};
