import { Button, DialogActions, Typography } from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TextField from "@material-ui/core/TextField";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import CommentBox from "../../../component/comments/CommentBox";

const useStyles = makeStyles((theme) => ({
  textBox: {},
  commentArea: {
    height: "50vh",
    overflowY: "scroll",
    border: "2px solid grey",
    borderRadius: "1%",
    padding: "10px",
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialogActions: {
    display: "flex",
    justifyContent: "space-between",
    width: "100%",
    margin: "0em 1em 1em 1em",
  },
}));

/**
 * @typedef {object} CommentsDialog2~User The User
 * @property {string} userid - The badge number
 * @property {string} username - The user's full name
 */

/**
 * @typedef {object} CommentsDialog2~Comment User clicked the close button
 * @property {CommentsDialog2~User} who - The user who made the comment
 * @property {string} when - The date the comment was saved
 * @property {string} text - The comment
 */

/**
 * @callback CommentsDialog2~onClose User clicked the close button
 */

/**
 * @callback CommentsDialog2~onSave User clicked the save button
 * @param {string} comment - The comment to save
 */

/**
 * Displays the comment dialog box
 * @param {boolean} open - true to show the dialog, false to hide
 * @param {CommentsDialog2~Comment[]} comments - The array of comments
 * @param {CommentsDialog2~onClose} onClose - The user clicked the close button
 * @param {CommentsDialog2~onSave} onSave - The user clicked the save button
 * @return {JSX.Element}
 * @constructor
 */
export function CommentsDialog2({ open, comments, onClose, onSave }) {
  const [comment, setComment] = useState("");

  // when the dialog opens, initialize the comment
  useEffect(() => {
    if (open) {
      setComment("");
    }
  }, [open]);

  const classes = useStyles();

  function handleChange(e) {
    setComment(e.target.value);
  }

  function handleKeyPress(ev) {
    if (ev.key === "Enter") {
      ev.preventDefault();
      handleSave();
    }
  }

  function handleSave() {
    onSave(comment);
  }

  const canSave = comment !== "";

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <DialogTitle>Comments</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        {comments.length === 0 ? (
          <NoComments />
        ) : (
          <CommentBoxes comments={comments} />
        )}

        <TextField
          autoFocus
          className={classes.textBox}
          multiline
          fullWidth
          variant="outlined"
          position="fixed"
          label="Comment"
          minRows={4}
          name="text"
          value={comment}
          onChange={handleChange}
          placeholder="Enter your comment here."
          onKeyPress={handleKeyPress}
        />
      </DialogContent>

      <DialogActions>
        <div className={classes.dialogActions}>
          <Button onClick={onClose} variant="outlined" color="secondary">
            Cancel
          </Button>
          <Button
            disabled={!canSave}
            onClick={handleSave}
            variant="contained"
            color="primary"
          >
            Add Comment
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
}

CommentsDialog2.propTypes = {
  open: PropTypes.bool.isRequired,
  comments: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
};

function NoComments() {
  return <Typography variant="h6">No Comments To Display...</Typography>;
}

function CommentBoxes({ comments }) {
  return (
    <>
      {comments.map((comments, n) => (
        <CommentBox c={comments} key={n} />
      ))}
    </>
  );
}

CommentBoxes.propTypes = {
  comments: PropTypes.array.isRequired,
};
