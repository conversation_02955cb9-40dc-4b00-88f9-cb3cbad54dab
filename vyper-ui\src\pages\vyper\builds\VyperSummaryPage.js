import React, { useContext } from "react";
import Alert from "@material-ui/lab/Alert";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { ComponentNameDisplay } from "src/component/component/ComponentNameDisplay";
import { BackToBuildFormLink } from "src/component/backbutton/BackToBuildFormLink";
import { TBD } from "src/component/tbd/Tbd";
import { None } from "src/pages/vyper/None";
import { BuildPageTitle } from "src/pages/vyper/BuildPageTitle";
import { DataModelsContext } from "src/DataModel";

const useStyles = makeStyles((theme) => ({
  root: {},
  Paper: {
    padding: theme.spacing(3),
  },
}));

export const VyperSummaryPage = ({ vyperNumber }) => {
  const { vyper, build } = useContext(DataModelsContext);

  const material = build?.material?.object?.Material;

  const oldMaterial = build?.material?.object?.OldMaterial;
  let specDevice = build?.multiBuild?.specDevice;
  if (specDevice === "New Multi-Build") {
    specDevice = build?.material?.object?.OldMaterial + "--";
  }

  const facility = build?.facility?.object?.PDBFacility;
  const owners = vyper?.owners?.map((o) => o.username).join(", ");
  const approvers = <TBD />;
  const business =
    build?.material?.object?.SBE == null
      ? ""
      : `${build?.material?.object?.SBE}/${build?.material?.object?.SBE1}/${build?.material?.object?.SBE2}`;
  const pkg =
    build?.material?.object?.PackagePin == null
      ? ""
      : `${build?.material?.object?.PackagePin}/${build?.material?.object?.PackageDesignator}/${build?.material?.object?.PackageGroup}`;
  const bomTemplate =
    build?.bomTemplate?.object?.name == null
      ? ""
      : `${build?.bomTemplate?.object.name}-${build?.bomTemplate?.object?.version}`;
  const flow = build?.flow?.object?.name;
  const automotive = build?.material?.object?.Automotive;
  const isolation = build?.material?.object?.Isolation;
  const armarc = build?.armarc?.object?.jobid || <None />;
  const atss =
    build?.atss?.facilityAt == null
      ? null
      : `${build?.atss?.facilityAt} ${build?.atss?.material} ${build?.atss?.status}` || (
          <None />
        );
  const dies = build?.dies?.dieInstances
    ?.map((di) => di.dies.map((d) => d.name))
    .flat()
    .join(", ");

  const selectionValues = (name) => {
    const selection = build?.selections.find((s) => s.name === name);
    if (selection == null) return <None />;
    return selection.items.map((item, n) => (
      <ComponentNameDisplay
        key={n}
        name={item.value}
        engineering={item.engineering}
      />
    ));
  };

  const symbols = build?.symbolization?.symbols
    ?.map((s) => s.object.name)
    .join(", ");
  const changes = build?.changelink?.changes
    ?.map((c) => c.object.changeNumber)
    .join(", ") || <None />;
  const pcns = build?.changelink?.pcns
    ?.map((c) => c.object.pcnNumber)
    .join(", ") || <None />;

  const checkInFlowRows = (name) => {
    let isInFlowRows = false;
    if (build?.buildFlow?.flowRows?.length > 0) {
      const buildRowsDefinition = build.buildFlow.flowRows;
      buildRowsDefinition.forEach((flowRowConfig) => {
        if (name === flowRowConfig.opnName) {
          isInFlowRows = true;
        }
      });
    }
    return isInFlowRows;
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />

      <BuildPageTitle build={build} title="Build Process Summary" />

      <Alert color="error">This build is for engineering purposes only.</Alert>

      <TableContainer>
        <Table size="small">
          <TableBody>
            <TableRow>
              <TableCell align="right" variant="head">
                Material:
              </TableCell>
              <TableCell align="left" variant="body">
                {material}
              </TableCell>
              <TableCell align="right" variant="head">
                Facility:
              </TableCell>
              <TableCell align="left" variant="body">
                {facility}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Old Material:
              </TableCell>
              <TableCell align="left" variant="body">
                {oldMaterial}
              </TableCell>
              <TableCell align="right" variant="head">
                Spec Device:
              </TableCell>
              <TableCell align="left" variant="body">
                {specDevice}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Approvers:
              </TableCell>
              <TableCell align="left" variant="body">
                {approvers}
              </TableCell>
              <TableCell align="right" variant="head">
                Owners:
              </TableCell>
              <TableCell align="left" variant="body">
                {owners}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Business (Niche):
              </TableCell>
              <TableCell align="left" variant="body">
                {business}
              </TableCell>
              <TableCell align="right" variant="head">
                Package:
              </TableCell>
              <TableCell align="left" variant="body">
                {pkg}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Bill of Process Template:
              </TableCell>
              <TableCell align="left" variant="body">
                {bomTemplate}
              </TableCell>
              <TableCell align="right" variant="head">
                Flow:
              </TableCell>
              <TableCell align="left" variant="body">
                {flow}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Automotive:
              </TableCell>
              <TableCell align="left" variant="body">
                {automotive}
              </TableCell>
              <TableCell align="right" variant="head">
                Isolation:
              </TableCell>
              <TableCell align="left" variant="body">
                {isolation}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Arm/Arc Job:
              </TableCell>
              <TableCell align="left" variant="body">
                {armarc}
              </TableCell>
              <TableCell align="right" variant="head">
                ATSS Copy:
              </TableCell>
              <TableCell align="left" variant="body">
                {atss}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                Die(s):
              </TableCell>
              <TableCell align="left" variant="body">
                {dies}
              </TableCell>
              <TableCell align="right" variant="head">
                Symbolization:
              </TableCell>
              <TableCell align="left" variant="body">
                {symbols}
              </TableCell>
            </TableRow>

            {build?.components?.map((component, x) =>
              component.instances.map((instance, y) =>
                instance.priorities.map((priority, z) => (
                  <TableRow key={`${x}-${y}-${z}`}>
                    <TableCell align="right" variant="head">
                      {component.name}:
                    </TableCell>
                    <TableCell align="left" variant="body">
                      {checkInFlowRows(component.name) ? (
                        <ComponentNameDisplay
                          name={priority.object.name}
                          engineering={priority.engineering}
                        />
                      ) : (
                        <div></div>
                      )}
                    </TableCell>

                    <TableCell align="right" variant="head">
                      Value:
                    </TableCell>
                    {checkInFlowRows(component.name) ? (
                      <TableCell align="left" variant="body">
                        {selectionValues(component.name)}
                      </TableCell>
                    ) : (
                      <TableCell align="left" variant="body">
                        <div></div>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )
            )}

            <TableRow>
              <TableCell align="right" variant="head">
                Changelink Changes
              </TableCell>
              <TableCell align="left" variant="body">
                {changes}
              </TableCell>
              <TableCell align="right" variant="head">
                Changelink PCNs
              </TableCell>
              <TableCell align="left" variant="body">
                {pcns}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell align="right" variant="head">
                &nbsp;
              </TableCell>
              <TableCell align="left" variant="body">
                &nbsp;
              </TableCell>
              <TableCell align="right" variant="head">
                &nbsp;
              </TableCell>
              <TableCell align="left" variant="body">
                &nbsp;
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};
