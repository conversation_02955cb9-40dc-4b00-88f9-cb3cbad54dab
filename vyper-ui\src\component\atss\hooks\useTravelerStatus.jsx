import { useEffect, useState } from "react";
import axios from "axios";

export const useTravelerStatus = (specDevice, facilityAt) => {
  const [statuses, setStatuses] = useState([]);

  const translateStatuses = (statuses) => {
    return statuses
      .map((s) => {
        if (s.startsWith("A")) {
          return "ACTIVE";
        }

        if (s.startsWith("W")) {
          return "WORKING";
        }

        return null;
      })
      .filter((s) => s != null);
  };

  useEffect(() => {
    const controller = new AbortController();

    if (!specDevice || !facilityAt) {
      return;
    }

    axios
      .get(
        `/vyper/v1/atss/autocomplete/statuses?specDevice=${specDevice}&facilityAt=${facilityAt}`,
        {
          signal: controller.signal,
        }
      )
      .then((response) => response.data)
      .then(translateStatuses)
      .then(setStatuses)
      .catch(console.log);

    return () => controller.abort();
  }, [specDevice, facilityAt]);

  return { statuses };
};
