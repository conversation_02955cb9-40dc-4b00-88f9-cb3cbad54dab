import PropTypes from "prop-types";
import React, { createContext } from "react";
import config from "/src/buildEnvironment";
// Context to make user authorization information available throughout application.
const AuthContext = createContext();

/**
 * Returns true if the authUser has one of the allowed roles
 * @param authUser - The user
 * @param {string | string[]} allowedRoles - The allowed role, or the list of allowed roles
 * @return {boolean} true if the user has the role
 */
export function isInRole(authUser, allowedRoles) {
  if (!authUser || !authUser.roles) {
    return false;
  }

  // get the user's roles and routes roles as lower case values in arrays
  // return true if any roles intersect

  // user roles is an array of {id, appId, appName, id, roleId, roleName
  // filter on appName === "Verify Preproduction Engineering Request (VYPER)"
  // map on roleName

  const userRoles = authUser.roles
    .filter((role) => role != null)
    .filter(
      (role) =>
        role.appName === "Verify Preproduction Engineering Request (VYPER)"
    )
    .map((role) => role.roleName)
    .map((s) => s?.toString())
    .map((s) => s?.toLowerCase());

  const routeRoles = [allowedRoles]
    .flat()
    .filter((s) => s != null)
    .map((s) => s.toString())
    .map((s) => s.toLowerCase());

  return userRoles.some((x) => routeRoles.includes(x));
  // return userRoles.filter(x => routeRoles.includes(x)).length > 0
}

export function isInternalUser() {
  return !config.externalUse;
}

const AuthContextWrapper = ({ authUser, authSuccess, children }) => {
  const isAuthenticated = () => {
    return !!authSuccess;
  };

  // Assumption is authUser contains a 'roles' attribute. The allowedRoles can be an
  // array of roles or a string with a single role value. See hook "useAuth" which is
  // responsible for returning the authUser.

  // make the context object:
  const authContext = {
    authUser,
    isInRole: (allowedRoles) => isInRole(authUser, allowedRoles),
    isAuthenticated,
    isInternalUser,
  };
  return (
    <AuthContext.Provider value={authContext}>{children}</AuthContext.Provider>
  );
};

// proptypes
AuthContext.Provider.propTypes = {
  authUser: PropTypes.object,
  authSuccess: PropTypes.bool,
};

// default props
AuthContext.Provider.defaultProps = {
  authUser: {},
  authSuccess: false,
};

export { AuthContext, AuthContextWrapper };
