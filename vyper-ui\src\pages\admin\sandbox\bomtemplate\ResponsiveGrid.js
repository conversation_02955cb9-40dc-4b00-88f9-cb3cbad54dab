import makeStyles from "@material-ui/core/styles/makeStyles";
import { Grid } from "@material-ui/core";
import React from "react";

const useStyles = makeStyles(() => ({
  root: {},
  key: {
    fontWeight: "bold",
    textAlign: "right",
    paddingRight: "1em",
    paddingBottom: "1em",
    "&::after": {
      content: '" : "',
    },
  },
  value: {
    paddingBottom: "1em",
  },
}));

/**
 *
 * @param items a list of objects of { title: "", value: "" }
 * @param columns # of columns to display on large screen, default to 3
 * @returns {JSX.Element}
 * @constructor
 */
export const ResponsiveGrid = ({ items, columns = 3 }) => {
  const classes = useStyles();

  const xs = [6, 6, 6][columns - 1];
  const md = [6, 6, 3][columns - 1];
  const lg = [6, 3, 2][columns - 1];

  return (
    <Grid className={classes.root} container>
      {items.map((item) => (
        <React.Fragment key={item.title}>
          <Grid className={classes.key} item xs={xs} md={md} lg={lg}>
            {item.title}
          </Grid>
          <Grid className={classes.value} item xs={xs} md={md} lg={lg}>
            {item.value}
          </Grid>
        </React.Fragment>
      ))}
    </Grid>
  );
};
