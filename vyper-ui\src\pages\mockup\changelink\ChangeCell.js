import React, { useContext } from "react";
import { Changes } from "./Changes";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
} from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";

export const ChangeCell = ({ vyper, build, onClick }) => {
  const { canEditChangeNumber } = useContext(HelperContext);
  const canEdit = canEditChangeNumber(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build)
  )
    return null;

  return (
    <div>
      <Changes
        changes={build.changelink.changes}
        onClick={onClick}
        canEdit={canEdit}
      />

      {build.changelink.changes.length === 0 ? (
        <DataCell source={null}>
          <VyperLink onClick={onClick} canEdit={canEdit}>
            click to select
          </VyperLink>
        </DataCell>
      ) : null}
    </div>
  );
};
