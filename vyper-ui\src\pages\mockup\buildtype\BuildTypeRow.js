import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { samenessDifference } from "src/component/sameness/sameness";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { BuildTypeCell } from "src/pages/mockup/buildtype/BuildTypeCell";
import { RowPrefix } from "src/pages/mockup/RowPrefix";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const BuildTypeRow = ({
  vyper,
  builds,
  onChange,
  showSameness,
  onAddNewBuild,
}) => {
  const { buildDao } = useContext(DataModelsContext);

  const handleBuildtypeChange = (buildtype, build) => {
    return buildDao
      .changeBuildType(vyper.vyperNumber, build.buildNumber, buildtype)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessDifference(builds),
      })}
      hover
    >
      <RowPrefix help="buildtype" title="Build Type" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <BuildTypeCell
            key={n}
            vyper={vyper}
            build={build}
            onSubmit={handleBuildtypeChange}
            onAddNewBuild={onAddNewBuild}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
