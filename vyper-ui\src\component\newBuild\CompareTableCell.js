import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import PropTypes from "prop-types";
import React from "react";

const useStyles = makeStyles({
  differentObjects: {},
  deviceSelected: {},
});

/**
 *
 * @param currentItem
 * @param copyItem
 * @param boolean showBuild - show or hide the copy item column
 * @returns {JSX.Element}
 * @constructor
 */
export const CompareTableCell = ({ currentItem, copyItem, showBuild }) => {
  const classes = useStyles();

  const differentObjects = (a, b) => {
    return a != null && b != null && a === b;
  };

  return (
    <>
      <TableCell
        align="center"
        className={
          differentObjects(currentItem, copyItem)
            ? classes.deviceSelected
            : classes.differentObjects
        }
      >
        {currentItem}
      </TableCell>

      {showBuild && (
        <TableCell
          align="center"
          className={
            differentObjects(currentItem, copyItem)
              ? classes.buildSelected
              : classes.differentObjects
          }
        >
          {copyItem}
        </TableCell>
      )}
    </>
  );
};

CompareTableCell.propTypes = {
  currentItem: PropTypes.any,
  copyItem: PropTypes.any,
  showBuild: PropTypes.bool.isRequired,
};
