import React, { useContext } from "react";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../vyper/FormStatus";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { HelperContext } from "src/component/helper/Helpers";

export const EslCell = ({ vyper, build, onClick }) => {
  const { canEditEsl } = useContext(HelperContext);
  const canEdit = canEditEsl(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Dry Bake")
  )
    return null;

  return (
    <DataCell source={build.esl?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        {build.esl?.object?.value || "click to select"}
      </VyperLink>
    </DataCell>
  );
};
