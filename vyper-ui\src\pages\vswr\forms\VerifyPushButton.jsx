import React, { useState, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import Button from "@material-ui/core/Button";
import axios from "axios";
import { ArrowRightAlt, Publish } from "@material-ui/icons";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@material-ui/core";
import useSnackbar from "/src/hooks/Snackbar";

import { BASE_VSCSWR_URL, VAR_TO_SCSWR_TITLES } from "./FormConstants";

const fetchData = (uri) => {
  return axios.get(`${BASE_VSCSWR_URL}${uri}`);
};

const postData = (uri, data, config = {}) => {
  return axios.post(`${BASE_VSCSWR_URL}${uri}`, data, config);
};

const genRow = (title, from, to) => (
  <Grid item container spacing={1} direction="row">
    <Grid item xs>
      {title}
    </Grid>
    <Grid item xs>
      {from}
    </Grid>
    <Grid item xs={12} sm={1}>
      <ArrowRightAlt color="secondary" />
    </Grid>
    <Grid item xs>
      {to}
    </Grid>
  </Grid>
);

const getDiffs = (newVswrForm, scswrForm) => {
  const ignoreFields = ["swrID", "generalComment", "requestDate"];

  return Object.entries(newVswrForm)
    .filter(([key]) => !ignoreFields.includes(key))
    .filter(([key, val]) => val != scswrForm[key])
    .filter(([key]) => !key == "sbe1" || scswrForm[key] != "WWMAKE")
    .map(([key, val]) => {
      const title = <b>{VAR_TO_SCSWR_TITLES[key]}:</b>;
      return genRow(title, scswrForm[key], val);
    });
};

const VerifyPushButton = (props) => {
  const {
    generalInfo,
    setGeneralInfo,
    requestorInfo,
    deviceInfo,
    bomInfo,
    dieInfo,
    traveler,
  } = props;

  const [isOpen, setIsOpen] = useState(false);
  const [existingScswr, setExistingScswr] = useState({});
  const [newVswrForm, setNewVswrForm] = useState({});
  const [diffs, setDiffs] = useState([]);
  const { enqueueErrorSnackbar, enqueueSuccessSnackbar } = useSnackbar();

  const getSelectedOrPriorityDie = () => {
    if (!dieInfo || !Array.isArray(dieInfo)) return [];
   
    const selected = dieInfo?.find(d => d.selected === true);
    if (selected) return [selected];
    
    const minPriority = Math.min(...dieInfo.map(d => d.priority));
    const PriorityDie = dieInfo.find(d => d.priority === minPriority);
    return PriorityDie ? [PriorityDie] : [];
  };

  const mapVswrToScswr = (generalInfo) => {
    const atswrForm = {
      generalInfo,
      requestorInfo,
      deviceInfo,
      bomInfo,
      traveler,
      dieInfo: getSelectedOrPriorityDie(),
    };
    postData("/mapVswrToScswr", atswrForm).then((response) => {
      setNewVswrForm(response.data);
    });
  };

  const fetchExistingScswr = (swrID) => {
    fetchData(`/fetchExistingScswr/${swrID}`).then((response) => {
      setExistingScswr(response.data);
    });
  };

  useEffect(() => {
    mapVswrToScswr(generalInfo);

    if (!generalInfo?.existingScswrID) {
      return;
    }

    fetchExistingScswr(generalInfo.existingScswrID);
  }, []);

  useEffect(() => {
    if (
      Object.keys(existingScswr).length === 0 ||
      Object.keys(newVswrForm).length === 0
    ) {
      return;
    }

    if (!existingScswr.swrID) {
      setDiffs([]);
      return;
    }
    console.log("#####newVswrForm####", newVswrForm);
    console.log("#####existingScswr####", existingScswr);
    let diffs = getDiffs(newVswrForm, existingScswr);
    if (diffs.length === 0) {
      setDiffs([]);
      return;
    }
    const header = genRow(<b>Field</b>, <b>Current</b>, <b>New</b>);

    setDiffs([header, ...diffs]);
    console.log("#####diffs####", diffs);
  }, [existingScswr, newVswrForm]);

  const handlePushToScswr = () => {
    const atswrForm = {
      generalInfo,
      requestorInfo,
      deviceInfo,
      bomInfo,
      dieInfo: getSelectedOrPriorityDie(),
      traveler,
    };
    if (generalInfo.existingScswrID != "") {
      postData("/pushExistingToScswr", atswrForm, {
        responseType: "text",
      }).then((response) => {
        if (response.status === 200 && !response.data.includes("Error")) {
          enqueueSuccessSnackbar(response.data);
        } else {
          enqueueErrorSnackbar(response.data);
        }
        fetchExistingScswr(generalInfo.existingScswrID);
      });
      handleClose();
    } else {
      postData("/pushNewToScswr", atswrForm, { responseType: "text" })
        .then((response) => {
          if (response.status == 200) {
            return response.data;
          }
          throw response;
        })
        .then((data) => {
          setGeneralInfo(data);
          fetchExistingScswr(data.existingScswrID);
          mapVswrToScswr(data);
          enqueueSuccessSnackbar("Successfully created new SCSWR.");
        })
        .catch(() => {
          enqueueErrorSnackbar("Error creating new SCSWR.");
        });

      handleClose();
    }
  };

  const handleOpen = () => {
    setIsOpen(true);
  };
  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      <Button
        startIcon={<Publish />}
        variant="contained"
        color="secondary"
        onClick={handleOpen}
      >
        Push to SCSWR
      </Button>

      <Dialog open={isOpen} fullWidth={true} maxWidth="md">
        <DialogTitle>
          {diffs.length > 0
            ? "This will update data in the SCSWR request. Do you still want to proceed?"
            : !generalInfo?.existingScswrID
            ? "A new SCSWR request will be created. Do you still want to proceed?"
            : "No new changes"}
        </DialogTitle>

        <DialogContent>
          <Grid container direction="column" spacing={3}>
            {diffs}
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button variant="contained" color="primary" onClick={handleClose}>
            Cancel
          </Button>
          {(diffs.length > 0 ||
            (diffs.length == 0 && !generalInfo?.existingScswrID)) && (
            <Button
              variant="contained"
              color="secondary"
              onClick={handlePushToScswr}
            >
              Yes
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default VerifyPushButton;
