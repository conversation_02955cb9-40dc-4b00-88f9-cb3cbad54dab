import React, { useContext } from "react";
import { AuthContext } from "src/component/common";
import { Experimental, New, MinorChange } from "../../pages/mockup/buildtype/BuildTypes";

export const Helper = ({ children }) => {
  const { authUser } = useContext(AuthContext);

  const isVyperOwner = (vyper, authUser) =>
    vyper?.owners?.some((usr) => usr.userid === authUser?.uid) || false;

  const isBuildState = (build, state) => build?.state === state || false;

  const isBuildType = (build, buildtype) =>
    build?.buildtype === buildtype || false;

  const defaultCanEdit = (vyper, build, field) => {
    const isOwner = isVyperOwner(vyper, authUser);
    const isDraft = isBuildState(build, "DRAFT");
    const isRework = isBuildState(build, "REWORK");
    const isPraDraft = isBuildState(build, "PRA_DRAFT");

    const isMinorChange = isBuildType(build, MinorChange);

    if(isMinorChange){
      const editableFieldsForMinorChange = [
        "Die",
        "Symbol",
        "Test",
        "PackConfig",
        "DryPack",
        "Esl",
      ];

      return isOwner && (isDraft || isRework || isPraDraft) && editableFieldsForMinorChange.includes(field);
    }

    return isOwner && (isDraft || isRework || isPraDraft);
  };

  const tkyFlowCanEdit = (vyper, build, field) => {
    const flowName = build.buildFlow?.flowName?.toUpperCase();
    const isTestFlow =
      flowName == undefined ||
      flowName == null ||
      flowName?.indexOf("TEST") >= 0 ||
      flowName?.includes("TKY");
    return defaultCanEdit(vyper, build, field) && isTestFlow;
  };

  const canAddPra = (build, hasPra = false) => {
    const isExperimental = isBuildType(build, Experimental);
    const isFinalApproved = isBuildState(build, "FINAL_APPROVED");
    return isFinalApproved && !isExperimental && !hasPra;
  };

  const materialFaclilityCanEdit = (vyper, build) => {
    const isOwner = isVyperOwner(vyper, authUser);
    const isDraft = isBuildState(build, "DRAFT");
    const isRework = isBuildState(build, "REWORK");
    const isPraDraft = isBuildState(build, "PRA_DRAFT");
    if (isPraDraft) {
      return false;
    }

    return isOwner && (isDraft || isRework || isPraDraft);
  };

  const canEditBuildtype = (vyper, build) => {
    const isOwner = isVyperOwner(vyper, authUser);
    const isDraft = isBuildState(build, "DRAFT");
    const isExperimental = isBuildType(build, Experimental);
    const isNew = isBuildType(build, New);
    return isOwner && isDraft && (isExperimental || isNew);
  };

  const canEditDescription = (vyper, build) => defaultCanEdit(vyper, build, "Description");
  const canEditScswrControlNumber = (vyper, build) =>
    defaultCanEdit(vyper, build, "ScswrControlNumber");
  const canEditMaterial = (vyper, build) =>
    materialFaclilityCanEdit(vyper, build);
  const canEditFacility = (vyper, build) =>
    materialFaclilityCanEdit(vyper, build);
  const canEditBackgrind = (vyper, build) => defaultCanEdit(vyper, build, "Backgrind");
  const canEditCopyFrom = (vyper, build) => defaultCanEdit(vyper, build, "CopyFrom");
  const canEditPackageNiche = (vyper, build) => defaultCanEdit(vyper, build, "PackageNiche");
  const canEditDie = (vyper, build) => defaultCanEdit(vyper, build, "Die");
  const canEditWaferSawMethod = (vyper, build) => defaultCanEdit(vyper, build, "WaferSawMethod");
  const canEditComponent = (vyper, build, title) => defaultCanEdit(vyper, build, title);
  const canEditSymbol = (vyper, build) => defaultCanEdit(vyper, build, "Symbol");
  const canEditTest = (vyper, build) => tkyFlowCanEdit(vyper, build, "Test");
  const canEditPackConfig = (vyper, build) => defaultCanEdit(vyper, build, "PackConfig");
  const canEditDryPack = (vyper, build) => defaultCanEdit(vyper, build, "DryPack");
  const canEditEsl = (vyper, build) => defaultCanEdit(vyper, build, "Esl");
  const canEditTurnkey = (vyper, build) => tkyFlowCanEdit(vyper, build, "Turnkey");
  const canEditChangeNumber = (vyper, build) => defaultCanEdit(vyper, build, "ChangeNumber");
  const canEditPcnNumber = (vyper, build) => defaultCanEdit(vyper, build, "PcnNumber");

  const canEditOwners = (vyper) => isVyperOwner(vyper, authUser);

  const currentUserIsSCP = (vyper, build) => {
    return authUser?.roles?.some((role) => {
      const matchFacility = role.groupName?.includes(
        `_${build?.facility?.object?.PDBFacility}_`?.toUpperCase()
      );
      const matchSCP = role.groupName?.includes("_SCP");
      return matchFacility && matchSCP;
    });
  };

  return (
    <HelperContext.Provider
      value={{
        isBuildState,
        isBuildType,
        canEditBuildtype,
        canEditDescription,
        canEditScswrControlNumber,
        canEditMaterial,
        canEditFacility,
        canEditBackgrind,
        canEditCopyFrom,
        canEditPackageNiche,
        canEditDie,
        canEditWaferSawMethod,
        canEditComponent,
        canEditSymbol,
        canEditTest,
        canEditPackConfig,
        canEditDryPack,
        canEditEsl,
        canEditTurnkey,
        canEditChangeNumber,
        canEditPcnNumber,
        canEditOwners,
        currentUserIsSCP,
        canAddPra,
      }}
    >
      {children}
    </HelperContext.Provider>
  );
};

export const HelperContext = React.createContext(null);
