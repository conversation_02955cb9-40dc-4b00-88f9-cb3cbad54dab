import React from "react";
import { NavLink } from "react-router-dom";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  root: {
    marginBottom: "1rem",
    display: "flex",
    justifyContent: "flex-start",
    position: "fixed",
    width: "95%",
    top: "48px",
    backgroundColor: "white",
    paddingTop: "20px",
    zIndex: 4,
  },
  active: {
    color: "red",
  },
});

export const BackToVscnFormLink = ({ vyperNumber, vscn }) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <NavLink
        exact
        to={`/projects/${vyperNumber}/vscns/${vscn?.vscnNumber}`}
        activeClassName={classes.active}
        style={{ marginRight: "40px" }}
      >
        {vscn?.vscnNumber}
      </NavLink>
      <NavLink
        to={`/projects/${vyperNumber}/vscns/${vscn?.vscnNumber}/selection`}
        activeClassName={classes.active}
      >
        Selection
      </NavLink>
    </div>
  );
};
