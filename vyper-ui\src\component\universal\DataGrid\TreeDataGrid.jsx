import React from "react";
import PropTypes from "prop-types";
import ClientTreeDataGrid from "./ClientTreeDataGrid";
import ServerTreeDataGrid from "./ServerTreeDataGrid";

const TreeDataGrid = (props) => {
  const { url } = props;
  // If a url is provided, assume a remote data setup
  const TreeDataGrid =
    url !== undefined && url !== null && url !== "" ? (
      <ServerTreeDataGrid {...props} />
    ) : (
      <ClientTreeDataGrid {...props} />
    );
  return TreeDataGrid;
};

TreeDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  data: PropTypes.array,
  detailPanel: PropTypes.any,
  handleSelect: PropTypes.func,
  selectable: PropTypes.bool,
  timestamp: PropTypes.string,
  title: PropTypes.string,
  url: PropTypes.string,
};

export default TreeDataGrid;
