import { ListItem, ListItemIcon, ListItemText } from "@material-ui/core";
import List from "@material-ui/core/List";
import makeStyles from "@material-ui/core/styles/makeStyles";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import React, { useContext, useEffect, useState } from "react";
import { ComponentMapContext } from "src/component/componentmap/ComponentMap";
import { PollForStateChanged } from "src/component/PollForStateChanged";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { AddOperationDialogContext } from "src/pages/mockup/operation/AddOperationDialog";
import { RemoveOperationDialogContext } from "src/pages/mockup/operation/RemoveOperationDialog";
import { AlertDialogContext } from "../../component/alert/AlertDialog";
import { ApprovalOperationContext } from "../../component/approvaloperation/ApprovalOperation";
import { BackToBuildFormLink } from "../../component/backbutton/BackToBuildFormLink";
import { CommentsDialogContext } from "../../component/comments/CommentsDialog";
import { AuthContext } from "../../component/common/auth";
import { ConfirmationDialogContext } from "../../component/cornfirmation/ConfirmationDialog";
import { useLocalStorage } from "../../component/hooks/useLocalStorage";
import { QuestionDialogContext } from "../../component/question/QuestionDialog";
import { logError } from "../functions/logError";
import { AtSelectionDialogContext } from "../mockup/atselection/AtSelectionDialog";
import { ChangeLog } from "../mockup/audit/ChangeLog";
import { Experimental, New } from "../mockup/buildtype/BuildTypes";
import { ApprovalBlockersDialog } from "../mockup/workflow/ApprovalBlockersDialog";
import { ApprovalDialog } from "../mockup/workflow/ApprovalDialog";
import {
  approveTask,
  processTasks,
  rejectTask,
  retrieveTaskApprovals,
} from "../mockup/workflow/approvalHelpers";
import { determineAtApproveBlockers } from "../mockup/workflow/blockers";
import { ReworkDialog } from "../mockup/workflow/ReworkDialog";
import { BuildPageTitle } from "../vyper/BuildPageTitle";
import { Comments } from "./comments/Comments";
import Header from "./header/Header";
import Options from "./Options";
import { Body } from "./traveler/Body";
import { TemplateBanner } from "src/pages/mockup/template/TemplateBanner";
import { custx } from "src/component/symbol/custx";
import { AtssTravelerDialog } from "src/component/atss/AtssTravelerDialog";
import { BuildNumberDialog } from "src/pages/mockup/fillcomponent/BuildNumberDialog";

const useStyles = makeStyles({
  root: {},
  split: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  comments: {
    paddingBottom: "4rem",
  },
  gridRow: {
    paddingBottom: "1rem",
  },
  optionBar: {
    position: "sticky",
    backgroundColor: "white",
    top: 85,
    paddingTop: "1px",
    zIndex: 10, // the icons have an index of 1, so we keep the optionBar on top
  },
  table: {
    backgroundColor: "#CA8F8F",
  },
  info: {
    color: "red",
  },
});

const stateAtReviewChange = "AT_REVIEW_CHANGE";
const stateBuReviewChange = "BU_REVIEW_CHANGE";

export const VyperSelectPage = ({ vyperNumber, buildNumber }) => {
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showQuestionDialog } = useContext(QuestionDialogContext);
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { showSelectionDialog } = useContext(AtSelectionDialogContext);
  const { componentMaps } = useContext(ComponentMapContext);
  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );
  const { open: openAddOperationDialog } = useContext(
    AddOperationDialogContext
  );
  const { open: openRemoveOperationDialog } = useContext(
    RemoveOperationDialogContext
  );
  const { open: openCommentsDialog } = useContext(CommentsDialogContext);
  const {
    vyperDao,
    vyper,
    buildDao,
    build,
    componentDao,
    components,
    operationDao,
    operations,
  } = useContext(DataModelsContext);
  const { authUser } = useContext(AuthContext);
  const [tasks, setTasks] = useState([]);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showBlockingDialog, setShowBlockingDialog] = useState(false);
  const [blockingReasons, setBlockingReasons] = useState([]);
  const [showReworkDialog, setShowReworkDialog] = useState(false);
  const [runStateChange, setRunStateChange] = useState(false);
  const [showTravelerDialog, setShowTravelerDialog] = useState(false);
  const [showBuildNumberDialog, setShowBuildNumberDialog] = useState(false);

  // manage the options dialog
  const [options, setOptions] = useLocalStorage("traveler.options", {
    attribute: true,
    paragraph: true,
    component: true,
    header: true,
    editbutton: true,
    rejection: true,
    comments: true,
    changelog: true,
    dragdrop: false,
  });

  // manage drag and drop state
  const [dragDropChanges, setDragDropChanges] = useState({
    operations: null, // Will store the modified operations array
    originalOperations: null, // Will store the original operations array
    hasChanges: false,
  });



  // Initialize original operations when drag drop is enabled
  useEffect(() => {
    if (options.dragdrop && build?.traveler?.operations && !dragDropChanges.originalOperations) {
      setDragDropChanges({
        operations: [...build.traveler.operations],
        originalOperations: [...build.traveler.operations],
        hasChanges: false,
      });
    } else if (!options.dragdrop && dragDropChanges.originalOperations) {
      // Reset when drag drop is disabled
      setDragDropChanges({
        operations: null,
        originalOperations: null,
        hasChanges: false,
      });
    }
  }, [options.dragdrop, build?.traveler?.operations]);

  /**
   * If the vyper number changes, get the vyper object
   */
  useEffect(() => {
    reloadVyper().catch(noop);
  }, [vyperNumber]);

  const reloadVyper = () => {
    return vyperDao.findByVyperNumber(vyperNumber).catch(noop);
  };

  /**
   * if build number changes, reload the vyper
   */
  useEffect(() => {
    reloadBuild().catch(noop);
  }, [buildNumber]);

  const reloadBuild = () => {
    return buildDao.findByBuildNumber(buildNumber).catch(noop);
  };

  /**
   * If the build number changes, get the approvals
   */
  useEffect(() => {
    if (authUser.uid === "") {
      return;
    }

    fetchTaskApprovals().catch(logError);
  }, [authUser, buildNumber]);

  /**
   * Get the atss operations
   */
  useEffect(() => {
    operationDao.list().catch(noop);
  }, []);

  /**
   * get the atss components
   */
  useEffect(() => {
    componentDao.list().catch(noop);
  }, []);

  // when the current user changes, or the users value is updated from task service

  const handleToggleAttribute = () =>
    setOptions({ ...options, attribute: !options.attribute });
  const handleToggleParagraph = () =>
    setOptions({ ...options, paragraph: !options.paragraph });
  const handleToggleComponent = () =>
    setOptions({ ...options, component: !options.component });
  const handleToggleHeader = () =>
    setOptions({ ...options, header: !options.header });
  const handleToggleEditButton = () =>
    setOptions({ ...options, editbutton: !options.editbutton });
  const handleToggleRejection = () =>
    setOptions({ ...options, rejection: !options.rejection });
  const handleToggleComments = () =>
    setOptions({ ...options, comments: !options.comments });
  const handleToggleChangelog = () =>
    setOptions({ ...options, changelog: !options.changelog });
  const handleToggleDragDrop = () => {
    const newDragDropValue = !options.dragdrop;

    if (newDragDropValue) {
      setOptions({
        ...options,
        dragdrop: true,
        header: false,
        paragraph: false,
        attribute: false,
        changelog: false,
        editbutton: false, // Turn off and lock edit buttons when drag drop is enabled
        comments: false
      });
    } else {
      // When disabling drag and drop, just turn it off
      setOptions({ ...options, dragdrop: false });
      setDragDropChanges({
        operations: null,
        originalOperations: null,
        hasChanges: false,
      });
    }
  };

  // Helper function to apply component order from destination operation to all operations
  const applyComponentOrderToAllOperations = (operations, destinationOperationName, componentName) => {
    // Find the destination operation
    const destOperation = operations.find(op => op.name === destinationOperationName);
    if (!destOperation) return operations;

    // Extract components of the specified type and their order from destination operation
    const destComponents = destOperation.components.filter(comp => comp.name === componentName);
    if (destComponents.length === 0) return operations;

    // Create an ordered list of component values from the destination operation
    const destComponentOrder = destComponents.map(component => component.value);

    // Apply the component order to all other operations
    return operations.map(operation => {
      if (operation.name === destinationOperationName) {
        return operation;
      }

      // Find components of the specified type in this operation
      const operationComponents = operation.components.filter(comp => comp.name === componentName);
      if (operationComponents.length === 0) {
        return operation;
      }

      // Create a map of component value to component for easy lookup
      const componentValueMap = {};
      operationComponents.forEach(component => {
        componentValueMap[component.value] = component;
      });

      // Reorder components according to destination operation's order
      const reorderedComponents = [];
      destComponentOrder.forEach(componentValue => {
        if (componentValueMap[componentValue]) {
          reorderedComponents.push(componentValueMap[componentValue]);
        }
      });

      // Add any components that exist in this operation but not in destination operation
      operationComponents.forEach(component => {
        if (!destComponentOrder.includes(component.value)) {
          reorderedComponents.push(component);
        }
      });

      // Reconstruct the components array maintaining original positions of non-target components
      const newComponents = [];
      let componentIndex = 0;

      operation.components.forEach(component => {
        if (component.name === componentName) {
          if (componentIndex < reorderedComponents.length) {
            newComponents.push(reorderedComponents[componentIndex]);
            componentIndex++;
          }
        } else {
          newComponents.push(component);
        }
      });

      return {
        ...operation,
        components: newComponents
      };
    });
  };

  // Drag and drop handlers
  const handleDragDropSave = () => {
    buildDao
      .reorderFlow(vyperNumber, buildNumber, dragDropChanges.operations)
      .then((updatedBuild) => {
        setDragDropChanges({
          operations: null,
          originalOperations: null,
          hasChanges: false,
        });
      })
      .catch((error) => {
        console.error("Failed to save reorder:", error);
        openAlert({
          title: "Save Failed",
          message: "Failed to save the reordering changes. Please try again.",
        });
      });
  };

  const handleDragDropCancel = () => {
    // Revert to original operations
    setDragDropChanges({
      operations: [...dragDropChanges.originalOperations],
      originalOperations: dragDropChanges.originalOperations,
      hasChanges: false,
    });
  };

  const handleOperationReorder = (result) => {
    if (!result.destination) return;

    // Use current operations from drag drop state or original build operations
    const currentOperations = dragDropChanges.operations || build.traveler.operations;
    const newOperations = Array.from(currentOperations);
    const [reorderedItem] = newOperations.splice(result.source.index, 1);
    newOperations.splice(result.destination.index, 0, reorderedItem);

    setDragDropChanges({
      ...dragDropChanges,
      operations: newOperations,
      hasChanges: true,
    });
  };

  const handleComponentReorder = (result) => {
    if (!result.destination) return;

    // Extract operation name from droppableId (format: "components-{operationName}")
    const sourceOperationName = result.source.droppableId.replace("components-", "");
    const destOperationName = result.destination.droppableId.replace("components-", "");

    // Use current operations from drag drop state or original build operations
    const currentOperations = dragDropChanges.operations || build.traveler.operations;
    const newOperations = [...currentOperations];

    // Find the operations
    const sourceOpIndex = newOperations.findIndex(op => op.name === sourceOperationName);
    const destOpIndex = newOperations.findIndex(op => op.name === destOperationName);

    if (sourceOpIndex === -1 || destOpIndex === -1) return;

    // Get the component being moved
    const componentBeingMoved = newOperations[sourceOpIndex].components[result.source.index];

    // Validation checks
    const validationErrors = [];

    // 1) Check if required component is being moved out of its operation
    if (sourceOperationName !== destOperationName && componentBeingMoved.required === "REQUIRED") {
      validationErrors.push(`Required component "${componentBeingMoved.name}" cannot be moved out of operation "${sourceOperationName}".`);
    }

    // 2) Check if moving this component breaks adjacency at the source location
    const sourceComponents = newOperations[sourceOpIndex].components;
    const sourceIndex = result.source.index;

    // Check if removing this component would break adjacency of same-named components
    if (sourceIndex > 0 && sourceIndex < sourceComponents.length - 1) {
      const componentAbove = sourceComponents[sourceIndex - 1];
      const componentBelow = sourceComponents[sourceIndex + 1];

      if (componentAbove.name === componentBelow.name && componentAbove.name !== componentBeingMoved.name) {
        validationErrors.push(`Cannot move component "${componentBeingMoved.name}" as it would break the adjacency of "${componentAbove.name}" components in operation "${sourceOperationName}".`);
      }
    }

    // 3) Check if component with same name already exists in destination operation
    if (sourceOperationName !== destOperationName) {
      const destComponents = newOperations[destOpIndex].components;
      const existingComponentIndices = destComponents
        .map((comp, index) => comp.name === componentBeingMoved.name ? index : -1)
        .filter(index => index !== -1);

      if (existingComponentIndices.length > 0) {
        // Component with same name exists, check if drop position is adjacent
        const dropIndex = result.destination.index;
        const isAdjacent = existingComponentIndices.some(existingIndex => {
          // Check if the drop position is directly adjacent to an existing component
          // or if it will be adjacent after the insertion
          return Math.abs(existingIndex - dropIndex) <= 1;
        });

        if (!isAdjacent) {
          validationErrors.push(`Component "${componentBeingMoved.name}" already exists in operation "${destOperationName}". It can only be placed adjacent to the existing component with the same name.`);
        }
      }

      // 4) Check if inserting at destination would break adjacency of existing same-named components
      const dropIndex = result.destination.index;
      if (dropIndex > 0 && dropIndex < destComponents.length) {
        const componentAbove = destComponents[dropIndex - 1];
        const componentBelow = destComponents[dropIndex];

        if (componentAbove.name === componentBelow.name && componentAbove.name !== componentBeingMoved.name) {
          validationErrors.push(`Cannot place component "${componentBeingMoved.name}" here as it would break the adjacency of "${componentAbove.name}" components in operation "${destOperationName}".`);
        }
      }
    }

    // 5) Additional validation for same-operation moves
    if (sourceOperationName === destOperationName) {
      const components = [...newOperations[sourceOpIndex].components];
      const sourceIndex = result.source.index;
      const dropIndex = result.destination.index;

      // Check if moving within same operation would break adjacency at destination
      // Simulate the move to check the final state
      const tempComponents = [...components];
      const [movedComponent] = tempComponents.splice(sourceIndex, 1);

      // Check if inserting at destination would break adjacency
      if (dropIndex > 0 && dropIndex < tempComponents.length) {
        const componentAbove = tempComponents[dropIndex - 1];
        const componentBelow = tempComponents[dropIndex];

        if (componentAbove.name === componentBelow.name && componentAbove.name !== movedComponent.name) {
          validationErrors.push(`Cannot place component "${movedComponent.name}" here as it would break the adjacency of "${componentAbove.name}" components in operation "${sourceOperationName}".`);
        }
      }

      // Check if components with same name must remain adjacent
      const otherSameNameIndices = components
        .map((comp, index) => comp.name === componentBeingMoved.name && index !== result.source.index ? index : -1)
        .filter(index => index !== -1);

      if (otherSameNameIndices.length > 0) {
        // Other components with same name exist, check if drop position is adjacent
        // Adjust indices for the fact that we're removing the source component first
        const adjustedOtherIndices = otherSameNameIndices.map(index =>
          index > result.source.index ? index - 1 : index
        );

        const isAdjacent = adjustedOtherIndices.some(adjustedIndex => {
          // Check if the drop position is directly adjacent to another component with same name
          return Math.abs(adjustedIndex - dropIndex) <= 1;
        });

        if (!isAdjacent) {
          validationErrors.push(`Component "${componentBeingMoved.name}" can only be placed adjacent to other components with the same name in operation "${sourceOperationName}".`);
        }
      }
    }

    // Final validation check - if any errors exist, show them and return
    if (validationErrors.length > 0) {
      openAlert({
        title: "Cannot Move Component",
        blockers: validationErrors,
      });
      return;
    }

    if (sourceOperationName === destOperationName) {
      // Moving within the same operation
      const components = [...newOperations[sourceOpIndex].components];
      const [reorderedComponent] = components.splice(result.source.index, 1);
      components.splice(result.destination.index, 0, reorderedComponent);

      newOperations[sourceOpIndex] = {
        ...newOperations[sourceOpIndex],
        components: components
      };

      // Correct priorities for the operation after the move
      newOperations[sourceOpIndex] = newOperations[sourceOpIndex];

      if (build.material?.object?.MCMChipCount === 1 && (componentBeingMoved.name === "Die" || componentBeingMoved.name === "Leadframe" || componentBeingMoved.name === "Mount Compound" || componentBeingMoved.name === "Wire" || componentBeingMoved.name === "Mold Compound")) {
        const updatedOperations = applyComponentOrderToAllOperations(newOperations, sourceOperationName, componentBeingMoved.name);
        setDragDropChanges({
          ...dragDropChanges,
          operations: updatedOperations,
          hasChanges: true,
        });
        return;
      }
      if (build.material?.object?.MCMChipCount !== 1 && componentBeingMoved.name === "Die") {
        const updatedOperations = applyComponentOrderToAllOperations(newOperations, sourceOperationName, componentBeingMoved.name);
        setDragDropChanges({
          ...dragDropChanges,
          operations: updatedOperations,
          hasChanges: true,
        });
        return;
      }

    } else {
      // Moving between different operations
      const sourceComponents = [...newOperations[sourceOpIndex].components];
      const destComponents = [...newOperations[destOpIndex].components];

      const [movedComponent] = sourceComponents.splice(result.source.index, 1);
      destComponents.splice(result.destination.index, 0, movedComponent);

      newOperations[sourceOpIndex] = {
        ...newOperations[sourceOpIndex],
        components: sourceComponents
      };

      newOperations[destOpIndex] = {
        ...newOperations[destOpIndex],
        components: destComponents
      };

      // Correct priorities for both source and destination operations after the move
      newOperations[sourceOpIndex] = newOperations[sourceOpIndex];
      newOperations[destOpIndex] = newOperations[destOpIndex];
    }

    if (build.material?.object?.MCMChipCount === 1 && (componentBeingMoved.name === "Die" || componentBeingMoved.name === "Leadframe" || componentBeingMoved.name === "Mount Compound" || componentBeingMoved.name === "Wire" || componentBeingMoved.name === "Mold Compound")) {
      const updatedOperations = applyComponentOrderToAllOperations(newOperations, destOperationName, componentBeingMoved.name);
      setDragDropChanges({
        ...dragDropChanges,
        operations: updatedOperations,
        hasChanges: true,
      });
    }
    else if (build.material?.object?.MCMChipCount !== 1 && componentBeingMoved.name === "Die") {
      const updatedOperations = applyComponentOrderToAllOperations(newOperations, destOperationName, componentBeingMoved.name);
      setDragDropChanges({
        ...dragDropChanges,
        operations: updatedOperations,
        hasChanges: true,
      });
    } 
    else {
      setDragDropChanges({
        ...dragDropChanges,
        operations: newOperations,
        hasChanges: true,
      });
    }
  };



  /**
   * change the operation name
   * @param {object} operation
   */
  const handleEditOperationName = (operation) => {
    showQuestionDialog({
      type: "native-select",
      title: "Change Operation",
      description: `Change this operation from ${operation.name} to ?`,
      value: operation.name,
      multiline: false,
      rows: 10,
      options: operations,
      onSave: (answer) => {
        return buildDao
          .changeFlowOperation(vyperNumber, buildNumber, operation.name, answer)
          .catch(noop);
      },
    });
  };

  /**
   * add a new operation
   * @param {object} operation
   */
  const handleAddOperation = (operation) => {
    // build the list of operation names that excludes the operations already in the build
    const existingNames = build.flow.object.operations.map(
      (operation) => operation.name
    );
    const filteredOperations = operations.filter(
      (name) => !existingNames.some((n) => n === name)
    );
    let operationName = operation?.name;

    openAddOperationDialog({
      build: build,
      operations: filteredOperations,
      onAdd: (data) => {
        // if user selected new operation to added before the current operation
        if (data.beforeOrAfter === "Before") {
          let index = existingNames.findIndex((name) => name === operationName);
          operationName = existingNames[index - 1];
        }

        return buildDao
          .addFlowOperation(
            vyperNumber,
            buildNumber,
            operationName || null,
            data.name,
            "OPTIONAL",
            data.engineering
          )
          .catch(noop);
      },
    });
  };

  /**
   * remove an operation
   * @param {object} operation
   * @param index
   */
  const handleRemoveOperation = (operation, index) => {
    openRemoveOperationDialog({
      build: build,
      operation: operation.name,
      required: operation.required,
      onRemove: (data) => {
        return buildDao
          .deleteFlowOperation(
            vyperNumber,
            buildNumber,
            operation.name,
            data.engineering,
            index
          )
          .catch(noop);
      },
    });
  };

  /**
   * edit a component name
   * @param {object} operation
   * @param {object} component
   */
  const handleEditComponentName = (operation, component) => {
    showQuestionDialog({
      type: "native-select",
      title: "Change Component",
      description: `Change this component from ${component.name} to ?`,
      value: component.name,
      multiline: false,
      rows: 10,
      options: components,
      onSave: (answer) => {
        return buildDao
          .editComponentName(
            vyperNumber,
            buildNumber,
            operation.name,
            component.name,
            answer
          )
          .catch(noop);
      },
    });
  };

  /**
   * Add a comment
   */
  const handleAddComment = () => {
    openCommentsDialog({
      comments: [...build.comments].reverse(),
      operations: build.flow.object.operations.map(
        (operation) => operation.name
      ),
      onSave: (comment) => {
        buildDao
          .addComment(
            vyper.vyperNumber,
            build.buildNumber,
            comment.who.username,
            comment.who.userid,
            comment.when,
            comment.text,
            comment.operation
          )
          .catch(noop);
      },
    });
  };

  /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

  const fetchTaskApprovals = () => {
    return retrieveTaskApprovals(buildNumber, authUser, setTasks).catch(
      logError
    );
  };

  const {
    unApprovedGroups,
    isFrozen,
    unApprovedGroupsAsObjects,
    isApprover,
    approvedGroups,
  } = processTasks(tasks, authUser);

  /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

  /**
   * The user clicked the approve button in the options row
   */
  const handleShowApproveDialog = () => setShowApprovalDialog(true);

  /**
   * The user clicked the close button in the approvals dialog
   */
  const handleCloseApproveDialog = () => setShowApprovalDialog(false);

  /**
   * The user clicked the approve button in the approval dialog.
   * @param {string} group - The group that is approving
   * @param {string} [comment] - The approval comment (optional)
   */
  function handleApproveBuild(group, comment) {
    setShowApprovalDialog(false);

    // should we block the approval?
    const blockers = determineAtApproveBlockers(
      group,
      isFrozen,
      unApprovedGroupsAsObjects,
      build,
      authUser,
      findApprovalOperationByOperation
    );

    // if we have blockers, show the dialog and exit
    if (blockers.length > 0) {
      setBlockingReasons(blockers);
      setShowBlockingDialog(true);
      return;
    }

    // update the task
    approveTask(tasks, authUser, comment, group)
      .then(() => {
        // refresh the build, vyper and tasks
        const promise1 = fetchTaskApprovals();
        const promise2 = buildDao.notifyAtGroupApproved(
          vyper.vyperNumber,
          buildNumber,
          group
        );
        const promise3 = vyperDao.findByVyperNumber(vyperNumber);
        return Promise.all([promise1, promise2, promise3]);
      })
      .then(() => {
        // poll for approval status changes
        setRunStateChange(true);
      })
      .catch(logError);
  }

  /**
   * User clicked the close button on the ApprovalBlockersDialog.
   */
  const handleCloseBlockingDialog = () => setShowBlockingDialog(false);

  /**
   * User clicked the rework button.
   */
  const handleShowReworkDialog = () => {
    const currentUserIsOwner = vyper.owners.some(
      (usr) => usr.userid === authUser.uid
    );
    const currentUserIsAt = authUser?.roles?.some((role) =>
      role.groupName?.includes(
        `_${build.facility.object.PDBFacility}_`.toUpperCase()
      )
    );

    const stateIsAtReviewChange = build.state === stateAtReviewChange;
    const stateIsBuReviewChange = build.state === stateBuReviewChange;

    const blockers = [];
    if (stateIsAtReviewChange && !(currentUserIsOwner || currentUserIsAt)) {
      blockers.push("You are not an owner, or a member of the A/T.");
    }
    if (stateIsBuReviewChange && !currentUserIsOwner) {
      blockers.push("You are not an owner.");
    }

    if (blockers.length > 0) {
      setBlockingReasons(blockers);
      setShowBlockingDialog(true);
      return;
    }
    setShowReworkDialog(true);
  };

  /**
   * The user clicked the close button in the approvals dialog
   */
  const handleCloseReworkDialog = () => setShowReworkDialog(false);

  /**
   * User clicked rework in the rework dialog.
   */
  const handleReworkBuild = (group, reason, comment) => {
    setShowReworkDialog(false);

    rejectTask(tasks, authUser, comment, reason, group)
      .then(() => {
        return buildDao.changeWorkflow(
          vyper.vyperNumber,
          build.buildNumber,
          "rework",
          group,
          reason
        );
      })
      .then(() => {
        const promise1 = fetchTaskApprovals();
        return Promise.resolve(promise1);
      })
      .catch(logError);
  };

  /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

  // edit a component value
  // noinspection JSUnusedLocalSymbol
  const handleEditComponentValue = (operation, component) => {
    // if this is an eng component, change to the vyper component name

    let cName = component.name;
    const componentMap = componentMaps.find(
      (cm) => cm.engineeringAtssComponentName === cName
    );
    if (componentMap != null) {
      cName = componentMap.name;
    }

    // get the selected items
    const selection = build.selections.find(
      (s) => s.operation === operation.name && s.name === cName
    );
    const selectedItems = selection == null ? [] : selection.items;

    // get the supplier part number / wire
    let supplierOrWire = build.components
      .find((c) => c.name === cName)
      ?.instances?.[0]?.priorities?.[0]?.object?.name?.replace(
        "Placeholder Supplier #: ",
        ""
      );

    // show the a/t selection dialog
    showSelectionDialog({
      selection: {},
      build: build,
      name: cName,
      selectionItems: selectedItems,
      supplierOrWire: supplierOrWire,
      disableAddEngineering: false,
      onSave: (selection) => {
        return buildDao
          .changeSelection(
            vyperNumber,
            buildNumber,
            operation.name,
            selection.name,
            selection.items
          )
          .catch(noop);
      },
    });
  };

  // add a component name

  const handleAddComponent = (operation, component) => {
    let description = `What component do you want to add? `;
    if (component == null) {
      description += `This will become the 1st component in the operation.`;
    } else {
      description += `This will be placed just after the ${component.name} component.`;
    }

    showQuestionDialog({
      type: "native-select",
      title: "Add Component",
      description: description,
      value: null,
      multiline: false,
      rows: 10,
      options: components,
      onSave: (answer) => {
        // if the component already exists in the flow operation, don't add it, but show the selection dialog

        const flowOperation = build.flow.object.operations.find(
          (fo) => fo.name === operation.name
        );
        const exists = flowOperation?.components?.find(
          (fc) => fc.name === answer
        );

        if (exists) {
          handleEditComponentValue(operation, { name: answer });
        } else {
          return buildDao
            .addFlowComponent(
              vyperNumber,
              buildNumber,
              operation.name,
              answer,
              "OPTIONAL",
              component?.name
            )
            .catch(noop);
        }
      },
    });
  };

  // remove a component name
  const handleRemoveComponent = (operation, component) => {
    // if component name starts with "Eng ", strip it off
    const cName = component.name.startsWith("Eng ")
      ? component.name.substring(4)
      : component.name;

    openConfirmation({
      title: "Remove Component",
      message: `Are you sure you want to remove the component "${cName}"?`,
      yesText: "Remove It",
      noText: "Cancel",
      onYes: () => {
        return buildDao
          .removeFlowComponent(vyperNumber, buildNumber, operation.name, cName)
          .catch(noop);
      },
    });
  };

  // return false if build is experimental and component is Placeholder
  // return false if build is new or experimental, and component is optional

  const validateComponents = (c) => {
    if (build.buildtype === Experimental) {
      if (c.value !== null && c.value.includes("Placeholder")) {
        return false;
      }
    }

    if (build.buildtype === New || build.buildtype === Experimental) {
      if (c.required === "OPTIONAL") {
        return false;
      }
    }

    // if this is a CUSTX component, check if intentionally blank
    if (custx.includes(c.name)) {
      const ignore =
        build.components.find((c1) => c1.name === c.name)?.instances[0]
          ?.priorities[0]?.object?.ignoreBlank === "Y";
      if (ignore) {
        return false;
      }
    }

    return c.value === null || c.value.includes("Placeholder");
  };

  /**
   * The user clicked an operation validation checkbox.
   * We verify that all the components are setup correctly, and the mb diagrams are approved (if needed).
   *
   * @param {object} operation The operation that is being checked or un-checked.
   * @returns {Promise} - The build - the updated build on success, or the current build if it wasn't updated.
   */
  function handleClickValidate(operation) {
    // are all component value's set
    const componentsNotSet = operation.components
      .filter(validateComponents)
      .map((c) => c.name);

    if (componentsNotSet.length !== 0) {
      openAlert({
        title: "Can not Validate the Operation",
        message: (
          <div>
            <div>These component do not have a value:</div>
            <List>
              {componentsNotSet.map((text) => (
                <ListItem key={text}>
                  <ListItemIcon>
                    <ArrowRightAltIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText>{text}</ListItemText>
                </ListItem>
              ))}
            </List>
          </div>
        ),
      });

      return Promise.resolve(build);
    }

    // are all legacy MB Diagrams approved?
    const allDiagramsApproved = operation.components
      .filter((component) => component.name === "MB Diagram")
      .map((component) => component.value)
      .filter((number) => !number.match(/^[0-9]{8}$/))
      .every((number) => build.diagramApprovals?.[number]?.type != null);

    if (!allDiagramsApproved && build.buildtype === New) {
      openAlert({
        title: "Can not Validate the Operation",
        message: "One or more MB Diagrams have not been approved.",
      });
      return Promise.resolve(build);
    }

    // allow the box to be checked
    return buildDao
      .toggleValidate(vyperNumber, buildNumber, operation.name)
      .catch(noop);
  }

  /////////////////////////////////////////////////////////////////////
  // fill unselected

  const handleClickFillUnselectedAtss = () => setShowTravelerDialog(true);

  const handleClickFillUnselectedVyper = () => setShowBuildNumberDialog(true);

  const handleClickFillUnselectedClear = () =>
    openConfirmation({
      title: "Clear Filled Data",
      message:
        "Are you sure you want to clear values that were filled by the fill unselected action?",
      onYes: () => {
        handleClear();
      },
    });

  const enableUnselectedClear = build?.fillComponent?.mode !== "NONE";

  const handleFillComponentAtss = (material, facility, status) => {
    return buildDao
      .fillComponentAtss(vyperNumber, buildNumber, facility, material, status)
      .then(() => setShowTravelerDialog(false))
      .catch(noop);
  };

  const handleFillComponentVyper = (number) => {
    return buildDao
      .fillComponentVyper(vyperNumber, buildNumber, number)
      .then(() => setShowBuildNumberDialog(false))
      .catch(noop);
  };

  const handleClear = () => {
    return buildDao.fillComponentClear(vyperNumber, buildNumber).catch(noop);
  };

  ////////////////////////////////////////////////////////////////////

  const handleCommentOperation = (operation) => {
    showQuestionDialog({
      type: "text",
      rows: 5,
      title: `Edit Operation Instruction for ${operation.name}`,
      description: `Enter manufacturing instructions that will appear above the operation in pre-production travelers.  Instructions are used to communicate any information the manufacturing line may need.`,
      value: operation.comment,
      multiline: true,
      options: operations,
      emptyAllowed: true,
      onSave: (answer) => {
        return buildDao
          .updateOperationComment(
            vyperNumber,
            buildNumber,
            operation.name,
            answer === "" ? null : answer
          )
          .catch(noop);
      },
    });
  };

  const handleUndoDelete = (operation) => {
    openConfirmation({
      title: "Restore Operation",
      message: `Are you sure you want to restore the operation "${operation.name}"?`,
      yesText: "Restore",
      noText: "Cancel",
      onYes: () => {
        return buildDao
          .restoreFlowOperation(vyperNumber, buildNumber, operation.name)
          .catch(noop);
      },
    });
  };

  // when the build status is changed (usually by approve or rework), re-fetch the build
  const handleStateChanged = (buildNumber, _state) => {
    buildDao.reload(buildNumber);
    setRunStateChange(false);
  };

  const handleSelectDiagramApproval = (build, diagram, approvalType) => {
    buildDao
      .approveDiagram(vyperNumber, buildNumber, diagram, approvalType)
      .catch(logError);
  };

  const classes = useStyles();

  if (build == null) {
    return null;
  }

  return (
    <div>
      <div className={classes.root}>
        <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />

        <div className={classes.optionBar}>
          <BuildPageTitle
            build={build}
            showState={true}
            title="Select Component Page"
          />

          <TemplateBanner build={build} />

          <Options
            className={classes.optionBar}
            build={build}
            tasks={tasks}
            options={options}
            onToggleAttribute={handleToggleAttribute}
            onToggleParagraph={handleToggleParagraph}
            onToggleComponent={handleToggleComponent}
            onToggleHeader={handleToggleHeader}
            onToggleEditButton={handleToggleEditButton}
            onToggleRejection={handleToggleRejection}
            onToggleComments={handleToggleComments}
            onToggleChangelog={handleToggleChangelog}
            onToggleDragDrop={handleToggleDragDrop}
            onClickFillUnselectedAtss={handleClickFillUnselectedAtss}
            onClickFillUnselectedVyper={handleClickFillUnselectedVyper}
            onClickFillUnselectedClear={handleClickFillUnselectedClear}
            enableUnselectedClear={enableUnselectedClear}
            isApprover={isApprover}
            isFrozen={isFrozen}
            buildState={build.state}
            groupsValidate={authUser.roles}
            onClickApprove={handleShowApproveDialog}
            onClickRework={handleShowReworkDialog}
            handleAddComment={handleAddComment}
            dragDropChanges={dragDropChanges}
            onDragDropSave={handleDragDropSave}
            onDragDropCancel={handleDragDropCancel}
          />
        </div>

        {options.comments ? (
          <div className={classes.comments}>
            <Comments vyper={vyper} build={build} />
          </div>
        ) : null}

        <h2>Traveler</h2>

        <pre>
          <Header build={build} options={options} />
        </pre>

        <div>
          <pre>
            <Body
              vyper={vyper}
              build={build}
              options={options}
              onEditOperation={handleEditOperationName}
              onAddOperation={handleAddOperation}
              onRemoveOperation={handleRemoveOperation}
              onEditComponentName={handleEditComponentName}
              onEditComponentValue={handleEditComponentValue}
              onAddComponent={handleAddComponent}
              onRemoveComponent={handleRemoveComponent}
              onClickValidate={handleClickValidate}
              onCommentOperation={handleCommentOperation}
              approvedGroups={approvedGroups}
              onUndoDelete={handleUndoDelete}
              onSelectDiagramApproval={handleSelectDiagramApproval}
              unApprovedGroups={unApprovedGroups}
              dragDropChanges={dragDropChanges}
              onOperationReorder={handleOperationReorder}
              onComponentReorder={handleComponentReorder}
            />
          </pre>
        </div>

        {options.changelog && <ChangeLog buildNumber={buildNumber} />}

        <PollForStateChanged
          run={runStateChange}
          buildNumber={build.buildNumber}
          onStatusChanged={handleStateChanged}
        />

        <ApprovalDialog
          open={showApprovalDialog}
          approvalGroups={unApprovedGroups}
          onApprove={handleApproveBuild}
          onClose={handleCloseApproveDialog}
        />
        <ReworkDialog
          open={showReworkDialog}
          reworkGroups={unApprovedGroups}
          onRework={handleReworkBuild}
          onClose={handleCloseReworkDialog}
          buildState={build.state}
        />
        <ApprovalBlockersDialog
          open={showBlockingDialog}
          reasons={blockingReasons}
          onClose={handleCloseBlockingDialog}
        />
      </div>

      <AtssTravelerDialog
        open={showTravelerDialog}
        defaultSpecDevice={build.material.object.OldMaterial}
        defaultFacilityAt={build.facility.object.PDBFacility}
        defaultStatus="ACTIVE"
        onClose={() => setShowTravelerDialog(false)}
        onSelect={handleFillComponentAtss}
        title="Fill Unselected from ATSS"
      />

      <BuildNumberDialog
        open={showBuildNumberDialog}
        onClose={() => setShowBuildNumberDialog(false)}
        onSelect={handleFillComponentVyper}
        title="Fill Unselected from VYPER"
      />
    </div>
  );
};
