<!DOCTYPE html>

<html lang="en">
<head>
    <script>
        //
        // NOTE: Modify per app.
        //
        var appName = "@ti/vyper-ui";
        var appPort = 3000;
        //
        // NOTE: Modify typically not needed.
        //
        var playgroundServer = "http://simba-dev.itg.ti.com/playground/instant-test?name=" + appName + "&url=" + appPort;
        var playgroundLocalhost = "http://localhost.dhcp.ti.com:10000/playground/instant-test?name=" + appName + "&url=" + appPort;
    </script>
    <style>
        body {
            font-family: "Helvetica Neue",
            Helvetica,
            Arial,
            sans-serif;
            background-color: #e5e5e5;
            color: black;
        }

        .highlight {
            background-color: #ffff00;
            color: #000000;
        }

        .app {
            color: black; /*#f4b821*/
            font-size: larger;
            font-weight: bold;
        }

        .danger {
            color: #cc0000;
        }

        .header {
            color: #cc0000;
        }

        .button {
            background-color: #cc0000;
            border: none;
            color: white;
            padding: 10px 22px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }

        a:link,
        a:visited,
        a:active {
            color: #118899;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
    <title></title>
</head>

<body>
<p><span class="app" id="app-name"></span> is a <a href="https://single-spa.js.org/docs/microfrontends-concept/" target="_blank">microfrontend</a> which runs
    within a <a href="https://single-spa.js.org/docs/getting-started-overview" target="_blank">single-spa</a> container. When developing, a <i>playground</i>
    container is used.</p>
<h2 class="header">Server Playground</h2>
<p>If your app uses APIs which are already running on the server, you can select this option.</p>
<p>
    <button class="button" onclick="handleServerClick()">Run in server playground</button>
</p>
<hr/>
<h2 class="header">Localhost Playground</h2>
<p>If your app uses any APIs that are running on your localhost, then you will need to run a <i>playground</i>
    container on your localhost and add API proxies as needed.</a></p>
<ol>
    <li>Download the <a href="https://bitbucket.itg.ti.com/projects/SIMBA/repos/playground/browse"
                        target="_blank">playground</a>
    </li>
    <li>Modify the proxies in <a href="https://bitbucket.itg.ti.com/projects/SIMBA/repos/playground/browse/webpack.config.js"
                                 target="_blank">webpack.config.js</a> using below as an example, or check <a
            href="https://webpack.js.org/configuration/dev-server/#devserverproxy"
            target="_blank">full reference</a>.</p>
        <pre>
  proxy: { 
    <span class="danger">"/api"</span>: { <span class="highlight">// NOTE: No app should be using <span class="danger">/api</span>, without an app prefix - i.e. rex/api, rex/data, vyper/api, vyper/api2, etc.</span>
      target: "http://simba-playground.itg.ti.com",
      changeOrigin: true
    },
    "/simba": {
      target: "http://simba-playground.itg.ti.com",
      changeOrigin: true
    },
    "/vyper": {
      target: "http://localhost:3000",
      changeOrigin: false
    },
    "/vyper/v1/api": {
      target: "http://localhost:3000",
      changeOrigin: false
    },
    "/vyper/mock": {
      target: "http://localhost:3000",
      changeOrigin: false
    }
}
</pre>
    <li>Once you have the <i>playground</i> running on your localhost with the required API proxies in place, select
        this option.
    </li>
</ol>
<p>
    <button class="button" onclick="handleLocalhostClick()">Run in localhost playground</button>
</p>
<script>
    function handleServerClick() {
        window.location.assign(playgroundServer)
    }

    function handleLocalhostClick() {
        window.location.assign(playgroundLocalhost)
    }

    document.addEventListener("DOMContentLoaded", function () {
        document.getElementById('app-name').innerHTML = appName;
    });
</script>
</body>

</html>
