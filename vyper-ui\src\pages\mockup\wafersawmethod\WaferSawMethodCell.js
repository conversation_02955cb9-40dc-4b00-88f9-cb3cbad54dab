import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import {
  hasFacility,
  hasMaterial,
  existsInFlow,
} from "src/pages/vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";

export const WaferSawMethodCell = ({ vyper, build, onClick }) => {
  const { canEditWaferSawMethod } = useContext(HelperContext);
  const canEdit = canEditWaferSawMethod(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !existsInFlow(build, "Wafer Saw Method")
  )
    return null;

  return (
    <DataCell source={build.waferSawMethod?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        {build.waferSawMethod?.object?.value || "click to select"}
      </VyperLink>
    </DataCell>
  );
};
