import React from "react";
import { VscnOperation } from "./VscnOperation";

export const VscnBody = ({
  vscn,
  vyper,
  build,
  options,
  onEditComponentValue,
}) => {
  return (
    <div>
      {vscn.traveler.operations
        .filter((operation) => operation.subflowType === "PACK")
        .map((operation, n) => (
          <VscnOperation
            key={n}
            vyper={vyper}
            build={build}
            operation={operation}
            options={options}
            onEditComponentValue={(o, c, cPos) =>
              onEditComponentValue(o, c, n, cPos)
            }
          />
        ))}
    </div>
  );
};
