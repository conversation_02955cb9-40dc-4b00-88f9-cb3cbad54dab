import { TableCell, TableRow } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../RowPrefix";
import { BuildNumberCell2 } from "./BuildNumberCell2";

/**
 * Display the row of build number headers.
 *
 * @param {*[]} items - The items to display
 * @param {string} help - The help text key
 * @param {string} title - The row's header text
 * @param {BuildNumberCell2~onGetConfig} onGetConfig - The callback function to get the item's configuration
 * @param {BuildNumberCell2~onClickMenu} onClickMenu - Called when the user clicks o menu item.
 * @return {JSX.Element}
 * @constructor
 */
export function BuildNumberRow2({
  items,
  help,
  title,
  onGetConfig,
  onClickMenu,
}) {
  return (
    <TableRow hover>
      <RowPrefix help={help} title={title} />
      {items.map((item, n) => (
        <TableCell key={n}>
          <BuildNumberCell2
            item={item}
            onGetConfig={onGetConfig}
            onClickMenu={onClickMenu}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

BuildNumberRow2.propTypes = {
  items: PropTypes.array.isRequired,
  help: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  onGetConfig: PropTypes.func.isRequired,
  onClickMenu: PropTypes.func.isRequired,
};
