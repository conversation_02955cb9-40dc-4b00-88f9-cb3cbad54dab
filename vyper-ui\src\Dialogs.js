import React from "react";
import { NewBuildDialog } from "src/component/newBuild/NewBuildDialog";
import { DataModels } from "src/DataModel";
import { AddPraDialog } from "src/pages/home/<USER>/AddPraDialog";
import { CreateDialog } from "src/pages/home/<USER>/CreateDialog";
import { AddOperationDialog } from "src/pages/mockup/operation/AddOperationDialog";
import { RemoveOperationDialog } from "src/pages/mockup/operation/RemoveOperationDialog";
import { VerifierDialog } from "src/pages/vyper/pras/VerifierDialog";
import { AlertDialog } from "./component/alert/AlertDialog";
import { ComponentDialog } from "./component/build-component/ComponentDialog";
import { CommentsDialog } from "./component/comments/CommentsDialog";
import { ConfirmationDialog } from "./component/cornfirmation/ConfirmationDialog";
import { DieDialog } from "./component/die/DieDialog";
import { FileUploadDialog } from "./component/fileupload/FileUploadDialog";
import { QuestionDialog } from "./component/question/QuestionDialog";
import { UserDialog } from "./component/user/UserDialog";
import { ViewDialog } from "./component/view/ViewDialog";
import { AtSelectionDialog } from "./pages/mockup/atselection/AtSelectionDialog";
import { AuditDialog } from "./pages/mockup/audit/AuditDialog";
import { ChangeDialog } from "./pages/mockup/changelink/ChangeDialog";
import { PcnDialog } from "./pages/mockup/changelink/PcnDialog";
import { CopyBuildDialog } from "./pages/mockup/copybuild/CopyBuildDialog";
import { SelectUserDialog } from "./pages/mockup/owners/SelectUserDialog";
import { PackageNicheDialog } from "./pages/mockup/packageniche/PackageNicheDialog";
import { TurnkeyDialog } from "./pages/mockup/turnkey/TurnkeyDialog";
import { WaferSawMethodDialog } from "./pages/mockup/wafersawmethod/WaferSawMethodDialog";
import { RejectionDialog } from "./pages/mockup/workflow/RejectionDialog";
import { SingleSelectionDialog } from "./component/component/SingleSelectionDialog";

export const Dialogs = ({ children }) => {
  return (
    <div>
      <AlertDialog>
        <DataModels>
          <UserDialog>
            <QuestionDialog>
              <DieDialog>
                <SelectUserDialog>
                  <ComponentDialog>
                    <SingleSelectionDialog>
                      <AtSelectionDialog>
                        <FileUploadDialog>
                          <ViewDialog>
                            <CommentsDialog>
                              <AuditDialog>
                                <ConfirmationDialog>
                                  <TurnkeyDialog>
                                    <WaferSawMethodDialog>
                                      <ChangeDialog>
                                        <PcnDialog>
                                          <PackageNicheDialog>
                                            <RejectionDialog>
                                              <CopyBuildDialog>
                                                <AddOperationDialog>
                                                  <RemoveOperationDialog>
                                                    <NewBuildDialog>
                                                      <AddPraDialog>
                                                        <CreateDialog>
                                                          <VerifierDialog>
                                                            {children}
                                                          </VerifierDialog>
                                                        </CreateDialog>
                                                      </AddPraDialog>
                                                    </NewBuildDialog>
                                                  </RemoveOperationDialog>
                                                </AddOperationDialog>
                                              </CopyBuildDialog>
                                            </RejectionDialog>
                                          </PackageNicheDialog>
                                        </PcnDialog>
                                      </ChangeDialog>
                                    </WaferSawMethodDialog>
                                  </TurnkeyDialog>
                                </ConfirmationDialog>
                              </AuditDialog>
                            </CommentsDialog>
                          </ViewDialog>
                        </FileUploadDialog>
                      </AtSelectionDialog>
                    </SingleSelectionDialog>
                  </ComponentDialog>
                </SelectUserDialog>
              </DieDialog>
            </QuestionDialog>
          </UserDialog>
        </DataModels>
      </AlertDialog>
    </div>
  );
};
