# Building

Vyper uses the maven wrapper as the build engine.
* mvnw clean install

# Releasing

To release a new version, create a release branch, merge into develop,
then merge into master, then create a new snapshot branch and merge into develop.

Note: This example shows releasing version 1.0.0, then starting version 1.0.1-SNAPSHOT. Change your versions accordingly.

1. In bitbucket, create a release branch
    * Name the branch as the release version (ex: 1.0.0)
    * Create a branch from the develop branch.


2. Update your local repository
    * `git pull`


3. Checkout the release Branch
    * `git checkout release/1.0.0`


4. Update the version numbers
    * update file /pom.xml (line ~21)
    
    `<version>1.0.0</version>`
    * update file /vyper-build/pom.xml (line ~16)
     
    `<version>1.0.0</version>`
    * vyper-ui/package.json (line ~3)

    `"version": "1.0.0"`


5. Update package.lock
    * Open a terminal
   
   `cd vyper-ui`

    `npm install`


6. Commit the changes

    `git commit -a -m "bump version to 1.0.0"`


7. Push the changes to bitbucket

   `git push`


8. In bitbucket create a pull request.
    * source branch: release/1.0.1-SNAPSHOT
    * destination branch: develop
    * click Continue
    * click Create


9. Merge the pull request
    * click Merge
    * un-check the "delete source branch after merging"
    * Click Merge.


10. Watch jenkins for successful build.
    * ui (dev): https://sctmg-jenkins-prod.itg.ti.com/job/dhms/job/simba/job/vyper-ui-dev/
    * api (dev): https://sctmg-jenkins-prod.itg.ti.com/blue/organizations/jenkins/specteam%2Fvyper-dev/activity/


11. in BitBucket, create another pull request
    * source branch: release/1.0.1-SNAPSHOT
    * destination branch: master or stage
    * click Continue
    * click Create


12. Merge the pull request
    * click Merge
    * check the "delete source branch after merging"
    * Click Merge.


13. Watch jenkins for successful build.
    * ui: (stage): https://sctmg-jenkins-prod.itg.ti.com/job/dhms/job/simba/job/vyper-ui-stage/
    * ui: (prod): https://sctmg-jenkins-prod.itg.ti.com/job/dhms/job/simba/job/vyper-ui-prod/
    * api (stage): https://sctmg-jenkins-prod.itg.ti.com/job/specteam/job/vyper-stage/
    * api: (prod): https://sctmg-jenkins-prod.itg.ti.com/blue/organizations/jenkins/specteam%2Fvyper-prod/activity/


14. update git in your terminal

    `git checkout develop`

    `git pull`


15. Create a release branch for the new snapshot
    
    `git checkout -b release/1.0.1-SNAPSHOT`


16. Update your local repository
    
    `git pull`


17. Checkout the release Branch
    
    `git checkout develop/1.0.1-SNAPSHOT`


18. Update the version numbers

    * update file /pom.xml (line ~21)
      
    `<version>1.0.1-SNAPSHOT</version>`
    
    * update file /vyper-build/pom.xml (line ~16)
    
    `<version>1.0.1-SNAPSHOT</version>`

    * vyper-ui/package.json (line ~3)
    
    `"version": "1.0.1-SNAPSHOT,`


19. Update package.lock
* Open a terminal

    `cd vyper-ui`
 
    `npm install`

20. Commit the changes

    `git commit -a -m "bump version to 1.0.1-SNAPSHOT`


21. Push the changes to bitbucket

    `git push --set-upstream origin release/snapshot-1.0.1`


22. In bitbucket create a pull request.
    * source branch: develop/1.0.1-SNAPSHOT
    * destination branch: develop
    * click Continue
    * click Create


23. Merge the pull request
    * click Merge
    * check the "delete source branch after merging"
    * Click Merge.       
   