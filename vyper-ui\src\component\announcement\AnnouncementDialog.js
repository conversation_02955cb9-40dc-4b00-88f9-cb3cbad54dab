import React from "react";

import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogActions from "@material-ui/core/DialogActions";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import Button from "@material-ui/core/Button";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";

const useStyles = makeStyles((theme) => ({
  root: {},

  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialog: {
    padding: "3rem",
    margin: "3rem",
  },
}));

export const AnnouncementDialog = ({ announcement, onClose, onMarkAsRead }) => {
  const handleMarkAsRead = () => onMarkAsRead(announcement);

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Dialog open={true} onClose={onClose} maxWidth="md">
        <DialogTitle>Announcement</DialogTitle>

        <IconButton className={classes.closeButton} onClick={onClose}>
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>
            <h4>{announcement.teaser}</h4>
            <hr />
            <span>{announcement.message}</span>
          </DialogContentText>
        </DialogContent>

        <DialogActions>
          <Button variant="contained" color="primary" onClick={onClose}>
            Close
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleMarkAsRead}
          >
            Mark As Read
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
