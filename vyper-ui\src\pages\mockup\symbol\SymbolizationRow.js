import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessSymbolization } from "../../../component/sameness/sameness";
import { SymbolCell } from "./SymbolCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const SymbolizationRow = ({ vyper, builds, onChange, showSameness }) => {
  const { buildDao } = useContext(DataModelsContext);

  const classes = useStyles();

  const handleSave = (build, location, name, picture, ecat, custs) => {
    return buildDao
      .changeSymbolization(
        vyper.vyperNumber,
        build.buildNumber,
        { location, name, picture },
        custs,
        ecat
      )
      .then((json) => onChange(json))
      .catch(noop);
  };

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessSymbolization(builds),
      })}
      hover
    >
      <RowPrefix help="symbolization" title="Symbolization" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <SymbolCell vyper={vyper} build={build} onSave={handleSave} />
        </TableCell>
      ))}
    </TableRow>
  );
};
