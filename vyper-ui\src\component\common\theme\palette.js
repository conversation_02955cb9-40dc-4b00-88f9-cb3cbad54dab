const primary = "#cc0000";
const primaryLight = "#ffffff";
const primaryContrastText = "#ffffff";
const secondary = "#118899";
const secondaryLight = "#ffffff";
const secondaryContrastText = "#ffffff";
const alertError = "#dd0000";
const alertErrorContrastText = "#ffffff";
const alertSuccess = "#4caf50";
const alertSuccessContrastText = "#ffffff";
const alertWarn = "#f3cd34";
const alertWarnContrastText = "#ffffff";
const alertInfo = "#cccccc";
const alertInfoContrastText = "#ffffff";

// Styling per TI Brand site - http://brand.ti.com/pages/bel-color.shtml
export default {
  primary: {
    main: primary,
    light: primaryLight,
    contrastText: primaryContrastText,
  },
  secondary: {
    main: secondary,
    light: secondaryLight,
    contrastText: secondaryContrastText,
  },
  success: {
    main: alertSuccess,
    contrastText: alertSuccessContrastText,
  },
  info: {
    main: alertInfo,
    contrastText: alertInfoContrastText,
  },
  warning: {
    main: alertWarn,
    contrastText: alertWarnContrastText,
  },
  error: {
    main: alertError,
    contrastText: alertErrorContrastText,
  },
};
