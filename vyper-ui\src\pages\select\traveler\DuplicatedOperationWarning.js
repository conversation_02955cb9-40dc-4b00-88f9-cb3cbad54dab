import makeStyles from "@material-ui/core/styles/makeStyles";
import React from "react";

const useStyles = makeStyles({
  highlight: {
    marginTop: "1em",
    marginBottom: "1em",
    padding: 7,
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
});

/**
 * Show the duplicated operation warning.
 *
 * @returns {JSX.Element}
 * @constructor
 */
export default function DuplicatedOperationWarning() {
  const classes = useStyles();
  return (
    <>
      <span> - </span>
      <span className={classes.highlight}>
        Duplicated operation. One must be deleted before validation or approval.
      </span>
    </>
  );
}
