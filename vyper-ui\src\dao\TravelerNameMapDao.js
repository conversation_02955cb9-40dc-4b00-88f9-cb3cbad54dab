import { DaoBase } from "src/component/fetch/DaoBase";

export class TravelerNameMapDao extends DaoBase {
  constructor(params) {
    super({
      name: "TravelerNameMapDao",
      url: "/vyper/v1/atss/traveler/name-map",
      ...params,
    });
  }

  list(page = 0, size = 20, sort = "componentName,asc") {
    return this.handleFetch(
      "list",
      `/?page=${page}&size=${size}&sort=${sort}`,
      "GET"
    );
  }

  create(travelerNameMap) {
    return this.handleFetch("create", ``, "POST", travelerNameMap);
  }

  update(id, travelerNameMap) {
    return this.handleFetch("update", `/${id}`, "POST", travelerNameMap);
  }

  delete(id) {
    return this.handleFetch("delete", `/${id}`, "DELETE");
  }
}
