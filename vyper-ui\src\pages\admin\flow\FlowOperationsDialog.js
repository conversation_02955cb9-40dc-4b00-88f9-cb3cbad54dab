import React, { useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import TextField from "@material-ui/core/TextField";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import MenuItem from "@material-ui/core/MenuItem";
import { Select } from "@material-ui/core";
import { noop } from "src/component/vyper/noop";

export const FlowOperationsDialog = ({ children }) => {
  const styles = makeStyles((theme) => ({
    closeButton: {
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
  }));

  const classes = styles();

  const [open, setOpen] = useState(false);
  const [parameters, setParameters] = useState({});

  /**
   *
   * @param {object} parameters
   * @param {string} parameters.type text, select
   * @param {string} parameters.title
   * @param {string} parameters.description
   * @param {string} parameters.value
   * @param {string} parameters.onSave
   * @param {string} parameters.onClose   optional callback for when the dialog is closed with no answer save.
   * @param {string} parameters.multiline
   * @param {string} parameters.rows
   * @param {string} parameters.options
   * @param {string} parameters.emptyAllowed  user is allowed to save with no selected value (default to false);
   */
  const handleOpen = (parameters) => {
    parameters.onSave = parameters.onSave || noop;
    parameters.onClose = parameters.onClose || noop;
    parameters.emptyAllowed = parameters.emptyAllowed || false;
    setParameters(parameters);
    setOpen(true);
  };

  const handleClose = () => {
    parameters.onClose();
    setParameters({});
    setOpen(false);
  };

  const handleChange = (e) =>
    setParameters({ ...parameters, value: e.target.value });

  const handleSave = () => {
    parameters.onSave(parameters.value);
    parameters.onClose();
    handleClose();
  };

  // determine if save button is enabled / disabled
  const disabled = parameters.emptyAllowed
    ? false
    : parameters.value == null ||
      parameters.value === "" ||
      parameters.value.trim().length === 0;

  return (
    <FlowOperationsDialogContext.Provider
      value={{
        showOperationsDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>{parameters.title}</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>{parameters.description}</DialogContentText>

          <TextField
            autoFocus
            fullWidth
            select
            margin="dense"
            name="value"
            value={parameters.value || ""}
            onChange={handleChange}
            variant="outlined"
            label="Select Flow"
          >
            {parameters.options.map((facility) => (
              <MenuItem key={facility} value={facility}>
                {facility}
              </MenuItem>
            ))}
          </TextField>

          <TextField
            autoFocus
            fullWidth
            select
            margin="dense"
            name="value"
            value={parameters.value || ""}
            onChange={handleChange}
            variant="outlined"
            label="Select Row"
          >
            {parameters.options.map((facility) => (
              <MenuItem key={facility} value={facility}>
                {facility}
              </MenuItem>
            ))}
          </TextField>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSave}
            variant="contained"
            color="primary"
            disabled={disabled}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </FlowOperationsDialogContext.Provider>
  );
};

export const FlowOperationsDialogContext = React.createContext(null);
