export const BASE_FETCH_DATA_URL = "/vyper/v1/fetch";
export const BASE_POST_DATA_URL = "/vyper/v1/post";
export const BASE_FETCH_OPTIONS_URL = "/vyper/v1/fetchOptions";
export const BASE_VSCSWR_URL = "/vyper/v1/VSCSWR";

export const numberFormatter = (params) => {
  const { value } = params;
  if (!value) {
    return "";
  }
  return value.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
};

export const findInvalidFields = (fields, required) => {
  let emptyFields = [];
  required.forEach((reqField, i) => {
    const { title, name } = reqField;
    const fieldValue = fields[name];
    if (!fieldValue || fieldValue.trim().length === 0) {
      emptyFields.push(title);
    }
  });
  return emptyFields;
};

export const isValidSubFields = (listFields, required) => {
  for (const fields of listFields) {
    for (const reqField of required) {
      const field = fields[reqField.name];
      if (field.length > 0) {
        return true;
      }
    }
  }
  return false;
};

export const DEFAULT_GENERAL_INFO_VALS = {
  title: "",
  swrID: "",
  currentStatus: "",
  purpose: "",
  currentRequestor: "",
  plant: "",
  facility: "",
  requestDate: "",
  swrType: "",
  buildtype: "",
  priority: "",
  vbuildID: "",
  existingScswrID: "",
  purchaseOrder: "",
  lineItem: "",
  io: "",
  groupEmail: "",
  copyEmail: "",
};

export const DEFAULT_REQUESTOR_INFO_VALS = {
  name: "",
  phone: "",
  email: "",
  itssID: "",
  costCenter: "",
  group: "",
};

export const DEFAULT_DEVICE_INFO_VALS = {
  swrID: "",
  sapMaterial: "",
  specDevice: "",
  sapBaseMaterial: "",
  sbe: "",
  sbe1: "",
  sbe2: "",
  industrySector: "",
  pin: "",
  pkg: "",
  pkgGroup: "",
  profitCenter: "",
  buildQuantity: "",
  apl: "",
  iso: "",
  mcm: "",
  offloadInfo: "",
};

export const DEFAULT_DIE_LOT_INFO_VALS = {
  matlMasterDieName: "",
  dieLot: "",
  plant: "",
  matShipStatus: "",
  deliveryNote: "",
  qtyToShip: "",
  dateShipped: "",
  sapWaybill: "",
};

export const DEFAULT_ASSEMBLY_INFO_VALS = {
  assemblyReq: "yes",
  header: "",
  baseOutline: "",
  wireDiameter: "",
  bondPadMetalization: "",
  mbPath: "",
  mbOrArcPath: "",
  forecastedFlag: "",
};

export const DEFAULT_PACKING_REQUIREMENTS_INFO_VALS = {
  stickerType: "",
  eWaiver: "",
  isRetestRMR: "",
  finishedGoodsDispo: "",
  waferSkeleton: "",
  plantCode: "",
  pdcUnrestrictedSale: "",
};

export const DEFAULT_SHIPPING_INFO_VALS = {
  attention: "",
  mailStation: "",
  plant: "",
  address: "",
  quantity: "",
  stateOfFinish: "",
  shipDeviceName: "",
};

export const VAR_TO_SCSWR_TITLES = {
  currentStatus: "Current Status",
  title: "SWR Title",
  atSite: "A/T",
  plantCode: "",
  sbe1: "SBE-1",
  profitCenter: "WW Profit Center",
  costCenter: "Cost Center",
  io: "IO #",
  itssID: "ITSS ID",
  email: "Requestor Email",
  name: "Requestor Name",
  phone: "Requestor Phone",
  requestDate: "Request Date",
  sapMaterial: "Matl Master Device Name",
  sapBaseMaterial: "Parent Device",
  pin: "Pin",
  pkg: "Pkg",
  specDevice: "Spec Device",
  purpose: "Purpose",
  generalComment: "SWR General Comments",
  pkgGroup: "Package Class",
  industrySector: "Market Category",
  lineItem: "Line Item",
  apl: "APL",
  mcm: "MCM",
  iso: "ISO",
  dieLot1: "Die Lot 1",
  dieName1: "Die Name 1",
  dieRev1: "Die Rev1",
  dieDesignator1: "Die Designator 1",
  dieSize1: "Die Size 1",
  scribeWidth1: "Scribe Width 1",
  fabCode1: "Fab Code 1",
  fabTech1: "Fab Tech 1",
  waferDiameter1: "Wafer Diameter 1",
  waferThickness1: "Wafer Thickness 1",
  backgrind1: "Backgrind 1",
  dualDie: "Dual Die",
  dieLot2: "Die Lot 2",
  dieName2: "Die Name 2",
  dieRev2: "Die Rev 2",
  dieDesignator2: "Die Designator 2",
  dieSize2: "Die Size 2",
  scribeWidth2: "Scribe Width 2",
  fabCode2: "Fab Code 2",
  fabTech2: "Fab Tech 2",
  waferDiameter2: "Wafer Diameter 2",
  waferThickness2: "Wafer Thickness 2",
  backgrind2: "Backgrind 2",
  tripleDie: "Triple Die",
  dieLot3: "Die Lot 3",
  dieName3: "Die Name 3",
  dieRev3: "Die Rev 3",
  dieDesignator3: "Die Designator 3",
  dieSize3: "Die Size 3",
  scribeWidth3: "Scribe Width 3",
  fabCode3: "Fab Code 3",
  fabTech3: "Fab Tech 3",
  waferDiameter3: "Wafer Diameter 3",
  waferThickness3: "Wafer Thickness 3",
  backgrind3: "Backgrind 3",
  quadDie: "Quad Die",
  dieLot4: "Die Lot 4",
  dieName4: "Die Name 4",
  dieRev4: "Die Rev 4",
  dieDesignator4: "Die Designator 4",
  dieSize4: "Die Size 4",
  scribeWidth4: "Scribe Width 4",
  fabCode4: "Fab Code 4",
  fabTech4: "Fab Tech 4",
  waferDiameter4: "Wafer Diameter 4",
  waferThickness4: "Wafer Thickness 4",
  backgrind4: "Backgrind 4",
  quintDie: "Quint Die",
  dieLot5: "Die Lot 5",
  dieName5: "Die Name 5",
  dieRev5: "Die Rev 5",
  dieDesignator5: "Die Designator 5",
  dieSize5: "Die Size 5",
  scribeWidth5: "Scribe Width 5",
  fabCode5: "Fab Code 5",
  fabTech5: "Fab Tech 5",
  waferDiameter5: "Wafer Diameter 5",
  waferThickness5: "Wafer Thickness 5",
  backgrind5: "Backgrind 5",
  travelerStatus: "Traveler Status",
  assemblyReq: "Assembly/Bump Requirements",
  shipType: "Material Shipped",
  deliveryNote: "Delivery Note",
  dateShipped: "Date Shipped",
  invoice: "Invoice",
  qtyToShip: "Qty to Ship",
  sapWaybill: "SAP Waybill #",
  header: "Header",
  leadFrame: "Leadframe/substrate",
  moldCompound: "Mold Compound",
  mountCompound: "Mount Compound",
  wire: "Wire P/N",
  solder: "Solderball",
  lid: "Lid",
  chipCap: "Chip Capacitor",
  bumpReq: "Bump Required",
  wireDiameter: "Wire Diameter",
};
