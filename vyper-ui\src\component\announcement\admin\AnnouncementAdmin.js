import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import DataGrid from "src/component/universal/DataGrid/DataGrid";
import {
  gridActionDeleteRecord,
  gridActionEditRecord,
} from "src/component/universal";
import AddIcon from "@material-ui/icons/Add";
import moment from "moment";
import { AnnouncementAdminDialog } from "src/component/announcement/admin/AnnouncementAdminDialog";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";
import { AnnouncementDao } from "src/dao/AnnouncementDao";
import { AuthContext } from "src/component/common";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { SpinnerContext } from "src/component/Spinner";
import {
  AlertDialogError<PERSON>andler,
  SpinnerLoading<PERSON>andler,
} from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

const useStyles = makeStyles({
  root: {},
  closeButton: {
    position: "absolute",
    right: "1px",
    color: "grey",
  },
  autocomplete: {
    minWidth: "13rem",
  },
  dialog: {
    padding: "3rem",
    margin: "3rem",
  },
});

export const AnnouncementAdmin = () => {
  const { authUser } = useContext(AuthContext);

  const blankAnnouncement = {
    teaser: null,
    message: null,
    author: authUser.uid,
    authorName: authUser.name,
    startDttm: null,
    endDttm: null,
    readRequired: "NO",
  };

  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);

  const [show, setShow] = useState(false);
  const [announcement, setAnnouncement] = useState(blankAnnouncement);
  const [announcements, setAnnouncements] = useState([]);

  const announcementDao = new AnnouncementDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  // load the list of announcements
  useEffect(() => {
    refresh();
  }, []);

  const refresh = () => {
    announcementDao
      .list()
      .then((json) => setAnnouncements(json.content))
      .catch(noop);
  };

  // columns for the data grid
  const columns = [
    { field: "id", title: "ID" },
    { field: "teaser", title: "Teaser" },
    {
      field: "startDttm",
      title: "Start Date",
      render: (rowData) => moment(rowData.startDttm).fromNow(),
      filtering: false,
    },
    {
      field: "endDttm",
      title: "End Date",
      render: (rowData) => moment(rowData.endDttm).fromNow(),
      filtering: false,
    },
  ].map((item) => {
    // add styles to the cells
    return {
      ...item,
      cellStyle: {
        width: 1000,
        maxWidth: 1000,
      },
      headerStyle: {
        width: 1000,
        maxWidth: 1000,
      },
    };
  });

  // user clicked the + button to add an announcement
  // reset the announcement, and show the dialog
  const handleAdd = () => {
    setAnnouncement(blankAnnouncement);
    setShow(true);
  };

  // user clicked the edit button
  const handleEdit = (event, row) => {
    setAnnouncement(row);
    setShow(true);
  };

  const handleSave = (announcement) => {
    setShow(false);

    // set the fetch options

    if (announcement.id == null) {
      announcementDao
        .create(announcement)
        .then((json) => setAnnouncements([...announcements, json]))
        .catch(noop);
    } else {
      announcementDao
        .update(announcement)
        .then((json) => {
          // replace the announcement
          const pos = announcements.findIndex((a) => a.id === json.id);
          if (-1 !== pos) {
            setAnnouncements([
              ...announcements.slice(0, pos),
              json,
              ...announcements.slice(pos + 1),
            ]);
          } else {
            setAnnouncements([...announcements, json]);
          }
        })
        .catch(noop);
    }
  };

  const handleDelete = (e, row) => {
    openConfirmation({
      title: "Delete Announcement",
      message: `Are you sure you want to delete announcement # ${row.id}?`,
      yesText: "Yes",
      noText: "No",
      onYes: () => {
        announcementDao
          .delete(row.id)
          .then(() =>
            setAnnouncements(announcements.filter((a) => a.id !== row.id))
          )
          .catch(noop);
      },
    });
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Announcements"
        data={announcements}
        columns={columns}
        actions={[
          gridActionEditRecord(handleEdit),
          gridActionDeleteRecord(handleDelete),
          {
            icon: () => <AddIcon />,
            isFreeAction: true,
            onClick: handleAdd,
          },
        ]}
      />
      {show && (
        <AnnouncementAdminDialog
          show={true}
          announcement={announcement}
          onClose={() => setShow(false)}
          onSave={handleSave}
        />
      )}
    </div>
  );
};
