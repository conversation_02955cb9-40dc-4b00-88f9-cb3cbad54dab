import { makeStyles } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";

const useStyles = makeStyles(() => ({
  root: {
    color: "black",
    padding: "1rem",
    margin: "1rem",
    backgroundColor: "#eeeeee",
    borderRadius: "0.25rem",
  },
}));

/**
 * Display a message for the bom components.
 *
 * @param pin
 * @param pkg
 * @param facility
 * @returns {JSX.Element}
 * @constructor
 */
export const BomFor = ({ pin, pkg, facility }) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      The available components list comes from the ATSS bom for pin = {pin},
      package = {pkg}, facility = {facility}.
    </div>
  );
};

BomFor.propTypes = {
  pin: PropTypes.number.isRequired,
  pkg: PropTypes.string.isRequired,
  facility: PropTypes.string.isRequired,
};
