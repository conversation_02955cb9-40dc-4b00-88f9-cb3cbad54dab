import React from "react";
import PropTypes from "prop-types";
import { AtssTemplateBanner } from "src/pages/mockup/template/AtssTemplateBanner";
import { SimilarTemplateBanner } from "src/pages/mockup/template/SimilarTemplateBanner";
import { VyperTemplateBanner } from "src/pages/mockup/template/VyperTemplateBanner";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles((theme) => ({
  template: {
    marginBottom: theme.spacing(2),
  },
}));

/**
 * Display a banner if the template type is not Bill of Process Template
 *
 * @param {object} build - the build object
 * @return {JSX.Element}
 * @constructor
 */
export const TemplateBanner = ({ build }) => {
  const classes = useStyles();

  switch (build?.templateSource?.templateType) {
    case "ATSS_FULL_COPY":
    case "ATSS":
      return (
        <div className={classes.template}>
          <AtssTemplateBanner build={build} />
        </div>
      );

    case "SIMILAR_PKGNICHE":
      return (
        <div className={classes.template}>
          <SimilarTemplateBanner build={build} />
        </div>
      );

    case "VYPER":
      return (
        <div className={classes.template}>
          <VyperTemplateBanner build={build} />
        </div>
      );
  }

  // for null, undefined or DEVICE_PKGNICHE, don't show a banner
  return null;
};

TemplateBanner.propTypes = {
  build: PropTypes.object.isRequired,
};
