export const headerItems = (build) => {
  const revision =
    build?.buildState === "FINAL_APPROVED" ? "ACTIVE" : "WORKING";

  let specDevice = build?.multiBuild?.specDevice;
  if (specDevice === "New Multi-Build") {
    specDevice = build?.material?.object?.OldMaterial + "--";
  }

  return [
    { title: "Current Date / Time", value: new Date().toLocaleString() },
    { title: "Pin", value: build?.material?.object?.PackagePin },
    { title: "Old Material", value: build?.material?.object?.OldMaterial },
    { title: "PKG", value: build?.material?.object?.PackageDesignator },
    { title: "Spec Device", value: specDevice },
    { title: "Package Group", value: build?.material?.object?.PackageGroup },
    { title: "A/T", value: build?.facility?.object?.PDBFacility },
    { title: "SPQ", value: build?.material?.object?.SPQ },
    { title: "Revision", value: revision },
    { title: "MOQ", value: build?.material?.object?.MOQ },
    { title: "SBE", value: build?.material?.object?.SBE },
    { title: "Industry Sector", value: build?.materialExtras?.industrySector },
    { title: "SBE-1", value: build?.material?.object?.SBE1 },
    { title: "Customer", value: "" }, // TODO: implement customer
    { title: "SBE-2", value: build?.material?.object?.SBE2 },
    { title: "Cust Part Name", value: "" }, // TODO: implement cust part name
    { title: "SAP Material", value: build?.material?.object?.Material },
    { title: "Cust Print Name", value: "" }, // TODO: implement cust print name
    { title: "SAP Base Material", value: build?.materialExtras?.basicMaterial },
    { title: "Cust Print Revision", value: "" }, // TODO: implement cust print revision
    { title: "Niche", value: build?.materialExtras?.niche },
    { title: "Type 4", value: "" }, // TODO: implement type4
    { title: "PRESP", value: build?.materialExtras?.presp },
  ];
};
