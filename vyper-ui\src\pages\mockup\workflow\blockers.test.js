import { diagramsApproved } from "./blockers";

describe("diagramsApproved", () => {
  test("is true if no components", () => {
    const build = {
      components: [],
    };

    expect(diagramsApproved(build)).toBe(true);
  });

  test("false if a legacy diagram has not been approved", () => {
    const build = {
      components: [
        {
          name: "MB Diagram",
          instances: [
            {
              priorities: {
                object: {
                  name: "123456",
                },
              },
            },
          ],
        },
      ],
    };

    expect(diagramsApproved(build)).toBe(false);
  });

  test("true if a legacy diagram has been approved", () => {
    const build = {
      diagramApprovals: {
        123456: {
          type: "PRODUCTION",
        },
      },
      components: [
        {
          name: "MB Diagram",
          instances: [
            {
              priorities: {
                object: {
                  name: "123456",
                },
              },
            },
          ],
        },
      ],
    };

    expect(diagramsApproved(build)).toBe(true);
  });

  test("true if it's a PAVV diagram", () => {
    const build = {
      components: [
        {
          name: "MB Diagram",
          instances: [
            {
              priorities: {
                object: {
                  name: "12345678",
                },
              },
            },
          ],
        },
      ],
    };

    expect(diagramsApproved(build)).toBe(true);
  });
});
