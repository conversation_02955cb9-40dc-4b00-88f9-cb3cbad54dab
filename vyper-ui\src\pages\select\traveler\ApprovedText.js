import makeStyles from "@material-ui/core/styles/makeStyles";
import moment from "moment";
import React from "react";
import { dateFormat } from "src/component/dateFormat";

const useStyles = makeStyles({
  approved: {
    backgroundColor: "#00CC00",
  },
});

export const ApprovedText = ({ approvedGroup }) => {
  const classes = useStyles();

  if (approvedGroup == null) {
    return null;
  }

  const name = approvedGroup.username;
  const date = moment(approvedGroup.date).format(dateFormat);

  return (
    <>
      &nbsp;-{" "}
      <span className={classes.approved}>
        {" "}
        Approved - {name} - {date}{" "}
      </span>
    </>
  );
};
