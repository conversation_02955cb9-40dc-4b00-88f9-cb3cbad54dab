import { useEffect, useState } from "react";
import { custLengthApi } from "src/component/symbol/api/custLengthApi";

/**
 * Fetch and store the symbols for a facility and at.
 * @param {string} pkg - The package designator
 * @param {string} facilityAt - The facility A/T site
 * @param {string} symbolName - The symbolization name
 * @param {string} pinCount - The pin count
 * @return {{lenAndCustNames: LenAndCustName[]}}
 */
export const useCustLength = (pkg, facilityAt, symbolName, pinCount) => {
  const [lenAndCustNames, setLenAndCustNames] = useState(
    /** @type {LenAndCustName[]} */ []
  );

  /**
   * Return the cust value's maximum length
   *
   * @param {string} pkg - The package designator
   * @param {string} facilityAt the pdb facility name
   * @return {Promise<LenAndCustName[]>} a promise that resolves to a list of symbols
   */
  useEffect(() => {
    if (
      pkg == null ||
      facilityAt == null ||
      symbolName == null ||
      pinCount == null
    ) {
      return;
    }

    custLengthApi(pkg, facilityAt, symbolName, pinCount)
      .then(setLenAndCustNames)
      .catch(console.log);
  }, [pkg, facilityAt, symbolName, pinCount]);

  return { lenAndCustNames };
};
