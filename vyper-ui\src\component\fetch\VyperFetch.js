import React, { useContext, useState } from "react";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import { SpinnerContext } from "../Spinner";
import { makeStyles } from "@material-ui/core/styles";
import { noop } from "src/component/vyper/noop";

const useStyles = makeStyles({
  messages: {
    paddingBottom: "0.5em",
  },
});

export const VyperFetch = ({ children }) => {
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const [open, setOpen] = useState(false);
  const handleClose = () => setOpen(false);

  const [message, setMessage] = useState();

  const content = (method, body) => {
    let data = {
      method: method,
      credentials: "include",
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Pragma: "no-cache",
        "Cache-Control": "no-cache",
      },
    };

    if (body != null) data.body = JSON.stringify(body);

    return data;
  };

  const handleError = (err) => {
    if (err instanceof Response) {
      err
        .text()
        .then((text) => {
          const json = JSON.parse(text);
          if (err.status !== 401) {
            let errMessage = `${json.message} (${err.status})`
            if ( json.fieldErrors ){
                const fieldErrorMessages = json.fieldErrors.map( fieldError => fieldError.field + " "+ fieldError.error).join("\n");
                errMessage = errMessage + "\n" + fieldErrorMessages;
            }
            setMessage(errMessage);
            setOpen(true);
          }
        })
        .catch((ex) => {
          console.log("error", err, ex);
          setMessage(
            `An error occurred. Please try again, or contact support for assistance. (${err.status})`
          );
          setOpen(true);
        });
    } else {
      console.log("error", err);
      setMessage(
        `An error occurred. Please try again, or contact support for assistance. (${err.status})`
      );
      setOpen(true);
    }

    return err;
  };

  const vget = (url, success = noop, error) => {
    showSpinner();
    return fetch(url, content("get"))
      .then((response) => {
        if (response.ok) return response;
        else throw response;
      })
      .then((response) => response.json())
      .then((json) => success(json))
      .catch((err) => (error || handleError)(err))
      .finally(() => hideSpinner());
  };

  const vpost = (url, body, success = noop, error) => {
    showSpinner();
    return fetch(url, content("post", body))
      .then((response) => response.json())
      .then((json) => success(json))
      .catch((err) => (error || handleError)(err))
      .finally(() => hideSpinner());
  };

  const vdelete = (url, body, success = noop, error) => {
    showSpinner();
    return fetch(url, content("delete", body))
      .then((response) => response.text())
      .then((text) => success(text))
      .catch((err) => (error || handleError)(err))
      .finally(() => hideSpinner());
  };

  const messages = message == null ? [] : message.split("\n");

  const classes = useStyles();

  return (
    <FetchContext.Provider
      value={{
        vget: vget,
        vpost: vpost,
        vdelete: vdelete,
      }}
    >
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          {messages.map((m, n) => (
            <div className={classes.messages} key={n}>
              {m}
            </div>
          ))}
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" color="primary" onClick={handleClose}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </FetchContext.Provider>
  );
};

export const FetchContext = React.createContext(null);
