import React from "react";
import { SimilarTemplateCell } from "src/pages/mockup/bomtemplate/SimilarTemplateCell";
import { NicheTemplateCell } from "src/pages/mockup/bomtemplate/NicheTemplateCell";
import { VyperTemplateCell } from "src/pages/mockup/bomtemplate/VyperTemplateCell";
import PropTypes from "prop-types";
import { AtssTemplateCell } from "src/pages/mockup/bomtemplate/AtssTemplateCell";

/**
 * Display the template information
 *
 * @param {object} vyper
 * @param {object} build
 * @return {JSX.Element}
 * @constructor
 */
export const BomTemplateCell = ({ vyper, build }) => {
  switch (build.templateSource?.templateType) {
    case "SIMILAR_PKGNICHE":
      return <SimilarTemplateCell vyper={vyper} build={build} />;
    case "ATSS":
      return <AtssTemplateCell vyper={vyper} build={build} />;
    case "VYPER":
      return <VyperTemplateCell vyper={vyper} build={build} />;
    default:
      return <NicheTemplateCell vyper={vyper} build={build} />;
  }
};

BomTemplateCell.propTypes = {
  vyper: PropTypes.object.isRequired,
  build: PropTypes.object.isRequired,
};
