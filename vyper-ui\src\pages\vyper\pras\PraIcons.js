import PropTypes from "prop-types";
import React from "react";
import { PraIcon } from "src/pages/vyper/pras/PraIcon";

/**
 * Display the verifier icons.
 *
 * @param verifiers
 * @returns {JSX.Element}
 * @function
 */
export const PraIcons = ({ verifiers }) => {
  return (
    <div>
      {verifiers.map((verifier, n) => (
        <PraIcon key={n} verifier={verifier} />
      ))}
    </div>
  );
};

PraIcons.propTypes = {
  verifiers: PropTypes.array.isRequired,
};
