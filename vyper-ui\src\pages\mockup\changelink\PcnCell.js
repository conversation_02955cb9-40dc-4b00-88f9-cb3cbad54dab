import { Pcns } from "./Pcns";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import React, { useContext } from "react";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
} from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";

export const PcnCell = ({ vyper, build, onClick }) => {
  const { canEditPcnNumber } = useContext(HelperContext);
  const canEdit = canEditPcnNumber(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build)
  )
    return null;

  return (
    <div>
      <Pcns pcns={build.changelink.pcns} onClick={onClick} canEdit={canEdit} />

      {build.changelink.pcns.length === 0 ? (
        <DataCell source={null}>
          <VyperLink onClick={onClick} canEdit={canEdit}>
            click to select
          </VyperLink>
        </DataCell>
      ) : null}
    </div>
  );
};
