import makeStyles from "@material-ui/core/styles/makeStyles";
import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "src/component/fetch/VyperFetch";

const useStyles = makeStyles({
  root: {},
});

export const AtAttributes = ({ facilityAt, name, value }) => {
  const { vget } = useContext(FetchContext);

  const [attributes, setAttributes] = useState([]);
  const [paragraph, setParagraph] = useState(null);

  // if the name or value changes, call the api to get the attributes/paragraph text
  useEffect(() => {
    if (name == null || value == null) {
      return;
    }

    // setAttributes([])
    // setParagraph(null)

    let url = "/vyper/v1/vyper/child";
    url += `?facilityAt=${facilityAt}`;
    url += `&name=${name}`;
    url += `&values=${value}`;

    vget(encodeURI(url), (json) => {
      setAttributes(json.attributes);
      setParagraph(json.paragraphText);
    });
  }, [name, value]);

  const classes = useStyles();

  return (
    <div className={classes.root}>
      {paragraph == null ? (
        <Attributes value={value} attributes={attributes} />
      ) : null}
      {paragraph != null ? (
        <Paragraph value={value} paragraph={paragraph} />
      ) : null}
    </div>
  );
};

export const Attributes = ({ value, attributes }) => {
  const useStyles = makeStyles({
    root: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "flex-start",
      alignItems: "center",
    },
    attributes: {
      height: "200px",
      maxHeight: "300px",
      overflowY: "scroll",
      width: "400px",
      maxWidth: "400px",
      border: "1px solid #ccc",
    },
    cell: {
      padding: "0 8px",
    },
  });

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <h3>{value == null ? "Attributes" : `Attributes for ${value}`}</h3>
      <div className={classes.attributes}>
        <Table size="small">
          <TableHead>
            <TableRow hover>
              <TableCell className={classes.cell}>Name</TableCell>
              <TableCell className={classes.cell}>Value</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {attributes.map((a) => (
              <TableRow key={a.key} hover>
                <TableCell className={classes.cell}>{a.key}</TableCell>
                <TableCell className={classes.cell}>{a.value}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export const Paragraph = ({ value, paragraph }) => {
  const useStyles = makeStyles({
    root: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "flex-start",
      alignItems: "center",
    },
    paragraph: {
      // width: "400px",
      fontFamily: "monospace",
    },
  });

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <h3>{value == null ? "Paragraph" : `Paragraph for ${value}`}</h3>
      <div className={classes.paragraph}>
        <pre>{paragraph}</pre>
      </div>
    </div>
  );
};
