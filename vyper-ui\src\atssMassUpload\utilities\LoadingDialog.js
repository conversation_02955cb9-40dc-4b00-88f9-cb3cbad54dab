import React from "react";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Grid } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  root: { position: "top" },
  DialogTitle: {
    padding: "0px",
    textAlign: "center",
  },
  dividerLine: {
    height: "1px",
    color: "red",
    backgroundColor: "grey",
  },
  dialog: {
    position: "absolute",
    top: "300px",
  },
}));

export const LoadingDialog = ({ open, message }) => {
  const classes = useStyles();
  return (
    <Dialog open={open} scroll="paper" classes={{ paper: classes.dialog }}>
      <DialogTitle className={classes.DialogTitle}>Loading</DialogTitle>
      <Grid item xs={12}>
        <hr className={classes.dividerLine} />
      </Grid>
      <DialogContent>{message}</DialogContent>
    </Dialog>
  );
};
