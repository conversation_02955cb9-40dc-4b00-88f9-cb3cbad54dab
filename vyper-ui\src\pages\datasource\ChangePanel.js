import { makeStyles } from "@material-ui/core/styles";
import React from "react";
import { DataGrid } from "../../component/universal";

const useMyStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(3),
  },
}));

export const ChangePanel = () => {
  const columns = [
    { title: "Change ID", field: "changeId" },
    { title: "Change Number", field: "changeNumber" },
    { title: "Change Type", field: "changeType" },
    { title: "Change Owner", field: "changeOwner" },
    { title: "Project Name", field: "projectName" },
    { title: "Change Title", field: "changeTitle" },
  ];

  const classes = useMyStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Changelink Changes"
        url={`/vyper/v1/changelink/change/search`}
        actions={[]}
        columns={columns}
        pageable
        pageSize={20}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
      />
    </div>
  );
};
