import PropTypes from "prop-types";
import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";

/**
 * @callback MaterialCell2~onGetMaterialObject
 * @param {*} item - the item
 * @return {MaterialObject} materialObject - the material object
 */

/**
 * Display the cell of the material object
 * @param {*} item - the item
 * @param {MaterialCell2~onGetMaterialObject} onGetMaterial - callback to get the material object
 * @return {JSX.Element}
 * @constructor
 */
export function MaterialCell2({ item, onGetMaterialObject }) {
  const materialObject = onGetMaterialObject(item);
  return <DataCell source={null}>{materialObject.Material}</DataCell>;
}

MaterialCell2.propTypes = {
  item: PropTypes.object.isRequired,
  onGetMaterialObject: PropTypes.func.isRequired,
};
