import PropTypes from "prop-types";
import React from "react";
import { VyperNavLink } from "../../mockup/VyperLink";

/**
 * Display the reference cell for a VSCN.
 * @param {Vscn} item: The vscn object
 * @return {JSX.Element}
 * @constructor
 */
export function VscnReferenceCell({ item: vscn }) {
  return (
    <>
      <VyperNavLink to={`/projects/${vscn.vyperNumber}/pras/${vscn.praNumber}`}>
        {vscn.praNumber}
      </VyperNavLink>
      <br />
      <VyperNavLink
        to={`/projects/${vscn.vyperNumber}/builds/${vscn.buildNumber}`}
      >
        {vscn.buildNumber}
      </VyperNavLink>
    </>
  );
}

VscnReferenceCell.propTypes = {
  item: PropTypes.object.isRequired,
};
