import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React from "react";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { BuildComment } from "./BuildComment";

/**
 * Create the table row for the build comment
 *
 * @param builds
 * @param handleAddComment
 * @returns {JSX.Element}
 * @constructor
 */
export const BuildCommentRow = ({ builds, handleAddComment }) => {
  return (
    <TableRow hover>
      <RowPrefix help="comment" title="Comment" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <BuildComment
            build={build}
            onChange={(c) => handleAddComment(c, build)}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
