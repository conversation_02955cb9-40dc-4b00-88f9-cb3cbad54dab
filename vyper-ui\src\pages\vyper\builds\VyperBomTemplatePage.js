import React, { useContext } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import LabelImportantIcon from "@material-ui/icons/LabelImportant";
import { BackToBuildFormLink } from "../../../component/backbutton/BackToBuildFormLink";
import { BuildPageTitle } from "../BuildPageTitle";
import { Grid } from "@material-ui/core";
import { DataModelsContext } from "src/DataModel";

const useStyles = makeStyles({
  align: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  context: {
    border: "1px solid red",
  },
  key: {
    fontWeight: "bold",
    textAlign: "right",
    paddingRight: "1em",
    paddingBottom: "1em",
    "&::after": {
      content: '" : "',
    },
  },
  value: {
    paddingBottom: "1em",
  },
});

export const VyperBomTemplatePage = ({ vyperNumber }) => {
  const { build } = useContext(DataModelsContext);

  const bomTemplate = build?.bomTemplate;

  const errors = bomTemplate?.object?.errors || [];

  const classes = useStyles();

  return (
    <div>
      <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />

      <BuildPageTitle build={build} title="Bill of Process Template" />

      <h3>Merge Context</h3>

      <Grid className={classes.context} container>
        {Object.entries(bomTemplate?.object?.context || {})
          .sort((a, b) => {
            if (a[0] < b[0]) return -1;
            if (a[0] > b[0]) return 1;
            return 0;
          })
          .map((k) => (
            <React.Fragment key={k}>
              <Grid className={classes.key} item xs={6} md={3} lg={2}>
                {k[0] && k[0].replace("BOM", "BOP")}
              </Grid>
              <Grid className={classes.value} item xs={6} md={3} lg={2}>
                {k[1] && k[1].replace("BOM", "BOP")}
              </Grid>
            </React.Fragment>
          ))}
      </Grid>

      <h3>Errors ({errors.length})</h3>

      <ol>
        {errors.map((error, n) => (
          <li key={n}>{error}</li>
        ))}
      </ol>

      <h3>Bill of Process Template Flow</h3>

      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow hover>
              <TableCell>Operation</TableCell>
              <TableCell>Operation Required</TableCell>
              <TableCell>Component</TableCell>
              <TableCell>Component Required</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {bomTemplate?.object?.operations?.map((operation, n) =>
              operation.components.map((component, x) => (
                <TableRow key={`${n}:${x}`} hover>
                  <TableCell className={classes.align}>
                    <LabelImportantIcon />
                    {operation.name}
                  </TableCell>
                  <TableCell>{operation.required}</TableCell>
                  <TableCell className={classes.align}>
                    {component.name}
                  </TableCell>
                  <TableCell>{component.required}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};
