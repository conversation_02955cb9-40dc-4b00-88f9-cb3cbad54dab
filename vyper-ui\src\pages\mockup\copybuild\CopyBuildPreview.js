import {
  makeStyles,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";

const useStyles = makeStyles({
  tableHeader: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
});

/**
 * Display a preview of the copy operation
 *
 * @param material The device name
 * @param facility The A/T site
 * @param buildType The build type
 * @param description The description of the new build
 * @param buildNumber The copy-from build number
 * @returns {JSX.Element}
 * @function
 */
export const CopyBuildPreview = ({
  material,
  facility,
  buildType,
  description,
  buildNumber,
}) => {
  const classes = useStyles();

  return (
    <TableContainer>
      <Table>
        <TableBody>
          <TableRow>
            <TableCell
              className={classes.tableHeader}
              colSpan={2}
              align="center"
            >
              Build Preview
            </TableCell>
          </TableRow>

          <TableRow>
            <TableCell>Device </TableCell>
            <TableCell>{material}</TableCell>
          </TableRow>

          <TableRow>
            <TableCell>Facility</TableCell>
            <TableCell>{facility}</TableCell>
          </TableRow>

          <TableRow>
            <TableCell>Build Type</TableCell>
            <TableCell>{buildType}</TableCell>
          </TableRow>

          <TableRow>
            <TableCell>Description</TableCell>
            <TableCell>{description}</TableCell>
          </TableRow>

          <TableRow>
            <TableCell>Copying From Build</TableCell>
            <TableCell>{buildNumber}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

CopyBuildPreview.propTypes = {
  material: PropTypes.string.isRequired,
  facility: PropTypes.string.isRequired,
  buildType: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  buildNumber: PropTypes.string.isRequired,
};
