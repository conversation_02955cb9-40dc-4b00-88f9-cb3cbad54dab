import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../RowPrefix";
import { MaterialCell2 } from "./MaterialCell2";

/**
 * Display a row of materials
 * @param {*[]} items - The items to display
 * @param {MaterialCell2~onGetMaterialObject} onGetMaterial - callback to get the material object
 * @return {JSX.Element}
 * @constructor
 */
export function MaterialRow2({ items, onGetMaterialObject }) {
  return (
    <TableRow hover>
      <RowPrefix help="device" title="OPN/Material Name" required />
      {items.map((item, n) => (
        <TableCell key={n}>
          <MaterialCell2
            item={item}
            onGetMaterialObject={onGetMaterialObject}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

MaterialRow2.propTypes = {
  items: PropTypes.array.isRequired,
};
