import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { WorkFlow } from "./WorkFlow";
import PropTypes from "prop-types";

/**
 * Create the row to display the workflow buttons
 *
 * @param builds
 * @param onChange
 * @param onSubmit
 * @param onDelete
 * @returns {JSX.Element}
 * @constructor
 */
export const WorkFlowRow = ({ builds, onChange, onDelete, onSubmit }) => {
  const { vyper, buildDao } = useContext(DataModelsContext);

  const handleWorkflow = (action, vyper, build, context) => {
    switch (action.toLowerCase()) {
      case "delete":
        return onDelete(action, build, context);

      case "submit":
        return onSubmit(action, build, context);

      default:
        return onChange(action, build, context);
    }
  };

  return (
    <TableRow hover>
      <RowPrefix help="workflow" title="WorkFlow" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <WorkFlow
            vyper={vyper}
            build={build}
            onWorkflow={handleWorkflow}
            onReload={() => {
              buildDao.reload(build.buildNumber).catch(noop);
            }}
            onChange={onChange}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};

WorkFlowRow.propTypes = {
  builds: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
};
