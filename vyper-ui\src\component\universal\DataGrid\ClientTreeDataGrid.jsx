import React from "react";
import PropTypes from "prop-types";
import MaterialTable from "material-table";
import { default as tableIcons } from "./icons";
import { tableConfig, styleOptions } from "./defaults";

const ClientTreeDataGrid = ({
  actions = [],
  columns = [],
  data = [],
  detailPanel = undefined,
  handleSelect,
  selectable = false,
  title = "",
}) => {
  return (
    <MaterialTable
      minRows={tableConfig.minRows}
      actions={actions}
      columns={columns}
      data={data}
      detailPanel={detailPanel}
      icons={tableIcons}
      onSelectionChange={selectable ? (rows) => handleSelect(rows) : null}
      options={{
        ...styleOptions,
        ...{ selection: selectable },
        ...{ filtering: tableConfig.filtering },
        ...{ pageSize: tableConfig.defaultPageSize },
        ...{ defaultExpanded: tableConfig.defaultExpanded },
        rowStyle: (data, index) => {
          if (JSON.stringify(data.tableData.childRows) !== "null") {
            return { backgroundColor: "#ecf2f9" };
          }
        },
      }}
      title={title}
      parentChildData={(row, rows) => rows.find((a) => a.id === row.parentId)}
      editable={{
        onRowUpdate: (newData, oldData) =>
          new Promise((resolve, reject) => {
            setTimeout(() => {
              //replace with on row update implementation
              resolve();
            }, 1000);
          }),
        onRowDelete: (oldData) =>
          new Promise((resolve, reject) => {
            setTimeout(() => {
              //replace with on row delete/obsolete implementation
              resolve();
            }, 1000);
          }),
      }}
    />
  );
};

ClientTreeDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  data: PropTypes.array.isRequired,
  detailPanel: PropTypes.any,
  handleSelect: PropTypes.func,
  selectable: PropTypes.bool,
  title: PropTypes.string,
};

export default ClientTreeDataGrid;
