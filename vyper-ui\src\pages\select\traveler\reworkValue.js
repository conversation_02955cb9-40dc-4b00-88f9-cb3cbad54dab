import React from "react";

/**
 * @typedef {Object} ReworkMatch
 * @property {boolean} match - True indicates that the old value matches the current value
 * @property {string} oldValue - The old value, if there was a mismatch
 * @property {string} newValue - The new value, if there was a mismatch
 */

/**
 * This function compares the current traveler operation and component to the rework traveler's value.
 *
 * @param {Object} reworkedTraveler - The old reworked traveler.
 * @param {Object} operation - The traveler operation
 * @param {Object} component - The traveler component
 * @param {number} priority - The component's priority
 * @returns {ReworkMatch} - The results of the comparison
 *
 */
export function compare(reworkedTraveler, operation, component, priority) {
  // validate inputs
  if (
    reworkedTraveler == null ||
    operation == null ||
    component == null ||
    priority == null
  ) {
    return {
      match: true,
    };
  }

  // get the rework operation
  const reworkOperation = reworkedTraveler.operations.find(
    (o) => o.name === operation.name
  );
  if (reworkOperation == null) {
    return {
      match: true,
    };
  }

  // get the rework components
  const reworkComponents = reworkOperation.components.filter(
    (c) => c.name === component.name
  );
  const reworkComponent = reworkComponents[priority - 1];

  // if reworkComponent == null, then the old value doesn't exist (a new priority has been added)
  if (reworkComponent == null) {
    return {
      match: false,
      oldValue: undefined,
      newValue: component.value,
    };
  }

  // compare the values
  if (reworkComponent.value === component.value) {
    // same value
    return {
      match: true,
    };
  } else {
    // different value
    return {
      match: false,
      oldValue: reworkComponent.value,
      newValue: component.value,
    };
  }
}
