import { Grid, Switch } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import React, { useState } from "react";
import { useHistory } from "react-router-dom";

const useStyles = makeStyles({
  root: {
    fontSize: "12px",
    padding: "5px",
    paddingLeft: ".5rem",
    borderLeft: "none",
    borderRight: "none",
    borderRadius: "7%",
    fontWeight: "bold",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
    top: 60,
  },
  header: {
    fontWeight: "bold",
  },
});

const VyperPraModeSwitch = ({ praMode = false, vyperNumber }) => {
  const [checkedValue, setCheckedValue] = useState(praMode);
  const history = useHistory();

  const onSwitchChange = () => {
    setCheckedValue(!checkedValue);
    if (checkedValue) {
      history.push("/projects/" + vyperNumber);
    } else {
      history.push("/projects/" + vyperNumber + "/pras");
    }
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Grid component="label" container alignItems="center" spacing={0}>
        <Grid item>Build Mode</Grid>
        <Grid item>
          <Switch
            checked={checkedValue}
            onChange={onSwitchChange}
            value="checkedValue"
            color="secondary"
          />
        </Grid>
        <Grid item>PRA Mode</Grid>
      </Grid>
    </div>
  );
};

export default VyperPraModeSwitch;
