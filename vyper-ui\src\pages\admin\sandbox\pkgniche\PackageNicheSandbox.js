import React, { useContext, useEffect } from "react";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Alert } from "@material-ui/lab";
import { MenuItem, Paper, TextField } from "@material-ui/core";
import Button from "@material-ui/core/Button";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";

const useStyles = makeStyles({
  form: {
    display: "flex",
    justifyContent: "space-around",
    alignItems: "center",
    padding: ".5rem",
    marginTop: "1rem",
    border: "1px solid red",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
  },
  paper: {
    margin: "1rem",
  },
});

export const PackageNicheSandbox = () => {
  const { sandboxDao, facilityDao, plantCodes } = useContext(DataModelsContext);

  const [device, setDevice] = useLocalStorage(
    "playground.packageniche.device",
    null
  );
  const [facilityAt, setFacilityAt] = useLocalStorage(
    "playground.packageniche.plantCode",
    null
  );
  const [result, setResult] = useLocalStorage(
    "playground.packageniche.result",
    {}
  );

  useEffect(() => {
    facilityDao.loadPlantCodes().catch(noop);
  }, []);

  const handleChangeDevice = (e) => setDevice(e.target.value);

  const handleChangeFacilityAt = (e) => setFacilityAt(e.target.value);

  const buttonDisabled =
    device == null || device === "" || facilityAt == null || facilityAt === "";

  const handleSubmit = () => {
    sandboxDao
      .packageNiche(device, facilityAt)
      .then((json) => setResult(json))
      .catch(noop);
  };

  const classes = useStyles();

  return (
    <div>
      <br />

      <Alert severity="success">
        This form allows you to view the data that Vyper loads from Package
        Niche
      </Alert>

      <form className={classes.form}>
        <div>
          <TextField
            label="Device"
            value={device || ""}
            onChange={handleChangeDevice}
          />
        </div>

        <div>
          <TextField
            label="Facility A/T"
            select
            value={facilityAt || ""}
            onChange={handleChangeFacilityAt}
          >
            {plantCodes.map((plantCode) => (
              <MenuItem key={plantCode.facilityAt} value={plantCode.facilityAt}>
                {plantCode.facilityAt}
              </MenuItem>
            ))}
          </TextField>
        </div>

        <div>
          <Button
            disabled={buttonDisabled}
            color="primary"
            variant="contained"
            onClick={handleSubmit}
          >
            Go
          </Button>
        </div>
      </form>

      <Paper>
        <div className={classes.paper}>
          <h3>Selected Package Niche</h3>
          <pre>
            {JSON.stringify(
              result?.packageNiche?.name || "-- none --",
              null,
              "\t"
            )}
          </pre>

          <h3>Potential Package Niches</h3>
          <pre>{JSON.stringify(result?.packageNiche?.names, null, "\t")}</pre>

          <h3>Package Niche Details</h3>
          <pre>
            {JSON.stringify(result?.packageNiche?.nicheDetails, null, "\t")}
          </pre>
        </div>
      </Paper>
    </div>
  );
};
