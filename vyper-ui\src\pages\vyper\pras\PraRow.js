import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React, { useContext } from "react";
import { Help } from "src/component/help/Help";
import { Required } from "src/component/required/Required";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { PraCell } from "src/pages/vyper/pras/PraCell";
import PraComponentCheck from "./PraComponentCheck";

/**
 * Display PRA components in the row
 *
 * @param name - The component name
 * @param vyper - The vyper object
 * @param pras - Array of PRA objects
 * @param onSave - Callback for saving component value updates
 * @param reqPrasCheckedGroups
 * @param taskAssignments
 * @param reqPrasCheckedGroups
 * @param taskAssignments
 * @returns {JSX.Element}
 * @function
 */
export const PraRow = ({
  name,
  vyper,
  pras,
  onSave,
  reqPrasCheckedGroups,
  taskAssignments,
}) => {
  const { praDao } = useContext(DataModelsContext);

  const getTooltip = () => {
    switch (name) {
      case "Leadframe":
        return "leadframe";

      case "Mount Compound":
        return "mountcompound";

      case "MB Diagram":
        return "mbdiagram";

      case "Wire":
        return "wire";

      case "Mold Compound":
      case "Flux":
        return "moldcompound";

      case "MSL":
        return "msl";

      default:
        return "component";
    }
  };

  const handleOnClickValidate = (vyperNumber, praNumber, compName, checked) => {
    return praDao
      .toggleChecked(vyperNumber, praNumber, compName, checked)
      .then((json) => onSave(json))
      .catch(noop);
  };

  const handleSave = (cName, oldValue, newValue, pra) => {
    return praDao
      .changeComponents(
        vyper.vyperNumber,
        pra.praNumber,
        cName,
        oldValue,
        newValue
      )
      .then((json) => onSave(json))
      .catch(noop);
  };

  if (name === "Flux") {
    return null;
  }

  return (
    <TableRow hover>
      <TableCell variant="head">
        <Help name={getTooltip()} /> {name} <Required required={true} />
      </TableCell>
      {pras.map((pra, n) => {
        // if this is the mount compound row, and we have a flux, show the flux instead

        let name2 = name;
        let vc = pra.validatedComponents[name];
        if (name === "Mount Compound") {
          const vc2 = pra.validatedComponents["Flux"];
          if (vc2) {
            vc = vc2;
            name2 = "Flux";
          }
        }

        const needsCheckBox = vc;
        const canEdit =
          name2 !== "Leadframe" && (!needsCheckBox || !vc.checked);
        const praTaskAssignments = taskAssignments.filter(
          (assignment) => assignment.taskName === pra.praNumber
        );

        return (
          <TableCell key={n}>
            <PraCell
              vyper={vyper}
              pra={pra}
              name={name2}
              onSave={handleSave}
              canEdit={canEdit}
            />
            {needsCheckBox && (
              <PraComponentCheck
                compName={name2}
                pra={pra}
                onClickValidate={handleOnClickValidate}
                reqCheckedGroups={reqPrasCheckedGroups[n]}
                praTaskAssignments={praTaskAssignments}
              />
            )}
          </TableCell>
        );
      })}
    </TableRow>
  );
};

PraRow.propTypes = {
  name: PropTypes.string.isRequired,
  vyper: PropTypes.object.isRequired,
  pras: PropTypes.array.isRequired,
  onSave: PropTypes.func.isRequired,
};
