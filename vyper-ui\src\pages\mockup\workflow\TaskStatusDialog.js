import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogActions from "@material-ui/core/DialogActions";
import React from "react";
import { ApprovalsHistory } from "src/pages/vyper/builds/ApprovalsHistory";

export const TaskStatusDialog = ({ open, handleClose, contextKey }) => {
  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogContent>
        <ApprovalsHistory contextKey={contextKey} />
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} variant="outlined" color="primary">
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};
export default TaskStatusDialog;
