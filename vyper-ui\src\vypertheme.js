// import {deepOrange} from "@material-ui/core/colors";

const vyperTheme = (paletteName) => {
  switch (paletteName) {
    case "Red":
      return red();
    case "Blue":
      return blue();
    case "Dark":
      return dark();
    default:
      return red();
  }
};

const red = () => {
  return {
    MuiPaper: {
      root: {
        // backgroundColor: deepOrange[900]
      },
    },
  };
};

const blue = () => {
  return {
    background: {
      // paper: deepOrange[900]
    },
  };
};

const dark = () => {
  return {
    background: {
      // paper: deepOrange[900]
    },
  };
};

export default vyperTheme;
