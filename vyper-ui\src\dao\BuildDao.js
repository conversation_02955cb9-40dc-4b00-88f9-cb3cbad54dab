import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

export class BuildDao extends DaoBase {
  constructor(params) {
    super({ name: "<PERSON>uildDao", url: "/vyper/v1/vyper", ...params });
    this.build = params.build || {};
    this.setBuild = params.setBuild || noop;
  }

  reload(buildNumber) {
    return this.findByBuildNumber(buildNumber, true);
  }

  findByBuildNumber(buildNumber, ignoreCache = false) {
    if (!ignoreCache && this.build?.buildNumber === buildNumber) {
      return Promise.resolve(this.build);
    }

    return this.handleFetch(
      "findByBuildNumber",
      `/findBuildByBuildNumber/${buildNumber}`,
      "GET"
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  findAllByVyperNumber(vyperNumber) {
    return this.handleFetch(
      "findAllByVyperNumber",
      `/findAllBuildsByVyperNumber/${vyperNumber}`,
      "GET"
    );
  }

  changeMaterial(vyperNumber, buildNumber, material) {
    return this.handleFetch("changeMaterial", `/material`, "POST", {
      vyperNumber,
      buildNumber,
      material,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeFacility(vyperNumber, buildNumber, facility) {
    return this.handleFetch("changeFacility", `/facility`, "POST", {
      vyperNumber,
      buildNumber,
      facility,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeBackgrind(vyperNumber, buildNumber, backgrindVal, backgrindSelected) {
    return this.handleFetch("changeBackgrind", `/backgrind`, "POST", {
      vyperNumber,
      buildNumber,
      backgrindVal,
      backgrindSelected,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeArmarc(vyperNumber, buildNumber, armarc) {
    return this.handleFetch("changeArmarc", `/armarc`, "POST", {
      vyperNumber,
      buildNumber,
      armarc,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeAtss(vyperNumber, buildNumber, atss) {
    return this.handleFetch("changeAtss", `/atss`, "POST", {
      vyperNumber,
      buildNumber,
      atss,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeVyper(vyperNumber, buildNumber, srcBuildNumber) {
    return this.handleFetch("changeVyper", `/vyperCopy`, "POST", {
      vyperNumber,
      buildNumber,
      srcBuildNumber,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changePkgNiche(vyperNumber, buildNumber, pkgNiche) {
    return this.handleFetch("changePkgNiche", `/pkgniche`, "POST", {
      vyperNumber,
      buildNumber,
      pkgNiche,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeDies(vyperNumber, buildNumber, dieInstances) {
    return this.handleFetch("changeDies", `/dies`, "POST", {
      vyperNumber,
      buildNumber,
      dieInstances,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeComponents(vyperNumber, buildNumber, component, revertPgs = false) {
    return this.handleFetch("changeBuildComponents", `/components`, "POST", {
      vyperNumber,
      buildNumber,
      component,
      revertPgs,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeSymbolization(vyperNumber, buildNumber, symbol, customs, ecat) {
    return this.handleFetch("changeSymbolization", `/symbolization`, "POST", {
      vyperNumber,
      buildNumber,
      symbol,
      customs,
      ecat,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeChangelinkChange(vyperNumber, buildNumber, changes) {
    return this.handleFetch(
      "changeChangelinkChange",
      `/changelink/change`,
      "POST",
      { vyperNumber, buildNumber, changes }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeChangelinkPcn(vyperNumber, buildNumber, pcns) {
    return this.handleFetch("changeSymbolization", `/changelink/pcn`, "POST", {
      vyperNumber,
      buildNumber,
      pcns,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  addCopiedBuild(vyperNumber, srcBuildNumber, buildtype, description) {
    return this.handleFetch("addCopiedBuild", `/addBuildCopy`, "POST", {
      vyperNumber,
      srcBuildNumber,
      buildtype,
      description,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  createVyperAndCopyBuild(vyperNumber, srcBuildNumber, buildtype, description) {
    return this.handleFetch(
      "createVyperAndCopyBuild",
      `/createVyperAndCopyBuild`,
      "POST",
      {
        vyperNumber,
        srcBuildNumber,
        buildtype,
        description,
      }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeWorkflow(
    vyperNumber,
    buildNumber,
    action,
    approvalGroup,
    rejectReason = null
  ) {
    return this.handleFetch("changeWorkflow", `/workflow`, "POST", {
      vyperNumber,
      buildNumber,
      action,
      approvalGroup,
      rejectReason,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  addComment(
    vyperNumber,
    buildNumber,
    username,
    userid,
    when,
    comment,
    operation
  ) {
    return this.handleFetch("addComment", `/comment`, "POST", {
      vyperNumber,
      buildNumber,
      username,
      userid,
      when,
      comment,
      operation,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeTest(vyperNumber, buildNumber, content) {
    return this.handleFetch("changeTest", `/test/upload`, "POST", {
      vyperNumber,
      buildNumber,
      content,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeWaferSawMethod(vyperNumber, buildNumber, value) {
    return this.handleFetch("changeWaferSawMethod", `/wafersawmethod`, "POST", {
      vyperNumber,
      buildNumber,
      value,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeEsl(vyperNumber, buildNumber, value) {
    return this.handleFetch("changeEsl", `/esl`, "POST", {
      vyperNumber,
      buildNumber,
      value,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  listPackConfigs(vyperNumber, buildNumber) {
    return this.handleFetch(
      "listPackConfigs",
      `/packconfig?vyperNumber=${vyperNumber}&buildNumber=${buildNumber}`,
      "GET"
    );
  }

  changePackConfig(vyperNumber, buildNumber, value) {
    return this.handleFetch("changePackConfig", `/packconfig`, "POST", {
      vyperNumber,
      buildNumber,
      value,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeDryBake(vyperNumber, buildNumber, value) {
    return this.handleFetch("changeDryBake", `/drybake`, "POST", {
      vyperNumber,
      buildNumber,
      value,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeTurnkey(vyperNumber, buildNumber, value) {
    return this.handleFetch("changeTurnkey", `/turnkey`, "POST", {
      vyperNumber,
      buildNumber,
      value,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  refreshPgs(vyperNumber, buildNumber) {
    return this.handleFetch("refreshPgs", `/refreshPgs`, "POST", {
      vyperNumber,
      buildNumber,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  refreshBomTemplate(vyperNumber, buildNumber) {
    return this.handleFetch(
      "refreshBomTemplate",
      `/refreshBomTemplate`,
      "POST",
      { vyperNumber, buildNumber }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  refreshFlow(vyperNumber, buildNumber) {
    return this.handleFetch("refreshFlow", `/refreshFlow`, "POST", {
      vyperNumber,
      buildNumber,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeDescription(vyperNumber, buildNumber, description) {
    return this.handleFetch("changeDescription", `/description`, "POST", {
      vyperNumber,
      buildNumber,
      description,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeScswrControlNumber(vyperNumber, buildNumber, scswrcontrolnumber) {
    return this.handleFetch(
      "changeScswrcontrolnumber",
      `/scswrcontrolnumber`,
      "POST",
      {
        vyperNumber,
        buildNumber,
        scswrcontrolnumber,
      }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeBuildType(vyperNumber, buildNumber, buildtype) {
    return this.handleFetch("changeBuildType", `/buildtype`, "POST", {
      vyperNumber,
      buildNumber,
      buildtype,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeFlowOperation(vyperNumber, buildNumber, oldName, newName) {
    return this.handleFetch(
      "changeFlowOperation",
      `/flow/operation/change`,
      "POST",
      { vyperNumber, buildNumber, oldName, newName }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  addFlowOperation(
    vyperNumber,
    buildNumber,
    after,
    name,
    required,
    engineering
  ) {
    return this.handleFetch("addFlowOperation", `/flow/operation`, "POST", {
      vyperNumber,
      buildNumber,
      after,
      name,
      required,
      engineering,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  deleteFlowOperation(vyperNumber, buildNumber, name, engineering, index) {
    return this.handleFetch(
      "deleteFlowOperation",
      `/flow/operation`,
      "DELETE",
      { vyperNumber, buildNumber, name, engineering, index }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  restoreFlowOperation(vyperNumber, buildNumber, name) {
    return this.handleFetch(
      "restoreFlowOperation",
      `/flow/operation/restore`,
      "POST",
      { vyperNumber, buildNumber, name }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  reorderFlow(vyperNumber, buildNumber, jsonContent) {
    return this.handleFetch(
      "reorderFlow",
      `/flow/reorder`,
      "POST",
      {vyperNumber, buildNumber, operations: jsonContent}
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  editComponentName(vyperNumber, buildNumber, operation, oldName, newName) {
    return this.handleFetch(
      "editComponentName",
      `/flow/component/change`,
      "POST",
      { vyperNumber, buildNumber, operation, oldName, newName }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  changeSelection(vyperNumber, buildNumber, oName, cName, items) {
    return this.handleFetch("changeSelection", `/selection`, "POST", {
      vyperNumber,
      buildNumber,
      operation: oName,
      name: cName,
      items,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  addFlowComponent(
    vyperNumber,
    buildNumber,
    operationName,
    name,
    required,
    after
  ) {
    return this.handleFetch("addFlowComponent", `/flow/component`, "POST", {
      vyperNumber,
      buildNumber,
      operationName,
      name,
      required,
      after,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  removeFlowComponent(vyperNumber, buildNumber, operationName, componentName) {
    return this.handleFetch(
      "removeFlowComponent",
      `/flow/component`,
      "DELETE",
      { vyperNumber, buildNumber, operationName, componentName }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  toggleValidate(vyperNumber, buildNumber, operation) {
    return this.handleFetch("toggleValidate", `/operation/validate`, "POST", {
      vyperNumber,
      buildNumber,
      operation,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  fillComponentVyper(vyperNumber, buildNumber, number) {
    return this.handleFetch(
      "fillComponentVyper",
      `/fillcomponent/vyper`,
      "POST",
      { vyperNumber, buildNumber, number }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  fillComponentAtss(vyperNumber, buildNumber, facility, material, status) {
    return this.handleFetch(
      "fillComponentAtss",
      `/fillcomponent/atss`,
      "POST",
      { vyperNumber, buildNumber, facility, material, status }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  fillComponentClear(vyperNumber, buildNumber) {
    return this.handleFetch(
      "fillComponentClear",
      `/fillcomponent/clear`,
      "POST",
      { vyperNumber, buildNumber }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  updateOperationComment(vyperNumber, buildNumber, operation, comment) {
    return this.handleFetch(
      "updateOperationComment",
      `/operation/comment`,
      "POST",
      { vyperNumber, buildNumber, operation, comment }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  retrieveState(buildNumber) {
    return this.handleFetch(
      "retrieveState",
      `/build/state/${buildNumber}`,
      "GET"
    );
  }

  updateBuild(
    mode,
    vyperNumber,
    buildNumber,
    material,
    facility,
    multiBuild,
    specDevice,
    description,
    buildType,
    copyBuildNumber
  ) {
    return this.handleFetch("updateBuild", `/updateBuild`, "POST", {
      mode,
      vyperNumber,
      buildNumber,
      material,
      facility,
      multiBuild,
      specDevice,
      description,
      buildType,
      copyBuildNumber,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  notifyAtGroupApproved(
    vyperNumber,
    buildNumber,
    approvalGroup,
    rejectReason = null
  ) {
    return this.handleFetch(
      "notifyAtGroupApproved",
      `/workflow/notifyAtGroupApproved`,
      "POST",
      { vyperNumber, buildNumber, approvalGroup, rejectReason }
    ).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  approveDiagram(vyperNumber, buildNumber, diagram, approvalType) {
    return this.handleFetch("approveDiagram", `/diagram/approve`, "POST", {
      vyperNumber,
      buildNumber,
      diagram,
      approvalType,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  checkArmarc(vyperNumber, buildNumber) {
    return this.handleFetch("checkArmarc", `/checkArmarc`, "POST", {
      vyperNumber,
      buildNumber,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }

  refreshAttributes(vyperNumber, buildNumber) {
    return this.handleFetch("refreshAttributes", `/refreshAttributes`, "POST", {
      vyperNumber,
      buildNumber,
    }).then((json) => {
      this.setBuild(json);
      return json;
    });
  }
}
