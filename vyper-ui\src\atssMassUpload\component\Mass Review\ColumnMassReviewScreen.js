import React, {
  useRef,
  useCallback,
  useMemo,
  useState,
  useEffect,
} from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS
import "./styles.css";
import getColumnDefinition, {
  combineColumnDefinition,
  getColumnComparisionColumnDefinition,
  prepareAgGridColumnsFromColumnMap,
  getRowComparisionLegend,
  flattenTravelerData,
} from "./utility";
import TooltipComponent from "./TooltipComponent";

const useStyles = makeStyles({
  root: {},
});

const ColumnMassReviewScreen = (props) => {
  const { unifiedView, travelerData, comparisionConfig } = props;

  const gridRef = useRef();
  const containerStyle = useMemo(() => ({ width: "100%", height: "100%" }), []);
  const gridStyle = useMemo(() => ({ height: "100%", width: "100%" }), []);
  const [gridApi, setGridApi] = useState(null);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const referenceTraveler = useRef([]);
  const projectDeviceTraveler = useRef([]);
  const unifiedViewRowHeaders = useRef([]);
  const nonUnifiedViewRowHeaders = useRef([]);
  const [flattenedData, setFlattenedData] = useState(null);
  const [columnDefs, setColumnDefs] = useState([]);
  const defaultColDef = useMemo(() => {
    return {
      resizable: true,
      tooltipComponent: TooltipComponent,
    };
  }, []);

  const autoSizeAll = useCallback(
    (skipHeader) => {
      const allColumnIds = [];
      gridColumnApi.getAllColumns().forEach((column) => {
        allColumnIds.push(column.getId());
      });
      gridColumnApi.autoSizeColumns(allColumnIds, skipHeader);
    },
    [gridColumnApi]
  );

  useEffect(() => {
    if (gridApi) {
      gridApi.setRowData(
        getRowComparisionLegend(
          unifiedView
            ? unifiedViewRowHeaders.current
            : nonUnifiedViewRowHeaders.current
        )
      );
    }
  }, [unifiedView]);

  useEffect(() => {
    referenceTraveler.current = {
      ...travelerData["referenceTraveler"],
      isReference: true,
    };
    projectDeviceTraveler.current = travelerData["projectDeviceTraveler"];
    flattenTravelerData([
      referenceTraveler.current,
      ...projectDeviceTraveler.current,
    ]);
    nonUnifiedViewRowHeaders.current = getColumnDefinition(
      referenceTraveler.current,
      comparisionConfig
    );
    setColumnDefs(
      getColumnComparisionColumnDefinition([
        referenceTraveler.current,
        ...projectDeviceTraveler.current,
      ])
    );
    let columnsMap = new Map();
    for (let i = 0; i < projectDeviceTraveler.current.length; ++i) {
      const map = combineColumnDefinition(
        nonUnifiedViewRowHeaders.current,
        getColumnDefinition(
          projectDeviceTraveler.current[i],
          comparisionConfig
        ),
        columnsMap
      );
      columnsMap = map;
    }
    unifiedViewRowHeaders.current =
      prepareAgGridColumnsFromColumnMap(columnsMap);

    setFlattenedData(
      flattenTravelerData([
        referenceTraveler.current,
        ...projectDeviceTraveler.current,
      ])
    );
  }, [travelerData]);

  useEffect(() => {
    if (gridApi && gridColumnApi) {
      gridApi.setColumnDefs(columnDefs);
      gridColumnApi.resetColumnState();
      gridColumnApi.autoSizeAllColumns();
      autoSizeAll(true);
    }
  }, [gridApi, gridColumnApi, columnDefs]);

  const onGridReady = useCallback((params) => {
    setGridApi(params.api);
    setGridColumnApi(params.columnApi);
  }, []);

  const classes = useStyles();
  if (!flattenedData) return <div>Loading...</div>;
  return (
    <div className={classes.root}>
      <div style={containerStyle}>
        <div className="test-container">
          <div style={gridStyle} className="ag-theme-alpine">
            <AgGridReact
              ref={gridRef}
              rowData={getRowComparisionLegend(
                nonUnifiedViewRowHeaders.current
              )}
              defaultColDef={defaultColDef}
              tooltipShowDelay={0}
              tooltipHideDelay={2000}
              onGridReady={onGridReady}
              gridOptions={{
                rowHeight: 25,
                context: {
                  data: flattenedData,
                  referenceData: flattenedData[0],
                },
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ColumnMassReviewScreen;
