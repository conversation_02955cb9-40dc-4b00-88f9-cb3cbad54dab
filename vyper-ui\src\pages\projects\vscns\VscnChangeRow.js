import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { VscnChangeCell } from "./VscnChangeCell";
import { SingleSelectionDialogContext } from "../../../component/component/SingleSelectionDialog";

export const VscnChangeRow = ({ vscns, onChange, data }) => {
  const { vscnDao } = useContext(DataModelsContext);
  const { openSingleSelectionDialog } = useContext(
    SingleSelectionDialogContext
  );

  const columns = [
    { field: "changeNumber", title: "Change Number" },
    { field: "changeType", title: "Change Type" },
    { field: "changeOwner", title: "Change Owner" },
    { field: "projectName", title: "Project Name" },
    { field: "changeTitle", title: "Change Title" },
  ];

  const handleChangeLinkChange = (vscnNumber, changeNumber) => {
    return vscnDao
      .changeChangelinkChange(vscnNumber, changeNumber)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleOpen = (vscn, data) => {
    openSingleSelectionDialog({
      columns,
      data: data?.content,
      title: "Select ChangeLink Number",
      handleSelect: (event, data) =>
        handleChangeLinkChange(vscn.vscnNumber, data?.changeNumber),
    });
  };

  return (
    <TableRow hover>
      <RowPrefix help="change" title="Change Number" />
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <VscnChangeCell vscn={vscn} onClick={() => handleOpen(vscn, data)} />
        </TableCell>
      ))}
    </TableRow>
  );
};
