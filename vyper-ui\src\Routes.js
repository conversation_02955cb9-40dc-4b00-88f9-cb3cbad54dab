import React from "react";
import {Switch} from "react-router-dom";
import {RouteManager, RouteNotFound} from "./component/common";
import {HomeIndexPage} from "./pages/home/<USER>";
import {AboutPage} from "./pages/about/AboutPage";
import {AdminIndexPage} from "./pages/admin/AdminIndexPage";
import {VscnListPage} from "./pages/list/VscnListPage"
import {TopsideIndexPage} from "./pages/topSymbol/topSymbolIndexPage";
import {DevicesPage} from "./pages/devices/DevicesPage";
import {PraBuildsPage} from "./pages/praBuilds/PraBuildsPage";
import {ReportIndexPage} from "./pages/report/ReportIndexPage";
import {DatasourceIndexPage} from "./pages/datasource/DatasourceIndexPage";
import {ComparePage} from "./pages/compare/ComparePage";
import {ATApprovalAdminPage} from "./pages/admin/ATApprovalAdminPage";
import {MyApprovalsPage} from "./pages/home/<USER>/approvals/MyApprovalsPage";
import {VyperPage} from "src/pages/projects/VyperPage";

// VSWR
import CreateForm from "src/pages/vswr/forms/CreateForm";
import ViewForm from "src/pages/vswr/forms/ViewForm";
import EditForm from "src/pages/vswr/forms/EditForm";

// Batch Processing
import ListSwrsPage from "src/pages/vswr/batchprocessing/ListSwrsPage";
import UpdateSwrsPage from "src/pages/vswr/batchprocessing/UpdateSwrsPage";
import ForecastSBEView from "src/pages/vswr/batchprocessing/ForecastSBEView";
import ForecastATView from "src/pages/vswr/batchprocessing/ForecastATView";
import { VyperTravelerPage } from "./traveler/pages/VyperTravelerPage";

// ATSS Mass Upload
import { AtssMassUploadPage } from "./atssMassUpload/pages/AtssMassUploadPage";
import { SpecChange } from "./atssMassUpload/component/specChange/SpecChange";
import MassReviewScreen from "./atssMassUpload/component/Mass Review/MassReviewScreen";
import { SpecChangeAtssActionPanel } from "./atssMassUpload/component/specChange/SpecChangeAtssActionPanel"

const Routes = () => {
  return (
    <Switch>
      <RouteManager exact path="/" component={HomeIndexPage} />

      {/* <RouteManager path="/my/projects" component={MyProjectsPage}/> */}
      <RouteManager path="/my/approvals" component={MyApprovalsPage} />

      <RouteManager path="/device" component={DevicesPage} />

      <RouteManager path="/praBuildsPage" component={PraBuildsPage} />
      <RouteManager path="/list/vscns" component={VscnListPage} />

      <RouteManager path="/compare" component={ComparePage} />
      <RouteManager path="/datasources" component={DatasourceIndexPage} />
      <RouteManager path="/reports" component={ReportIndexPage} />
      <RouteManager
        path="/admin"
        component={AdminIndexPage}
        allowedRoles={["IT_ADMINS"]}
      />
      <RouteManager
        path="/ATApprovalAdminPage"
        component={ATApprovalAdminPage}
      />
      <RouteManager
        path="/admin/ATApprovalAdminPage"
        component={ATApprovalAdminPage}
      />
      <RouteManager path="/topsymbol" component={TopsideIndexPage} />
      <RouteManager path="/about" component={AboutPage} />

      <RouteManager
        exact
        path="/vswr/create"
        component={CreateForm}
        internalOnly
      />
      <RouteManager
        exact
        path="/vswr/create/:vbuildID"
        component={CreateForm}
        internalOnly
      />
      <RouteManager exact path="/vswr/view" component={ViewForm} internalOnly />
      <RouteManager
        exact
        path="/vswr/view/:vswrID"
        component={ViewForm}
        internalOnly
      />

      <RouteManager exact path="/vswr/edit/:vswrID" component={EditForm} />

      {/* Batch Processing Routes */}
      <RouteManager path="/batch/listswrs" component={ListSwrsPage} />
      <RouteManager path="/batch/updateswrs" component={UpdateSwrsPage} />
      <RouteManager
        path="/batch/forecastsbeview/:swrId"
        component={ForecastSBEView}
      />
      <RouteManager path="/batch/forecastsbeview" component={ForecastSBEView} />
      <RouteManager path="/batch/forecastatview" component={ForecastATView} />
      <RouteManager path="/traveler/:number?" component={VyperTravelerPage} />

      {/* ATSS Mass Upload Project Routes */}
      <RouteManager
        exact
        path="/atssmassupload/projects"
        component={AtssMassUploadPage}
      />
      <RouteManager
        exact
        path="/atssmassupload/project/:projNumber/specchanges"
        component={SpecChange}
      />
       <RouteManager path="/projects/:vyperNumber" component={VyperPage}/>
       <RouteManager path="/atssmassupload/projects/review/:projectId" component={MassReviewScreen}/>
       <RouteManager path="/atssmassupload/projects/actions/:projectId" component={SpecChangeAtssActionPanel}/>

      <RouteManager component={RouteNotFound} />

      {/*<RouteManager path="/help" component={HelpPage}/>*/}
      {/*<RouteManager path="/list" component={VyperListPage}/>*/}
      {/*<RouteManager path="/selection/:buildNumber" component={AtSelectionPage}/>*/}
      {/*<RouteManager path="/queue" component={AtQueuePage}/>*/}
    </Switch>
  );
};
export default Routes;
