import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import {
  ConsoleError<PERSON>andler,
  NoopLoadingHandler,
} from "src/component/fetch/DaoBase";
import useInterval from "src/component/hooks/useInterval";
import { noop } from "src/component/vyper/noop";
import { BuildDao } from "src/dao/BuildDao";

/**
 * Callback for when a job changes state
 * @callback PollForStateChanged~onStatusChanged
 * @param {string} buildNumber - The build number
 * @param {string} state - The state of the build
 */

/**
 * Start polling the API for the status of a build. If the status changes
 * to one of the final states,
 * @param [boolean] run - set to true to start/reset the polling
 * @param {string} buildNumber - The build number to check
 * @param {number} [repeat=30] - The number of times to poll
 * @param {number} [interval=2000] - The delay (in milliseconds) between api calls
 * @param {PollForStateChanged~onStatusChanged} onStatusChanged - callback called when the state changes to a final state
 * @returns {JSX.Element}
 * @constructor
 */
export const PollForStateChanged = ({
  run,
  buildNumber,
  repeat = 30,
  interval = 2000,
  onStatusChanged,
}) => {
  const states = ["BU_REVIEW_CHANGE", "FINAL_APPROVED", "REWORK"];

  const [counter, setCounter] = useState(repeat);

  // we use our own BuildDao because we changed the loading handler
  const buildDao = new BuildDao({
    errorHandler: new ConsoleErrorHandler(),
    loadingHandler: new NoopLoadingHandler(),
  });

  // when run is set to true, reset the counter
  useEffect(() => {
    if (run) {
      setCounter(repeat);
    }
  }, [run]);

  useInterval(
    () => {
      // fetch the status
      buildDao
        .retrieveState(buildNumber)
        .then((json) => {
          // if one of the selected states, call the callback
          if (states.includes(json.state)) {
            onStatusChanged(buildNumber, json.state);
          }
        })
        .catch(noop);

      // decrement the counter
      setCounter((c) => c - 1);
    },
    run && counter > 0 ? interval : null
  );

  return <div />;
};

PollForStateChanged.propTypes = {
  run: PropTypes.bool.isRequired,
  buildNumber: PropTypes.string.isRequired,
  repeat: PropTypes.number,
  interval: PropTypes.number,
  onStatusChanged: PropTypes.func.isRequired,
};
