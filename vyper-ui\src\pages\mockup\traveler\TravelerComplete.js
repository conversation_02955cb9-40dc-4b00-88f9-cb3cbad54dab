import { Incomplete } from "src/pages/mockup/traveler/Incomplete";
import React from "react";
import { Complete } from "src/pages/mockup/traveler/Complete";

export const TravelerComplete = ({ build }) => {
  let complete = build?.traveler?.operations
    .flatMap((operation) => operation.components)
    .every((component) => component.value != null);

  if (build?.traveler?.operations.length === 0) {
    complete = false;
  }

  return complete ? <Complete /> : <Incomplete />;
};
