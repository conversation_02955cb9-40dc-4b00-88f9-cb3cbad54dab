import React, { useContext } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { QuestionLink } from "src/component/question/QuestionLink";
import { HelperContext } from "src/component/helper/Helpers";

export const DescriptionCell = ({ vyper, build, onSave }) => {
  const { canEditDescription } = useContext(HelperContext);
  const canEdit = canEditDescription(vyper, build);

  return (
    <DataCell source={null}>
      <QuestionLink
        title="Edit the Build Description"
        description="Please enter the description for this build."
        value={build.description}
        onSave={onSave}
        multiline
        defaultValue="click to select"
        canEdit={canEdit}
      />
    </DataCell>
  );
};
