import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../RowPrefix";
import { CommentCell2 } from "./CommentCell2";

/**
 * Displays a row of comments.
 * @param {*[]} items - The items
 * @param {CommentCell2~onGetComments} onGetComments - Called to get the list of comments
 * @param {CommentCell2~onSave} onSave - Called to save the comment
 * @return {JSX.Element}
 * @constructor
 */
export function CommentRow2({ items, onGetComments, onSave }) {
  return (
    <TableRow hover>
      <RowPrefix help="comment" title="Comment" />
      {items.map((item, n) => (
        <TableCell key={n}>
          <CommentCell2
            item={item}
            onGetComments={onGetComments}
            onSave={onSave}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

CommentRow2.propTypes = {
  items: PropTypes.array.isRequired,
  onGetComments: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
};
