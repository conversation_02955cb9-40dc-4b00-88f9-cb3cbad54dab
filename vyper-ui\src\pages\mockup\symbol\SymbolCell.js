import React, { useContext, useState } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { HelperContext } from "src/component/helper/Helpers";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../vyper/FormStatus";
import { VyperLink } from "../VyperLink";
import { SymbolDialog } from "src/component/symbol/SymbolDialog";
import { custx } from "src/component/symbol/custx";

export const SymbolCell = ({ vyper, build, onSave }) => {
  const [open, setOpen] = useState(false);
  const { canEditSymbol } = useContext(HelperContext);
  const canEdit = canEditSymbol(vyper, build);

  const buildType = build.buildtype;

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Symbolization")
  )
    return null;

  // get the symbol
  const symbol = build.symbolization.symbols[0];

  // get the symbol name text
  const text =
    symbol?.object?.name == null
      ? "click to select symbol"
      : `${symbol.object.location} = ${symbol.object.name}`;

  // get the ecat
  const ecatValue = build.components.find((c) => c.name === "ECAT")
    ?.instances[0]?.priorities[0]?.object?.name;
  const ecatText = `ECAT = ${ecatValue || "click to select"}`;

  // get the custs
  const custs = [];
  custx.map((name) => {
    const component = build.components.find((c) => c.name === name);
    if (component != null) {
      const value = component.instances[0]?.priorities[0]?.object?.name;
      const ignoreBlank =
        component.instances[0]?.priorities[0]?.object?.ignoreBlank;
      custs.push({ name, value : value?.length==0 ? null : value, ignoreBlank });
    }
  });

  const symbolName = build.symbolization.symbols[0]?.object?.name;

  return (
    <DataCell source={symbol?.source}>
      <VyperLink onClick={() => setOpen(true)} canEdit={canEdit}>
        <div>{text}</div>
        <div>{ecatText}</div>
        {custs.map((cust) => (
          <div key={cust.name}>
            {cust.name} = {cust.value}{" "}
            {(cust.value == null || cust.value === "") &&
            cust.ignoreBlank === "Y"
              ? "- intentional blank -"
              : ""}
          </div>
        ))}
      </VyperLink>

      <SymbolDialog
        open={open}
        onClose={() => setOpen(false)}
        facilityAt={build.facility.object.PDBFacility}
        pkg={build.material.object.PackageDesignator}
        pin={build.material.object.PackagePin}
        buildType={buildType}
        defaultName={symbolName}
        defaultEcat={ecatValue}
        defaultCusts={custs}
        onSave={(location, name, picture, ecat, custs) =>
          onSave(build, location, name, picture, ecat, custs).then(() =>
            setOpen(false)
          )
        }
      />
    </DataCell>
  );
};
