import IconButton from "@material-ui/core/IconButton";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import PropTypes from "prop-types";
import React from "react";
import { DataGrid } from "../universal";
import CompareTable from "./CompareTable";

/**
 * Displays the table used when copying an existing vyper to this new vyper
 *
 * @param materialData - data about this build
 * @param copyBuild - data about this build (may be the same as pgs data)
 * @param boolean showCurrentSelectedBuild - show or hide the current build
 * @param onSelectBuild - called when the user selects a build in the table
 * @param minorChangeTableData records displayed in the table
 * @param buildType - the current build type
 * @returns {JSX.Element|null}
 * @constructor
 */
export const SelectCopyBuild = ({
  materialData,
  copyBuild,
  showCurrentSelectedBuild,
  minorChangeTableData,
  buildType,
  onSelectBuild,
}) => {
  const columns = [
    { field: "BUILD_NUMBER", title: "Build Number" },
    { field: "BUILDTYPE", title: "Build Type" },
    { field: "BUILD_FLOW", title: "Build Flow" },
    { field: "STATE", title: "State" },
    { field: "MATERIAL", title: "Material" },
    { field: "FACILITY", title: "Facility" },
    { field: "PINS", title: "Pins" },
    { field: "PKG", title: "PKG" },
    { field: "SBE", title: "SBE" },
    { field: "SBE1", title: "SBE1"},
  ];

  return (
    <>
      <br />
      <div>
        {minorChangeTableData.length !== 0 ? (
          <>
            <DataGrid
              title={
                <div>
                  <h3>Select a Build to Copy</h3>
                  Below list is pre-filtered with final approved build of new build type of same SBE/Pin/Pkg {buildType}.
                </div>
              }
              columns={columns}
              data={minorChangeTableData}
              pageable
              pageSize={5}
              pageSizeOptions={[5, 10, 20, 50, 100, 200]}
              options={{
                search: false,
              }}
              actions={[
                {
                  icon: () => (
                    <IconButton color="primary" size="small">
                      <ArrowRightAltIcon />
                    </IconButton>
                  ),
                  tooltip: "Select",
                  onClick: onSelectBuild,
                },
              ]}
            />
          </>
        ) : (
          <>No Builds Found...</>
        )}
        <br/>
        <CompareTable
          currentObject={{
            material: materialData.material,
            facility: materialData.facility,
            pins: materialData.pins,
            pkg: materialData.pkg,
            sbe: materialData.sbe,
            sbe1: materialData.sbe1,
          }}
          copyObject={copyBuild}
          showCurrent={
            showCurrentSelectedBuild && minorChangeTableData.length !== 0
          }
        />

        <br />
        <br />
      </div>
    </>
  );
};

SelectCopyBuild.propTypes = {
  materialData: PropTypes.object.isRequired,
  copyBuild: PropTypes.object,
  showCurrentSelectedBuild: PropTypes.bool.isRequired,
  minorChangeTableData: PropTypes.array.isRequired,
  buildType: PropTypes.string,
  onSelectBuild: PropTypes.func.isRequired,
};
