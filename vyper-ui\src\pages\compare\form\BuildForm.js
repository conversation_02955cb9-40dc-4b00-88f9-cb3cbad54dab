import React, { useContext, useState } from "react";
import {
  Button,
  FormControlLabel,
  FormGroup,
  makeStyles,
} from "@material-ui/core";
import { useLocalStorage } from "../../../component/hooks/useLocalStorage";
import { BuildNumberAutocomplete } from "../../../autocomplete/components/BuildNumberAutocomplete";
import { CompareBuild } from "../CompareBuild";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";

const useStyles = makeStyles({
  root: {},
  form: {},
  widget: {
    marginRight: "3rem",
  },
});

export const BuildForm = () => {
  const { buildDao } = useContext(DataModelsContext);

  const [buildNumber1, setBuildNumber1] = useLocalStorage(
    "compare.build.build1"
  );
  const [buildNumber2, setBuildNumber2] = useLocalStorage(
    "compare.build.build2"
  );

  const [build1, setBuild1] = useState();
  const [build2, setBuild2] = useState();

  const handleSubmit = (e) => {
    e.preventDefault();

    buildDao
      .findByBuildNumber(buildNumber1)
      .then((json) => setBuild1(json))
      .catch(noop);

    buildDao
      .findByBuildNumber(buildNumber2)
      .then((json) => setBuild2(json))
      .catch(noop);
  };

  const enableButton = buildNumber1 != null && buildNumber2 != null;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <form className={classes.form} onSubmit={handleSubmit}>
        <FormGroup row>
          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                id="build-form-build1"
                label="Build Number 1"
                variant="outlined"
                aria-describedby="build1-text"
                defaultNumber={buildNumber1}
                onSelect={(number) => setBuildNumber1(number)}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                id="build-form-build2"
                label="Build Number 2"
                variant="outlined"
                aria-describedby="build2-text"
                defaultNumber={buildNumber2}
                onSelect={(number) => setBuildNumber2(number)}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <Button
                type="submit"
                color="primary"
                variant="contained"
                disabled={!enableButton}
              >
                Compare
              </Button>
            }
          />
        </FormGroup>
      </form>

      <CompareBuild build1={build1} build2={build2} />
    </div>
  );
};
