import {
  checkIsInGroup,
  NON_VALIDATABLE_OPERATIONS,
} from "../../select/traveler/ValidatedOperation";
import { New } from "../buildtype/BuildTypes";

/**
 * This is a set of helper functions for dealing with workflow button blockers.
 */

const stateRework = "REWORK";
const stateAtReviewChange = "AT_REVIEW_CHANGE";

/**
 * Return the list of reasons an approval cannot be done.
 *
 * @param {string} approvingGroup - the group being approved.
 * @param {boolean} isFrozen - true if the approval group has already been approved.
 * @param {ApprovalHelper~GroupObject[]} unApprovedGroupsAsObjects - List of the groups that have not approved yet
 * @param {object} build - The build
 * @param {UseAuth~AuthUser} authUser - the current user
 * @param {function} findApprovalOperationByOperation - function to find the approval operation
 * @returns {string[]} - The list of reasons the approval is blocked. Empty list means no reasons to block the approval
 */
export function determineAtApproveBlockers(
  approvingGroup,
  isFrozen,
  unApprovedGroupsAsObjects,
  build,
  authUser,
  findApprovalOperationByOperation
) {
  const blockers = [];

  if (!(isAtReviewChange(build) || isRework(build))) {
    blockers.push("State is not A/T review change or rework.");
  }

  if (!currentUserIsAt(authUser, build)) {
    blockers.push("You are not a member of the A/T.");
  }

  if (isFrozen) {
    blockers.push("This group has already approved or reworked.");
  }

  if (blockedOnDiagrams(build, authUser, approvingGroup)) {
    blockers.push("A MB Diagram has not been approved.");
  }

  unValidatedOperations({
    validationGroups: [
      { groupName: approvingGroup, groupText: approvingGroup },
    ],
    isValidated: unApprovedGroupsAsObjects,
    build,
    findApprovalOperationByOperation,
  }).forEach((o) => blockers.push(`The operation is not checked: ${o}.`));

  return blockers;
}

/**
 * Returns true if the build state is REWORK
 *
 * @param {object} build - The build object
 * @returns {boolean}
 */
function isRework(build) {
  return build.state === stateRework;
}

/**
 * Returns true if the build state is AT_REVIEW_CHANGE
 *
 * @param {object} build - The build object
 * @returns {boolean}
 */
function isAtReviewChange(build) {
  return build.state === stateAtReviewChange;
}

/**
 * Returns true if the current user is in the A/T for the build's device.
 *
 * @param authUser
 * @param {object} build - The build object
 * @returns {boolean | undefined}
 */
function currentUserIsAt(authUser, build) {
  return authUser?.roles?.some((role) =>
    role.groupName?.includes(
      `_${build.facility.object.PDBFacility}_`.toUpperCase()
    )
  );
}

/**
 * Returns true if the current user is in the A/T PREBOND or BOND groups for the build's device.
 *
 * @param authUser
 * @param {object} build - The build object
 * @returns {boolean | undefined}
 */
function currentUserIsAtPrebondOrBond(authUser, build) {
  return (
    authUser?.roles?.some((role) =>
      role.groupName?.includes(
        `_${build.facility.object.PDBFacility}_ASSY_PREBOND`.toUpperCase()
      )
    ) ||
    authUser?.roles?.some((role) =>
      role.groupName?.includes(
        `_${build.facility.object.PDBFacility}_ASSY_BOND`.toUpperCase()
      )
    )
  );
}

/**
 * Returns true if the MB Diagrams have all been approved.
 *
 * @param {object} build - The build object
 * @returns {boolean}
 */
export function diagramsApproved(build) {
  // get the MB Diagram numbers
  // filter out pavv
  // make sure the approval is set (not null)

  return build.components
    .filter((component) => component.name === "MB Diagram")
    .flatMap((component) => component?.instances ?? [])
    .flatMap((instance) => instance?.priorities ?? [])
    .flatMap((priority) => priority?.object?.name ?? [])
    .filter((number) => !number.match(/^[0-9]{8}$/))
    .every((number) => build.diagramApprovals?.[number]?.type != null);
}

/**
 * Returns a list of operation names that have not been checked.
 *
 * @param validationGroups
 * @param {object[]} isValidated -  a [{groupName:,groupText}] of the groups that have not approved yet
 * @param {object} build - The build object
 * @param findApprovalOperationByOperation
 * @returns {*}
 */
function unValidatedOperations({
  validationGroups,
  isValidated,
  build,
  findApprovalOperationByOperation,
}) {
  let grps;
  if (validationGroups != null && validationGroups.length > 0) {
    grps = validationGroups;
  } else {
    grps = isValidated;
  }

  const groups = grps
    .map((role) => role.groupName)
    .filter((groupName) => groupName != null)
    .map((groupName) => groupName.toLowerCase());

  // grab some data from the build
  const buildFacility = build.facility?.object?.PDBFacility;
  const buildSbe = build.material?.object?.SBE;
  const buildSbe1 = build.material?.object?.SBE1;

  return (
    build.traveler.operations

      // filter out engineering deleted operations - they don't need to be approved
      .filter((operation) => !operation.engineeringDeleted)

      // filter out operations that do not need to be approved by the current user
      .filter((operation) => {
        const ao = findApprovalOperationByOperation(operation.name);
        return groups.some((group) => {
          return checkIsInGroup(
            group,
            ao,
            buildFacility,
            buildSbe,
            buildSbe1,
            operation
          );
        });
      })

      // filter out operations that are already validated
      .filter((operation) => {
        const vo = build.validatedOperations.find(
          (v) => operation.name.toLowerCase() === v.operation.toLowerCase()
        );
        return vo?.when == null;
      })

      // return the name of the operation
      .map((operation) => operation.name)

      // filter out the non-validatable operations
      .filter((operation) => !NON_VALIDATABLE_OPERATIONS.includes(operation))
  );
}

/**
 * Returns true if the build type is New
 * @param build
 */
function isBuildTypeNew(build) {
  return build.buildtype === New;
}

/**
 * Check if the mb diagrams have been approved
 *
 * if build type <> New, return false
 * if approver group <> _PREBOND or _BOND, return false
 * if user is not in the a/t site with prebond or bond group, return false
 * if the diagrams are approved, return false
 * otherwise, return true
 *
 * @param {object} build The build
 * @param {object} authUser - The current user
 * @param {string} approvingGroup - The group currently approving
 * @returns {boolean}
 */
function blockedOnDiagrams(build, authUser, approvingGroup) {
  const correctBuildType = isBuildTypeNew(build);
  const correctApproverGroup = approvingGroup.endsWith("BOND");
  const correctUser = currentUserIsAtPrebondOrBond(authUser, build);
  const diagramsAreApproved = diagramsApproved(build);

  return (
    correctBuildType &&
    correctApproverGroup &&
    correctUser &&
    !diagramsAreApproved
  );
}
