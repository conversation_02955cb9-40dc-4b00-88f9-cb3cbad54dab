import React from "react";
import { ComponentNameDisplay } from "src/component/component/ComponentNameDisplay";
import { SourceIcon } from "../../../../component/sourceicon/SourceIcon";
import VyperButton from "../../../../component/VyperButton";
import Attributes from "../../../select/traveler/Attributes";
import { rightPad } from "../../../select/traveler/Padding";
import Paragraph from "../../../select/traveler/Paragraph";
import { custx } from "src/component/symbol/custx";

export const VscnComponent = ({
  build,
  options,
  disabled,
  operation,
  component,
  onEditValue,
  priority,
  checkDisableComponent,
}) => {
  const getComponentValue = () => {
    // if(component.required === "OPTIONAL" && !component.value && disabled){
    //     return '[select]'
    // }
    return component.value || "[select]";
  };
  const checkComponentError = () => {
    // There is no error if optional and left blank
    if (component.required === "OPTIONAL" && !component.value && disabled) {
      return false;
    }
    return component.value == null;
  };

  const value = getComponentValue();
  const space = 25 - value.length;

  let priorityString = "0" + priority;

  // handle the special case - custx, ignore blank
  const ignoreBlank =
    custx.includes(component.name) &&
    build.components.find((c) => c.name === component.name)?.instances?.[0]
      ?.priorities?.[0]?.object?.ignoreBlank === "Y";

  return (
    <div>
      &nbsp;&nbsp;&nbsp;
      <span>
        <SourceIcon
          source={component.sourceName}
          heading={"This component was added by"}
        />
        {rightPad(component.name, 30)}:&nbsp;
        <SourceIcon
          source={component.sourceValue}
          heading={"This value was selected by"}
        />
        {ignoreBlank ? (
          <span>intentionally blank</span>
        ) : (
          <span>
            <VyperButton
              disabled={checkDisableComponent}
              onClick={() => onEditValue(operation, component)}
            >
              <ComponentNameDisplay
                error={checkComponentError()}
                name={value}
                engineering={component.engineering}
              />
            </VyperButton>
            <span>{rightPad(" ", space)}</span>
            <span>{priorityString}</span>
          </span>
        )}
        <br />
        <Attributes attributes={component.attributes} options={options} />
        <Paragraph paragraph={component.paragraph} options={options} />
      </span>
    </div>
  );
};
