import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import { Tab, Tabs } from "@material-ui/core";
import { TabPanel } from "src/pages/admin/sandbox/TabPanel";
import React, { useEffect } from "react";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    backgroundColor: theme.palette.background.paper,
    display: "flex",
    paddingTop: 0,
    minWidth: "20vw",
  },
  tabs: {
    // borderRight: `1px solid ${theme.palette.divider}`,
    overflow: "visible",
    // marginRight: theme.spacing(3),
  },
  tab: {
    whiteSpace: "nowrap",
    textAlign: "left",
  },
  content: {
    paddingLeft: theme.spacing(3),
  },
}));

/**
 * Display a list of tabs in a vertical arrangement.
 *
 * @param {string} storageKey - The localstorage key for storing the selected tab
 * @param {object[]} tabs - The list of tabs
 * @param {number} selectTab - The zero-based integer of the default tab.
 * @returns {JSX.Element}
 * @constructor
 */
export const VerticalTabs = ({ storageKey, tabs, selectTab }) => {
  // store selected tab's index in storage
  const [value, setValue] = useLocalStorage(storageKey, 0);
  const handleChange = (event, newValue) => setValue(newValue);

  // if there is a default value, use it
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const name = params.get("name");
    let tabIndex = tabs.findIndex((tab) => tab.label === name);
    if (-1 !== tabIndex) setValue(tabIndex);
  }, []);

  // on startup, if there is a selected tab, show it
  useEffect(() => {
    if (selectTab != null) {
      setValue(selectTab);
    }
  }, []);

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Tabs
        orientation="vertical"
        variant="scrollable"
        value={value}
        onChange={handleChange}
        className={classes.tabs}
        indicatorColor="primary"
      >
        {tabs.map((tab, n) => (
          <Tab key={n} className={classes.tab} label={tab.label} />
        ))}
      </Tabs>

      <div className={classes.content}>
        {tabs.map((tab, n) => (
          <TabPanel key={n} value={value} index={n}>
            {tab.control}
          </TabPanel>
        ))}
      </div>
    </div>
  );
};

VerticalTabs.propTypes = {
  storageKey: PropTypes.string.isRequired,
  tabs: PropTypes.array.isRequired,
  selectTab: PropTypes.number,
};
