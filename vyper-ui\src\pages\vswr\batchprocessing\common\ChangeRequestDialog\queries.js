import axios from "axios";
import { useMutation } from "react-query";
import { BASE_URL } from "../../common/scswrAPI";
import useSnackbar from "../../../../../hooks/Snackbar";

export let useSwrsRequestUpdater = (options = {}) => {
  let { enqueueSuccessSnackbar } = useSnackbar();
  let { onSuccess } = options;
  let swrsRequestUpdater = useMutation(
    (props) => {
      let { swrIds, changeReason, table } = props;
      return axios.post(
        `${BASE_URL}/proceedUpdateRequests?swrIds=${swrIds}&changeReason=${changeReason}&swrUpdate=${table}`
      );
    },
    {
      onSuccess: (data) => {
        enqueueSuccessSnackbar("Successfully updated swr change(s)");
        if (onSuccess) onSuccess(data);
      },
      onError: () => {},
    }
  );

  return swrsRequestUpdater;
};
