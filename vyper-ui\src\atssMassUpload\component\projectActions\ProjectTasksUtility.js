import config from "../../../buildEnvironment";
import { roleServiceGetGroups } from "../../../component/api/roleService";
import {
  createVscnTask,
  updateTask,
  fetchTaskAssignments,
  updateTaskAssignment,
} from "../../../component/api/taskService2";
/**
    Function to submit the project for AT review

*/
export const submitProjectToAtGroup = (
  project,
  projectDevices,
  authUser,
  action
) => {
  if (action.toLowerCase() === "submit") {
    return getRoleSvcGroups(project, projectDevices)
      .then((groups) => {
        if (groups.length === 0) {
          throw new Error("There are no approver groups defined for facility "+project.facilityAt);
        } else {
          return groups;
        }
      })
      .then((atApproverGroups) => {
        if (atApproverGroups == null || atApproverGroups.length == 0) {
          return;
        }

        const buApproverAids = project.ownerId;
        const groupNames = atApproverGroups.map((grp) => grp.name).join(", ");
        const metaData = [
          {
            attrName: "project number",
            attrValue: project?.projNumber,
          },
          { attrName: "description", attrValue: project?.projName },
          { attrName: "title", attrValue: project?.projNumber },
          { attrName: "groups", attrValue: groupNames },
          { attrName: "owner", attrValue: buApproverAids },
          {
            attrName: "project id",
            attrValue: project?.projId,
          },
          {
            attrName: "ref vyper number",
            attrValue: "VYPER" + project?.refVyperBuildNumber?.split("-")[0].slice("VBUILD".length),
          },
        ];

        const taskInfo = {
          taskName: project?.projNumber,
          ctxIdLabel: "MUProjectNumber",
          ctxIdValue: project?.projNumber,
        };

       const atBranches = [
         {
           stateName: "VSCN_AT_REVIEW",
           branchName: "AT_APPROVE",
         },
         {
           stateName: "VSCN_AT_REVIEW",
           branchName: "REJECT",
         },
       ];

        return createVscnTask(
          metaData,
          taskInfo,
          atApproverGroups,
          buApproverAids,
          atBranches
        );
      });
  }
};

/**
 * Fetch the current approval groups from RoleService
 *
 * @param vyper
 * @param build
 * @returns {Promise}
 */
export const getRoleSvcGroups = async (project, projectDevices) => {
  // Internal role svc api
  const urlRoleSvc = `${config.roleSvcEnv.apiUrl}VYPER_AT_${project.facilityAt}`;

  // Internal role service for EM subcons
  const urlEmRoleSvc = `/vyper/v1/emas/plant/roles/${project.facilityAt}`;

  const plantDetails = await getPlantDetails(project.facilityAt);
  const url = plantDetails?.plantType === "A" ? urlRoleSvc : urlEmRoleSvc;

  return fetch(url, {
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Pragma: "no-cache",
      "Cache-Control": "no-cache",
    },
  })
    .then((response) => {
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`${response.status}: ${response.statusText}`);
      }
    })
    .then((page) =>
      extractVscnGroups(
        project,
        projectDevices,
        project.facilityAt,
        page.content ? page.content : page.groups
      )
    );
};

const getPgsMaterialsInfo = async (...material) => {
  try {
    const response = await fetch(
      `/pgs/v1/pgs/objects/Device?limit=500&expandFromRel=MaterialDie&expandToRel=MaterialDevice&q=Material%3A(${material})`
    );
    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof Error) {
      console.error(" Error:", error.message);
    } else {
      console.error("Error:", error);
    }
  }
};

/**
    Find out the necessary groups for approval
*/
export const extractVscnGroups = async (
  project,
  projectDevices,
  facility,
  groups
) => {
  const projectMaterial = projectDevices[0].material;
  const pgsMaterialData = await getPgsMaterialsInfo(projectMaterial);
  const refObjectArray = Object.keys(pgsMaterialData.metadata.refObjects);
  const materialAttrs = refObjectArray
    .map((refId) => pgsMaterialData.metadata.refObjects[refId])
    .filter(
      (refObj) =>
        refObj.type === "Material" && refObj.attrs.Material === projectMaterial
    )
    .map((refObj) => refObj.attrs)[0];
    // Added flow changes. Mostly Test and Pack
    let flowNamesArray = new Array();
    projectDevices.forEach((device) => flowNamesArray.push(...getFlowsByDevice(device)));
    const flowNames = [...new Set(flowNamesArray)];

  const finalGroups =  groups.filter((group) => isGroupNeeded(materialAttrs, flowNames, facility, group.name));
  return finalGroups;
};

/**
    Check if spec change has this group
*/
export const isGroupNeeded = (
  materialAttrs,
  flowNames,
  facility,
  groupName
) => {
  const flowFromName = groupName.split("_")[2];
  const isTestGroupSbe =
    (groupName.includes("TEST") &&
    (groupName.endsWith(materialAttrs.SBE)) ||
    groupName.endsWith(materialAttrs.SBE1));
  return (
    groupName.startsWith("VYPER") &&
    groupName.includes(facility) &&
    flowNames.includes(flowFromName) > 0 &&
    (isTestGroupSbe || !groupName.includes("TEST"))
  );
};

/**
 */
export const getFlowsByDevice = (projectDevice) => {
  return projectDevice.specChanges
    .filter(
      (chg) =>
        chg.flowType != "ASSEMBLY" ||
        (chg.componentName.includes("Symbol") && chg.attributeName == null)
    )
    .map((chg) => chg.flowType.substr(0, 3) + chg.flowType.substr(-1, 1));
};

export const getPlantDetails = async (facilityAt) => {
  const response = await fetch(
    `/vyper/v1/bdw/plantDetails?facility=${facilityAt}`
  );
  return await response.json();
};

/**
    get the list of tasks pending
*/
export const fetchAllTasks = (project, authUser, setTasks) => {
  // exit if we don't have a current user
  if (authUser.uid === "" || project.projNumber === "" || project.projNumber === undefined ) {
    return Promise.resolve([]);
  }

  return fetchTaskAssignments(
    `VSCN_Approval~MUProjectNumber~${project.projNumber}`
  ).then((taskAssignments) => {
    setTasks(taskAssignments.value || []);
    return taskAssignments;
  });
};

/**
    Function to update task
    Approve / Reject
*/
export const updateVscnTask = () => {};

/**
 * process the Task Assignment records
 * @param {TaskService~TaskAssignment[]} tasks - the task assignment records
 * @param {UseAuth~AuthUser} authUser - the current user
 * @returns {ApprovalHelper~ProcessTasks}
 */
export function processTasks(tasks, authUser) {
  const taskAssignments = determineTaskAssignments(tasks, authUser);
  const taskUser = determineUsers(taskAssignments);
  const unApprovedGroups = determineUnapprovedGroups(taskAssignments);
  const isFrozen = determineIsFrozen(unApprovedGroups);
  const unApprovedGroupsAsObjects =
    determineUnapprovedGroupsAsObjects(unApprovedGroups);
  const isApprover = determineIsApprover(authUser, taskUser);
  const approvedGroups = determineApprovedGroups(tasks);
  const allGroups = determineAllGroups(tasks, authUser);

  return {
    taskAssignments,
    taskUser,
    approvedGroups,
    unApprovedGroups,
    unApprovedGroupsAsObjects,
    allGroups,
    isApprover,
    isFrozen,
  };
}

/**
 * filter the tasks down to the records that match the current user, the current iteration, and not approved
 * @param {TaskService~TaskAssignment[]} tasks - task assignment records for current user that are not approved
 * @param {UseAuth~AuthUser} authUser - the current user
 * @return {TaskService~TaskAssignment[]}
 */
function determineTaskAssignments(tasks, authUser) {
  // noinspection JSValidateTypes
  return tasks
    .filter((task) => task.current)
    .filter((task) => !task.complete)
    .filter((task) => !!task.availBranchNames)
    .filter((task) => task.userId === authUser.uid.toLowerCase());
}

/**
 * get the unique list of groups that haven't approved yet
 * @param {TaskService~TaskAssignment[]} taskAssignments - all of the task assignment records
 * @return {string[]}
 */
function determineUnapprovedGroups(taskAssignments) {
  return [...new Set(taskAssignments.map((task) => task.fnctName))];
}

/**
 * determine if the current build is frozen (the current user has no groups to approve)
 * @param {string[]} unApprovedGroups - the unapproved groups
 * @type {boolean}
 */
function determineIsFrozen(unApprovedGroups) {
  return unApprovedGroups.length === 0;
}

/**
 * @typedef ApprovalHelper~GroupObject
 * @property {string} groupName - the name of the group
 * @property {string} groupText - the text of the group
 */
/**
 * Return the unapproved groups as objects
 *
 * @param {string[]} unApprovedGroups - the list of unapproved groups
 * @returns {ApprovalHelper~GroupObject[]}
 */
function determineUnapprovedGroupsAsObjects(unApprovedGroups) {
  return unApprovedGroups.map((group) => ({
    groupName: group,
    groupText: group,
  }));
}

/**
 * return the current user id if they have not approved, and the group has not approved the build.
 * @param {TaskService~TaskAssignment[]} taskAssignments
 * @returns {string|null}
 */
function determineUsers(taskAssignments) {
  return taskAssignments?.[0]?.userId;
}

/**
 * determine if the current user approved this task
 * @param {UseAuth~AuthUser} authUser - the current user
 * @param {string|undefined} users - the user who approved the task, or undefined
 * @returns {boolean} - true if the current is an approver of this task
 */
function determineIsApprover(authUser, users) {
  return authUser.uid.toLowerCase() === users;
}

/**
 * get the list of groups that have approved
 * @param {TaskService~TaskAssignment[]} tasks - the list of tasks assignments for the build.
 * @return {ApprovalHelper~GroupObject[]} array of {group, username, date} of the approved, completed tasks.
 */
function determineApprovedGroups(tasks) {
  // noinspection JSValidateTypes
  return tasks
    .filter((task) => task.complete && task.current)
    .filter((task) => task.branchName.includes("AT_APPROVE"))
    .map((task) => ({
      group: task.fnctName,
      username: task.userName,
      date: task.modifiedDttm,
    }));
}

/**
 * get the complete list of groups for the current user.
 * @param {TaskService~TaskAssignment[]} tasks - task assignment records for current user that are not approved
 * @param {UseAuth~AuthUser} authUser - the current user
 * @return {string[]} - array of group names.
 */
function determineAllGroups(tasks, authUser) {
  return [
    ...new Set(
      tasks
        .filter((task) => task.userId === authUser.uid.toLowerCase())
        .filter((task) => task.current)
        .map((task) => task.fnctName)
    ),
  ];
}

/**
 * Approve the task
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function approveTask(tasks, authUser, comment, group) {
  return approveOrRejectTheTask(
    tasks,
    authUser,
    TASK_UPDATE_APPROVE,
    comment,
    "",
    group
  );
}

/**
 * Reject the task
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} reason - The rejection reason, or null
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function rejectTask(tasks, authUser, comment, reason, group) {
  return approveOrRejectTheTask(
    tasks,
    authUser,
    TASK_UPDATE_REWORK,
    comment,
    reason,
    group
  );
}

/**
 * Update the current task (Approve or Rework)
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} action - Approve or Reject
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} reason - The rejection reason, or null
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function approveOrRejectTheTask(
  tasks,
  authUser,
  action,
  comment,
  reason,
  group
) {
  // find the record for the current user and group
  // get the taskUuid and asmtUuid
  const assignment = tasks.find(
    (assignment) =>
      assignment.fnctName === group &&
      assignment.userId.toLowerCase() === authUser.uid.toLowerCase() &&
      assignment.current &&
      !assignment.complete
  );

  const branchName = assignment?.availBranchNames.find((branch) =>
    branch.toLowerCase().includes(action.toLowerCase())
  );

  if (!branchName) {
    throw new Error(`Branch not found! for action ${action}`);
  }

  // build the payload
  const taskChangesPayload = {
    branchName: branchName,
    commentText: JSON.stringify({
      reason: reason,
      comment: comment,
    }),
  };

  // update the task
  return updateTaskAssignment(
    assignment.taskUuid,
    assignment.asmtUuid,
    taskChangesPayload
  );
}
