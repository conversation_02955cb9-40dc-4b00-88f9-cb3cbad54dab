import React, { useContext, useEffect, useState } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { ListAlt } from "@material-ui/icons";
import { BackToBuildFormLink } from "src/component/backbutton/BackToBuildFormLink";
import { BuildPageTitle } from "src/pages/vyper/BuildPageTitle";
import Tooltip from "@material-ui/core/Tooltip";
import Button from "@material-ui/core/Button";
import { headerItems } from "src/pages/mockup/traveler/headerItems";
import { DataModelsContext } from "src/DataModel";
import { TemplateBanner } from "src/pages/mockup/template/TemplateBanner";

const useStyles = makeStyles({
  root: {},
  floatRight: {
    float: "right",
  },
  hiddenSpan: {
    display: "none",
  },
  comment: {
    fontWeight: "bold",
  },
});

export const VyperTravelerPlainPage = ({ vyperNumber, buildNumber }) => {
  const { build } = useContext(DataModelsContext);

  const [traveler, setTraveler] = useState();

  useEffect(() => {
    // build the traveler

    const pad =
      "                                                                                ";
    const lpad = (str, length) => (pad + str).slice(-length);
    const rpad = (str, length) => (str + pad).substring(0, length);

    let traveler = "";

    const append = (head1, data1, head2, data2) => {
      let ret = lpad(head1, 19) + ": " + rpad(data1, 31) + "  ";

      if (head2 != null) {
        ret += lpad(head2, 20) + ": " + data2 + " \n";
      }

      return ret;
    };

    const paragraph = (text) => {
      if (text == null) {
        return null;
      } else {
        return (
          text
            .split("\n")
            .map((line) => "     " + line)
            .join("\n") + "\n"
        );
      }
    };

    // traveler title
    traveler += pad.slice(-27) + buildNumber + " Device Specification\n";
    traveler += "\n";

    // traveler header
    const items = headerItems(build);
    for (let n = 0; n < items.length; n += 2) {
      const item1 = items[n];
      const item2 = items[n + 1];
      traveler += append(
        item1?.title,
        item1?.value,
        item2?.title,
        item2?.value
      );
    }

    traveler += "\n";
    traveler += "\n";

    build?.traveler?.operations

      // remove any engineering-deleted operations
      ?.filter(
        (operation) =>
          operation.engineeringDeleted == null ||
          operation.engineeringDeleted === false
      )
      ?.filter((operation) => operation.name !== "TEST")

      ?.forEach((operation) => {
        traveler += "\n";
        traveler += operation.name;
        traveler += "\n";
        if (operation.comment) {
          traveler += "\n";

          operation.comment.split("\n").forEach((line) => {
            traveler += "### " + line + "\n";
          });

          traveler += "\n";
          traveler += "\n";
        }

        const priorities = {};

        operation.components.forEach((component) => {
          let priority;
          if (priorities[component.name] == null) {
            priority = 1;
          } else {
            priority = priorities[component.name] + 1;
          }
          priorities[component.name] = priority;

          traveler += "   ";
          traveler += rpad(component.name, 30);
          traveler += ": ";
          traveler += rpad(component.value || "", 52);
          traveler += ("00" + priority).slice(-2);
          traveler += "\n";
          component.attributes.forEach((attribute) => {
            traveler += "      ";
            traveler += rpad(attribute.name, 27);
            traveler += ": ";
            traveler += attribute.value || "";
            traveler += "\n";
          });
          if (component.paragraph != null) {
            traveler += paragraph(component.paragraph);
            traveler += "\n";
          }
        });
      });

    setTraveler(traveler);
  }, [build]);

  const downloadTxtFile = () => {
    const element = document.createElement("a");
    const file = new Blob([document.getElementById("myInput").value], {
      type: "text/plain",
    });
    element.href = URL.createObjectURL(file);
    element.download = buildNumber + ".txt";
    document.getElementById("hiddenSpan").appendChild(element); // Required for this to work in FireFox
    element.click();
  };

  // if (build == null) return null;

  const classes = useStyles();

  return (
    <div>
      <span id="hiddenSpan" className={classes.hiddenSpan}>
        Hidden Span here
        <textarea id="myInput" value={traveler} />
      </span>

      <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />

      <BuildPageTitle build={build} title="Traveler" />

      <TemplateBanner build={build} />

      <div>
        <Tooltip title="Download as Plain Text file" arrow>
          <Button
            variant="contained"
            color="secondary"
            onClick={downloadTxtFile}
            style={{ fontWeight: "bold" }}
          >
            <ListAlt /> Download as Text File
          </Button>
        </Tooltip>
      </div>

      <div className={classes.root}>
        <pre>{traveler}</pre>
      </div>
    </div>
  );
};
