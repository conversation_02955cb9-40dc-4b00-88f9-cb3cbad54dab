import { useEffect } from "react";
import { useLocation } from "react-router-dom";

/**
 * Moves to the top of the page when the page changes.
 * Add the <ScrollToTop> component just above the <Routes/> tag (in App.js)
 *
 * https://reactrouter.com/web/guides/scroll-restoration
 * @returns {null}
 * @constructor
 */
export default function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}
