import { Column<PERSON><PERSON>, <PERSON>rid<PERSON><PERSON> } from "ag-grid-community";

export const TIAgEventType = {
  CELL_ACTION_CLICKED: "cellActionClicked",
  CELL_LINK_CLICKED: "cellLinkClicked",
};

/** @type {ColumnA<PERSON>} */
export const EmptyColumnApi = null;

/** @type {GridApi} */
export const EmptyGridApi = null;

/** @type {{api: GridApi, columnApi: ColumnApi}} */
export const EmptyGridRef = null;

export const DefaultPageSize = 50;
