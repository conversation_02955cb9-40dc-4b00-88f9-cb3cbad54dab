import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { FetchContext } from "src/component/fetch/VyperFetch";
import { DataGrid } from "src/component/universal";

const useStyles = makeStyles({
  root: {},
});

export const WaferSawMethodPanel = () => {
  const { vget, vpost, vdelete } = useContext(FetchContext);

  const columns = [{ field: "name", title: "Name" }];

  // download the wafer saw methods
  const [data, setData] = useState([]);
  useEffect(() => {
    vget("/vyper/v1/wafersawmethod?size=1000", (json) => setData(json.content));
  }, []);

  const handleRowAdd = (newData) => {
    return new Promise((resolve) => {
      vpost("/vyper/v1/wafersawmethod/", newData, (method) => {
        setData([...data, method]);
        resolve();
      });
    });
  };

  const handleRowUpdate = (newData, oldData) => {
    return new Promise((resolve) => {
      vpost(`/vyper/v1/wafersawmethod/${newData.id}`, newData, () => {
        const data2 = [...data];
        data2[oldData.tableData.id] = newData;
        setData(data2);
        resolve();
      });
    });
  };

  const handleRowDelete = (oldData) => {
    return new Promise((resolve) => {
      vdelete(`/vyper/v1/wafersawmethod/${oldData.id}`, {}, () => {
        const data2 = [...data];
        data2.splice(oldData.tableData.id, 1);
        setData(data2);
        resolve();
      });
    });
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Wafer Saw Methods"
        data={data}
        columns={columns}
        editable={true}
        onRowAdd={handleRowAdd}
        onRowUpdate={handleRowUpdate}
        onRowDelete={handleRowDelete}
        options={{
          pageSize: 10,
        }}
      />
    </div>
  );
};
