import React, { useState, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import Chip from "@material-ui/core/Chip";
import Button from "@material-ui/core/Button";
import { ListAlt, Edit, Add } from "@material-ui/icons";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import { useParams } from "react-router-dom";

const tabSpaceSize = 4;

const longestText = (travelerInfo) => {
  let longestName = 0;
  let longestValue = 0;

  travelerInfo?.operations?.forEach((operation) => {
    if (operation.name === "HEADER" || operation.engineeringDeleted) {
      return;
    }
    longestName = Math.max(longestName, operation.name.length);

    operation.components.forEach((component) => {
      let componentValue = component.value ? component.value : "";

      longestName = Math.max(longestName, tabSpaceSize + component.name.length);
      longestValue = Math.max(longestValue, componentValue.length);

      component.attributes.forEach((attribute) => {
        let attributeValue = component.value ? attribute.value : "";

        longestName = Math.max(
          longestName,
          2 * tabSpaceSize + attribute.name.length
        );
        longestValue = Math.max(longestValue, attributeValue.length);
      });
    });
  });

  return { longestName, longestValue };
};

const downloadTxtFile = (txt) => {
  const element = document.createElement("a");
  const file = new Blob([txt], {
    type: "text/plain",
  });
  element.href = URL.createObjectURL(file);
  element.download = "myFile.txt";
  document.body.appendChild(element);
  element.click();
};

const ManufacturingInfo = (props) => {
  const {
    classes,
    travelerInfo,
    comments = {},
    handleCommentClick,
    headerItems,
    readOnly = false,
    vbuildID,
  } = props;
  const [plainTravelerHTML, setPlainTravelerHTML] = useState([]);
  const [plainTravelerTXT, setPlainTravelerTXT] = useState("");

  const formatTraveler = (travelerInfo, comments) => {
    let plainTravelerHTML = [];
    let plainTravelerTXT = "";
    //find longest text to account for minimum amount of tabs
    const { longestName, longestValue } = longestText(travelerInfo);
    //extra tabs to add after min req tabs calculated
    const extraSpaces = 4;

    plainTravelerTXT += `${" ".repeat(
      (longestName + longestValue - 30) / 2
    )}${vbuildID} Device Specification\n\n`;
    plainTravelerHTML.push(<pre>{plainTravelerTXT}</pre>);

    for (let i = 0; i < headerItems.length; i += 2) {
      let headerRow = "";
      const item1 = headerItems[i];
      const item2 = headerItems[i + 1];
      let spaces = " ".repeat(19 - item1.title.length);
      headerRow += `${spaces}${item1.title}: ${item1.value}`;
      if (item2) {
        spaces = " ".repeat(30 - (item1?.value?.length || 0));
        headerRow += `${spaces}${item2.title}: ${item2.value}`;
      }
      plainTravelerTXT += `${headerRow}\n`;
      plainTravelerHTML.push(<pre>{headerRow}</pre>);
    }
    plainTravelerTXT += "\n\n";
    plainTravelerHTML.push(
      <>
        <br />
        <br />
      </>
    );

    travelerInfo?.operations?.forEach((operation) => {
      const priorities = {};
      if (operation.name === "HEADER" || operation.engineeringDeleted) {
        return;
      }
      plainTravelerTXT += `${operation.name}\n`;
      plainTravelerHTML.push(<pre>{`${operation.name}\n`}</pre>);

      if (operation.name in comments) {
        const comment =
          comments[operation.name][comments[operation.name].length - 1].comment;
        plainTravelerTXT += `######   ${comment}   ######\n`;
        if (readOnly) {
          plainTravelerHTML.push(`######   ${comment}   ######\n`);
        } else {
          plainTravelerHTML.push(
            <Chip
              color="secondary"
              label={comment}
              onClick={() => {
                handleCommentClick(operation.name);
              }}
              clickable
              icon={<Edit />}
            />
          );
        }
      } else {
        if (!readOnly) {
          plainTravelerHTML.push(
            <Chip
              color="primary"
              label={"Add New Manufacturing Instruction"}
              onClick={() => {
                handleCommentClick(operation.name);
              }}
              clickable
              icon={<Add />}
            />
          );
        }
      }

      operation.components.forEach((component) => {
        let travelerOpAtt = "";
        if (component.name in priorities) {
          priorities[component.name]++;
        } else {
          priorities[component.name] = 1;
        }

        let componentValue = component.value ? component.value : "";

        //find min tabs required to match longest text
        let tabValCount =
          extraSpaces + longestName - (tabSpaceSize + component.name.length);
        let tabInstanceCount =
          extraSpaces + longestValue - componentValue.length + 1;

        travelerOpAtt += `${" ".repeat(tabSpaceSize)}${component.name}`;
        travelerOpAtt += `${" ".repeat(tabValCount)}:${componentValue}`;
        travelerOpAtt += `${" ".repeat(tabInstanceCount)}0${
          priorities[component.name]
        }\n`;

        component.attributes.forEach((attribute) => {
          let attributeValue = component.value ? attribute.value : "";
          let tabValCount =
            extraSpaces +
            longestName -
            (2 * tabSpaceSize + attribute.name.length);

          travelerOpAtt += `${" ".repeat(tabSpaceSize * 2)}${attribute.name}`;
          travelerOpAtt += `${" ".repeat(tabValCount)}:${attributeValue}\n`;
        });

        if (component.paragraph !== null) {
          travelerOpAtt +=
            component.paragraph
              .split("\n")
              .map((line) => `${" ".repeat(tabSpaceSize * 2)}${line}`)
              .join("\n") + "\n";
        }

        plainTravelerTXT += travelerOpAtt;
        plainTravelerHTML.push(<pre>{travelerOpAtt}</pre>);
      });
    });
    return { plainTravelerTXT, plainTravelerHTML };
  };

  useEffect(() => {
    const { plainTravelerTXT, plainTravelerHTML } = formatTraveler(
      travelerInfo,
      comments
    );
    setPlainTravelerTXT(plainTravelerTXT);
    setPlainTravelerHTML(plainTravelerHTML);
  }, [travelerInfo, comments]);

  return (
    <Paper elevation={24} className={classes.paper}>
      <Typography variant="h6">Manufacturing Information </Typography>
      <Grid>
        <Button
          variant="contained"
          color="secondary"
          onClick={() => {
            downloadTxtFile(plainTravelerTXT);
          }}
          style={{ fontWeight: "bold" }}
        >
          <ListAlt /> Download as Text File
        </Button>
        {plainTravelerHTML}
      </Grid>
    </Paper>
  );
};
export default ManufacturingInfo;
