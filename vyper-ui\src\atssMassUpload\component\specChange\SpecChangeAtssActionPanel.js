import React, { useState, useEffect, useContext, useRef } from "react";
import { useHistory, usePara<PERSON>, Link } from "react-router-dom";
import TiServerAgGrid, { exportGridAsExcel } from "../../../lib/TiServerAgGrid";
import { makeStyles, withStyles } from "@material-ui/core/styles";
import { Button, Typography, Checkbox } from "@material-ui/core";
import ErrorOutlineRoundedIcon from "@material-ui/icons/ErrorOutlineRounded";
import MuiAccordion from "@material-ui/core/Accordion";
import MuiAccordionSummary from "@material-ui/core/AccordionSummary";
import MuiAccordionDetails from "@material-ui/core/AccordionDetails";
import Grid from "@material-ui/core/Grid";
import { ProjectHeader } from "../newProject/ProjectHeader";
import { ProjectHeaderLinks } from "../specChange/ProjectHeaderLinks";
import { MuProjectActions } from "../projectActions/MuProjectActions";
import { AtssScnStatusDialog } from "../projectActions/AtssScnStatusDialog";
import { CamsDiffPopup } from "./CamsDiffPopup";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { AgGridReact } from "ag-grid-react"; // the AG Grid React Component
import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS
import './SpecChangeAtssActionPanel.css';

import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { AuthContext } from "src/component/common/auth";
import AgGridChangeSelectCellEditor from "./AgGridChangeSelectCellEditor";

const Accordion = withStyles({
  root: {
    border: "1px solid rgba(0, 0, 0, .125)",
    boxShadow: "none",
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
    "&$expanded": {
      margin: "auto",
    },
  },
  expanded: {},
})(MuiAccordion);

const AccordionSummary = withStyles((theme) => ({
  root: {
    backgroundColor: "rgba(0, 0, 0, .05)",
    borderBottom: "1px solid rgba(0, 0, 0, .125)",
  },
  content: {
    "&$expanded": {
      margin: "1px 0",
    },
  },
  expanded: {},
}))(MuiAccordionSummary);

const AccordionDetails = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
    weight: "bold",
  },
}))(MuiAccordionDetails);

const useStyles = makeStyles((theme) => ({
  deviceGridStyle: {
    "& .ag-header-row": {
      backgroundColor: theme.palette.primary.main,
      color: "white",
    },
  },
  gridLayout: {
    justifyContent: "flex-start",
  },
}));

export const SpecChangeAtssActionPanel = () => {
  const { projectId } = useParams();
  const classes = useStyles();
  const history = useHistory();
  const { vpost, vget } = useContext(FetchContext);
  const [projectInfo, setProjectInfo] = useState({ projectId: projectId });
  const [isLoading, setIsLoading] = useState(true);
  const [camsDiff, setCamsDiff] = useState(null);
  const [camsPopupOpen, setCamsPopupOpen] = useState(false);

  const [headerExpandFlag, setHeaderExpandFlag] = useState(false);
  const [projectDevices, setProjectDevices] = useState(null);
  const handleChange = (panel) => (event, newExpanded) => {
    setHeaderExpandFlag(!headerExpandFlag);
  };

  const [scnLog, setScnLog] = useState({});
  const [atssScnPopup, setAtssScnPopup] = useState(false);
  const gridRef = useRef();
  const { open: openError } = useContext(ErrorDialogContext);
  const currentPageUrlObj = {
    title: "Actions",
    link: `/vyper/atssmassupload/projects/actions/${projectId}`,
  };
  const { authUser } = useContext(AuthContext);

  /**
        handle the changes performed in the grid
    */
  const saveSelection = (props, newSelection) => {
    if (projectInfo?.ownerId !== authUser.uid) return;
    const updatedProjectDevice = props.data;
    if (props.colDef.field === "isMultiBuild") {
      updatedProjectDevice.isMultiBuild =
        updatedProjectDevice.isMultiBuild != undefined
          ? !updatedProjectDevice.isMultiBuild
          : true;
      postDeviceData(updatedProjectDevice);
    }
    if (props.colDef.field === "cmsNumber") {
      updatedProjectDevice.cmsNumber = newSelection;
      postDeviceData(updatedProjectDevice);
    }
    setProjectDevices(
      projectDevices.map((device) =>
        props.data.id === device.id ? updatedProjectDevice : device
      )
    );
  };

  const redirectToMassReview = (e) => {
    history.push(`/atssmassupload/projects/review/${projectInfo.projId}`);
  };

  const gotoUploadPage = (e) => {
    history.push(`/atssmassupload/project/${projectInfo.projNumber}/specchanges`);
  };

  // TBD to show the log of device change history
  const showDeviceLog = (lineData) => {
    if (lineData != undefined) {
      // Call API and show error
      vget(
        `/vyper/v1/atssmassupload/project/device/${lineData.id}/log`,
        (atssScnList) => {
          if (atssScnList?.length > 0) {
            const latestLog = atssScnList.filter((a) => a.logDate != null);
            setScnLog(latestLog[0]);
            setAtssScnPopup(true);
          }
        }
      );
    }
  };

  const vscnNumberRenderer = (node) => {
    const rowData = node.data;
    if (rowData !== undefined) {
      const vyperNumber =
        "VYPER" + rowData.vscnNumber?.split("-")[0].slice("VSCN".length);
      return (
        <Link to={`/projects/${vyperNumber}/vscns/${rowData.vscnNumber}`}>
          {rowData.vscnNumber}
        </Link>
      );
    }
    return <></>;
  };

  const gridColumnsConfig = [
    {
      field: "id",
      hide: true,
    },
    {
      headerName: "Material",
      field: "material",
      headerCheckboxSelection: projectInfo?.projStatus !== "AT REVIEW",
      checkboxSelection: projectInfo?.projStatus !== "AT REVIEW",
      showDisabledCheckboxes: true,
      minWidth: 230,
    },
    {
      headerName: "Old Material",
      field: "oldMaterial",
    },
    {
      headerName: "Spec Device",
      field: "specDevice",
    },
    {
      headerName: "Multi build",
      field: "isMultiBuild",
      width: 150,
      cellRenderer: (props) => {
        return (
          <div>
            <Checkbox
              size="small"
              color="secondary"
              checked={props.value ? true : false}
              onChange={(event) => saveSelection(props, null)}
            />
          </div>
        );
      },
    },
    {
      headerName: "VSCN",
      field: "vscnNumber",
      width: 170,
      cellRenderer: vscnNumberRenderer,
    },
    {
      headerName: "ATSS SCN",
      field: "scnId",
      cellDataType: "number",
      width: 120,
    },
    {
      headerName: "Device Status",
      field: "status",
    },
    {
      headerName: "ChangeLink",
      field: "cmsNumber",
      editable: true,
      cellEditor: (props) => {
        return (
          <AgGridChangeSelectCellEditor
            onChange={(changeNumber) => saveSelection(props, changeNumber)}
            {...props}
          />
        );
      },
    },
    {
      headerName: "PIM Check",
      field: "pimCheckValidation",
    },
    {
      headerName: "Ref Spec Id",
      field: "refSpecId",
    },
    {
      headerName: "Actions",
      cellRenderer: (props) => {
        return (
          <div>
            <ErrorOutlineRoundedIcon
              fontSize="small"
              onClick={() => showDeviceLog(props.data)}
              color="action"
            />
          </div>
        );
      },
    },
  ];

  const defaultGridColDef = { resizable: true, enableCellTextSelection: true };

  /** Load the project info of the project */
  useEffect(() => {
    refreshProjectInfo();
  }, []);

  const [devicesFromScnStatusRefresh, setDevicesFromScnStatusRefresh] = useState(null);

  const refreshProjectInfo = (scnStatusRefreshData) => {
    setDevicesFromScnStatusRefresh(scnStatusRefreshData);
    setIsLoading(true);
    vget(`/vyper/v1/atssmassupload/project/${projectId}`, (json) => {
      setIsLoading(false);
      const projectInfoUpd = json;
      if (
        (json?.projStatus === "DRAFT" || json?.projStatus === "VALIDATED") &&
        json?.praState === "PRA_APPROVED"
      ) {
        projectInfoUpd.projStatus = "REF VALIDATED";
      }
      setProjectInfo(projectInfoUpd);
    });
  };

  const loadDeviceData = () => {
    if(devicesFromScnStatusRefresh?.length > 0){
      setProjectDevices(devicesFromScnStatusRefresh);
      setDevicesFromScnStatusRefresh(null);
      return;
    }
    setIsLoading(true);
    vget(`/vyper/v1/atssmassupload/projects/${projectId}/devices`, (json) => {
      setIsLoading(false);
      setProjectDevices(json);
      gridRef.current?.api.refreshCells();
    });
  };

  /** Load the spec changes for the project */
  useEffect(() => {
    loadDeviceData();
  }, [projectInfo]);

  /**
        Save the project device info
    */
  const postDeviceData = (projectDeviceInfo) => {
    const projectDeviceInfoFields = {
      id: projectDeviceInfo.id,
      isMultiBuild: projectDeviceInfo.isMultiBuild,
      cmsNumber: projectDeviceInfo.cmsNumber,
    };

    vpost(
      `/vyper/v1/atssmassupload/project/device/save`,
      projectDeviceInfoFields
    );
  };

  /** Check the new component and attributes */
  const seeCamsDiff = () => {
    if (camsDiff != null && camsDiff != undefined) {
      return setCamsPopupOpen(true);
    }
    vget(
      `/vyper/v1/atssmassupload/traveler/${projectInfo.projId}/camsdiff`,
      (json) => {
        setCamsDiff(json);
        setCamsPopupOpen(true);
      }
    );
  };

  const handleCloseDialog = () => {
    setCamsPopupOpen(!camsPopupOpen);
  };

  if (isLoading) return <div>Loading...</div>;
  const isValidated = projectInfo.projStatus != "DRAFT";

  return (
    <div>
      <ProjectHeaderLinks
        projectHeaderInfo={projectInfo}
        currentPage={currentPageUrlObj}
      />
      <Accordion
        square
        expanded={headerExpandFlag}
        onChange={handleChange("panel1")}
      >
        <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          <Grid
            container
            spacing={2}
            justifyContent="flex-start"
            alignItems="flex-start"
          >
            <Grid item xs>
              Project Name:{" "}
              <Typography variant="outlined">
                {" "}
                {projectInfo?.projName}
              </Typography>
            </Grid>
            <Grid item xs>
              Facility:{" "}
              <Typography variant="outlined">
                {" "}
                {projectInfo?.facilityAt}
              </Typography>
            </Grid>
            <Grid item xs>
              Status :
              <Typography variant="outlined">
                {" "}
                {projectInfo?.projStatus}{" "}
              </Typography>
            </Grid>
          </Grid>
        </AccordionSummary>
        <AccordionDetails>
          <ProjectHeader
            projectHeaderInfo={projectInfo}
            setProjectHeaderInfo={setProjectInfo}
          />
        </AccordionDetails>
      </Accordion>
      {isValidated && (
        <MuProjectActions
          gridRef={gridRef}
          projectInfo={projectInfo}
          gotoUploadPage={gotoUploadPage}
          seeCamsDiff={seeCamsDiff}
          projectDevices={projectDevices}
          redirectToMassReview={redirectToMassReview}
          setProjectInfo={setProjectInfo}
          refreshData={refreshProjectInfo}
        />
      )}
      <div
        style={{ height: "800px" }}
        className={`ag-theme-alpine ${classes.deviceGridStyle}`}
      >
        <AgGridReact
          ref={gridRef}
          defaultColDef={defaultGridColDef}
          columnDefs={gridColumnsConfig}
          rowData={projectDevices}
          animateRows={true}
          rowSelection={"multiple"}
          rowMultiSelectWithClick={true}
          enableCellTextSelection={true}
        />
      </div>
      <CamsDiffPopup
        deviceCamsList={camsDiff}
        open={camsPopupOpen}
        handleCloseDialog={handleCloseDialog}
      />

      <AtssScnStatusDialog
        open={atssScnPopup}
        atssScn={scnLog}
        onClose={() => {
          setAtssScnPopup(false);
        }}
      />
    </div>
  );
};
