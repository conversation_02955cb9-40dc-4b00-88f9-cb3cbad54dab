import React from "react";
import { TableCell } from "@material-ui/core";
import { VyperLink } from "../../mockup/VyperLink";
import { PraIcons } from "src/pages/vyper/pras/PraIcons";
import { makeStyles } from "@material-ui/styles";
import { DataCell } from "src/component/datacell/DataCell";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
  icons: {
    minWidth: 150,
  },
  inline: {
    display: "inline-block",
  },
  noMargin: {
    margin: 0,
  },
}));

export const VscnTestCell = ({ vscn, handleUpload, index }) => {
  const classes = useStyles();

  const getVerifiers = (testComponent) => {
    console.log({
      result: vscn.verifiers.filter(
        (verifier) => verifier.value === testComponent.value
      ),
    });
    return vscn.verifiers.filter(
      (verifier) => verifier.value === testComponent.value
    );
  };

  let uniqueTestComponents = vscn.test?.travelerOperations
    ?.flatMap((travelerOperation) => travelerOperation?.components)
    .filter(
      (component) =>
        component.name.includes("Test Program") ||
        component.name.includes("DEV Type")
    )
    .reduce((acc, curr) => {
      if (!acc.find((e) => e.name === curr.name)) {
        acc.push(curr);
      }
      return acc;
    }, []);

  return (
    <DataCell source={vscn.test?.source}>
      {uniqueTestComponents.map((testComponent, n) => (
        <TestDisplay
          vscn={vscn}
          handleUpload={handleUpload}
          verifiers={getVerifiers(testComponent)}
          index={n}
          testComponent={testComponent}
        />
      ))}
    </DataCell>
  );
};

const TestDisplay = ({
  vscn,
  handleUpload,
  verifiers,
  index,
  testComponent,
}) => {
  const classes = useStyles();

  let testComponentTitle;

  if (testComponent) {
    testComponentTitle = `${testComponent.name} : ${testComponent.value}`;
    if (testComponent.name?.includes("Test Program")) {
      let revisionAttribute = testComponent.attributes.find(
        (a) => a.name === "Revision"
      );
      if (revisionAttribute) {
        testComponentTitle += `(${revisionAttribute.value})`;
      }
    }
  }

  return (
    <div className={classes.root}>
      <div className={classes.icons}>
        <PraIcons verifiers={verifiers} />
      </div>
      <div className={classes.inline}>
        <VyperLink onClick={(e) => handleUpload(vscn)} key={index}>
          <div>{testComponentTitle}</div>
        </VyperLink>
      </div>
    </div>
  );
};
