import React from "react";
import { TableCell } from "@material-ui/core";
import { VyperLink } from "../../mockup/VyperLink";
import { PraIcons } from "src/pages/vyper/pras/PraIcons";
import { makeStyles } from "@material-ui/styles";
import { DataCell } from "src/component/datacell/DataCell";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
  icons: {
    minWidth: 150,
  },
}));

export const VscnTestCell = ({ vscn, handleUpload, key }) => {
  const classes = useStyles();

  const verifiers = vscn.verifiers.filter(
    (verifier) => verifier.name === "Test Program 1"
  );

  const fetchComponentValue = (componentName) => {
    let value = undefined;
    vscn.test?.travelerOperations?.find((operation) =>
      operation?.components?.find((component) => {
        if (component.name === componentName) {
          value = component.value;
          return true;
        }
        return false;
      })
    );
    return value;
  };

  const fetchAttributeValue = (componentName, attributeName) => {
    let value = undefined;
    vscn.test?.travelerOperations?.find((operation) =>
      operation?.components
        ?.find((component) => component.name === componentName)
        ?.attributes?.find((attribute) => {
          if (attribute.name === attributeName) {
            value = attribute.value;
            return value;
          }
        })
    );
    return value;
  };

  return (
    <DataCell source={vscn.test?.source}>
      <TestDisplay
        vscn={vscn}
        handleUpload={handleUpload}
        verifiers={verifiers}
        fetchAttributeValue={fetchAttributeValue}
        fetchComponentValue={fetchComponentValue}
        key={key}
      />
    </DataCell>
  );
};

const TestDisplay = ({
  vscn,
  handleUpload,
  verifiers,
  fetchComponentValue,
  fetchAttributeValue,
  key,
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <div className={classes.icons}>
        <PraIcons verifiers={verifiers} />
      </div>
      <div className={classes.root}>
        <TableCell key={key}>
          <VyperLink onClick={(e) => handleUpload(vscn)}>
            <div> Test Program 1 : {fetchComponentValue("Test Program 1")}</div>
            <div>
              Test Program Revision :{" "}
              {fetchAttributeValue("Test Program 1", "Revision")}
            </div>
            <div> DEV Type 1 : {fetchComponentValue("DEV Type 1")}</div>
          </VyperLink>
        </TableCell>
      </div>
    </div>
  );
};
