import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";

const useStyles = makeStyles({
  buttons: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    gap: "5px",
  },
});

/**
 * @callback WorkFlowCell2~onClick The user clicked a workflow button
 * @param {*} item - The item that was clicked.
 * @param {string} action - The action that was clicked.
 *
 */

/**
 * Display a cell of the workflow row
 * @param {*} item - The item to display.
 * @param {*} Component - The component that displays the item.
 * @param {WorkFlowCell2~onClick} onClick - Called when the user clicks a button
 * @return {JSX.Element}
 * @constructor
 */
export function WorkFlowCell2({ item, as: Component, onClick }) {
  const classes = useStyles();

  return (
    <DataCell source={null}>
      <div className={classes.buttons}>
        <Component item={item} onClick={onClick} />
      </div>
    </DataCell>
  );
}

WorkFlowCell2.propTypes = {
  item: PropTypes.any.isRequired,
  as: PropTypes.any.isRequired,
  onClick: PropTypes.func.isRequired,
};
