import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { DieDialogContext } from "../../../component/die/DieDialog";
import { samenessDies } from "../../../component/sameness/sameness";
import { DieCell } from "./DieCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

/**
 * Create the row to display die information
 *
 * @param vyper
 * @param builds
 * @param onChange
 * @param showSameness
 * @returns {JSX.Element}
 * @constructor
 */
export const DieRow = ({ vyper, builds, onChange, showSameness }) => {
  const { open: openDialog } = useContext(DieDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeDies = (dieInstances, build) => {
    return buildDao
      .changeDies(vyper.vyperNumber, build.buildNumber, dieInstances)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    openDialog({
      facility: build.facility?.object?.PDBFacility,
      instances: build.dies.dieInstances,
      build,
      onSave: (instances) => handleChangeDies(instances, build),
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessDies(builds),
      })}
      hover
    >
      <RowPrefix help="die" title="Dies" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <DieCell vyper={vyper} build={build} onClick={handleClick} />
        </TableCell>
      ))}
    </TableRow>
  );
};
