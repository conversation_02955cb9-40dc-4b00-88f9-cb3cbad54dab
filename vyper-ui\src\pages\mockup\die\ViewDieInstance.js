import React from "react";
import { existsInFlow } from "../../vyper/FormStatus";

export const ViewDieInstance = ({ instance, build }) => {
  if (instance.dies.length === 0) {
    return <div>click to select</div>;
  }

  const display = (die) => {
    let text = die.name;

    text += " ";
    if (die.incomingWaferThick == null && existsInFlow(build, "Backgrind")) {
      text += "(click to select thickness)";
    } else if (existsInFlow(build, "Backgrind")) {
      text += "(" + die.incomingWaferThick + " uM)";
    }

    return text;
  };

  return (
    <div>
      {instance.dies.map((die, n) => (
        <div key={n}>{display(die)}</div>
      ))}
    </div>
  );
};
