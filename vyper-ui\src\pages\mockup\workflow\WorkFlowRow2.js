import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { Help } from "../../../component/help/Help";
import { WorkFlowCell2 } from "./WorkFlowCell2";

/**
 * Display the work flow row.
 * @param {*[]} items - The items to display/
 * @param {*} as - The component to display.
 * @param {WorkFlowCell2~onClick} onClick - Called when the user clicks a button.
 * @return {JSX.Element}
 * @constructor
 */
export function WorkFlowRow2({ vscns, as, onClick, currentUserIsOwner }) {
  return (
    <TableRow hover>
      <TableCell variant="head">
        <Help name="workflow" /> WorkFlow
      </TableCell>
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <WorkFlowCell2
            vscn={vscn}
            as={as}
            onClick={onClick}
            currentUserIsOwner={currentUserIsOwner}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

WorkFlowRow2.propTypes = {
  vscns: PropTypes.array.isRequired,
  as: PropTypes.any.isRequired,
  onClick: PropTypes.func.isRequired,
};
