import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { PcnCell } from "./PcnCell";
import { PcnDialogContext } from "./PcnDialog";

export const PcnRow = ({ vyper, builds, onChange }) => {
  const { open } = useContext(PcnDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeLinkPcn = (pcns, build) => {
    return buildDao
      .changeChangelinkPcn(vyper.vyperNumber, build.buildNumber, pcns)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleOpen = (build) => {
    open({
      rows: build.changelink.pcns.map((c) => c.object),
      onSave: (rows) => handleChangeLinkPcn(rows, build),
    });
  };

  return (
    <TableRow hover>
      <RowPrefix help="pcns" title="PCN Number(s)" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <PcnCell
            vyper={vyper}
            build={build}
            onClick={() => handleOpen(build)}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
