import { Dialog, DialogTitle, <PERSON><PERSON>, MenuItem } from "@material-ui/core";
import React, { useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { DialogContent, DialogActions } from "@material-ui/core";
import TextField from "@material-ui/core/TextField";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
});
export const BackgrindDialog = ({
  open,
  isBackgrindReq,
  setIsBackgrindReq,
  onClose,
  onSave,
}) => {
  const [backgrind, setBackgrind] = useState([{ name: "YES" }, { name: "NO" }]);
  const classes = useStyles();

  const handleCancel = () => {
    onClose();
  };
  const handleSave = () => {
    onSave(isBackgrindReq, null);
    onClose();
  };

  return (
    <Dialog className={classes.root} open={open} maxWidth="md" fullWidth>
      <DialogTitle
        classes={{
          root: classes.title,
        }}
      >
        Incoming Backgrind Information
      </DialogTitle>
      <DialogContent>
        <TextField
          variant="outlined"
          select
          id="backgrind"
          label="Is Backgrind Needed?"
          fullWidth
          value={isBackgrindReq}
          onChange={(e) => setIsBackgrindReq(e.target.value)}
        >
          {backgrind?.map((b) => (
            <MenuItem key={b.name} value={b.name}>
              {b.name}
            </MenuItem>
          ))}
        </TextField>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="primary" onClick={handleCancel}>
          Cancel
        </Button>
        <Button variant="contained" color="primary" onClick={handleSave}>
          Submit
        </Button>
      </DialogActions>
    </Dialog>
  );
};
