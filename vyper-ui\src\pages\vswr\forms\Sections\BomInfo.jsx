import React from "react";
import Grid from "@material-ui/core/Grid";
import { AgGridReact } from "ag-grid-react";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import { numberFormatter } from "../FormConstants";

const colorCellRender = (node) => {
  const rowData = node.data;
  const colID = node.column.colId;
  if (rowData && colID && rowData[colID]) {
    if (rowData[colID].toLowerCase() === "yes") {
      return { backgroundColor: "#DDFFDD" };
    }
    if (rowData[colID].toLowerCase() === "no") {
      return { backgroundColor: "#FFFFCC" };
    }
  }
  return null;
};

const columnDefs = [
  { headerName: "Sequence", field: "sequence", width: "102px" },
  { headerName: "Component", field: "component", width: "155px" },
  {
    headerName: "Traveler Component",
    field: "travelerComponent",
    width: "178px",
  },
  {
    headerName: "Unrestricted",
    field: "unrestricted",
    width: "123px",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Stock in Tfr",
    field: "stock",
    width: "120px",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Qual Inspe",
    field: "qual",
    width: "110px",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Restricted",
    field: "restricted",
    width: "110px",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Blocked",
    field: "blocked",
    width: "100px",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Returns",
    field: "returns",
    width: "100px",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Available?",
    field: "isAvailable",
    width: "110px",
    cellStyle: colorCellRender,
  },
  // { headerName: 'Forecasted Component', field: 'forecastedComponent', width: '200px'},
  // { headerName: 'Match?', field: 'match', width: '100px', cellStyle: colorCellRender},
];

const defaultColDef = {
  headerClass: "ti-ag-header",
  resizable: true,
  suppressMovable: true,
};

const BomInfo = (props) => {
  const { classes, bomData } = props;

  return (
    <Paper
      elevation={24}
      className={classes.secondaryPaper}
      style={{ width: "100%" }}
    >
      <Typography variant="h6">BOM Information </Typography>
      <Grid>
        <div
          className={"ti-server-ag-grid ag-theme-alpine"}
          style={{ width: "100%", height: "100%" }}
        >
          <AgGridReact
            rowData={bomData}
            columnDefs={columnDefs}
            domLayout={"autoHeight"}
            suppressColumnVirtualisation={true}
            defaultColDef={defaultColDef}
            enableCellTextSelection={true}
          />
        </div>
      </Grid>
    </Paper>
  );
};
export default BomInfo;
