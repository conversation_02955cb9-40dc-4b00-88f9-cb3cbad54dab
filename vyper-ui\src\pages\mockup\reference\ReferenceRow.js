import React from "react";
import { TableRow, TableCell } from "@material-ui/core";
import { Help } from "../../../component/help/Help";
import { Required } from "../../../component/required/Required";
import ReferenceCell from "./ReferenceCell";
const ReferenceRow = ({ vyper, pras, onClick }) => {
  return (
    <TableRow hover>
      <TableCell variant="head">
        <Help name="reference" />
        Reference
      </TableCell>
      {pras.map((pra, n) => (
        <TableCell key={n}>
          <ReferenceCell vyper={vyper} pra={pra} onClick={onClick} />
        </TableCell>
      ))}
    </TableRow>
  );
};

export default ReferenceRow;
