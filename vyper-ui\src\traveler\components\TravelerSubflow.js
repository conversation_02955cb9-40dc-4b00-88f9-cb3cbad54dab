import PropTypes from "prop-types";
import React from "react";
import { TravelerOperation } from "./TravelerOperation";

/**
 * Return the subflow row for the traveler.
 * @param {TravelerSubflow} subFlow - The traveler's subflow.
 * @returns {JSX.Element}
 * @constructor
 */

export function TravelerSubflow({ subFlow }) {
  return (
    <>
      {subFlow.operations.map((operation, n) => (
        <TravelerOperation key={n} operation={operation} />
      ))}
    </>
  );
}

TravelerSubflow.propTypes = {
  subFlow: PropTypes.object.isRequired,
};
