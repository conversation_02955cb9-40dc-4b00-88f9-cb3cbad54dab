import {
  createVscnTask,
  fetchTaskAssignments,
  updateTaskAssignment,
  updateTask,
} from "../../../component/api/taskService2";

export const submitVscnToAtGroup = (vscn, owners, action) => {
  const atApproverGroups = vscn.changedComponentsGroup;
  if (atApproverGroups == null || atApproverGroups.length == 0) {
    throw new Error("No VSCN changes found for AT Review");
  }

  const ownerAids = owners.map(obj => obj.userid).join(", ");
  const groupNames = atApproverGroups.join(", ");
  const metaData = [
    {
      attrName: "project number",
      attrValue: vscn?.vscnNumber,
    },
    { attrName: "description", attrValue: vscn?.vscnNumber },
    { attrName: "title", attrValue: vscn?.vscnNumber },
    { attrName: "groups", attrValue: groupNames },
    { attrName: "owner", attrValue: ownerAids },
    { attrName: "device id", attrValue: vscn.material.object.Material },
  ];

  const taskInfo = {
    taskName: vscn?.vscnNumber,
    ctxIdLabel: "VscnNumber",
    ctxIdValue: vscn?.vscnNumber,
  };

  const atBranches = [
    {
      stateName: "VSCN_AT_REVIEW",
      branchName: "AT_APPROVE",
    },
    {
      stateName: "VSCN_AT_REVIEW",
      branchName: "REJECT",
    },
  ];
  if (action.toLowerCase() === "submit") {
    return createVscnTask(
      metaData,
      taskInfo,
      atApproverGroups,
      ownerAids,
      atBranches
    );
  } else if (action.toLowerCase() === "resubmit") {
    const contextKey = `VSCN_Approval~VscnNumber~${vscn.vscnNumber}`;
    const taskChangesPayload = {
      ignoreCheck: true,
      branchName: "SUBMIT_AT",
      metaList: metaData,
      fnctList: [],
    };
    return updateTask(
      contextKey,
      taskChangesPayload,
      atApproverGroups,
      ownerAids,
      atBranches,
      []
    );
  }
};

export const fetchAllVscnTasks = (vscn, authUser, setTasks) => {
  // exit if we don't have a current user
  if (
    authUser.uid === "" ||
    vscn.vscnNumber === "" ||
    vscn.vscnNumber === undefined
  ) {
    return Promise.resolve([]);
  }

  return fetchTaskAssignments(
    `VSCN_Approval~VscnNumber~${vscn.vscnNumber}`
  ).then((taskAssignments) => {
    setTasks(taskAssignments.value || []);
    return taskAssignments;
  });
};

/**
 * process the Task Assignment records
 * @param {TaskService~TaskAssignment[]} tasks - the task assignment records
 * @param {UseAuth~AuthUser} authUser - the current user
 * @returns {ApprovalHelper~ProcessTasks}
 */
export function processTasks(tasks, authUser) {
  const taskAssignments = determineTaskAssignments(tasks, authUser);
  const taskUser = determineUsers(taskAssignments);
  const unApprovedGroups = determineUnapprovedGroups(taskAssignments);
  const isFrozen = determineIsFrozen(unApprovedGroups);
  const unApprovedGroupsAsObjects =
    determineUnapprovedGroupsAsObjects(unApprovedGroups);
  const isApprover = determineIsApprover(authUser, taskUser);
  const approvedGroups = determineApprovedGroups(tasks);
  const allGroups = determineAllGroups(tasks, authUser);

  return {
    taskAssignments,
    taskUser,
    approvedGroups,
    unApprovedGroups,
    unApprovedGroupsAsObjects,
    allGroups,
    isApprover,
    isFrozen,
  };
}

/**
 * filter the tasks down to the records that match the current user, the current iteration, and not approved
 * @param {TaskService~TaskAssignment[]} tasks - task assignment records for current user that are not approved
 * @param {UseAuth~AuthUser} authUser - the current user
 * @return {TaskService~TaskAssignment[]}
 */
function determineTaskAssignments(tasks, authUser) {
  // noinspection JSValidateTypes
  return tasks
    .filter((task) => task.current)
    .filter((task) => !task.complete)
    .filter((task) => !!task.availBranchNames)
    .filter((task) => task.userId === authUser.uid.toLowerCase());
}

/**
 * get the unique list of groups that haven't approved yet
 * @param {TaskService~TaskAssignment[]} taskAssignments - all of the task assignment records
 * @return {string[]}
 */
function determineUnapprovedGroups(taskAssignments) {
  return [...new Set(taskAssignments.map((task) => task.fnctName))];
}

/**
 * determine if the current build is frozen (the current user has no groups to approve)
 * @param {string[]} unApprovedGroups - the unapproved groups
 * @type {boolean}
 */
function determineIsFrozen(unApprovedGroups) {
  return unApprovedGroups.length === 0;
}

/**
 * @typedef ApprovalHelper~GroupObject
 * @property {string} groupName - the name of the group
 * @property {string} groupText - the text of the group
 */
/**
 * Return the unapproved groups as objects
 *
 * @param {string[]} unApprovedGroups - the list of unapproved groups
 * @returns {ApprovalHelper~GroupObject[]}
 */
function determineUnapprovedGroupsAsObjects(unApprovedGroups) {
  return unApprovedGroups.map((group) => ({
    groupName: group,
    groupText: group,
  }));
}

/**
 * return the current user id if they have not approved, and the group has not approved the build.
 * @param {TaskService~TaskAssignment[]} taskAssignments
 * @returns {string|null}
 */
function determineUsers(taskAssignments) {
  return taskAssignments?.[0]?.userId;
}

/**
 * determine if the current user approved this task
 * @param {UseAuth~AuthUser} authUser - the current user
 * @param {string|undefined} users - the user who approved the task, or undefined
 * @returns {boolean} - true if the current is an approver of this task
 */
function determineIsApprover(authUser, users) {
  return authUser.uid.toLowerCase() === users;
}

/**
 * get the list of groups that have approved
 * @param {TaskService~TaskAssignment[]} tasks - the list of tasks assignments for the build.
 * @return {ApprovalHelper~GroupObject[]} array of {group, username, date} of the approved, completed tasks.
 */
function determineApprovedGroups(tasks) {
  // noinspection JSValidateTypes
  return tasks
    .filter((task) => task.complete && task.current)
    .filter((task) => task.branchName.includes("AT_APPROVE"))
    .map((task) => ({
      group: task.fnctName,
      username: task.userName,
      date: task.modifiedDttm,
    }));
}

/**
 * get the complete list of groups for the current user.
 * @param {TaskService~TaskAssignment[]} tasks - task assignment records for current user that are not approved
 * @param {UseAuth~AuthUser} authUser - the current user
 * @return {string[]} - array of group names.
 */
function determineAllGroups(tasks, authUser) {
  return [
    ...new Set(
      tasks
        .filter((task) => task.userId === authUser.uid.toLowerCase())
        .filter((task) => task.current)
        .map((task) => task.fnctName)
    ),
  ];
}

/**
 * Update the current task (Approve or Rework)
 * @param {TaskService~TaskAssignment[]} tasks - The task assignment records
 * @param {UseAuth~AuthUser} authUser - the authenticated user
 * @param {string} action - Approve or Reject
 * @param {string} [comment] - The approval / rejection comment
 * @param {string} reason - The rejection reason, or null
 * @param {string} group - The group being approved / rejected
 * @returns {Promise<T>}
 */
export function approveOrRejectTheTask(
  tasks,
  authUser,
  action,
  comment,
  reason,
  group
) {
  // find the record for the current user and group
  // get the taskUuid and asmtUuid
  const assignment = tasks.find(
    (assignment) =>
      assignment.fnctName === group &&
      assignment.userId.toLowerCase() === authUser.uid.toLowerCase() &&
      assignment.current &&
      !assignment.complete
  );

  const branchName = assignment?.availBranchNames.find((branch) =>
    branch.toLowerCase().includes(action.toLowerCase())
  );

  if (!branchName) {
    throw new Error(`Branch not found! for action ${action}`);
  }

  // build the payload
  const taskChangesPayload = {
    branchName: branchName,
    commentText: JSON.stringify({
      reason: reason,
      comment: comment,
    }),
  };

  // update the task
  return updateTaskAssignment(
    assignment.taskUuid,
    assignment.asmtUuid,
    taskChangesPayload
  );
}
