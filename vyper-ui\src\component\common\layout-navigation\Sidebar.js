import React from "react";
import clsx from "clsx";
import PropTypes from "prop-types";
import config from "../../../buildEnvironment";
import { makeStyles } from "@material-ui/styles";
import { Divider, Drawer } from "@material-ui/core";
import tiLogoText from "./ti-logo-text.svg";

import ProfileView from "./ProfileView";
import SidebarNav from "./SidebarNav";

const { drawerWidth } = config;
const drawerMargin = 48;
const useStyles = makeStyles((theme) => ({
  drawer: {
    width: drawerWidth, // should match Layout.drawerWidth
    // [theme.breakpoints.up('lg')]: {
    marginTop: drawerMargin,
    // }
  },
  root: {
    backgroundColor: theme.palette.white,
    display: "flex",
    flexDirection: "column",
    height: "100%",
    padding: theme.spacing(2),
  },
  divider: {
    margin: theme.spacing(2, 0),
  },
  nav: {
    marginBottom: theme.spacing(2),
  },
  footer: {
    marginTop: "auto",
    // [theme.breakpoints.up('lg')]: {
    marginBottom: drawerMargin,
    // }
  },
}));

const Sidebar = (props) => {
  const { open, variant, onClose, className, ...rest } = props;

  const classes = useStyles();

  return (
    <Drawer
      anchor="left"
      classes={{ paper: classes.drawer }}
      onClose={onClose}
      open={open}
      variant={variant}
    >
      <div {...rest} className={clsx(classes.root, className)}>
        <ProfileView />
        <Divider className={classes.divider} />
        <SidebarNav className={classes.nav} />
        <div className={classes.footer}>
          <img
            src={tiLogoText}
            style={{ width: 200, height: 25.7 }}
            alt="Texas Instruments Inc."
          />
        </div>
      </div>
    </Drawer>
  );
};

Sidebar.propTypes = {
  className: PropTypes.string,
  onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
  variant: PropTypes.string.isRequired,
};

export default Sidebar;
