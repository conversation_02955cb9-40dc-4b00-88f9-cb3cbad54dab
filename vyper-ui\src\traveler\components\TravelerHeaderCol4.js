import PropTypes from "prop-types";
import React from "react";

/**
 * Returns the formatted value for the 4th column
 * @param {*} value - The value to display. nulls are converted to empty strings
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerHeaderCol4({ value }) {
  return <>{(value == null ? "" : value.toString()).padEnd(31, " ")}</>;
}

TravelerHeaderCol4.propTypes = {
  value: PropTypes.any,
};
