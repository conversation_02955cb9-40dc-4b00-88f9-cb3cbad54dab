import "src/polyfills";
import "ti-fetch";
import "fontsource-roboto/latin.css";
import "./set-public-path";
import config from "./buildEnvironment";
import React from "react";
import ReactDOM from "react-dom";
import singleSpaReact from "single-spa-react";
import App from "./App";
import { enableES5 } from "immer";

// polyfill for immer/ie11
enableES5();

// ti-fetch
// https://bitbucket.itg.ti.com/projects/REACT/repos/ti-fetch/browse/README.md
// https://confluence.itg.ti.com/display/PLUE/Upgrade+ti-fetch
const { apiGatewayUrl } = config;
fetch.configure({
  gatewayUrl: apiGatewayUrl,
  isSecure: true,
  isBadResponseThrown: true,
  loginWindowName: "_vyper",
});

const lifecycles = singleSpaReact({
  React,
  ReactDOM,
  rootComponent: App,
  errorBoundary(err, info, props) {
    // Customize the root error boundary for your microfrontend here.
    return null;
  },
});

export const { bootstrap, mount, unmount } = lifecycles;
