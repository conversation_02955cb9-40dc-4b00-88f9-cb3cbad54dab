import React from "react";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import { DataGrid } from "../universal";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import Dialog from "@material-ui/core/Dialog";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  gridItem: {
    marginBottom: theme.spacing(2),
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  button: {
    width: "100px",
  },
}));

export const ArmarcDialog = ({ open, onClose, onSave, build }) => {
  const columns = [
    { title: "Job ID", field: "jobId" },
    { title: "Queue", field: "queue" },
    { title: "Device", field: "device" },
    { title: "A/T Site", field: "atSite" },
    { title: "Rules File", field: "rulesFile" },
    { title: "Leadframe", field: "leadframe" },
    { title: "MB Edge", field: "mbEdge" },
    { title: "Wire Diameter", field: "wireDiameter" },
    { title: "Wire Type", field: "wireType" },
    { title: "Mount Compound", field: "mountCompound" },
    { title: "Mold Compound", field: "moldCompound" },
    { title: "MSL", field: "msl" },
  ];

  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle className={classes.title}>Copy From Selector</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <DataGrid
          title="ARM ARC Jobs"
          url={`/vyper/v1/armarc/search`}
          columns={columns}
          pageable
          pageSize={20}
          pageSizeOptions={[5, 10, 20, 50, 100, 200]}
          actions={[
            {
              icon: () => <CloseIcon />,
              tooltip: "Close",
              isFreeAction: true,
              onClick: onClose,
            },
            {
              icon: () => (
                <IconButton color="primary" size="small">
                  <ArrowRightAltIcon />
                </IconButton>
              ),
              tooltip: "Select",
              onClick: (_e, armarc) => {
                onSave(armarc, build);
              },
            },
          ]}
        />
      </DialogContent>
    </Dialog>
  );
};
