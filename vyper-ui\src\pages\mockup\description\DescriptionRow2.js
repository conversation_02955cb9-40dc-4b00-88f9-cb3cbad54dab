import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../RowPrefix";
import { DescriptionCell2 } from "./DescriptionCell2";

/**
 * Displays a row of descriptions.
 * @param {*[]} items - The items whose descriptions are being displayed
 * @param {DescriptionCell2~onDescription} onDescription - callback to retrieve the item's description.
 * @param {DescriptionCell2~onEditable} onEditable - callback to determine if the item description is editable.
 * @param {DescriptionCell2~onSave} onSave - callback to save the item
 * @returns {JSX.Element}
 * @constructor
 */
export function DescriptionRow2({ items, onDescription, onEditable, onSave }) {
  return (
    <TableRow hover>
      <RowPrefix help="description" title="Description" required />
      {items.map((item, n) => (
        <TableCell key={n}>
          <DescriptionCell2
            item={item}
            onDescription={onDescription}
            onEditable={onEditable}
            onSave={onSave}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

DescriptionRow2.propTypes = {
  items: PropTypes.array.isRequired,
  onDescription: PropTypes.func.isRequired,
  onEditable: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
};
