import React, { useMemo, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import Typography from "@material-ui/core/Typography";
import TextField from "@material-ui/core/TextField";
import Paper from "@material-ui/core/Paper";

const GeneralInfo = (props) => {
  const {
    classes,
    formState,
    setFormState,
    handleExistingScswrInfo,
    readOnly = false,
  } = props;

  const handleChange = (evt) => {
    if (readOnly) {
      return;
    }
    const { name, value } = evt.target;
    if (value !== undefined) {
      setFormState((prevState) => {
        return {
          ...prevState,
          [name]: value,
        };
      });
    }
  };

  const handleExistingScswrIDChange = (evt) => {
    const { name, value } = evt.target;
    if (value === undefined || value.length > 11) {
      return;
    }

    handleExistingScswrInfo(value);
    setFormState((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const defaultFieldProps = useMemo(() => {
    return {
      InputLabelProps: {
        className: classes.textField,
      },
      fullWidth: true,
      disabled: readOnly,
      style: readOnly ? { background: "#DCDCDC" } : {},
      variant: "outlined",
      color: "secondary",
      onChange: handleChange,
    };
  }, []);

  useEffect(() => {
    if (
      formState.existingScswrID === "" &&
      formState.scswrcontrolnumber &&
      formState.scswrcontrolnumber !== "" &&
      formState.scswrcontrolnumber !== undefined &&
      formState.scswrcontrolnumber !== null
    ) {
      handleExistingScswrInfo(formState.scswrcontrolnumber);
      setFormState((prevState) => {
        return {
          ...prevState,
          existingScswrID: formState.scswrcontrolnumber,
        };
      });
    }
  }, [formState.scswrcontrolnumber]);

  return (
    <Paper elevation={24} className={classes.paper}>
      <Typography variant="h6"> General Information </Typography>
      <Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            {...defaultFieldProps}
            onChange={handleExistingScswrIDChange}
            value={
              formState.existingScswrID || formState.scswrcontrolnumber || ""
            }
            name={"existingScswrID"}
            label="Existing SCSWR ID"
          />
        </Grid>
        <Grid container>
          <TextField
            {...defaultFieldProps}
            fullWidth
            required={true}
            error={!formState.title}
            value={formState.title || ""}
            name={"title"}
            label="Title"
          />
        </Grid>
        <Grid container>
          <TextField
            {...defaultFieldProps}
            fullWidth
            multiline
            rows={4}
            value={formState.purpose || ""}
            name={"purpose"}
            label="Purpose"
          />
        </Grid>

        <Grid container spacing={1}>
          <Grid item>
            <TextField
              label="VSWR ID"
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.vswrID || ""}
              name={"vswrID"}
            />
          </Grid>
          <Grid item>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.vbuildID}
              name={"vbuildID"}
              label="VBuild ID"
            />
          </Grid>
          <Grid item>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.existingScswrID || ""}
              name={"existingScswrID"}
              label="Existing SCSWR ID"
            />
          </Grid>
          <Grid item>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.currentStatus || ""}
              name={"currentStatus"}
              label="Current Status"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.plant}
              name={"plant"}
              label="VBuild Plant"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.facility}
              name={"facility"}
              label="VBuild Facility"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.vbuildMaterial || ""}
              name={"vbuildMaterial"}
              label="VBuild Material"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.specDevice || ""}
              name={"specDevice"}
              label="VBuild Spec Device"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              label="SCSWR Plant"
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.scswrPlant || ""}
              name={"scswrPlant"}
            />
          </Grid>
          <Grid item xs>
            <TextField
              label="SCSWR A/T"
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.scswrFacility || ""}
              name={"scswrFacility"}
            />
          </Grid>
          <Grid item xs>
            <TextField
              label="SCSWR Material"
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.scswrMaterial || ""}
              name={"scswrMaterial"}
            />
          </Grid>
          <Grid item xs>
            <TextField
              label="SCSWR Spec Device"
              {...defaultFieldProps}
              disabled
              style={{ background: "#DCDCDC" }}
              value={formState.scswrSpecDevice || ""}
              name={"scswrSpecDevice"}
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.pin || ""}
              disabled
              style={{ background: "#DCDCDC" }}
              name={"pin"}
              label="Pin"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.pkg || ""}
              disabled
              style={{ background: "#DCDCDC" }}
              name={"pkg"}
              label="Pkg"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.pkgGroup || ""}
              disabled
              style={{ background: "#DCDCDC" }}
              name={"pkgGroup"}
              label="Pkg Group"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.currentRequestor || ""}
              disabled
              style={{ background: "#DCDCDC" }}
              name={"currentRequestor"}
              label="Current Requestor"
            />
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};
export default GeneralInfo;
