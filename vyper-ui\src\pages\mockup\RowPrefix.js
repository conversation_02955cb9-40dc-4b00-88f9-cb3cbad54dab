import { makeStyles } from "@material-ui/core";
import TableCell from "@material-ui/core/TableCell";
import React from "react";
import { Help } from "src/component/help/Help";
import { Required } from "src/component/required/Required";

const useStyles = makeStyles(() => ({
  nowrap: {
    whiteSpace: "nowrap",
  },
}));

export const RowPrefix = ({ title, help, required = false }) => {
  const classes = useStyles();

  return (
    <TableCell className={classes.nowrap} variant="head">
      <Help name={help} />
      {` ${title} `}
      <Required required={required} />
    </TableCell>
  );
};
