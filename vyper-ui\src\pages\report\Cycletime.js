import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { Alert } from "@material-ui/lab";
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "src/component/fetch/VyperFetch";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import useQueryParam from "src/component/queryparams/useQueryParam";
import { BuildNumberAutocomplete } from "../../autocomplete/components/BuildNumberAutocomplete";
import { formatDate } from "../../component/dateFormat";

const useStyles = makeStyles({
  header: {
    marginTop: 0,
  },
  description: {
    marginBottom: "2em",
  },
  form: {
    paddingBottom: "2em",
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "flex-end",
    gap: "1em",
  },
});

export const Cycletime = () => {
  const { vget } = useContext(FetchContext);

  // if there is a build number in the url, get it
  const [defaultBuildNumber] = useQueryParam("buildNumber", null);

  // when form is submitted, fetch the vyper, and extract the build
  const [records, setRecords] = useState([]);

  const [buildNumber, setBuildNumber] = useLocalStorage(
    "report.cycletime.buildnumber",
    ""
  );

  // if we have a build number on the url, make it the default
  useEffect(() => {
    if (defaultBuildNumber != null && defaultBuildNumber !== "") {
      setBuildNumber(defaultBuildNumber);
    }
  }, []);

  const handleClickRun = () => {
    if (buildNumber == null) {
      return;
    }
    vget(`/vyper/v1/report/cycletime/${buildNumber}`, (records) => {
      setRecords(records);
    });
  };

  const totalAge = records.reduce((acc, cur) => acc + cur.durationInDays, 0);

  const downloadLink = `/vyper/v1/report/cycletime/${buildNumber}/download`;

  const classes = useStyles();

  return (
    <div>
      <h2 className={classes.header}>CycleTime</h2>

      <Alert className={classes.description} severity="warning">
        This report will show all of the workflow steps for a single build, and
        the amount of time spent in each step.
      </Alert>

      <div className={classes.form}>
        <div>
          <BuildNumberAutocomplete
            defaultNumber={buildNumber}
            onSelect={(number) => setBuildNumber(number)}
          />
        </div>

        <div>
          <Button
            type="submit"
            color="primary"
            variant="contained"
            disabled={buildNumber == null || buildNumber === ""}
            onClick={handleClickRun}
          >
            Run
          </Button>
        </div>

        <div>
          <Button
            type="submit"
            color="secondary"
            variant="contained"
            disabled={records.length === 0}
            href={downloadLink}
          >
            Download Excel
          </Button>
        </div>
      </div>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Vyper Number</TableCell>
            <TableCell>Build Number</TableCell>
            <TableCell>State</TableCell>
            <TableCell align="right">Age (Days)</TableCell>
            <TableCell>Facility A/T</TableCell>
            <TableCell>Material</TableCell>
            <TableCell>Submitter</TableCell>
            <TableCell>Start Date</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {records.map((record, n) => (
            <TableRow key={n} hover>
              <TableCell>{record.vyperNumber}</TableCell>
              <TableCell>{record.buildNumber}</TableCell>
              <TableCell>{record.state}</TableCell>
              <TableCell align="right">
                {record.durationInDays?.toFixed(2)}
              </TableCell>
              <TableCell>{record.facilityAt}</TableCell>
              <TableCell>{record.material}</TableCell>
              <TableCell>{record.submitter}</TableCell>
              <TableCell>
                {record.stateStart == null ? "" : formatDate(record.stateStart)}
              </TableCell>
            </TableRow>
          ))}
          <TableRow hover>
            <TableCell>&nbsp;</TableCell>
            <TableCell>&nbsp;</TableCell>
            <TableCell>Total Age (Days):</TableCell>
            <TableCell align="right">{totalAge?.toFixed(2)}</TableCell>
            <TableCell>&nbsp;</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
