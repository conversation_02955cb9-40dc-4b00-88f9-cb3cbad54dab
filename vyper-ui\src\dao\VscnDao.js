import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

/**
 * @typedef {object} Material
 * @property {MaterialObject} object
 */

/**
 * @typedef {object} MaterialObject - A Material object
 * @property {string} Material
 * @property {string} MultiChipModule
 * @property {string} PackageDesignator
 * @property {string} PackageGroup
 * @property {number} MOQ
 * @property {string} Automotive
 * @property {number} SPQ
 * @property {string} PackingConfig
 * @property {string} OldMaterial
 * @property {string[]} FlowName
 * @property {string} SBE
 * @property {string} Isolation
 * @property {number} MCMChipCount
 * @property {string} SBE2
 * @property {string} SBE1
 * @property {number} PackagePin
 */

/**
 * @typedef {object} Facility
 * @property {FacilityObject} object
 */

/**
 * @typedef {object} FacilityObject
 * @property {string} PDBFacility
 * @property {number} PlantCode
 * @property {string} PlantLoc
 * @property {string} PlantName
 * @property {string} PlantType
 */

/**
 * @typedef {object} Vscn - VSCN object
 * @property {number} version - The version number
 * @property {string} vscnNumber - The VSCN number
 * @property {string} praNumber - The PRA number
 * @property {string} buildNumber - The build number
 * @property {string} vyperNumber - The vyper number
 * @property {string} state - The state.
 * @property {string} description - The description.
 * @property {CommentsDialog~Comment[]} comments - The comments
 * @property {Material} material - The material
 * @property {Facility} facility - The facility
 * @property {PimCell~PimSetup} pimSetup - The PIM Setup
 */

// noinspection JSUnusedGlobalSymbols
export class VscnDao extends DaoBase {
  constructor(params) {
    super({ name: "VscnDao", url: "/vyper/v1/vscn", ...params });
    this.vscn = params.vscn || {};
    this.setVscn = params.setVscn || noop;
  }

  /**
   * Create a new VSCN.
   * @param {string} vyperNumber - The vyper number
   * @param {string} buildNumber - The build number
   * @param {string} praNumber - The PRA number
   * @returns {Promise<Vscn>}
   */
  create(vyperNumber, buildNumber, praNumber) {
    return this.handleFetch("create", `/`, "POST", {
      vyperNumber,
      buildNumber,
      praNumber,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  reload(vscnNumber) {
    return this.findByVscnNumber(vscnNumber, true);
  }

  /**
   * Fetch the vscn
   * @param {string} vscnNumber - The VSCN number
   * @returns {Promise<Vscn>}
   */
  findByVscnNumber(vscnNumber, ignoreCache = false) {
    if (!ignoreCache && this.vscn?.vscnNumber === vscnNumber) {
      return Promise.resolve(this.vscn);
    }
    return this.handleFetch(
      "findByVscnNumber",
      `/findByVscnNumber?vscnNumber=${vscnNumber}`,
      "GET"
    ).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  /**
   * Fetch the list of VSCNs that match the PRA number.
   * @param {string} praNumber - The PRA number
   * @returns {Promise<Vscn[]>}
   */
  findAllByPraNumber(praNumber) {
    return this.handleFetch(
      "findAllByPraNumber",
      `/findAllByPraNumber?praNumber=${praNumber}`,
      "GET"
    );
  }

  /**
   * Fetch the list of VSCNs that match the build number.
   * @param {string} buildNumber - The build number
   * @returns {Promise<Vscn[]>}
   */
  findAllByBuildNumber(buildNumber) {
    return this.handleFetch(
      "findAllByBuildNumber",
      `/findAllByBuildNumber?buildNumber=${buildNumber}`,
      "GET"
    );
  }

  /**
   * Fetch the list of VSCNs that match the vyper number.
   * @param {string} vyperNumber - The VSCN number
   * @returns {Promise<Vscn[]>}
   */
  findAllByVyperNumber(vyperNumber) {
    return this.handleFetch(
      "findAllByVyperNumber",
      `/findAllByVyperNumber?vyperNumber=${vyperNumber}`,
      "GET"
    );
  }

  /**
   * Change the description of the vscn
   * @param {string} vscnNumber - The VSCN number
   * @param {string} description - The new description
   * @returns {Promise<Vscn>}
   */
  changeDescription(vscnNumber, description) {
    return this.handleFetch("changeDescription", `/changeDescription`, "POST", {
      vscnNumber,
      description,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  /**
   * Change the workflow (state) of the vscn
   * @param {string} vscnNumber - The VSCN number
   * @param {string} action - The new action
   * @returns {Promise<Vscn>}
   */
  changeWorkFlow(vscnNumber, action) {
    return this.handleFetch("changeWorkFlow", `/changeWorkFlow`, "POST", {
      vscnNumber,
      action,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  /**
   * Add a comment
   * @param {string} vscnNumber - The VSCN number
   * @param {string} comment - The comment
   * @return {Promise<Vscn>}
   */
  addComment(vscnNumber, comment) {
    return this.handleFetch("addComment", `/addComment`, "POST", {
      vscnNumber,
      text: comment,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  /**
   * Change the PIM setup needed state
   * @param {string} vscnNumber - The VSCN number
   * @param {boolean} state - The needed state
   * @return {Promise<Vscn>}
   */
  changePimSetupNeededState(vscnNumber, state) {
    return this.handleFetch(
      "changePimSetupNeededState",
      `/changePimSetupNeededState`,
      "POST",
      { vscnNumber, state }
    ).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  /**
   * Change the PIM setup validated state
   * @param {string} vscnNumber - The VSCN number
   * @param {boolean} state - The validated state
   * @return {Promise<Vscn>}
   */
  changePimSetupValidatedState(vscnNumber, state) {
    return this.handleFetch(
      "changePimSetupValidatedState",
      `/changePimSetupValidatedState`,
      "POST",
      { vscnNumber, state }
    ).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changeComponents(vyperNumber, vscnNumber, name, items) {
    return this.handleFetch("changeVscnComponents", `/components`, "POST", {
      vyperNumber,
      vscnNumber,
      name,
      items,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changeTest(vscnNumber, content) {
    return this.handleFetch("changeVscnTest", `/test/upload`, "POST", {
      vscnNumber,
      content,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changePackConfig(vscnNumber, value) {
    return this.handleFetch("changeVscnPackConfig", `/packconfig`, "POST", {
      vscnNumber,
      value,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changeSelection(vscnNumber, oName, cName, items) {
    return this.handleFetch("changeSelection", `/selection`, "POST", {
      vscnNumber,
      operation: oName,
      component: cName,
      items,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changeSymbolization(vscnNumber, symbol, customs, ecat) {
    return this.handleFetch("changeSymbolization", `/symbolization`, "POST", {
      vscnNumber,
      symbol,
      customs,
      ecat,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changeMaterial(vscnNumber, material) {
    return this.handleFetch("changeMaterial", `/material`, "POST", {
      vscnNumber,
      material,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  changeChangelinkChange(vscnNumber, changeNumber) {
    return this.handleFetch(
      "changeChangelinkChange",
      `/changelink/change`,
      "POST",
      { vscnNumber, changeNumber }
    ).then((json) => {
      this.setVscn(json);
      return json;
    });
  }

  refreshVscn(vscnNumber) {
    return this.handleFetch("refreshVscn", `/refresh`, "POST", {
      vscnNumber,
    }).then((json) => {
      this.setVscn(json);
      return json;
    });
  }
}
