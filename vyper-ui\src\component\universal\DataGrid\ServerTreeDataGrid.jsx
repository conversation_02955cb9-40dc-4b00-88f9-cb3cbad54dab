import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import MaterialTable from "material-table";
import Refresh from "@material-ui/icons/Refresh";
import { PlusIcon } from "../Icons";
import { PrimaryButton } from "../Buttons";
import { default as tableIcons } from "./icons";
import { tableConfig, styleOptions } from "./defaults";
import { refreshData } from "./pageable";

const ServerTreeDataGrid = ({
  actions = [],
  rowDataDependentActions = [],
  columns = [],
  detailPanel = [],
  handleSelect,
  pageable = false,
  selectable = false,
  timestamp = null,
  title = "",
  url,
}) => {
  const tableRef = React.createRef();
  const [editable, setEditable] = useState();

  const refreshAction = [
    {
      icon: () => <Refresh />,
      tooltip: "Refresh Data",
      isFreeAction: true,
      onClick: () => tableRef.current && tableRef.current.onQueryChange(),
    },
  ];

  const handleAddRow = () => {
    tableRef.current.state.showAddRow = true;
    setEditable({
      onRowAdd: (newData) => {
        new Promise((resolve, reject) => {
          // setData([...data, newData]);
          // setEditable();
          // resolve();
        });
      },
    });
  };

  useEffect(() => {
    tableRef.current && tableRef.current.onQueryChange();
  }, [url, timestamp]);

  // TODO: Need to pass the Add button from component using the tree data grid
  return (
    <div>
      <PrimaryButton
        icon={<PlusIcon />}
        label="Create Qual Vehicle"
        handleClick={() => handleAddRow()}
      />
      <div>&nbsp;</div>
      <MaterialTable
        minRows={tableConfig.minRows}
        actions={[
          ...refreshAction,
          ...actions,
          (rowData) => rowDataDependentActions(rowData),
        ]}
        columns={columns}
        data={(query) =>
          new Promise((resolve, reject) => {
            refreshData(url, query, resolve, reject, pageable);
          })
        }
        detailPanel={detailPanel}
        icons={tableIcons}
        onSelectionChange={selectable ? (rows) => handleSelect(rows) : null}
        options={{
          ...styleOptions,
          ...{ selection: selectable },
          ...{ filtering: tableConfig.filtering },
          ...{ pageSize: tableConfig.defaultPageSize },
          ...{ defaultExpanded: tableConfig.defaultExpanded },
          rowStyle: (data, index) => {
            if (JSON.stringify(data.tableData.childRows) !== "null") {
              return { backgroundColor: "#ecf2f9" };
            }
          },
        }}
        tableRef={tableRef}
        title={title}
        parentChildData={(row, rows) => rows.find((a) => a.id === row.parentId)}
        editable={{
          onRowAdd: (newData) =>
            new Promise((resolve, reject) => {
              setTimeout(() => {
                alert(JSON.stringify(newData));
                // const data = this.state.data;
                // data.push(newData);
                // this.setState({ data }, () => resolve());
                resolve();
              }, 1000);
            }),
          onRowUpdate: (newData, oldData) =>
            new Promise((resolve, reject) => {
              setTimeout(() => {
                alert(
                  "OLD DATA: " +
                    JSON.stringify(oldData) +
                    "UPDATED DATA: " +
                    JSON.stringify(newData)
                );
                // const data = this.state.data;
                // data.push(newData);
                // this.setState({ data }, () => resolve());
                resolve();
              }, 1000);
            }),
          onRowDelete: (oldData) =>
            new Promise((resolve, reject) => {
              setTimeout(() => {
                alert("Obsolete below data: " + JSON.stringify(oldData));
                // const data = this.state.data;
                // data.push(oldData);
                // this.setState({ data }, () => resolve());
                resolve();
              }, 1000);
            }),
        }}
      />
    </div>
  );
};

ServerTreeDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  detailPanel: PropTypes.any,
  handleSelect: PropTypes.func,
  pageable: PropTypes.bool,
  selectable: PropTypes.bool,
  timestamp: PropTypes.string,
  title: PropTypes.string,
  url: PropTypes.string.isRequired,
  rowDataDependentActions: PropTypes.func,
};

export default ServerTreeDataGrid;
