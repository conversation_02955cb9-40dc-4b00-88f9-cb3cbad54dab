import TableCell from "@material-ui/core/TableCell";
import TextField from "@material-ui/core/TextField";
import MenuItem from "@material-ui/core/MenuItem";
import React from "react";

export const TypeCell = ({ instance, onTypeChange }) => {
  // let rule = (instances.length === 1) ? "1" : ">1";
  // let types = dieTypes
  //     .filter(dt => dt.rule === rule)
  //     .map(dt => dt.type)

  const types = [
    { id: 1, rule: "1", type: "Die" },
    { id: 2, rule: ">1", type: "Side-By-Side" },
    { id: 3, rule: ">1", type: "Stacked" },
  ];

  return (
    <TableCell>
      <TextField
        id="outlined-select-currency"
        select
        value={instance.type || ""}
        onChange={onTypeChange}
        variant="outlined"
      >
        {types.map((type) => (
          <MenuItem key={type.id} value={type.type}>
            {type.type}
          </MenuItem>
        ))}
      </TextField>
    </TableCell>
  );
};
