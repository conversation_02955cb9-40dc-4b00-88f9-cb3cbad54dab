import React from "react";
import { Link } from "react-router-dom";
import { DataGrid } from "../../../component/universal";

export const VyperListPage = () => {
  const columns = [
    {
      title: "Vyper Number",
      field: "vyperNumber",
      render: (rowData) => (
        <Link to={`/project/${rowData.vyperNumber}`}>
          {rowData.vyperNumber}
        </Link>
      ),
    },
    { title: "Title", field: "title" },
    { title: "Description", field: "description" },
    { title: "Devices", field: "materials" },
    { title: "Facilities", field: "facilities" },
    { title: "Owner Names", field: "ownerNames" },
    { title: "Owner UserIDs", field: "ownerUserids" },
  ];

  return (
    <div>
      <DataGrid
        title="All Projects"
        url="/vyper/v1/vyper/project/all"
        columns={columns}
        pageable
        // timestamp={timestamp}
        // maxBodyHeight={"1500"}
        pageSize={20}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
      />
    </div>
  );
};
