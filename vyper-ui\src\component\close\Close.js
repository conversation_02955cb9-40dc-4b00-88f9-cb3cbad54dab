import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Tooltip from "@material-ui/core/Tooltip";

export const Close = ({ onClose, tip }) => {
  const classes = makeStyles(() => ({
    root: {
      marginLeft: "0.5em",
      fontWeight: "bold",
      color: "red",
      cursor: "pointer",
    },
  }))();

  return (
    <Tooltip title={tip} placement="top" arrow>
      <span className={classes.root} onClick={onClose}>
        X
      </span>
    </Tooltip>
  );
};
