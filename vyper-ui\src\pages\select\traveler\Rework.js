import React from "react";
import PropTypes from "prop-types";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { compare } from "src/pages/select/traveler/reworkValue";

const useStyles = makeStyles({
  changedValue: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
});

/**
 * This component compares the current traveler's operation/component to the reworked traveler
 * and outputs a message if there is a difference.
 *
 * @param {Object} reworkedTraveler - The prior traveler
 * @param {Object} operation - The current traveler's operation
 * @param {Object} component - The current traveler's component
 * @param {number} priority - The value's priority
 * @returns {null|JSX.Element}
 *
 */
export const Rework = ({
  reworkedTraveler,
  operation,
  component,
  priority,
  build,
}) => {
  const classes = useStyles();
  const rework = compare(reworkedTraveler, operation, component, priority);

  let displayMessage;
  
  if(build.state.toLowerCase() === "draft" && build.buildtype === "Minor Change"){
    displayMessage = "value of reference approved build";
  }
  else{
    displayMessage = "value at time of rework";
  }


  return rework.match ? null : (
    <span>
      {" "}
      {displayMessage}:{" "}
      <span className={classes.changedValue}>
        {rework.oldValue || "un-selected"}
      </span>
    </span>
  );
};

Rework.propTypes = {
  reworkedTraveler: PropTypes.object,
  operation: PropTypes.object,
  component: PropTypes.object,
  priority: PropTypes.number,
};
