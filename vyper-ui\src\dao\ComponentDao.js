import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

export class Component<PERSON>ao extends DaoBase {
  constructor(params) {
    super({ name: "Component<PERSON><PERSON>", url: "/vyper/v1/vyper", ...params });
    this.components = params.components || [];
    this.setComponents = params.setComponents || noop;
  }

  list() {
    if (this.components?.length > 0) {
      return Promise.resolve(this.components);
    } else {
      return this.handleFetch("list", "/allComponents", "GET").then((json) => {
        this.setComponents(json);
        return this.components;
      });
    }
  }
}
