import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import BuildtypeSelect from "../buildtype/BuildtypeSelect";
import { BuildTypeBlockerDialog } from "./BuildTypeBlockerDialog";
import { useState } from "react";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
});

export const BuildTypeDialog = ({
  buildtype,
  onChange,
  open,
  onClose,
  onSubmit,
  hasDevelopmentTemplate,
  buildNumber,
  onAddNewBuild,
}) => {
  const handleCancel = () => onClose();
  const [openBlockerDialog, setOpenBlockerDialog] = useState(false);

  const handleSave = () => {
    if (
      (buildtype === "New" || buildtype === "Minor Change") &&
      hasDevelopmentTemplate === true
    ) {
      setOpenBlockerDialog(true);
      return;
    }

    onSubmit(buildtype);
  };

  const handleCloseBlockerDialog = () => setOpenBlockerDialog(false);

  const classes = useStyles();

  return (
    <Dialog className={classes.root} open={open} maxWidth="md" fullWidth>
      <DialogTitle className={classes.title}>Select Build Type</DialogTitle>
      <DialogContent>
        <BuildtypeSelect onChange={onChange} value={buildtype} />
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="primary" onClick={handleCancel}>
          Cancel
        </Button>
        <Button variant="contained" color="primary" onClick={handleSave}>
          {" "}
          Submit
        </Button>
      </DialogActions>
      <BuildTypeBlockerDialog
        onAddNewBuild={onAddNewBuild}
        buildNumber={buildNumber}
        open={openBlockerDialog}
        onClose={handleCloseBlockerDialog}
        onCloseBuildTypeDialog={onClose}
      />
    </Dialog>
  );
};
