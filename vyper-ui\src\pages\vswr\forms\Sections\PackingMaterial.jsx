import React, { useCallback } from "react";
import Grid from "@material-ui/core/Grid";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import { AgGridReact } from "ag-grid-react";
import { numberFormatter } from "../FormConstants";

const defaultColDef = {
  headerClass: "ti-ag-header",
  resizable: true,
  suppressMovable: true,
  flex: 2,
};

const colorCellRender = (node) => {
  const rowData = node.data;
  const colID = node.column.colId;
  if (rowData && colID && rowData[colID]) {
    if (rowData[colID].toLowerCase() === "yes") {
      return { backgroundColor: "#DDFFDD" };
    }
    if (rowData[colID].toLowerCase() === "no") {
      return { backgroundColor: "#FFFFCC" };
    }
  }
  return null;
};

const columnDefs = [
  { headerName: "Sequence", field: "sequence" },
  { headerName: "Component", field: "component", flex: 3 },
  { headerName: "Traveler Component", field: "travelerComponent", flex: 3 },
  {
    headerName: "Unrestricted",
    field: "unrestricted",
    valueFormatter: numberFormatter,
  },
  {
    headerName: "Stock in Tfr",
    field: "stock",
    valueFormatter: numberFormatter,
  },
  { headerName: "Qual Inspe", field: "qual", valueFormatter: numberFormatter },
  {
    headerName: "Restricted",
    field: "restricted",
    valueFormatter: numberFormatter,
  },
  { headerName: "Blocked", field: "blocked", valueFormatter: numberFormatter },
  { headerName: "Returns", field: "returns", valueFormatter: numberFormatter },
  {
    headerName: "Available?",
    field: "isAvailable",
    cellStyle: colorCellRender,
  },
];

const PackingMaterial = (props) => {
  const { classes, packingMaterialData } = props;

  return (
    <Paper
      elevation={24}
      className={classes.secondaryPaper}
      style={{ width: "100%" }}
    >
      <Typography variant="h6">Packing Material</Typography>
      <Grid>
        <div
          className={"ti-server-ag-grid ag-theme-alpine"}
          style={{ width: "100%", height: "100%" }}
        >
          <AgGridReact
            rowData={packingMaterialData}
            columnDefs={columnDefs}
            domLayout={"autoHeight"}
            suppressColumnVirtualisation={true}
            defaultColDef={defaultColDef}
            enableCellTextSelection={true}
          />
        </div>
      </Grid>
    </Paper>
  );
};
export default PackingMaterial;
