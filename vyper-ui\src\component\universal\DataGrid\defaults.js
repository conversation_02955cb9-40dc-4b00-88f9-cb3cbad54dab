export const tableConfig = {
  defaultPageSize: 10,
  filtering: true,
  minRows: 10,
  defaultExpanded: true,
  defaultPageSizeOptions: [5, 10, 15, 20],
  defaultPaging: true,
};

export const styleOptions = {
  headerStyle: {
    backgroundColor: "#cc0000",
    color: "#FFF",
    position: "sticky",
    top: 0,
    padding: 5,
    fontSize: "13px",
  },
  cellStyle: {
    width: 100,
    minWidth: 100,
    fontSize: "14px",
    height: 30,
  },
  maxBodyHeight: "600px",
};

export const editableStyleOptions = {
  headerStyle: {
    backgroundColor: "#cc0000",
    color: "#FFF",
    position: "sticky",
    top: 0,
    padding: 5,
  },
  cellStyle: {
    width: 200,
    minWidth: 200,
  },
  maxBodyHeight: "600px",
};
