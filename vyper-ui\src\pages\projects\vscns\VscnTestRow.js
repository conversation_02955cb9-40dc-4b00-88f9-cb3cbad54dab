import { TableCell, TableRow } from "@material-ui/core";
import React, { useContext } from "react";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { FileUploadDialogContext } from "../../../component/fileupload/FileUploadDialog";
import { ViewDialogContext } from "../../../component/view/ViewDialog";
import { VyperLink } from "../../mockup/VyperLink";
import { makeStyles } from "@material-ui/styles";
import { VscnTestCell } from "./VscnTestCell";

export const VscnTestRow = ({ vyper, vscns, onChange }) => {
  const { open: openUploadDialog } = useContext(FileUploadDialogContext);
  const { vscnDao } = useContext(DataModelsContext);

  const handleChangeTest = (content, vscn) => {
    return vscnDao
      .changeTest(vscn.vscnNumber, content)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleUpload = (vscn) => {
    openUploadDialog({
      content: vscn.test.content,
      onSave: (content) => handleChangeTest(content, vscn),
    });
  };

  return (
    <TableRow hover>
      <RowPrefix help="test" title="Test" required />
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <VscnTestCell vscn={vscn} key={n} handleUpload={handleUpload} />
        </TableCell>
      ))}
    </TableRow>
  );
};
