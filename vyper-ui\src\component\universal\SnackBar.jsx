import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";

import Snackbar from "@material-ui/core/Snackbar";
import Mui<PERSON>lert from "@material-ui/lab/Alert";

function Alert(props) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

export const BaseSnackbar = ({ open, handleClose, children, autohide }) => {
  return (
    <Snackbar
      anchorOrigin={{
        vertical: "top",
        horizontal: "center",
      }}
      open={open}
      autoHideDuration={autohide}
      onClose={handleClose}
    >
      {children}
    </Snackbar>
  );
};

BaseSnackbar.propTypes = {
  open: PropTypes.bool,
  message: PropTypes.string,
  handleSnackbarClose: PropTypes.func,
  severity: PropTypes.string,
  autohide: PropTypes.number,
};

export const StandardSnackbar = ({
  autohide,
  open,
  message,
  handleSnackbarClose,
  severity,
}) => {
  return (
    <Snackbar
      open={open}
      autoHideDuration={autohide}
      onClose={handleSnackbarClose}
    >
      <Alert onClose={handleSnackbarClose} severity={severity}>
        {message}
      </Alert>
    </Snackbar>
  );
};

StandardSnackbar.propTypes = {
  open: PropTypes.bool,
  message: PropTypes.string,
  handleSnackbarClose: PropTypes.func,
  severity: PropTypes.string,
};

export const SuccessSnackbar = ({
  autohide = 5000,
  message,
  onCloseCallback,
}) => {
  const [open, setOpen] = useState(true);
  const handleClose = (event, reason) => {
    setOpen(false);
    if (typeof onCloseCallback === "function") onCloseCallback(event, reason);
  };

  return (
    <BaseSnackbar autohide={autohide} open={open} handleClose={handleClose}>
      <Alert onClose={handleClose} severity="success">
        {message}
      </Alert>
    </BaseSnackbar>
  );
};

export const ErrorSnackbar = ({
  autohide = 5000,
  message,
  onCloseCallback,
  closeOnClickAway = true,
}) => {
  const [open, setOpen] = useState(true);
  const handleClose = (event, reason) => {
    setOpen(false);
    if (typeof onCloseCallback === "function") onCloseCallback(event, reason);
  };

  return (
    <BaseSnackbar
      autohide={autohide}
      open={open}
      handleClose={closeOnClickAway == true ? onCloseCallback : null}
    >
      <Alert onClose={handleClose} severity="error">
        {message.indexOf("\n") != -1
          ? message.split("\n").map((msg) => {
              return (
                <>
                  {msg}
                  <br />{" "}
                </>
              );
            })
          : message}
      </Alert>
    </BaseSnackbar>
  );
};
