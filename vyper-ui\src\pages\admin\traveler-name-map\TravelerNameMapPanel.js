import React, { useContext, useEffect, useState } from "react";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { AlertDialogErrorHandler } from "../../../component/fetch/DaoBase";
import { DataGrid } from "../../../component/universal";
import { TravelerNameMapDao } from "../../../dao/TravelerNameMapDao";
import { logError } from "../../functions/logError";

/**
 * Manage the records for the traveler name map table.
 *
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerNameMapPanel() {
  const [data, setData] = useState([]);
  const { open: openAlert } = useContext(AlertDialogContext);

  const travelerNameMapDao = new TravelerNameMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
  });

  useEffect(() => {
    refresh().catch(logError);
  }, []);

  const refresh = () => {
    return travelerNameMapDao
      .list(0, 1000)
      .then((json) => setData(json.content))
      .catch(logError);
  };

  const columns = [
    {
      field: "componentName",
      title: "Component Name",
      defaultSort: "asc",
      validate: (rowData) => {
        return rowData.componentName === ""
          ? "component name cannot be blank"
          : "";
      },
    },
    {
      field: "vyperAttributeName",
      title: "Vyper Attribute Name",
      validate: (rowData) => {
        return rowData.vyperAttributeName === ""
          ? "vyper name cannot be blank"
          : "";
      },
    },
    {
      field: "atssAttributeName",
      title: "ATSS Attribute Name",
    },
  ];

  function handleRowAdd(newData) {
    return new Promise((resolve, reject) => {
      travelerNameMapDao
        .create(newData)
        .then((travelerNameMap) => {
          setData([...data, travelerNameMap]);
          resolve();
        })
        .catch(() => reject());
    });
  }

  function handleRowUpdate(newData, oldData) {
    return new Promise((resolve, reject) => {
      travelerNameMapDao
        .update(oldData.id, newData)
        .then((travelerNameMap) => {
          setData(
            data.map((tnm) =>
              tnm.id === travelerNameMap.id ? travelerNameMap : tnm
            )
          );
          resolve();
        })
        .catch(() => reject());
    });
  }

  function handleRowDelete(oldData) {
    return new Promise((resolve, reject) => {
      travelerNameMapDao
        .delete(oldData.id)
        .then(() => {
          setData(data.filter((tnm) => tnm.id !== oldData.id));
          resolve();
        })
        .catch(() => reject());
    });
  }

  return (
    <div>
      <h3>Traveler Name Map</h3>

      <div>
        This table allows you to maintain how the Vyper traveler attributes are
        converted into ATSS traveler attributes. It shows the component name and
        vyper attribute name, and an optional atss attribute name. If there is
        no record in this table, then the vyper attribute is used as the atss
        attribute. If the component and vyper attribute exist, and the atss
        attribute is blank, then the attribute is not placed in the final
        traveler. If the component and vyper attribute and atss attribute are
        set, then the atss attribute name will be output to the traveler,
        instead of the vyper attribute.
      </div>

      <br />

      <DataGrid
        title="Traveler Name Map"
        columns={columns}
        data={data}
        editable={true}
        onRowAdd={handleRowAdd}
        onRowUpdate={handleRowUpdate}
        onRowDelete={handleRowDelete}
        options={{
          search: false,
          pageSize: 10,
          pageSizeOptions: [5, 10, 20, 50, 100, 200],
        }}
      />
    </div>
  );
}
