import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useState, useRef, useEffect } from "react";
import { Comment } from "./Comment";
import { CommentBox } from "../../../component/comments/CommentBox";
import { filterComments } from "../../vyper/filter/filterComments";
import { Select, FormControl, InputLabel, MenuItem } from "@material-ui/core";

export const Comments = ({ vyper, build }) => {
  const useStyles = makeStyles((theme) => ({
    root: {},
    titlebar: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
    },
    title: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      marginTop: theme.spacing(2),
      marginBottom: theme.spacing(2),
    },
    comment: {
      paddingBottom: theme.spacing(3),
    },
    commentArea: {
      height: "50vh",
      width: "70%",
      overflowY: "scroll",
      border: "2px solid grey",
      borderRadius: "1%",
      padding: "10px",
    },
  }));

  const [operationFilters, setOperationFilters] = useState(
    build.comments
      .map((c) => c.operation)
      .filter((op) => op !== null)
      .filter((op, i, op2) => op2.indexOf(op) === i)
  );
  const [currentFilters, setCurrentFilters] = useState({
    operation: "All Comments",
  });

  const comments = filterComments(build.comments, currentFilters);
  const messagesEndRef = useRef(null);
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: "smooth",
      inline: "start",
      block: "end",
    });
  };

  const classes = useStyles();
  useEffect(() => {
    scrollToBottom();

    return () => {};
  }, []);

  return (
    <div className={classes.root}>
      <div className={classes.titlebar}>
        <div className={classes.title}>Comments ({build.comments.length})</div>
      </div>
      {comments.length === 0 ? null : (
        <>
          <FormControl
            style={{ width: "300px" }}
            variant="outlined"
            sx={{ width: 300 }}
          >
            <InputLabel>Comment Filter</InputLabel>
            <Select
              value={currentFilters.operation}
              label="Filters"
              onChange={(e) =>
                setCurrentFilters({
                  ...currentFilters,
                  operation: e.target.value,
                })
              }
            >
              <MenuItem value={"All Comments"}>{"All Comments"}</MenuItem>

              {operationFilters.map((opName, n) => (
                <MenuItem value={opName} key={n}>
                  {opName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <div className={classes.commentArea}>
            {comments.map((comment, n) => (
              <CommentBox c={comment} n={n} />
            ))}
            <div ref={messagesEndRef} />
          </div>
        </>
      )}
    </div>
  );
};
