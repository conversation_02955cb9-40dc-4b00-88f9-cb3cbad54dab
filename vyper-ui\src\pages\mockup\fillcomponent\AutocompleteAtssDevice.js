import React, { useEffect, useState } from "react";
import { Autocomplete } from "@material-ui/lab";
import TextField from "@material-ui/core/TextField";
import CircularProgress from "@material-ui/core/CircularProgress";

export const AutocompleteAtssDevice = ({ device, onChangeDevice, disabled }) => {
  const [open, setOpen] = React.useState(false);
  const [options, setOptions] = React.useState([]);

  const [search, setSearch] = React.useState(device);
  const handleInputChange = (e, v) => setSearch(v);
  const [loading, setLoading] = useState(false);

  const handleChangeDevice = (e, v) => onChangeDevice(v);

  useEffect(() => {
    if (search == null || search === "") {
      return undefined;
    }

    setLoading(true);

    fetch(`/vyper/v1/autocomplete/specDevice?search=${search}`)
      .then((response) => response.json())
      .then((json) => setOptions(json))
      .catch((e) => console.log(e))
      .finally(() => setLoading(false));

    //
    // let active = true;
    //
    // (async () => {
    //     const url =
    //
    //     setLoading(true)
    //     const response = await fetch(url);
    //     const devices = await response.json();
    //     if (active) {
    //         setOptions(devices);
    //     }
    //     setLoading(false)
    // })();
    //
    // return () => {
    //     active = false;
    //     setLoading(false);
    // };
  }, [search]);

  React.useEffect(() => {
    if (!open) {
      setOptions([]);
    }
  }, [open]);

  return (
    <Autocomplete
      disabled={disabled}
      open={open}
      onOpen={() => setOpen(true)}
      onClose={() => setOpen(false)}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Spec Device"
          variant="outlined"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {loading ? (
                  <CircularProgress color="inherit" size={20} />
                ) : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
          }}
        />
      )}
      options={options}
      loading={loading}
      inputValue={search}
      onInputChange={handleInputChange}
      value={device}
      onChange={handleChangeDevice}
      style={{ minWidth: "20em" }}
    />
  );
};
