import { Button, Grid, Paper } from "@material-ui/core";
import React from "react";
import PropTypes from "prop-types";

import { FlowDataRow } from "./FlowDataRow";

export const Flow = ({
  flowData,
  onAddFlowData,
  defaultFlow,
  onChangeFlowData,
  onDeleteFlowData,
  buildType,
}) => {
  const datalength = flowData.length;
  const lastFlowData = flowData[flowData.length - 1];
  const showAddButton =
    datalength > 0 &&
    (lastFlowData.flowName === defaultFlow.flowName ||
      lastFlowData.flowName == "" ||
      lastFlowData.allowedFlows.length == 1 ||
      lastFlowData.allowedFlows.find((flow) => flow.id === lastFlowData.flowId)
        .successorFlows.length === 0)
      ? false
      : true;
  const showDeleteButton = flowData.length > 1;
  return (
    <Grid container spacing={1}>
      <Grid item xs={12} style={{ paddingLeft: "20px", paddingBottom: "0px" }}>
        <Button
          variant="contained"
          color="secondary"
          onClick={onAddFlowData}
          disabled={!showAddButton || buildType==="Minor Change"}
        >
          Add Flow Data
        </Button>
      </Grid>
      <Grid item xs={12}>
        <Paper style={{ padding: "10px" }}>
          {flowData.map((item, index) => (
            <FlowDataRow
              key={index}
              item={item}
              index={index}
              onChangeFlowData={onChangeFlowData}
              onDeleteFlowData={onDeleteFlowData}
              showDeleteButton={showDeleteButton}
              firstFlowTemplateType={flowData[0]?.templateSource?.templateType}
              firstFlowDevice={flowData[0]?.device?.Material}
              firstFlowHasBomTemplateForPkgNiche={
                flowData[0]?.hasBomTemplateForPkgNiche
              }
              buildType={buildType}
            />
          ))}
        </Paper>
      </Grid>
    </Grid>
  );
};

Flow.propTypes = {
  flowData: PropTypes.array.isRequired,
  flows: PropTypes.array.isRequired,
  onAddFlowData: PropTypes.func.isRequired,
  onChangeFlowData: PropTypes.func.isRequired,
  onDeleteFlowData: PropTypes.func.isRequired,
  defaultFlow: PropTypes.any,
};
