import React, { useState, useEffect, useContext } from "react";

import { makeStyles } from "@material-ui/core/styles";
import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  DialogActions,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@material-ui/core";
import CloseIcon from "@material-ui/icons/Close";
import CommentBox from "./CommentBox";
// import { SimbaUserContext } from "src/lib/common/user/SimbaUserContext";
import { AuthContext } from "src/component/common/auth";

const useStyles = makeStyles((theme) => ({
  commentArea: {
    height: "50vh",
    overflowY: "scroll",
    border: "2px solid grey",
    borderRadius: "1%",
    padding: "10px",
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialogActions: {
    display: "flex",
    justifyContent: "space-between",
    width: "100%",
    margin: "0em 1em 1em 1em",
  },
}));

const CommentDialog = (props) => {
  // const simbaUser = useContext(SimbaUserContext);
  const { authUser: currentUser } = useContext(AuthContext);

  const { comments, handleClose, setComments, showComments, defaultSelected } =
    props;
  const classes = useStyles();
  const [newComment, setNewComment] = useState("");
  const [selectedOperation, setSelectedOperation] = useState(defaultSelected);
  const [operationOptions, setOperationOptions] = useState([]);
  const [commentAreaComments, setCommentAreaComments] = useState([]);

  useEffect(() => {
    setCommentAreaComments(comments[defaultSelected] || []);
  }, [comments]);

  useEffect(() => {
    setSelectedOperation(defaultSelected);
    //prevents duplicate entries on drop down
    setOperationOptions(new Set([defaultSelected, ...Object.keys(comments)]));
    setCommentAreaComments(comments[defaultSelected] || []);
  }, [defaultSelected]);

  const handleChange = (evt) => {
    const operation = evt.target.value;
    setSelectedOperation(operation);
    setCommentAreaComments(comments[operation] || []);
  };

  const handleSave = () => {
    if (!newComment) {
      return;
    }
    // let name = currentUser.name.split(',');;
    const commentObj = {
      userID: currentUser.uid,
      userName: currentUser.name,
      dttm: new Date().toLocaleString(),
      operation: selectedOperation,
      comment: newComment,
    };

    setComments((prevState) => {
      const comments = { ...prevState };
      if (selectedOperation in comments) {
        comments[selectedOperation] = [
          ...comments[selectedOperation],
          commentObj,
        ];
      } else {
        comments[selectedOperation] = [commentObj];
      }
      return comments;
    });
    setNewComment("");
  };

  return (
    <Dialog open={showComments} onClose={handleClose} fullWidth maxWidth="lg">
      <DialogTitle>Comments</DialogTitle>
      <IconButton className={classes.closeButton} onClick={handleClose}>
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <div className={classes.commentArea}>
          {commentAreaComments.length !== 0 ? (
            commentAreaComments.map((comment, i) => (
              <CommentBox comment={comment} i={i} />
            ))
          ) : (
            <Typography variant="h6">No Comments To Display...</Typography>
          )}
        </div>

        <TextField
          label="Comment"
          value={newComment || ""}
          onChange={(e) => setNewComment(e.target.value)}
          position="fixed"
          fullWidth
          multiline
          variant="outlined"
          rows={4}
          placeholder="Enter your comment here."
          autoFocus
          onKeyPress={(ev) => {
            if (ev.key === "Enter") {
              handleSave();
              ev.preventDefault();
            }
          }}
        />
      </DialogContent>
      <DialogActions>
        <div className={classes.dialogActions}>
          <FormControl
            style={{ width: "300px" }}
            variant="outlined"
            sx={{ width: 300 }}
          >
            <InputLabel>Operation</InputLabel>
            <Select value={selectedOperation} onChange={handleChange}>
              {operationOptions.map((operation, i) => (
                <MenuItem value={operation} key={i}>
                  {operation}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Button onClick={handleSave} variant="contained" color="primary">
            Add Comment
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
};
export default CommentDialog;
