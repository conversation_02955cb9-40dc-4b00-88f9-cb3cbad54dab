import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

export class AuditDao extends DaoBase {
  constructor(params) {
    super({ name: "AuditDao", url: "/vyper/v1/audit", ...params });
    this.audits = params.audits || [];
    this.setAudits = params.setAudits || noop;
  }

  findAllByVyperNumber(vyperNumber) {
    return this.handleFetch(
      "findAllByVyperNumber",
      `/findAllByVyperNumber/${vyperNumber}`,
      "GET"
    ).then((json) => {
      this.setAudits(json);
      return json;
    });
  }

  findAllByBuildNumber(buildNumber) {
    return this.handleFetch(
      "findAllByBuildNumber",
      `/findAllByBuildNumber/${buildNumber}`,
      "GET"
    ).then((json) => {
      this.setAudits(json);
      return json;
    });
  }

  findAllByPraNumber(praNumber) {
    return this.handleFetch(
      "findAllByPraNumber",
      `/findAllByPraNumber/${praNumber}`,
      "GET"
    ).then((json) => {
      this.setAudits(json);
      return json;
    });
  }

  findAllByVscnNumber(vscnNumber) {
    return this.handleFetch(
      "findAllByVscnNumber",
      `/findAllByVscnNumber/${vscnNumber}`,
      "GET"
    ).then((json) => {
      this.setAudits(json);
      return json;
    });
  }
}
