import PropTypes from "prop-types";
import React, { useContext } from "react";
import { PraIconPgs } from "src/pages/vyper/pras/PraIconPgs";
import { PraIconSvg } from "src/pages/vyper/pras/PraIconSvg";
import { VerifierDialogContext } from "src/pages/vyper/pras/VerifierDialog";

/**
 * Display a verifier icon
 *
 * @param verifier
 * @returns {JSX.Element}
 * @function
 */
export const PraIcon = ({ verifier }) => {
  const { openVerifierDialog } = useContext(VerifierDialogContext);

  const handleClickIcon = () => {
    openVerifierDialog({ verifier });
  };

  // determine the hover text of the icon based on the verifier's message
  let hover;
  if (verifier.messages.length === 0) {
    hover = verifier.source;
  } else {
    hover = verifier.messages[0].text;
    if (verifier.messages[0].leftValue == null) {
    } else {
      hover += " = " + verifier.messages[0].leftValue;
    }
  }

  // special handling for the die, as the verifier message doesn't make sense.
  if (verifier.source === "SOURCE_DIE") {
    hover =
      verifier.status === "FULLY_VERIFIED"
        ? "Die - Validated"
        : "Die - Not Validated";
  }

  if (verifier.source === "SOURCE_PGS") {
    return (
      <PraIconPgs verifier={verifier} hover={hover} onClick={handleClickIcon} />
    );
  } else {
    return (
      <PraIconSvg verifier={verifier} hover={hover} onClick={handleClickIcon} />
    );
  }
};

PraIcon.propTypes = {
  verifier: PropTypes.object.isRequired,
};
