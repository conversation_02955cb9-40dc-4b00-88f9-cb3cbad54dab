import React, { useContext, useEffect, useState } from "react";
import { formatDate } from "../../../component/dateFormat";
import { DataGrid } from "../../../component/universal";
import {
  AlertDialogError<PERSON>and<PERSON>,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { AuditDao } from "src/dao/AuditDao";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { SpinnerContext } from "src/component/Spinner";
import { noop } from "src/component/vyper/noop";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

export const ChangeLog = ({ buildNumber }) => {
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const [audits, setAudits] = useState([]);

  const auditDao = new AuditDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  useEffect(() => {
    auditDao
      .findAllByBuildNumber(buildNumber)
      .then((json) => setAudits(json))
      .catch(noop);
  }, [buildNumber]);

  let columns = [
    { title: "ID", field: "id", hidden: true, internal: true },
    { title: "Detail", field: "detail", internal: true },
    { title: "When", field: "when", internal: true },
    {
      title: "Vyper Number",
      field: "vyperNumber",
      hidden: true,
      internal: true,
    },
    {
      title: "Build Number",
      field: "buildNumber",
      hidden: true,
      internal: true,
    },
    { title: "User Name", field: "username", internal: true },
    { title: "User userid", field: "userid" },
    { title: "Activity", field: "activity", hidden: true, internal: true },
  ];

  if (externalUse) {
    columns = columns.filter((column) => column.internal);
  }

  const items = audits
    // sort newest first
    .sort((a, b) => {
      if (a.when > b.when) return -1;
      if (a.when < b.when) return 1;
      return 0;
    })
    // change date format
    .map((audit) => ({ ...audit, when: formatDate(audit.when) }));

  return (
    <div>
      <h2>Change Log</h2>
      <DataGrid
        title="Change Log"
        data={items}
        columns={columns}
        options={{ pageSize: 20, pageSizeOptions: [5, 10, 20, 100, 200] }}
      />
    </div>
  );
};
