import Button from "@material-ui/core/Button";
import IconButton from "@material-ui/core/IconButton";
import VisibilityIcon from "@material-ui/icons/Visibility";
import { makeStyles } from "@material-ui/styles";
import React, { useMemo, useRef, useState } from "react";
import { Link } from "react-router-dom";
import { useLocalStorage } from "../../component/hooks/useLocalStorage";
import TiServerAgGrid, { exportGridAsExcel } from "../../lib/TiServerAgGrid";

const useStyles = makeStyles(() => ({
  actionIcon: {
    fontSize: "1rem",
  },
  header: {
    width: "100%",
    fontSize: "1.25rem",
    fontWeight: "bold",
    marginBottom: "5px",
    display: "flex",
    justifyContent: "space-between",
  },
}));

let body = document.body,
  html = document.documentElement;
let height =
  Math.max(
    body.scrollHeight,
    body.offsetHeight,
    html.clientHeight,
    html.scrollHeight,
    html.offsetHeight
  ) - 200;

export function VscnListPage() {
  const [filterState, setFilterState] = useLocalStorage(
    "tableFilterStates.vscnFilterState",
    {}
  );
  const [hasTableLoaded, setHasTableLoaded] = useState(false);
  const gridRef = useRef();
  const classes = useStyles();

  const onFirstDataRendered = (api) => {
    gridRef.current = api;
    setHasTableLoaded(true);
  };

  const exportAsExcel = () => {
    const api = gridRef.current.api;
    const columnApi = gridRef.current.columnApi;
    exportGridAsExcel(api, columnApi, {
      columnFilter: (column) => {
        return column.getColDef().headerName !== "Actions";
      },
      fileName: "VSCNs_Vyper.xlsx",
    });
  };

  // must use useMemo, or filter bar looses focus as user is typing in it
  const defaultColDef = useMemo(
    () => ({
      filterParams: {
        suppressAndOrCondition: true,
        filterOptions: [
          {
            displayKey: "contains",
            displayName: "Contains",
            predicate: () => {}, //needed to use display key and name
          },
          {
            displayKey: "=",
            displayName: "Equal",
            predicate: () => {}, //needed to use display key and name
          },
          {
            displayKey: "<>",
            displayName: "Not Equal",
            predicate: () => {}, //needed to use display key and name
          },
          {
            displayKey: "<",
            displayName: "Less Than",
            predicate: () => {}, //needed to use display key and name
          },
          {
            displayKey: ">",
            displayName: "Greater Than",
            predicate: () => {}, //needed to use display key and name
          },
          {
            displayKey: "<=",
            displayName: "Less Than or Equal",
            predicate: () => {}, //needed to use display key and name
          },
          {
            displayKey: ">=",
            displayName: "Greater Than or Equal",
            predicate: () => {}, //needed to use display key and name
          },
        ],
      },
    }),
    []
  );

  function actionRenderer(node) {
    let classes = {};
    const rowData = node.data;
    return rowData == null ? (
      <></>
    ) : (
      <Link to={`/projects/${rowData.vyperNumber}/vscns/${rowData.vscnNumber}`}>
        <IconButton color="secondary">
          <VisibilityIcon className={classes.actionIcon} />
        </IconButton>
      </Link>
    );
  }

  function vscnNumberRenderer(node) {
    const rowData = node.data;
    return rowData == null ? (
      <></>
    ) : (
      <Link to={`/projects/${rowData.vyperNumber}/vscns/${rowData.vscnNumber}`}>
        {rowData.vscnNumber}
      </Link>
    );
  }

  function praNumberRenderer(node) {
    const rowData = node.data;
    return rowData == null ? (
      <></>
    ) : (
      <Link to={`/projects/${rowData.vyperNumber}/pras/${rowData.praNumber}`}>
        {rowData.praNumber}
      </Link>
    );
  }

  function buildNumberRenderer(node) {
    const rowData = node.data;
    return rowData == null ? (
      <></>
    ) : (
      <Link
        to={`/projects/${rowData.vyperNumber}/builds/${rowData.buildNumber}/selection`}
      >
        {rowData.buildNumber}
      </Link>
    );
  }

  function vyperNumberRenderer(node) {
    const rowData = node.data;
    return rowData == null ? (
      <></>
    ) : (
      <Link to={`/projects/${rowData.vyperNumber}`}>{rowData.vyperNumber}</Link>
    );
  }

  // must use useMemo, or filter bar looses focus as user is typing in it
  const columns = useMemo(
    () => [
      {
        headerName: "Actions",
        cellRenderer: actionRenderer,
        filter: false,
        sortable: false,
        width: 90,
      },
      {
        headerName: "VSCN Number",
        field: "vscnNumber",
        cellRenderer: vscnNumberRenderer,
        linkCellRendererParams: {
          links: (params) => {
            /** @type {ResultRowData} */
            const rowData = params.data;
            const label = `${rowData.vscnNumber}`;
            const url =
              location.origin +
              `/vyper/projects/${rowData.vyperNumber}/vscns/${rowData.vscnNumber}`;
            return { label, url };
          },
        },
      },
      {
        headerName: "PRA Number",
        field: "praNumber",
        cellRenderer: praNumberRenderer,
        linkCellRendererParams: {
          links: (params) => {
            const rowData = params.data;
            const label = `${rowData.praNumber}`;
            const url =
              location.origin +
              `/vyper/projects/${rowData.vyperNumber}/pras/${rowData.praNumber}`;
            return { label, url };
          },
        },
      },
      {
        headerName: "Build Number",
        field: "buildNumber",
        cellRenderer: buildNumberRenderer,
        linkCellRendererParams: {
          links: (params) => {
            const rowData = params.data;
            const label = `${rowData.buildNumber}`;
            const url =
              location.origin +
              `/vyper/projects/${rowData.vyperNumber}/builds/${rowData.buildNumber}/selection`;
            return { label, url };
          },
        },
      },
      { headerName: "Flow Type", field: "flowType" },
      {
        headerName: "Vyper Number",
        field: "vyperNumber",
        cellRenderer: vyperNumberRenderer,
        linkCellRendererParams: {
          links: (params) => {
            const rowData = params.data;
            const label = `${rowData.vyperNumber}`;
            const url =
              location.origin + `/vyper/projects/${rowData.vyperNumber}`;
            return { label, url };
          },
        },
      },
      { headerName: "State", field: "state" },
      { headerName: "Description", field: "description" },
      { headerName: "Material", field: "material" },
      { headerName: "Facility", field: "facility_at" },
    ],
    []
  );

  return (
    <div>
      <div className={classes.header}>
        VSCNs
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Button
            variant="contained"
            onClick={() => {
              gridRef.current.setFilterModel({});
            }}
            color="secondary"
            style={{ marginRight: "10px" }}
          >
            Clear filters
          </Button>
          <Button variant="contained" onClick={exportAsExcel} color="secondary">
            Export as Excel
          </Button>
        </div>
      </div>
      <TiServerAgGrid
        url="/vyper/v1/vscn/search"
        columnDefs={columns}
        defaultColDef={defaultColDef}
        style={{ height }}
        paginationSelectOptions={[5, 10, 20]}
        defaultPageSize={20}
        getFilterModel={hasTableLoaded && setFilterState}
        onFirstDataRendered={onFirstDataRendered}
        initialFilterModel={filterState}
      />
    </div>
  );
}
