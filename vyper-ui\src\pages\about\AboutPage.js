import React from "react";
import pkg from "../../../package.json";
import makeStyles from "@material-ui/core/styles/makeStyles";

const styles = makeStyles(() => ({
  root: {},
  header: {},
  version: {
    fontSize: "1rem",
  },
}));

export const AboutPage = () => {
  const classes = styles();

  return (
    <div className={classes.root}>
      <h1 className={classes.header}>
        About Vyper <span className={classes.version}>({pkg.version})</span>
      </h1>

      <p>
        VYPER stands for VerifY Preproduction Engineering Request. VYPER
        performs a comprehensive validation of preproduction materials
        previously entered in existing design databases to prepare for an
        initial SWR build. VYPER also creates a preproduction ATSS like traveler
        using A/T templates of defined operations and components.
      </p>
    </div>
  );
};
