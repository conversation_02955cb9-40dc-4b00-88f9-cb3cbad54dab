import React, { useMemo, useEffect, useState } from "react";
import Grid from "@material-ui/core/Grid";
import TextField from "@material-ui/core/TextField";
import { FormControl, InputLabel, Select, MenuItem } from "@material-ui/core";

const shipmentStatusOptions = [
  "Wait for SBE Coordinator - Wafers or units in another factory (Fab or AT)",
  "In-transit from requestor to another factory (Fab or AT) - A delivery note/SAP shipment information is required",
  "Already in A/T stock",
];

const DieLotInfoFields = (props) => {
  const { classes, formState, setFormState } = props;

  const handleChange = (evt) => {
    const { name, value } = evt.target;
    setFormState((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const defaultFieldProps = useMemo(() => {
    return {
      InputLabelProps: {
        className: classes.textField,
      },
      variant: "outlined",
      color: "secondary",
      onChange: handleChange,
      fullWidth: true,
      required: true,
    };
  }, []);

  return (
    <Grid style={{ marginTop: "10px" }}>
      <Grid container spacing={1}>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.dieLot}
            name={"dieLot"}
            label="Die Lot/SAP Lot"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            required={false}
            value={formState.waferNumToUse}
            name={"waferNumToUse"}
            label="Which wafer number to use"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.probed}
            name={"probed"}
            label="Probed"
          />
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.isInkless}
            name={"isInkless"}
            label="Is Lot Inkless"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.buildBy}
            name={"buildBy"}
            label="Build By"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            required={false}
            value={formState.stdMapLocation}
            name={"stdMapLocation"}
            label="Standard Map Location?"
          />
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            required={false}
            value={formState.pickupBin}
            name={"pickupBin"}
            label="Pick-up Bin"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.useGecs}
            name={"useGecs"}
            label="Use GECs"
          />
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs={12} sm={9}>
          <FormControl fullWidth color="secondary" variant="outlined">
            <InputLabel className={classes.textField}>
              Material Shipment Status
            </InputLabel>
            <Select
              {...defaultFieldProps}
              name={"matShipStatus"}
              value={formState.matShipStatus}
            >
              <MenuItem key={-1} value="">
                <em></em>
              </MenuItem>
              {shipmentStatusOptions.map((item, i) => (
                <MenuItem key={i} value={item}>
                  {item}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            label="Date Shipped"
            type="date"
            value={formState.dateShipped}
            name={"dateShipped"}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.deliveryNote}
            name={"deliveryNote"}
            label="Delivery Note"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.invoice}
            name={"invoice"}
            label="Invoice"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.qtyToShip}
            name={"qtyToShip"}
            label="Quantity to Ship"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.sapWaybill}
            name={"sapWaybill"}
            label="SAP Waybill#"
          />
        </Grid>
      </Grid>
    </Grid>
  );
};
export default DieLotInfoFields;
