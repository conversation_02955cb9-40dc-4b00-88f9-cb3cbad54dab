import { DaoBase } from "src/component/fetch/DaoBase";

let fetchId = 1;

export class LdapDao extends DaoBase {
  constructor(params) {
    super({ name: "<PERSON>dap<PERSON><PERSON>", url: "/vyper/v1/vyper/ldap", ...params });
  }

  lookup(search) {
    return this.handleFetch("lookup", `/lookup`, "POST", {
      search: search,
      fetchId: ++fetchId,
    }).then((json) => {
      if (fetchId === json.fetchId) {
        return json;
      } else {
        return Promise.reject("old results - not used");
      }
    });
  }
}
