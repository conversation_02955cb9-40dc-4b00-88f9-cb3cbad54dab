import PersonIcon from "@material-ui/icons/Person";
import AttachMoneyIcon from "@material-ui/icons/AttachMoney";
import MemoryIcon from "@material-ui/icons/Memory";
import ComputerIcon from "@material-ui/icons/Computer";
import LanguageIcon from "@material-ui/icons/Language";
import AccountBalanceIcon from "@material-ui/icons/AccountBalance";
import NewReleasesIcon from "@material-ui/icons/NewReleases";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import BrokenImageIcon from "@material-ui/icons/BrokenImage";
import LocalGasStationIcon from "@material-ui/icons/LocalGasStation";
import MenuBookIcon from "@material-ui/icons/MenuBook";
import LockIcon from "@material-ui/icons/Lock";
import VpnLockIcon from "@material-ui/icons/VpnLock";
import LibraryBooksIcon from "@material-ui/icons/LibraryBooks";
import FireplaceIcon from "@material-ui/icons/Fireplace";
import config from "../../../src/buildEnvironment";
const { externalUse } = config;
/**
 * determines which icon to display for the source
 *
 * @param source
 * @returns an object like: { icon: ..., color: ..., text: ... }

 */
export const determineIcon = (source) => {
  if (source == null) {
    return null;
  }

  if (source.user == null && source.system === null) {
    return null;
  }

  if (source.user?.username != null) {
    return {
      icon: PersonIcon,
      color: "#ff0000",
      text: externalUse
        ? source.user.username
        : source.user.username + " / " + source.user.userid,
    };
  }

  if (source.system?.name == null) {
    return null;
  }

  switch (source.system.name.toLowerCase()) {
    case "pgs":
      return {
        icon: AttachMoneyIcon,
        color: "#ba8900",
        text: "PGS",
      };

    case "armarc":
      return {
        icon: MemoryIcon,
        color: "#00b000",
        text: "ARMARC",
      };

    case "atss":
      return {
        icon: ComputerIcon,
        color: "#0000ff",
        text: "ATSS",
      };

    case "vyper":
      return {
        icon: LanguageIcon,
        color: "#800080",
        text: "VYPER",
      };

    case "bomtemplate":
    case "bom_template":
      return {
        icon: AccountBalanceIcon,
        color: "#CC0000",
        text: "Bill of Process Template",
      };

    case "todo":
      return {
        icon: NewReleasesIcon,
        color: "hsla(25, 100%, 50%, 1)",
        text: "Icon Not Implemented",
      };

    case "unselected":
      return {
        icon: ArrowRightAltIcon,
        color: "#CC0000",
        text: "Value Not Selected",
      };

    case "fill_components":
      return {
        icon: LocalGasStationIcon,
        color: "#00CC00",
        text: "Fill In Unselected Component",
      };
    case "oss":
      return {
        icon: MenuBookIcon,
        color: "#0048b5",
        text: "OSS",
      };
    case "locked_vyper":
      return {
        icon: LockIcon,
        color: "#CC0000",
        text: "Build Page",
      };
    case "global":
      return {
        icon: VpnLockIcon,
        color: "#42a7f5",
        text: "Global",
      };
    case "atcams":
      return {
        icon: LibraryBooksIcon,
        color: "#6340ff",
        text: "AT CAMS",
      };
    case "atbom":
      return {
        icon: FireplaceIcon,
        color: "#f5ad42",
        text: "AT BOM",
      };
    default:
      return {
        icon: BrokenImageIcon,
        color: "#000000",
        text: "unknown: " + JSON.stringify(source),
      };
  }
};
