import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import MenuIcon from "@material-ui/icons/Menu";
import Menu from "@material-ui/core/Menu";
import MenuItem from "@material-ui/core/MenuItem";
import { ComponentNameDisplay } from "../../component/ComponentNameDisplay";
import { Close } from "../../close/Close";

export const PriorityRow = ({
  instance,
  priority,
  index,
  onRemovePriority,
  onChangePriority,
}) => {
  const styles = makeStyles(() => ({
    cell: {
      display: "flex",
      justifyContent: "flex-start",
      alignItems: "center",
      whiteSpace: "nowrap",
    },

    menuButton: {
      cursor: "pointer",
    },
  }));

  const [anchorEl, setAnchorEl] = React.useState();
  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handleRemove = (n) => {
    onRemovePriority(n);
    handleClose();
  };

  const handleShowAttributes = () => {};

  /**
   * determine the priorities available for the menu
   * can swap priority to self
   * can't swap priority to a blank item
   **/
  const priorities = [];
  instance.priorities.forEach((item, p) => {
    if (p !== index) priorities.push(p);
  });

  const handleChangePriority = (newPriority) => {
    onChangePriority(index, newPriority);
    handleClose();
  };

  const getName = () => {
    let result = priority.object.name;
    if (priority.object.revision) {
      result += " " + priority.object.revision;
    } else if (priority.object.Revision) {
      result += " " + priority.object.Revision;
    }
    return result;
  };

  const classes = styles();
  const priorityDisplayName = priority.object.PartNumber
    ? priority.object.name + " (" + priority.object.PartNumber + ")"
    : getName();

  return (
    <TableCell>
      <div className={classes.cell}>
        <MenuIcon className={classes.menuButton} onClick={handleClick} />
        <Menu
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleClose}
        >
          {priorities.map((newPriority) => (
            <MenuItem
              key={newPriority}
              onClick={() => handleChangePriority(newPriority)}
            >
              Change Priority from {index + 1} to {newPriority + 1}
            </MenuItem>
          ))}

          <MenuItem onClick={() => handleRemove(index)}>
            Remove {priorityDisplayName}
          </MenuItem>
        </Menu>

        <ComponentNameDisplay
          name={priorityDisplayName}
          engineering={priority.engineering}
        />

        <Close
          onClose={() => handleRemove(index)}
          tip="click to remove this component"
        />
      </div>
    </TableCell>
  );
};
