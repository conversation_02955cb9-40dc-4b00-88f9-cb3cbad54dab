import { TableCell, TableRow } from "@material-ui/core";
import React from "react";
import CloseIcon from "@material-ui/icons/Close";
import CheckIcon from "@material-ui/icons/Check";

/**
 * Show the verifier message as a table row
 *
 * @param message The VerifierMessage object
 * @returns {JSX.Element}
 * @function
 */
export const MessageRow = ({ message }) => {
  const left =
    message?.leftName == null
      ? "N/A"
      : `${message.leftName} = ${message.leftValue || "null"}`;
  const right =
    message?.rightName == null
      ? "N/A"
      : `${message.rightName} = ${message.rightValue || "null"}`;

  return (
    <TableRow hover>
      <TableCell>
        {message.ok ? (
          <CheckIcon htmlColor="#007700" />
        ) : (
          <CloseIcon htmlColor="#cc0000" />
        )}
      </TableCell>
      <TableCell>{message.text}</TableCell>
      <TableCell>{left}</TableCell>
      {message.rightName && <TableCell>{right}</TableCell>}
    </TableRow>
  );
};
