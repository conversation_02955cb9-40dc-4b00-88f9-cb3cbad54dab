/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FetchContext } from "src/component/fetch/VyperFetch";
import { Table, TableContainer, TableHead, TableRow } from "@material-ui/core";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import { AtSelectionRow } from "src/pages/mockup/atselection/AtSelectionRow";
import { BackToBuildFormLink } from "src/component/backbutton/BackToBuildFormLink";
import { BuildPageTitle } from "src/pages/vyper/BuildPageTitle";

export const AtSelectionPage = () => {
  const { buildNumber } = { ...useParams() };
  const { vget, vpost } = useContext(FetchContext);
  const [vyper, setVyper] = useState();
  const [build, setBuild] = useState();

  // fetch the vyper when the build number changes
  useEffect(() => {
    if (buildNumber == null) return;
    vget(`/vyper/v1/vyper/findByBuildNumber/${buildNumber}`, setVyper);
  }, [buildNumber]);

  // when the vyper changes, store the matching build
  useEffect(() => {
    if (vyper == null) return;
    setBuild(vyper.builds.find((build) => build.buildNumber === buildNumber));
  }, [vyper]);

  const handleSave = (selection, build) => {
    const selectionPosition = build.selections.findIndex(
      (s) => s.name === selection.name
    );

    vpost(
      "/vyper/v1/vyper/selection",
      {
        vyperNumber: vyper.vyperNumber,
        buildNumber: buildNumber,
        selectionPosition: selectionPosition,
        name: selection.name,
        items: selection.items,
      },
      setVyper
    );
  };

  if (build == null) return null;

  return (
    <div>
      <BackToBuildFormLink vyperNumber={vyper.vyperNumber} build={build} />

      <BuildPageTitle build={build} title={"A/T Selections"} />

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow hover>
              <TableCell>Component Name</TableCell>
              <TableCell>Business Selection</TableCell>
              <TableCell colSpan={2}>A/T Selection</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {build.selections.map((selection, n) => (
              <AtSelectionRow
                key={n}
                build={build}
                selection={selection}
                onSave={(s) => handleSave(s, build)}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};
