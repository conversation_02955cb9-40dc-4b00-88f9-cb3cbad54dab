import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React, { useContext } from "react";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { FileUploadDialogContext } from "../../../component/fileupload/FileUploadDialog";
import { ViewDialogContext } from "../../../component/view/ViewDialog";
import { Test } from "./Test";

export const TestRow = ({ vyper, builds, onChange }) => {
  const { open: openUploadDialog } = useContext(FileUploadDialogContext);
  const { open: openViewDialog } = useContext(ViewDialogContext);
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeTest = (content, build) => {
    return buildDao
      .changeTest(vyper.vyperNumber, build.buildNumber, content)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleUpload = (build) => {
    openUploadDialog({
      onSave: (content) => handleChangeTest(content, build),
    });
  };

  const handleEdit = (build) => {
    openUploadDialog({
      content: build.test.content,
      onSave: (content) => handleChangeTest(content, build),
    });
  };

  const handleView = (build) => {
    openViewDialog({
      title: `View Test Flow for ${build.buildNumber}`,
      content: build.test.content,
      handleEdit,
      build,
    });
  };

  const handleDelete = (build) => {
    openConfirmation({
      title: "Delete Test Operations",
      message: `Are you sure you want to delete the test operation(s)?`,
      yesText: "Yes",
      noText: "No",
      onYes: () => {
        return buildDao
          .changeTest(vyper.vyperNumber, build.buildNumber, null)
          .then((json) => onChange(json))
          .catch(noop);
      },
    });
  };

  return (
    <TableRow hover>
      <RowPrefix help="test" title="Test" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <Test
            vyper={vyper}
            build={build}
            onUpload={() => handleUpload(build)}
            onDelete={() => handleDelete(build)}
            onView={() => handleView(build)}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
