import React, { useContext, useEffect } from "react";
import { Route, Switch, useParams } from "react-router-dom";
import { DataModelsContext } from "src/DataModel";
import { VyperPraFormPage } from "src/pages/vyper/pras/VyperPraFormPage";
import { noop } from "src/component/vyper/noop";

export const VyperPraPage = ({ vyperNumber }) => {
  const { praDao } = useContext(DataModelsContext);
  const { praNumber } = useParams();

  const params = { vyperNumber, praNumber };

  return (
    <Switch>
      <Route
        exact
        path="/projects/:vyperNumber/pras/:praNumber"
        render={() => {
          return <VyperPraFormPage {...params} />;
        }}
      />
      <Route
        exact
        path="/projects/:vyperNumber/pras"
        render={() => {
          return <VyperPraFormPage {...params} />;
        }}
      />
    </Switch>
  );
};

export default VyperPraPage;
