import React, { useEffect, useState } from "react";
import { MenuItem, TextField } from "@material-ui/core";

const statusMapping = { A: "Active", W: "Working" };

export const AutoFetchAtssStatus = ({
  device,
  facility,
  status,
  onChangeStatus,
  disabled
}) => {
  const [statusArr, setStatusArr] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    if (device == null || facility == null) {
      return;
    }

    fetch(
      `/vyper/v1/vyper/atss/status/lookup?specDevice=${device}&facility=${facility}`
    )
      .then((response) => {
        response
          .json()
          .then((resultArr) =>
            setStatusArr(resultArr?.map((rec) => statusMapping[rec?.charAt(0)]))
          );
      })
      .catch((e) => console.log(e))
      .finally(() => setLoading(false));
  }, [device, facility]);

  return (
    <TextField
      disabled={disabled}
      variant="outlined"
      fullWidth
      select
      label="Traveler Status"
      value={status || ""}
      onChange={(e) => onChangeStatus(e.target.value)}
    >
      {statusArr.map((status) => (
        <MenuItem key={status} value={status?.toUpperCase()}>
          {status}
        </MenuItem>
      ))}
    </TextField>
  );
};
