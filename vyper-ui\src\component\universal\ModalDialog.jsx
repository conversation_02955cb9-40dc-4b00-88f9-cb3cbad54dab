import React from "react";
import PropTypes from "prop-types";
import Dialog from "@material-ui/core/Dialog";
import DialogActions from "@material-ui/core/DialogActions";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import DialogTitle from "@material-ui/core/DialogTitle";
import Modal from "@material-ui/core/Modal";
import Alert from "@material-ui/lab/Alert";
import { PrimaryButton, DangerButton, NeutralButton } from "./Buttons";

export const ModalComponent = ({ children }) => {
  return (
    <Modal disableBackdropClick disableEscapeKeyDown open>
      {children}
    </Modal>
  );
};

export const BasicModalDialog = ({
  title,
  children,
  buttons,
  handleClose,
  size = "xs",
}) => {
  return (
    <>
      <Dialog
        aria-labelledby="ti-dialog-title"
        fullWidth
        maxWidth={size}
        onClose={handleClose}
        open
        scroll="body"
      >
        <DialogTitle id="ti-dialog-title">{title}</DialogTitle>
        <DialogContent dividers>{children}</DialogContent>
        <DialogActions>
          {buttons
            ? buttons.map((button, i) => {
                return (
                  <ModalButton
                    handleClick={button.onClick}
                    icon={button.icon}
                    key={`mdl-btn-${i}-${button.text}`}
                    text={button.text}
                    variant={button.variant}
                    disabled={button.disabled}
                  />
                );
              })
            : null}
        </DialogActions>
      </Dialog>
    </>
  );
};

const ModalButton = ({
  text,
  icon,
  variant = "neutral",
  handleClick,
  disabled,
}) => {
  switch (variant) {
    case "primary":
      return (
        <PrimaryButton
          icon={icon}
          label={text}
          handleClick={handleClick}
          disabled={disabled}
        />
      );
    case "danger":
      return (
        <DangerButton
          icon={icon}
          label={text}
          handleClick={handleClick}
          disabled={disabled}
        />
      );
    case "neutral":
    default:
      return (
        <NeutralButton
          icon={icon}
          label={text}
          handleClick={handleClick}
          disabled={disabled}
        />
      );
  }
};

ModalButton.propTypes = {
  handleClick: PropTypes.func,
  icon: PropTypes.node,
  text: PropTypes.string,
  variant: PropTypes.oneOf(["primary", "danger", "neutral"]),
  disabled: PropTypes.bool,
};

BasicModalDialog.propTypes = {
  buttons: PropTypes.arrayOf(PropTypes.shape(ModalButton.propTypes)),
  children: PropTypes.any,
  handleClose: PropTypes.func,
  size: PropTypes.oneOf(["xs", "sm", "md", "lg"]),
  title: PropTypes.string,
};

export const InfoAlertDialog = ({ message, handleClose }) => {
  return (
    <BasicModalDialog
      buttons={[
        {
          text: "Close",
          onClick: handleClose,
        },
      ]}
      title="Information"
    >
      {message}
    </BasicModalDialog>
  );
};

InfoAlertDialog.propTypes = {
  handleClose: PropTypes.func,
  message: PropTypes.string.isRequired,
};

export const ConfirmDialog = ({
  message = "Are you sure?",
  handleYes,
  handleNo,
  title,
  disabled,
}) => {
  return (
    <BasicModalDialog
      buttons={[
        {
          text: "Yes",
          variant: "primary",
          onClick: handleYes,
          disabled: disabled,
        },
        {
          text: "No",
          variant: "neutral",
          onClick: handleNo,
          disabled: disabled,
        },
      ]}
      title={title ? title : "Confirmation"}
    >
      {message}
    </BasicModalDialog>
  );
};

ConfirmDialog.propTypes = {
  handleNo: PropTypes.func,
  handleYes: PropTypes.func,
  message: PropTypes.object.isRequired,
  title: PropTypes.string,
};
