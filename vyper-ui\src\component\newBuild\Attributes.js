import { Paper, TableBody } from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import Table from "@material-ui/core/Table";
import TableCell from "@material-ui/core/TableCell";
import TableContainer from "@material-ui/core/TableContainer";
import TableRow from "@material-ui/core/TableRow";
import Tooltip from "@material-ui/core/Tooltip";
import ArrowDropDownIcon from "@material-ui/icons/ArrowDropDownOutlined";
import ArrowDropUpIcon from "@material-ui/icons/ArrowDropUpOutlined";
import Alert from "@material-ui/lab/Alert";
import React, { useState } from "react";
import PropTypes from "prop-types";

/**
 * Show the material attributes
 *
 * @param device - The material attributes
 * @returns {JSX.Element|null}
 * @constructor
 */
export const Attributes = ({ device, facility }) => {
  const [showDeviceAttr, setShowDeviceAttr] = useState(false);

  if (device == null || device == undefined) {
    return null;
  }

  return (
    <div>
      <Tooltip title="Show/Hide Device Attributes">
        <IconButton
          aria-label="Show/Hide"
          onClick={() => setShowDeviceAttr(!showDeviceAttr)}
        >
          Device Attribute Details
          {showDeviceAttr ? (
            <ArrowDropDownIcon color="primary" />
          ) : (
            <ArrowDropUpIcon color="primary" />
          )}
        </IconButton>
      </Tooltip>

      {device.PackageNiche == null && facility?.PDBFacility != null && (
        <Alert severity="error">
          The Device and Facility you have selected is missing a Package Niche
          and needs to be added to the Galileo package parent.
          <br />
          <br />
          <strong>Note:</strong> it may take up to 12 hours after entry in
          Galileo for VYPER to be able to reflect changes.
        </Alert>
      )}

      {showDeviceAttr ? (
        <div>
          <hr />
          <h5>Device Attributes</h5>
          <TableContainer component={Paper}>
            <Table>
              <TableBody>
                {Object.keys(device || {})
                  .filter((key) => key != "PackageNicheMapping")
                  .sort((a, b) => {
                    if (a > b) {
                      return 1;
                    }
                    if (a < b) {
                      return -1;
                    }
                    return 0;
                  })
                  .map((key) => (
                    <TableRow key={key} hover>
                      <TableCell>{key}</TableCell>
                      <TableCell>{device[key]}</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          <hr />
        </div>
      ) : null}
    </div>
  );
};

Attributes.propTypes = {
  device: PropTypes.object,
};
