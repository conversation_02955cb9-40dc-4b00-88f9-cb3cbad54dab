import makeStyles from "@material-ui/core/styles/makeStyles";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import TableCell from "@material-ui/core/TableCell";
import TableContainer from "@material-ui/core/TableContainer";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { InstanceRow } from "./InstanceRow";

const useStyles = makeStyles(() => ({
  header: {
    marginLeft: "1rem",
    marginRight: "1rem",
  },
}));

/**
 * Show the selected component instances/priorities and values as a table
 *
 * @param component
 * @param selectedInstanceRow
 * @param onSelect
 * @param onRemovePriority
 * @param onChangePriority
 * @returns {JSX.Element}
 * @constructor
 */
export const Selected = ({
  component,
  selectedInstanceRow,
  onSelect,
  onRemovePriority,
  onChangePriority,
}) => {
  // determine the max number of priorities
  const numPriorities = component.instances
    .map((instance) => instance.priorities.length)
    .reduce((acc, cur) => Math.max(acc, cur), 0);

  const classes = useStyles();

  return (
    <div>
      <h3 className={classes.header}>Selected {component.name}(s)</h3>

      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow hover>
              <TableCell>Instance</TableCell>
              {new Array(numPriorities).fill(null).map((v, n) => (
                <TableCell key={n}>{`Priority ${n + 1}`}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {component.instances.map((instance, n) => (
              <InstanceRow
                key={n}
                index={n}
                component={component}
                instance={instance}
                selected={n === selectedInstanceRow}
                onSelect={onSelect}
                onRemovePriority={(pindex) => onRemovePriority(n, pindex)}
                onChangePriority={onChangePriority}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

Selected.propTypes = {
  component: PropTypes.object.isRequired,
  selectedInstanceRow: PropTypes.number,
  onSelect: PropTypes.func.isRequired,
  onRemovePriority: PropTypes.func.isRequired,
  onChangePriority: PropTypes.func.isRequired,
};
