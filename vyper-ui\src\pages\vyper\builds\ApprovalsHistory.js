import React, { useContext, useEffect, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import { formatDate } from "../../../component/dateFormat";
import { BackToBuildFormLink } from "../../../component/backbutton/BackToBuildFormLink";
import { DataModelsContext } from "src/DataModel";
import { noop } from "src/component/vyper/noop";
import { fetchTaskAssignments } from "../../../component/api/taskService2";

const defaultColDef = {
  headerClass: "ti-ag-header",
  resizable: true,
  suppressMovable: true,
};

const getRowStyle = (params) =>
  params.data.approvalAction.includes("Prior")
    ? { backgroundColor: "#A9A9A9" }
    : {};

const columns = [
  { headerName: "Group Name", field: "approvalGroupName", width: 215 },
  { headerName: "Name", field: "approverName", width: 150 },
  { headerName: "Creator", field: "creator", width: 150 },
  { headerName: "Action", field: "approvalAction", width: 146 },
  { headerName: "Comment", field: "approvalComment", flex: 1 },
  { headerName: "Rework Reason", field: "rejectReason" },
  { headerName: "Action Date/Time", field: "approvalDateTime" },
];

export const ApprovalsHistory = ({ vyperNumber, buildNumber, contextKey }) => {
  const { build, buildDao } = useContext(DataModelsContext);
  const [records, setRecords] = useState([]);

  useEffect(() => {
    if (buildNumber == null) {
      return;
    }
    buildDao.findByBuildNumber(buildNumber).catch(noop);
  }, [buildNumber]);

  // get the pending approvals from task service
  useEffect(() => {
    try {
      fetchTaskAssignments(contextKey)
        .then((assignments) => {
          return assignments.value
            .filter(
              ({ current, complete, fnctSatisfied }) =>
                (current && !fnctSatisfied) || (complete && fnctSatisfied)
            )
            .sort((a, b) => {
              //default to empty string if undefined
              const branchNameA = a.branchName || "";
              const branchNameB = b.branchName || "";
              if (a.modifiedDttm > b.modifiedDttm) return -1;
              if (a.modifiedDttm < b.modifiedDttm) return 1;
              if (branchNameA > branchNameB) return 1;
              if (branchNameA < branchNameB) return -1;
              return 0;
            });
        })
        .then((assignments) => {
          let lastRework = false;
          let currGroups = new Set();
          return assignments.map((assignment) => {
            const {
              current,
              fnctName,
              userId,
              userName,
              creatorName,
              branchName,
              commentText,
              modifiedDttm,
            } = assignment;
            const { comment, reason } = JSON.parse(commentText || "{}");

            let approvalAction = "";
            if (branchName) {
              const hasRework = branchName?.includes("rework");
              if (!lastRework) {
                lastRework = hasRework;
              }
              approvalAction =
                current || (!lastRework && !currGroups.has(fnctName))
                  ? "Current "
                  : "Prior ";
              approvalAction += hasRework ? "Rework" : "Approved";
            }

            //if most recent, showing hide previous actions
            if (current || branchName) {
              currGroups.add(fnctName);
            }

            return {
              approvalGroupName: fnctName,
              approverId: userId,
              approverName: userName,
              creator: creatorName,
              approvalAction: approvalAction,
              approvalComment: comment,
              rejectReason: reason,
              approvalDateTime: formatDate(modifiedDttm),
            };
          });
        })
        .then((historyTasks) => setRecords(historyTasks));
    } catch (error) {
      console.log(
        "Task Service seems to be down now, You may want to try later."
      );
      console.error(error);
    }
  }, []);

  return (
    <div>
      {vyperNumber && (
        <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />
      )}

      <div
        className={"ti-server-ag-grid ag-theme-alpine"}
        style={{
          marginBottom: "10px",
          paddingTop: "25px",
          width: "100%",
          height: "100%",
        }}
      >
        <AgGridReact
          rowData={records}
          domLayout={"autoHeight"}
          columnDefs={columns}
          defaultColDef={defaultColDef}
          getRowStyle={getRowStyle}
          enableCellTextSelection={true}
        />
      </div>
    </div>
  );
};
