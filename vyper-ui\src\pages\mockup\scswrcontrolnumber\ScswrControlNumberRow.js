import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import PropTypes from "prop-types";
import React from "react";
import { samenessDifference } from "src/component/sameness/sameness";
import { ScswrControlNumberCell } from "src/pages/mockup/scswrcontrolnumber/ScswrControlNumberCell";
import { RowPrefix } from "src/pages/mockup/RowPrefix";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

/**
 *
 * @param vyper
 * @param builds
 * @param showSameness
 * @param onSave
 * @returns {JSX.Element}
 * @constructor
 */
export const ScswrControlNumberRow = ({
  vyper,
  builds,
  showSameness,
  handleRowChange: onSave,
}) => {
  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessDifference(builds),
      })}
      hover
    >
      <RowPrefix
        help="scswrcontrolnumber"
        title="SCSWR Control Number"
      />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <ScswrControlNumberCell
            key={n}
            vyper={vyper}
            build={build}
            onSave={(text) => onSave(text, build)}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};

ScswrControlNumberRow.propTypes = {
  vyper: PropTypes.object.isRequired,
};
