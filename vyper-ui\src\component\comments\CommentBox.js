import React from "react";
import { Grid, Typography, Paper, Tooltip } from "@material-ui/core";
import { UsernameAvatar } from "./UsernameAvatar";
import { formatDate } from "../dateFormat";
import moment from "moment";

export const CommentBox = ({ c, n }) => {
  return (
    <Paper
      style={{
        padding: "10px 30px 10px ",
        marginTop: 10,
        backgroundColor: "#FAF9F6",
      }}
      key={n}
    >
      <Grid container wrap="nowrap" spacing={1} key={n}>
        <Grid item>
          <UsernameAvatar username={c.who.username} />
        </Grid>
        <Grid item xs zeroMinWidth>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <div style={{ float: "left" }}>
              <h4 style={{ margin: 0, textAlign: "left" }}>{c.who.username}</h4>
            </div>
            <div style={{ float: "left" }}>
              <p style={{ textAlign: "right", color: "gray" }}>
                Operation Category: {c?.operation || "General"}
              </p>
            </div>
          </div>
          <p style={{ textAlign: "left" }}>{c.text}</p>
          <Tooltip title={formatDate(c.when)} placement="top-start" arrow>
            <p style={{ textAlign: "left", color: "gray" }}>
              posted {moment(c.when).fromNow()}
            </p>
          </Tooltip>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default CommentBox;
