import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  Grid,
} from "@material-ui/core";
import React, { useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import AddBuildButton from "./AddBuildButton";
import AddPraButton from "./AddPraButton";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  actionButtons: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-evenly",
    gap: "5px",
    marginBottom: "1rem",
  },
  gridContainer: {
    display: "flex",
  },
  gridButtons: {
    justifyContent: "center",
  },
  gridText: {
    textAlign: "left",
    marginTop: "auto",
    marginBottom: "auto",
  },
  gridSubHeading: {
    textAlign: "center",
  },
  dividerLine: {
    height: "1px",
    color: "red",
    backgroundColor: "red",
  },
});
export const CreateDialog = ({ children }) => {
  const [open, setOpen] = useState(false);

  // Submit functions
  const [addNewPra, setAddNewPra] = useState(undefined);
  const [addNewBuild, setAddNewBuild] = useState(undefined);

  const handleOpen = ({ onAddPra, onAddBuild }) => {
    setAddNewPra(() => onAddPra);
    setAddNewBuild(() => onAddBuild);
    setOpen(true);
  };
  const onAddNewBuild = (
    mode,
    vyperNumber,
    buildNumber,
    flowData,
    description,
    buildType,
    copyBuildNumber,
    symbolChoice
  ) => {
    handleClickSave();
    addNewBuild(
      mode,
      vyperNumber,
      buildNumber,
      flowData,
      description,
      buildType,
      copyBuildNumber,
      symbolChoice
    );
  };

  const onAddNewPra = (buildnumber) => {
    handleClickSave();
    addNewPra(buildnumber);
  };

  // close the dialog when cancel or escape is clicked/pressed
  const handleClickCancel = () => {
    setOpen(false);
    resetValues();
  };
  // This is the onsubmit function
  const handleClickSave = () => {
    resetValues();
    setOpen(false);
  };
  // Resets all the values in the dialog
  const resetValues = () => {};

  const classes = useStyles();

  return (
    <CreateDialogContext.Provider
      value={{
        openCreateDialog: handleOpen,
      }}
    >
      <Dialog open={open} maxWidth="lg">
        <DialogTitle>Select What to Create</DialogTitle>
        <DialogContent>
          <Grid
            container
            className={classes.gridContainer}
            spacing={0}
            alignItems="center"
          >
            <Divider />
            <BuildButton onAddNewBuild={onAddNewBuild} />
            <Divider />
            <GridSubHeader
              subheader={
                "Use to convert a Final Approved New or Minor Change VYPER Build into a new PRA"
              }
            />
            <PRAButton onAddNewPra={onAddNewPra} />
            <Divider />
            <GridSubHeader
              subheader={
                "Use to change OPN, Test Program and/or symbol - No manufacturing build needed"
              }
            />
            <AtssSCNButton
              onAddNewAtssSCN={() => {
                console.log("onAddNewAtssSCN");
              }}
            />
            <VyperSCNButton
              onAddNewVyperSCN={() => {
                console.log("onAddNewVyperSCN");
              }}
            />
            <Divider />
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleClickCancel}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </CreateDialogContext.Provider>
  );
};

export const CreateDialogContext = React.createContext(null);

const BuildButton = ({ onAddNewBuild }) => {
  const classes = useStyles();
  return (
    <>
      <Grid item xs={2} className={classes.gridButton}>
        <AddBuildButton handleAddNewBuild={onAddNewBuild} />
      </Grid>
      <Grid item xs={10} className={classes.gridText}>
        <p>
          Click ADD BUILD to create a new VYPER Build - Experiment, New, or
          Minor Change Build Types
        </p>
      </Grid>
    </>
  );
};

const PRAButton = ({ onAddNewPra }) => {
  const classes = useStyles();
  return (
    <>
      <Grid item xs={2} className={classes.gridButton}>
        <AddPraButton handleAddNewPra={onAddNewPra} />
      </Grid>
      <Grid item xs={10} className={classes.gridText}>
        <p>Click Start PRA to find an eligible VYPER Build</p>
      </Grid>
    </>
  );
};
const AtssSCNButton = ({ onAddNewAtssSCN }) => {
  const classes = useStyles();
  return (
    <>
      <Grid item xs={2} className={classes.gridButton}>
        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={onAddNewAtssSCN}
        >
          Atss SCN
        </Button>
      </Grid>
      <Grid item xs={10} className={classes.gridText}>
        <p>Click ATSS to use an active ATSS</p>
      </Grid>
    </>
  );
};

const VyperSCNButton = ({ onAddNewVyperSCN }) => {
  const classes = useStyles();
  return (
    <>
      <Grid item xs={2} className={classes.gridButton}>
        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={onAddNewVyperSCN}
        >
          Vyper SCN
        </Button>
      </Grid>
      <Grid item xs={10} className={classes.gridText}>
        <p>Click VYPER to find a PRA_APPROVED VYPER Build</p>
      </Grid>
    </>
  );
};

const Divider = () => {
  const classes = useStyles();
  return (
    <Grid item xs={12}>
      <hr className={classes.dividerLine} />
    </Grid>
  );
};
const GridSubHeader = ({ subheader }) => {
  const classes = useStyles();
  return (
    <Grid item xs={12} className={classes.gridSubHeading}>
      <h3>{subheader}</h3>
    </Grid>
  );
};
