import React from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  MenuItem,
  TextField,
} from "@material-ui/core";
import DialogContentText from "@material-ui/core/DialogContentText";
import { DateTimePicker, MuiPickersUtilsProvider } from "@material-ui/pickers";
import DateFnsUtils from "@date-io/date-fns";
import { Formik } from "formik";
import * as yup from "yup";
import Button from "@material-ui/core/Button";
import moment from "moment";

export const FlowRowMapDialog = ({
  show,
  rowData,
  onSave,
  onClose,
  mffFlows,
  defaultRows,
  handleChange,
}) => {
  console.log({ mffFlows, defaultRows, id: rowData?.id });

  const disableSave =
    rowData?.flowName !== undefined && rowData?.opnName !== undefined;
  console.log({ disableSave });
  return (
    <Dialog open={show} onClose={onClose}>
      <DialogTitle>
        {rowData?.id == undefined
          ? "Create Flow Row Map"
          : `Edit Flow Row Map #${rowData.id}`}
      </DialogTitle>

      <DialogContent>
        <DialogContentText>
          <Grid container>
            <Grid item xs={12}>
              <TextField
                autoFocus
                fullWidth
                select
                margin="dense"
                name="value"
                value={rowData?.flowName || ""}
                onChange={(e) => handleChange(e, "flowName")}
                variant="outlined"
                label="Select Flow"
              >
                {mffFlows.map((flow) => (
                  <MenuItem key={flow} value={flow}>
                    {flow}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12}>
              <TextField
                autoFocus
                fullWidth
                select
                margin="dense"
                name="value"
                value={rowData?.opnName || ""}
                onChange={(e) => handleChange(e, "opnName")}
                variant="outlined"
                label="Select Row"
              >
                {defaultRows.map((row) => (
                  <MenuItem key={row} value={row}>
                    {row}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                variant="outlined"
                type="number"
                label="Row Sequence"
                name="rowSequence"
                fullWidth
                value={rowData?.opnSeq || ""}
                onChange={(e) => handleChange(e, "opnSeq")}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                variant="outlined"
                fullWidth
                multiline
                rows={3}
                label="Row Description"
                name="description"
                value={rowData?.opnDesc || ""}
                onChange={(e) => handleChange(e, "opnDesc")}
              />
            </Grid>
          </Grid>
        </DialogContentText>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined" color="primary">
          Cancel
        </Button>
        <Button
          onClick={onSave}
          variant="contained"
          color="primary"
          disabled={!disableSave}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};
