import React from "react";
import HelpIcon from "@material-ui/icons/Help";
import { makeStyles } from "@material-ui/core/styles";
import Tooltip from "@material-ui/core/Tooltip";
import withStyles from "@material-ui/core/styles/withStyles";

export const Help = ({ name }) => {
  const styles = makeStyles(() => ({
    help: {
      fontSize: "1rem",
      color: "hsla(50, 100%, 33%, 1)",
    },
  }));

  const descriptions = {
    build:
      "This row shows the build number for each device, " +
      "as well as the status. Use this number to identify the specific " +
      "device in this traveler.",

    workflow:
      "These buttons allow you to change the state of this build." +
      " Use them to submit, approve or rework the build as well as view the approval status.",

    comment:
      "Comments for this build are added and viewed on this row." +
      " Use comments to pass information back-and-forth between the business unit and A/T Site.",

    device: "Use this row to pick the device for this build.",

    facility: "Use this row to pick the A/T Facility for this build.",

    backgrind: "Use this row to add Backgrind Thickness.",

    armarc:
      "This row is optional. If you wish to load component data from an armarc job " +
      "then you can select it here.",

    atss:
      "This row is optional. If you wish to load component data from an ATSS traveler " +
      "then you can select it here.",

    vypercopy:
      "This row is optional. If you wish to load component data from VYPER " +
      "then you can select it here.",

    die: "Use this row to enter the incoming wafer thickness for each die, add or delete dies or change the priority.",

    copyfrom:
      "Use this row to copy data from ARM/ARC, ATSS or another VYPER Build." +
      " Copied data will override PGS data if different.",

    component: "This row allows you to pick the components used in the build.",

    symbolization: "This row allows you to choose or alter the symbolization.",

    selection:
      "The A/T can choose the actual component values used in the build here.",

    bomtemplate:
      "The template is the foundation of a flow, and contains the initial operations and " +
      "components. Normally, this comes from the Bill of Process Template, but it could come from a ATSS copy-from or " +
      "another Vyper build.",

    flow: "The flow is the display of manufacturing steps used in the VYPER Build Traveler",

    summary:
      "The summary shows the selections used for the device in an " +
      "easy-to-read single page.",

    traveler:
      "The traveler shows a ATSS-like traveler showing all operations, " +
      "components and attributes.",

    url: "When clipped, the COPY Button will copy the URL for this build into your clipboard so you can paste it into SCSWR.",

    description:
      "Use this row to enter or edit the Build Description to facilitate identification or purpose of the build.",

    buildtype:
      "Use this row to change the Build Type if allowed." +
      " If Minor Change is selected an approved VYPER Build from Build Type New or Minor Change must be referenced.",

    pkgniche:
      "Use this row to change or select the desired Package Niche for this build.",

    wafersawmethod: "Use this row to select the desired wafer saw method.",

    leadframe:
      "The row allows you to pick the Leadframe Supplier Number for this build.",

    mountcompound:
      "The row allows you to pick the Mount Compound Name for this build.",

    mbdiagram:
      "The row allows you to pick the MB Diagram Number for this build.",

    wire: "The row allows you to pick the Wire for this build.",

    moldcompound:
      "The row allows you to pick the Mold Compound Name for this build.",

    msl: "This row allows you to change the moisture level packing requirement for this build.",

    test:
      "This row is only enabled if TKY Option is chosen." +
      " This row allows you to upload test information from a file or directly paste the information into " +
      "the screen as well as viewing or deleting previously entered test information.",

    packConfig: "This row allows you to choose how you want the build packed.",

    dryBake: "Select whether this build requires Dry Bake (Yes or No).",

    esl:
      "This allows you to select the Shelf Life or how long it can " +
      "stay unsold in the PDC - Standard is 2yrs/Extended is 5yrs.",

    turnkey:
      "Default is NON-TKY for assembly only processing.  Select TKY if test is needed for this build.",
  };

  const HtmlTooltip = withStyles((theme) => ({
    tooltip: {
      backgroundColor: "rgb(253, 250, 234) ",
      color: "rgba(0, 0, 0, 0.87)",
      maxWidth: 220,
      fontSize: theme.typography.pxToRem(12),
      border: "1px solid black",
      borderRadius: 12,
    },
  }))(Tooltip);

  const classes = styles();

  return (
    <HtmlTooltip title={descriptions[name] || name} placement="top" arrow>
      <span className={classes.help}>
        <HelpIcon fontSize="inherit" />
      </span>
    </HtmlTooltip>
  );
};
