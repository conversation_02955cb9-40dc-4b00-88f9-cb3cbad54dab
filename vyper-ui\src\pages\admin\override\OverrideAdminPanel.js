import {
  Button,
  FormControlLabel,
  FormGroup,
  makeStyles,
  MenuItem,
  TextField,
} from "@material-ui/core";
import React, { useContext, useState } from "react";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { BuildNumberAutocomplete } from "../../../autocomplete/components/BuildNumberAutocomplete";
import {
  AlertDialog<PERSON>rror<PERSON><PERSON><PERSON>,
  SpinnerLoadingHandler,
} from "../../../component/fetch/DaoBase";
import { convertBuildNumbertoVyperNumber } from "../../../component/helper/convertBuildNumbertoVyperNumber";
import { SpinnerContext } from "../../../component/Spinner";
import { OverridesDao } from "../../../dao/OverridesDao";
import { logError } from "../../functions/logError";

const useStyles = makeStyles(() => ({
  root: {},
  widget: {
    width: "16rem",
  },
}));

/**
 * This component shows an administrative panel that allows the build state to be overridden.
 *
 * @returns {JSX.Element}
 * @constructor
 */
export function OverrideAdminPanel() {
  const [buildNumber, setBuildNumber] = useState("");
  const [buildState, setBuildState] = useState("");

  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  function handleClickUpdateBuildState() {
    if (buildNumber === "" || buildState === "") return;

    const overridesDao = new OverridesDao({
      errorHandler: new AlertDialogErrorHandler(openAlert),
      loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    });

    overridesDao
      .buildState(
        convertBuildNumbertoVyperNumber(buildNumber),
        buildNumber,
        buildState
      )
      .catch(logError);
  }

  const enabled = buildNumber !== "" && buildState !== "";

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <h3>Change Build's State</h3>

      <form className={classes.form}>
        <FormGroup row>
          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                variant="outlined"
                defaultNumber={buildNumber}
                onSelect={(number) => setBuildNumber(number)}
              />
            }
          />
        </FormGroup>

        <FormGroup row>
          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <TextField
                fullWidth
                select
                value={buildState}
                label="Build State"
                onChange={(e) => setBuildState(e.target.value)}
                variant="outlined"
              >
                <MenuItem value="AT_REVIEW_CHANGE">AT_REVIEW_CHANGE</MenuItem>
                <MenuItem value="BU_REVIEW_CHANGE">BU_REVIEW_CHANGE</MenuItem>
                <MenuItem value="CANCELED">CANCELED</MenuItem>
                <MenuItem value="DRAFT">DRAFT</MenuItem>
                <MenuItem value="FINAL_APPROVED">FINAL_APPROVED</MenuItem>
                <MenuItem value="REWORK">REWORK</MenuItem>
              </TextField>
            }
          />
        </FormGroup>

        <Button
          variant="contained"
          color="primary"
          disabled={!enabled}
          onClick={handleClickUpdateBuildState}
        >
          Update
        </Button>
      </form>
    </div>
  );
}
