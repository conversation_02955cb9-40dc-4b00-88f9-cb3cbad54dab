import axios from "axios";
import { useMutation, useQuery } from "react-query";
import { BASE_URL } from "../common/scswrAPI";
import useSnackbar from "../../../../hooks/Snackbar";
import { saveAs } from "file-saver";
import { ACCEPTABLE_UPLOAD_FILE_TYPES } from "./config";

export let useSwrsFetcher = (swrTab, options = {}) => {
  let { enqueueErrorSnackbar } = useSnackbar();
  let { onSuccess } = options;
  let swrsFetcher = useQuery(
    ["listswrs", "swrTab", swrTab],
    () => {
      return axios
        .get(`${BASE_URL}/getlistswrs?swrTab=${swrTab}`)
        .then((res) => res.data);
    },
    {
      onSuccess: (data) => {
        if (onSuccess) onSuccess(data);
      },
      onError: () => {
        enqueueErrorSnackbar("An error occured while trying to load swrs.");
      },
    }
  );

  return swrsFetcher;
};

export let useSwrsExcelUploader = (options = {}) => {
  let { enqueueSuccessSnackbar, enqueueWarningSnackbar } = useSnackbar();
  let { onSuccess } = options;
  let swrsExcelUploader = useMutation(
    (file) =>
      new Promise((resolve, reject) => {
        if (!ACCEPTABLE_UPLOAD_FILE_TYPES.includes(file.type)) {
          reject(["Must upload a valid excel file only"]);
        }
        let formData = new FormData();
        formData.append("file", file);
        axios
          .post(`${BASE_URL}/scswrUpload`, formData)
          .then((res) => {
            resolve(res.data);
          })
          .catch((error) => {
            if (error.response.status === 400) {
              reject(
                error.response.data.uploadErrors.map((e) => {
                  let splitBr = e.split("<br/>").filter((e) => e.length > 0);
                  let specialSplit = splitBr.flatMap((d) =>
                    d.split("$_").filter((e) => e.length > 0)
                  );
                  return specialSplit;
                })
              );
            } else {
              reject(["An error occured while trying to upload excel file"]);
            }
          });
      }),
    {
      onSuccess: (data) => {
        let { swrIdNoUpdate, swrIdUpdate } = data;
        if (swrIdNoUpdate.length > 0 && swrIdUpdate.length === 0) {
          enqueueWarningSnackbar("No swr change(s) were made");
        } else if (swrIdNoUpdate.length === 0 && swrIdUpdate.length > 0) {
          enqueueSuccessSnackbar("Successfully uploaded swr change(s)");
        }
        if (onSuccess) onSuccess(data);
      },
      onError: () => {},
    }
  );
  return swrsExcelUploader;
};

export let useSwrsExcelDownloader = (options = {}) => {
  let { enqueueErrorSnackbar } = useSnackbar();
  let { onSuccess } = options;
  let swrsExcelUploader = useMutation(
    (gridApi) => {
      let swrIds = gridApi.getSelectedRows().map((row) => {
        return row.SWR_ID;
      });
      if (swrIds.length === 0) {
        alert("No Swrs Have been Selected!");
        return;
      }
      saveAs(`${BASE_URL}/scswrDownload?swrIds=` + swrIds.join(","));
    },
    {
      onSuccess: (data) => {
        if (onSuccess) onSuccess(data);
      },
      onError: () => {
        enqueueErrorSnackbar(
          "An error occured while trying to download excel file"
        );
      },
    }
  );
  return swrsExcelUploader;
};
