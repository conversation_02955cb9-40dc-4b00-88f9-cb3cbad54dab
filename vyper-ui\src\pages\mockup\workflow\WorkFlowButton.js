import React, { useContext } from "react";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import makeStyles from "@material-ui/core/styles/makeStyles";
import List from "@material-ui/core/List";
import { ListItem, ListItemIcon, ListItemText } from "@material-ui/core";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import Button from "@material-ui/core/Button";

export const WorkFlowButton = ({ visible, blockers, onClick, value }) => {
  const { open } = useContext(AlertDialogContext);

  const useStyles = makeStyles((theme) => ({
    button: {
      marginRight: theme.spacing(1),
      //marginBottom: theme.spacing(1),
    },
  }));

  const handleClick = () => {
    if (blockers.length === 0) {
      onClick();
    } else {
      const message = (
        <div>
          <div>
            You are unable to perform that action. Here are the reasons:
          </div>
          <List>
            {blockers.map((text) => (
              <ListItem key={text}>
                <ListItemIcon>
                  <ArrowRightAltIcon color="primary" />
                </ListItemIcon>
                <ListItemText>{text}</ListItemText>
              </ListItem>
            ))}
          </List>
        </div>
      );
      // const text = "You are unable to perform that action. Here are the reasons:\n\n" + blockers.join("<br/>")
      open({ title: "Can't Perform Action: " + value, message: message });
    }
  };

  const classes = useStyles();

  return visible ? (
    <Button
      className={classes.button}
      variant="contained"
      color="primary"
      size="small"
      type="button"
      name="action"
      value={value}
      onClick={handleClick}
    >
      {value}
    </Button>
  ) : null;
};
