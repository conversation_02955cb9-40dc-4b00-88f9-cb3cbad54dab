import React, { useState, useRef, useContext } from "react";
import Button from "@material-ui/core/Button";
import ButtonGroup from "@material-ui/core/ButtonGroup";
import { FormControl, InputLabel, Select, MenuItem } from "@material-ui/core";
import CircularProgress from "@material-ui/core/CircularProgress";
import { AuthContext, VSWR_ROLE_GROUPS } from "src/component/common/auth";
import { ForbiddenErrorPage } from "../common/HttpErrorPage";
const { SBE, BTH } = VSWR_ROLE_GROUPS;

import GetAppIcon from "@material-ui/icons/GetApp";
import PublishIcon from "@material-ui/icons/Publish";
import { Alert } from "@material-ui/lab";
import PageHeader from "../common/PageHeader";
import {
  tableOptions,
  columnDefs,
  ACCEPTABLE_UPLOAD_FILE_TYPES,
} from "./config";
import {
  useSwrsFetcher,
  useSwrsExcelUploader,
  useSwrsExcelDownloader,
} from "./queries";
import ChangeRequestDialog from "../common/ChangeRequestDialog";
import VswrAgGridReact from "../common/VswrAgGridReact";
import useSnackbar from "../../../../hooks/Snackbar";

const ListSwrsPage = () => {
  let { closeSnackbar } = useSnackbar();
  const gridRef = useRef(null);
  const [selectedTableOption, setSelectedTableOption] = useState(0);
  const swrsFetcher = useSwrsFetcher(tableOptions[selectedTableOption].value);
  const swrsExcelUploader = useSwrsExcelUploader({
    onSuccess: () => {
      swrsFetcher.refetch();
    },
  });
  const swrsExcelDownloader = useSwrsExcelDownloader();
  const { authUser } = useContext(AuthContext);
  let userVswrSecurityRole = authUser?.vswrSecurity?.roleCategory;
  let hasSbeAccess = userVswrSecurityRole === SBE;
  let hasBthAccess = userVswrSecurityRole === BTH;

  if (!(hasSbeAccess || hasBthAccess)) {
    return <ForbiddenErrorPage />;
  }

  return (
    <div
      className="App BatchContainer"
      style={{ display: "flex", flexDirection: "column", gap: "16px" }}
    >
      <PageHeader headerTitle={"SWR List"} />
      <ButtonGroup variant="contained" style={{ width: "fit-content" }}>
        <Button
          onClick={() => {
            swrsExcelDownloader.mutate(gridRef.current?.api);
          }}
          disabled={
            swrsExcelDownloader.isLoading || swrsExcelUploader.isLoading
          }
          startIcon={
            swrsExcelDownloader.isLoading && <CircularProgress size={"24px"} />
          }
        >
          <GetAppIcon /> Download To Excel
        </Button>
        <Button
          color="secondary"
          component="label"
          disabled={
            swrsExcelDownloader.isLoading || swrsExcelUploader.isLoading
          }
          startIcon={
            swrsExcelUploader.isLoading && <CircularProgress size={"24px"} />
          }
        >
          <PublishIcon /> Upload Excel File
          <input
            type="file"
            hidden
            value={""} // force on change to always be called
            accept={ACCEPTABLE_UPLOAD_FILE_TYPES.join(",")}
            onChange={(event) => {
              closeSnackbar();
              let target = event.target;
              swrsExcelUploader.mutate(target.files[0]);
            }}
          />
        </Button>
      </ButtonGroup>

      {swrsExcelUploader.data &&
        swrsExcelUploader.data.swrIdUpdate.length > 0 &&
        swrsExcelUploader.data.swrIdNoUpdate.length > 0 && (
          <div
            className={"alert-box"}
            style={{ display: "flex", gap: "8px", flexDirection: "column" }}
          >
            <Alert severity="success">
              The following SWR ID(s) have been uploaded:
              {swrsExcelUploader.data.swrIdUpdate.map((id) => (
                <p>{id}</p>
              ))}
            </Alert>
            <Alert severity="warning">
              Note, the following SWR ID(s) have no updates:
              {swrsExcelUploader.data.swrIdNoUpdate.map((id) => (
                <p>{id}</p>
              ))}
            </Alert>
          </div>
        )}

      {swrsExcelUploader.error && (
        <div
          className={"alert-box"}
          style={{ display: "flex", gap: "8px", flexDirection: "column" }}
        >
          {swrsExcelUploader.error.map((errorData) => (
            <Alert severity="error">
              {Array.isArray(errorData)
                ? errorData.map((e) => <p>{e}</p>)
                : errorData}
            </Alert>
          ))}
        </div>
      )}

      <div
        style={{
          width: "min-content",
          display: "flex",
          gap: "16px",
          flexDirection: "column",
        }}
      >
        <FormControl style={{ margin: 0 }}>
          <InputLabel>Show SWRs From Table</InputLabel>
          <Select
            variant={"outlined"}
            style={{ width: "400px" }}
            value={selectedTableOption}
            onChange={(e) => {
              let target = e.target;
              setSelectedTableOption(target.value);
            }}
          >
            {tableOptions.map((option, i) => (
              <MenuItem key={option.value} value={i}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {tableOptions[selectedTableOption].value !== "allSwr" && (
          <ChangeRequestDialog
            triggerButtonLabel={tableOptions[selectedTableOption].actionLabel}
            onSuccessfulChange={() => {
              swrsFetcher.refetch();
            }}
            onOpenRequest={() => {
              let swrIds = gridRef.current.api.getSelectedRows().map((row) => {
                return row.SWR_ID;
              });
              if (swrIds.length === 0) {
                return Promise.reject("No Swrs Have been Selected!");
              }
              let table = tableOptions[selectedTableOption].value;
              return Promise.resolve({ swrIds, table });
            }}
          />
        )}
      </div>

      {swrsFetcher.isLoading || swrsFetcher.isRefetching ? (
        <CircularProgress />
      ) : (
        <VswrAgGridReact
          ref={gridRef}
          rowData={swrsFetcher.data}
          columnDefs={columnDefs}
        />
      )}
    </div>
  );
};
export default ListSwrsPage;
