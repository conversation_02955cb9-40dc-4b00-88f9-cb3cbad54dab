import Moment from "moment";

export const dateFormat = "MM/DD/YYYY h:mm:ss a";
export const dateOnlyFormat = "MM/DD/YY";
export const timeOnlyFormat = "h:mm:ss a";
export const dateGridFormat = "MM/DD/YYYY h:mm:ss";

export const formatDate = (date) => Moment(date).format(dateFormat);
export const separatedFormatDate = (date) => {
  return {
    date: Moment(date).format(dateOnlyFormat),
    time: Moment(date).format(timeOnlyFormat),
  };
};
