import config from "../../buildEnvironment";

/**
 * Fetch the current approval groups from RoleService
 *
 * @param vyper
 * @param build
 * @returns {Promise}
 */
export const roleServiceGetGroups = (vyper, build) => {
  // Internal role svc api
  const urlRoleSvc = `${config.roleSvcEnv.apiUrl}VYPER_AT_${build.facility.object.PDBFacility}`;

  // Internal role service for EM subcons
  const urlEmRoleSvc = `/vyper/v1/emas/plant/roles/${build.facility.object.PDBFacility}`;

  const url =
    build.facility.object.PlantType === "A" ? urlRoleSvc : urlEmRoleSvc;
  return fetch(url, {
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Pragma: "no-cache",
      "Cache-Control": "no-cache",
    },
  })
    .then((response) => {
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`${response.status}: ${response.statusText}`);
      }
    })
    .then((page) =>
      extractApprovalGroups(
        vyper,
        build,
        page.content ? page.content : page.groups
      )
    );
};

/**
 * given the content of the role service group's, returns a list of groups that must approve the build.
 *
 * records looks like this:
 *
 *    [
 *        {
 *            "id": "D403204DC32B400293986F90015F0817",
 *            "app": "AF3FDF2298A1BE7FE05343CBB40A375C",
 *            "name": "VYPER_FMX_ASSY_BOND",
 *            "parentGroupPathName": "VYPER_AT_FMX",
 *            "parent": "D3AE0F1E5A86473D804E5048B6FB2B39",
 *            "roleGroup": true
 *        },
 *        {
 *            "id": "FA25674271F941D0931CCF858F3E504B",
 *            "app": "AF3FDF2298A1BE7FE05343CBB40A375C",
 *            "name": "VYPER_FMX_ASSY_FINISH",
 *            "parentGroupPathName": "VYPER_AT_FMX",
 *            "parent": "D3AE0F1E5A86473D804E5048B6FB2B39",
 *            "roleGroup": true
 *        },
 *        ...
 *    ]
 *
 * @param vyper
 * @param build
 * @param records
 * @function
 *
 */
export const extractApprovalGroups = (vyper, build, records) => {
  return (
    records
      ?.filter((record) => shouldAddGroup(record, build))
      .map((record) => record) || []
  );
};

/**
 * Determines if an approval group should be added to the list of approvers.
 *
 * @param record
 * @param build
 * @return true if the group should be included
 * @function
 */
export const shouldAddGroup = (record, build) => {
  // pull out the needed fields
  const roleGroup = record.roleGroup;
  const name = record.name;
  const facility = "_" + build.facility.object.PDBFacility + "_";
  const turnkey = build.turnkey.value;
  const sbe = build.material.object.SBE;
  const sbe1 = build.material.object.SBE1;
  const isDevelopment = build.bomTemplate.object.isDevelopment;
  const hasEngineeringOperations = build.traveler.operations.some(
    (operation) => operation.engineering === "Y"
  );

  // determine if the group should be added
  return (
    roleGroup &&
    (isBasicGroup(name, facility) ||
      isTestGroup(name, facility, turnkey, sbe, sbe1) ||
      isDevelopmentTemplateAndNoTest(name, facility, isDevelopment) ||
      isScpAndHasEngineeringOperations(
        name,
        facility,
        hasEngineeringOperations
      )) &&
    isGroupInBuildFlow(name, build)
  );
};

/**
 * Return true if this is a basic group.
 * It will return true for _BOND, _Finish, _PREBOND and _PACK
 * it will return false for _SCP  and _TEST
 *
 * @param name
 * @param facility
 * @function
 */
export const isBasicGroup = (name, facility) => {
  return (
    name.startsWith("VYPER") &&
    name.includes(facility) &&
    !name.endsWith("_SCP") &&
    !name.includes("TEST")
  );
};

/**
 * Returns true if this is a test group.
 * Must have TEST_ in the name, and turnkey == NON-TKY
 *
 * @param name
 * @param facility
 * @param turnkey
 * @param sbe
 * @param sbe1
 * @function
 */
export const isTestGroup = (name, facility, turnkey, sbe, sbe1) => {
  return (
    name.startsWith("VYPER") &&
    name.includes(facility) &&
    name.includes("TEST_") &&
    turnkey !== "NON-TKY" &&
    (name.endsWith("_" + sbe) || name.endsWith("_" + sbe1))
  );
};

/**
 * Return true if this is an developmental BOM Template and not test.
 * Must be a development bom template, and have _SCP in the name, and NOT have TEST_ in the name
 * @param name
 * @param facility
 * @param isDevelopment
 * @function
 */
export const isDevelopmentTemplateAndNoTest = (
  name,
  facility,
  isDevelopment
) => {
  return (
    name.startsWith("VYPER") &&
    name.includes(facility) &&
    isDevelopment &&
    name.endsWith("_SCP") &&
    !name.includes("TEST_")
  );
};

/**
 * Return true if the record is _SCP, and the traveler has engineering operations.
 * Must have _SCP in the name, and have engineering operations.
 *
 * @param name
 * @param facility
 * @param hasEngineeringOperations
 * @function
 */
export const isScpAndHasEngineeringOperations = (
  name,
  facility,
  hasEngineeringOperations
) => {
  return (
    name.startsWith("VYPER") &&
    name.includes(facility) &&
    name.endsWith("_SCP") &&
    hasEngineeringOperations
  );
};

/**
 * Return true MFF flow is in the traveler operations.
 *
 * @param name
 * @param build
 * @param turnkey
 * @function
 */
export const isGroupInBuildFlow = (name, build, turnkey) => {
  // return true if we don't have a buildFlow name, or it is TKY

  const flowName = build.buildFlow?.flowName;
  if (
    flowName == null ||
    flowName === "" ||
    flowName === "TKY" ||
    turnkey === "TKY"
  ) {
    return true;
  }

  const flowFromName = name.split("_")[2];
  const facility = "_" + build.facility.object.PDBFacility + "_";

  // Retrieve in the format of ASSEMBLY => ASSY, PACK, TEST
  const flowNames = build.traveler.operations
    .map((opr) => opr.subflowType.substr(0, 3) + opr.subflowType.substr(-1, 1))
    .filter((flowName, index, arr) => arr.indexOf(flowName) === index);

  return (
    name.startsWith("VYPER") &&
    name.includes(facility) &&
    flowNames.includes(flowFromName)
  );
};
