import React from "react";
import { useHistory } from "react-router-dom";
import Link from "@material-ui/core/Link";
import { CellClickedEvent, ICellRendererParams } from "ag-grid-community";
import { TIAgEventType } from "./GridConstants";

/** @type {CSSStyleDeclaration} */
const LINK_STYLE = {
  paddingRight: "1rem",
  whiteSpace: "nowrap",
};

const DEFAULT_COLOR = "secondary";

/**
 * @typedef {object} LinkCellLink
 * @property {string} label Link display label.
 * @property {string} url Link URL.
 */

/**
 * @typedef {LinkCellLink | Array<LinkCellLink>} LinkCellLinks
 */

/**
 * @typedef {object} LinkCellRendererProps
 * @property {string} color Color of the link (e.g. primary, secondary).
 * @property {string} style style of the link .
 * @property {LinkCellLinks | (params: ICellRendererParams) => LinkCellLinks} links Cell link definitions.
 * @property {(event: CellClickedEvent, link: LinkCellLink) => void} onClick Event handler.
 */

/**
 * Get links from cell renderer parameters.
 * @param {ICellRendererParams & LinkCellRendererProps} params
 * @returns {Array<LinkCellLink>}
 */
function getLinks(params) {
  if (params.links) {
    let tempLinks = params.links;
    if (typeof tempLinks === "function") {
      tempLinks = tempLinks(params);
    }
    if (tempLinks != null) {
      return Array.isArray(tempLinks) ? tempLinks : [tempLinks];
    }
  } else {
    if (params.value != null) {
      const values = Array.isArray(params.value)
        ? params.value
        : [params.value];
      return values.map((item) =>
        typeof item === "object" ? item : { label: item, url: item }
      );
    }
  }
  return [];
}

/**
 * @param {ICellRendererParams & LinkCellRendererProps} props Component props.
 */
export const TIAgLinkCellRenderer = (props) => {
  const history = useHistory();
  const links = getLinks(props);
  const color = props.color || DEFAULT_COLOR;
  const style = props.style || LINK_STYLE;

  /**
   * @param {React.MouseEvent} event Click event.
   * @param {LinkCellLink} link Cell action index.
   */
  const handleClick = (event, link) => {
    event.preventDefault();
    event.stopPropagation();
    if (props.onClick) {
      /** @type {CellClickedEvent} */
      const actionEvent = {
        type: TIAgEventType.CELL_LINK_CLICKED,
        api: props.api,
        columnApi: props.columnApi,
        node: props.node,
        data: props.data,
        rowIndex: props.rowIndex,
        rowPinned: null,
        context: props.context,
        event: event,
        column: props.column,
        colDef: props.colDef,
        value: props.value,
      };
      props.onClick(actionEvent, link);
    } else {
      if (link.url && new RegExp("^https?://").test(link.url)) {
        window.open(link.url);
      } else {
        history.push(link.url);
      }
    }
  };

  return (
    <div>
      {links.map((link, index) => (
        <React.Fragment key={index}>
          {link.url != null && link.url !== "" ? (
            <Link
              href={link.url}
              onClick={(event) => handleClick(event, link)}
              color={color}
              style={style}
            >
              {link.label}
            </Link>
          ) : (
            <span>{link.label}</span>
          )}
          <wbr />
        </React.Fragment>
      ))}
    </div>
  );
};

export default TIAgLinkCellRenderer;
