import { Button } from "@material-ui/core";
import React, { useContext } from "react";
import { NewBuildDialogContext } from "src/component/newBuild/NewBuildDialog";
import makeStyles from "@material-ui/core/styles/makeStyles";
import PropTypes from "prop-types";

const useStyles = makeStyles({
  buttonColor: {
    backgroundColor: "hsla(160, 100%, 35%, 1)",
    color: "white",
    "&:hover": {
      backgroundColor: "hsla(160, 100%, 25%, 1)",
    },
  },
});
export function AddBuildButton({ handleAddNewBuild }) {
  const { openNewBuildDialog } = useContext(NewBuildDialogContext);
  const classes = useStyles();

  const handleClickAddBuild = () => {
    openNewBuildDialog({
      vyperNumber: undefined,
      buildNumber: undefined,
      description: undefined,
      buildType: undefined,
      symbolChoice: undefined,
      onSave: (
        mode,
        vyperNumber,
        buildNumber,
        flowData,
        description,
        buildType,
        copyBuildNumber,
        symbolChoice
      ) => {
        return handleAddNewBuild(
          mode,
          vyperNumber,
          buildNumber,
          flowData,
          description,
          buildType,
          copyBuildNumber,
          symbolChoice
        );
      },
    });
  };
  return (
    <Button
      variant="contained"
      className={classes.buttonColor}
      onClick={handleClickAddBuild}
    >
      Add Build
    </Button>
  );
}

export default AddBuildButton;

AddBuildButton.propTypes = {
  handleAddNewBuild: PropTypes.func,
};
