import makeStyles from "@material-ui/core/styles/makeStyles";
import clsx from "clsx";
import React, { useContext } from "react";
import { Droppable, Draggable } from "react-beautiful-dnd";
import DragIndicatorIcon from "@material-ui/icons/DragIndicator";
import { EngineeringIcon } from "src/component/icons/EngineeringIcon";
import { VyperLink } from "src/pages/mockup/VyperLink";
import { ApprovedText } from "src/pages/select/traveler/ApprovedText";
import { ApprovalOperationContext } from "../../../component/approvaloperation/ApprovalOperation";
import { AuthContext } from "../../../component/common";
import { SourceIcon } from "../../../component/sourceicon/SourceIcon";
import { AddRequiredButton } from "./buttons/AddRequiredButton";
import { OperationButtons } from "./buttons/OperationButtons";
import { Component } from "./Component";
import DuplicatedOperationWarning from "./DuplicatedOperationWarning";
import {
  NON_VALIDATABLE_OPERATIONS,
  ValidatedOperation,
  validateOperation,
} from "./ValidatedOperation";

const useStyles = makeStyles({
  icon: {
    fontSize: "0.75em",
    "&:hover": {
      backgroundColor: "#ccc",
    },
  },
  operationComment: {
    fontSize: "1.5rem",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
    display: "flex",
    justifyContent: "center",
    border: "1px solid red",
  },
  operationText: {
    paddingTop: "0.5em",
    paddingBottom: "0.5em",
  },
  centered: {
    display: "flex",
    whiteSpace: "nowrap",
  },
  deleted: {
    textDecoration: "line-through",
  },
  dragHandle: {
    display: "inline-flex",
    alignItems: "center",
    cursor: "grab",
    color: "#666",
    marginRight: "8px",
    marginLeft: "24px",
    "&:hover": {
      color: "#333",
    },
    "&:active": {
      cursor: "grabbing",
    },
  },
  draggableContainer: {
    display: "flex",
    alignItems: "flex-start",
  },
  componentWrapper: {
    flex: 1,
  },
  draggingComponent: {
    backgroundColor: "#f0f8ff",
    borderRadius: "4px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  },
});

export const Operation = ({
  vyper,
  build,
  options,
  operation,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  validatedOperation,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  reworkedTraveler,
  onSelectDiagramApproval,
  unApprovedGroups,
  isDragDropEnabled,
}) => {
  const { authUser } = useContext(AuthContext);

  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );

  const vo = validateOperation({
    buildState: build.state,
    buildFacility: build.facility.object.PDBFacility,
    sbe: build.material.object.SBE,
    sbe1: build.material.object.SBE1,
    operation: operation,
    authUser,
    validatedOperation,
    findApprovalOperationByOperation: findApprovalOperationByOperation,
    operations: build.traveler.operations,
    unApprovedGroups,
  });

  const classes = useStyles();

  // disable changes when the operation has been validated
  const disabled = vo.checked;

  // disable component edit changes depending on build state and owner
  const disableComponentEdit = !vo.enabled || disabled;
  const checkDisableComponent = () => {
    // Check the build state
    let buildState = build.state;

    switch (buildState) {
      case "AT_REVIEW_CHANGE":
        return disableComponentEdit;

      default:
        return disabled;
    }
  };

  const ao = findApprovalOperationByOperation(operation.name);

  // if this operation has been approved, get the approvingGroup object
  // which is { group:"", username:"", date:""}
  const approvedGroup = approvedGroups.find((group) =>
    group.group.toLowerCase().includes(ao?.groupText?.toLowerCase())
  );

  // used to track the component, and determine the priority
  const priorityMap = {};

  const showApproval = !NON_VALIDATABLE_OPERATIONS.includes(operation?.name);

  return (
    <div>
      <br />

      <OperationButtons
        disabled={disabled}
        options={options}
        operation={operation}
        onAdd={onAddOperation}
        onEdit={onEditOperation}
        onRemove={onRemoveOperation}
        onComment={onCommentOperation}
        vyper={vyper}
        build={build}
        vo={vo}
      />

      <SourceIcon
        disabled={operation.engineeringDeleted}
        source={operation.source}
        heading="The operation was added by"
      />

      <ValidatedOperation
        vo={vo}
        operation={operation}
        onClickValidate={onClickValidate}
      >
        <span
          className={clsx({ [classes.deleted]: operation.engineeringDeleted })}
        >
          <VyperLink
            canEdit={operation.engineeringDeleted}
            onClick={() => onUndoDelete(operation)}
          >
            {operation.name}
          </VyperLink>
        </span>
        {operation.engineering === "Y" ? <EngineeringIcon /> : null}
      </ValidatedOperation>

      {vo.isDuplicated && <DuplicatedOperationWarning />}

      {showApproval && <ApprovedText approvedGroup={approvedGroup} />}

      <br />

      {operation.comment == null ? null : (
        <div className={classes.operationComment}>
          <span className={classes.operationText}>{operation.comment}</span>
        </div>
      )}

      {options.editbutton && options.component ? (
        <div>
          &nbsp;&nbsp;&nbsp;
          <AddRequiredButton
            title="Add Component"
            disabled={disabled}
            onClick={() => onAddComponent(operation, null)}
          />
        </div>
      ) : null}

      {options.component && (
        <Droppable
          droppableId={`components-${operation.name}`}
          type="component"
          isDropDisabled={!isDragDropEnabled}
        >
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {operation.components.map((component, n) => {
                let priority;
                if (component.name in priorityMap) {
                  priorityMap[component.name]++;
                  priority = priorityMap[component.name];
                } else {
                  priorityMap[component.name] = 1;
                  priority = 1;
                }

                return (
                  <Draggable
                    key={`${operation.name}-${component.name}-${n}`}
                    draggableId={`component-${operation.name}-${component.name}-${n}`}
                    index={n}
                    isDragDisabled={!isDragDropEnabled}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={clsx(
                          classes.draggableContainer,
                          snapshot.isDragging && classes.draggingComponent
                        )}
                        style={provided.draggableProps.style}
                      >
                        {isDragDropEnabled && (
                          <div {...provided.dragHandleProps} className={classes.dragHandle}>
                            <DragIndicatorIcon fontSize="small" />
                          </div>
                        )}
                        <div className={classes.componentWrapper}>
                          <Component
                            key={n}
                            vyper={vyper}
                            build={build}
                            disabled={disabled}
                            operation={operation}
                            component={component}
                            options={options}
                            onEditName={(o, c) => onEditComponentName(o, c, n)}
                            onEditValue={(o, c) => onEditComponentValue(o, c, n)}
                            onAdd={(o, c) => onAddComponent(o, c, n)}
                            onRemove={(o, c) => onRemoveComponent(o, c, n)}
                            priority={priority}
                            reworkedTraveler={reworkedTraveler}
                            onSelectDiagramApproval={onSelectDiagramApproval}
                            checkDisableComponent={checkDisableComponent()}
                          />
                        </div>
                      </div>
                    )}
                  </Draggable>
                );
              })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      )}
    </div>
  );
};
