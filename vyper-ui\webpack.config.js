const webpackMerge = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react");
const path = require("path");
const { EnvironmentPlugin } = require("webpack");

module.exports = (webpackConfigEnv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "ti",
    projectName: "vyper-ui",
    webpackConfigEnv,
  });

  return webpackMerge.smart(defaultConfig, {
    module: {
      rules: [
        {
          test: /\.(png|jpe?g|gif|svg|woff(2)?|ttf)$/i,
          use: [
            {
              loader: "file-loader",
            },
          ],
        },
      ],
    },
    plugins: [new EnvironmentPlugin(["REACT_APP_ENV"])],
    devServer: {
      port: 3000,
      historyApiFallback: true,
      hot: true,
      open: true,
    },
    resolve: {
      alias: {
        src: path.resolve(__dirname, "src/"),
      },
      extensions: [".js", ".jsx", ".json", ".css"],
    },
    externals: ["react", "react-dom", "@ti/simba-common-util"],
  });
};
