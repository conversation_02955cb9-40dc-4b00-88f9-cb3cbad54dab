import { But<PERSON>, DialogActions } from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import MenuItem from "@material-ui/core/MenuItem";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TextField from "@material-ui/core/TextField";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { logError } from "../../functions/logError";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "white",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    padding: theme.spacing(1),
  },
  required: {
    color: "red",
    fontSize: "1.5rem",
  },
}));

/**
 * called when the user clicks rework on the ReworkDialog.
 * @callback ReworkDialog~onRework
 * @param {string} group - The approving group.
 * @param {string} reason - The approving group.
 * @param {string|null} comment - the rework comment, or null if no comment.
 */

/**
 * called when the user clicks cancel on the ReworkDialog.
 * @callback ReworkDialog~onClose
 */

/**
 * Display the A/T rework dialog.
 * @param {boolean} open - set true/false to show/hide the dialog
 * @param {string[]} reworkGroups - The list of rework groups
 * @param {ReworkDialog~onRework} onRework - callback called when the use clicks rework
 * @param {ReworkDialog~onClose} onClose - callback called when the user clicks cancel
 * @param buildState
 * @returns {JSX.Element}
 * @function
 */
export function ReworkDialog({
  open,
  reworkGroups,
  onRework,
  onClose,
  buildState,
}) {
  const [group, setGroup] = useState("");
  const [reason, setReason] = useState("");
  const [comment, setComment] = useState("");
  const [reasons, setReasons] = useState([]);

  const { vget } = useContext(FetchContext);
  const isStateBuReview = buildState === "BU_REVIEW_CHANGE";

  // reset the form when the dialog opens
  useEffect(() => {
    if (!open) {
      return;
    }

    // setting default value since we are removing group dropdown when state is in bu review
    if (isStateBuReview) {
      setGroup("BU_APPROVERS");
    } else {
      setGroup("");
    }

    setReason("");
    setComment("");
    setReasons([]);
  }, [open]);

  /**
   * When the group changes, fetch the list of reasons.
   */
  useEffect(() => {
    if (group == null) {
      return;
    }

    // determine the reject_reason's group text
    let groupText;
    if (group.endsWith("_PREBOND")) {
      groupText = "ASSY_PREBOND";
    } else if (group.endsWith("_BOND")) {
      groupText = "ASSY_BOND";
    } else if (group.endsWith("_FINISH")) {
      groupText = "ASSY_FINISH";
    } else if (group.includes("_TEST_")) {
      groupText = "_TEST_";
    } else if (group.endsWith("_PACK")) {
      groupText = "ASSY_PACK";
    } else if (group.endsWith("_SCP")) {
      groupText = "ASSY_SCP";
    } else if (group === "BU_APPROVERS") {
      groupText = "BU_APPROVERS";
    } else {
      groupText = null;
    }

    // get the reasons
    if (groupText != null) {
      vget(`/vyper/v1/vyper/searchReasons?grp=${groupText}`, setReasons).catch(
        logError
      );
    }
  }, [group]);

  const handleChangeGroup = (e) => setGroup(e.target.value);
  const handleChangeReason = (e) => setReason(e.target.value);
  const handleChangeComment = (e) => {
    // comment will become reason when state is in bu review since we removed reason dropdowns
    if (isStateBuReview) {
      setReason(e.target.value);
    }

    setComment(e.target.value);
  };
  const handleClickRework = () => {
    // comment will become reason when state is in bu review
    onRework(group, reason, isStateBuReview ? "" : comment || null);
  };

  // enable the save button when the group is set

  const canChooseReason = group !== "";
  const canSave = isStateBuReview
    ? reason !== ""
    : group !== "" && reason !== "";
  const isCommentRequired = isStateBuReview ? "Required" : "Optional";

  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className={classes.title}>Action: Rework</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        {isStateBuReview ? undefined : (
          <div>
            <div>
              <span className={classes.required}>*</span>
              <span>Approval group:</span>
            </div>

            <TextField
              autoFocus
              fullWidth
              select
              margin="dense"
              name="group"
              value={group}
              onChange={handleChangeGroup}
              variant="outlined"
            >
              {reworkGroups.map((g) => (
                <MenuItem key={g} value={g}>
                  {g}
                </MenuItem>
              ))}
            </TextField>

            <div>
              <span className={classes.required}>*</span>
              <span>Pick a reason:</span>
            </div>

            <TextField
              autoFocus
              fullWidth
              select
              margin="dense"
              name="reason"
              value={reason}
              onChange={handleChangeReason}
              variant="outlined"
              disabled={!canChooseReason}
            >
              {reasons.map((r) => (
                <MenuItem key={r} value={r}>
                  {r}
                </MenuItem>
              ))}
            </TextField>
          </div>
        )}

        <div>
          <span>({isCommentRequired}) Type in a reason for Rework:</span>
        </div>

        <TextField
          fullWidth
          multiline
          minRows={5}
          margin="dense"
          name="comment"
          value={comment}
          onChange={handleChangeComment}
          variant="outlined"
        />
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="primary" onClick={onClose}>
          Cancel
        </Button>

        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={!canSave}
          onClick={handleClickRework}
        >
          Rework
        </Button>
      </DialogActions>
    </Dialog>
  );
}

ReworkDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  reworkGroups: PropTypes.array.isRequired,
  onRework: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};
