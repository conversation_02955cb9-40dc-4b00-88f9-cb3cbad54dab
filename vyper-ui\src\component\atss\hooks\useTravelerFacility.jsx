import { useEffect, useState } from "react";
import axios from "axios";

export const useTravelerFacility = (specDevice) => {
  const [options, setOptions] = useState([]);

  useEffect(() => {
    const controller = new AbortController();

    axios
      .get(`/vyper/v1/atss/autocomplete/facility?specDevice=${specDevice}`, {
        signal: controller.signal,
      })
      .then((response) => response.data)
      .then((json) => {
        setOptions(json);
      })
      .catch(console.log);

    return () => controller.abort();
  }, [specDevice]);

  return { options };
};
