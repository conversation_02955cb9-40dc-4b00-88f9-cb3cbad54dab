import { InputAdornment } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TextField from "@material-ui/core/TextField";
import Autocomplete, {
  createFilterOptions,
} from "@material-ui/lab/Autocomplete";
import { produce } from "immer";
import React, { useContext, useState } from "react";
import { existsInFlow } from "../../pages/vyper/FormStatus";
import { FetchContext } from "../fetch/VyperFetch";

export const DieCell = ({
  facility,
  die,
  rowIndex,
  colIndex,
  onChangeDie,
  build,
}) => {
  const style = makeStyles((theme) => ({
    root: {},
    nowrap: {},
    TextField: {
      minWidth: "15rem",
    },
    withoutLabel: {
      marginTop: theme.spacing(3),
    },
    thickness: {
      minWidth: 90,
    },
  }));

  const { vget } = useContext(FetchContext);

  const [options, setOptions] = useState([die]);

  const handleAutocompleteChange = (e) => {
    vget(`/vyper/v1/autocomplete/die?search=${e.target.value}`, (json) => {
      setOptions(json.map((name) => ({ name: name })));
    });
  };

  const handleChangeAnswer = (e, value, reason) => {
    if (reason === "clear") {
      onChangeDie(rowIndex, colIndex, {
        incomingWaferThick: 0,
      });
    } else {
      // get the attributes
      onChangeDie(rowIndex, colIndex, {});
      vget(
        `/vyper/v1/vyper/attributes?facilityAt=${facility}&name=Die&values=${value.name}`,
        (json) => {
          const newDie = produce(value, (draft) => {
            //set the die name
            draft.object = { name: value.name };
            // add the attributes to the value object.
            json.forEach((item) => (draft.object[item.key] = item.value));
          });
          onChangeDie(rowIndex, colIndex, newDie);
        }
      );
    }
  };

  const handleChangeThick = (e) => {
    const value = parseInt(e.target.value, 10);
    if (isNaN(value)) {
      return;
    }

    onChangeDie(
      rowIndex,
      colIndex,
      produce(die, (draft) => {
        draft.incomingWaferThick = value;
      })
    );
  };
  const isInvalid = () => {
    return die.incomingWaferThick < 100 || die.incomingWaferThick > 1000;
  };
  const filterOptions = createFilterOptions({
    trim: true,
  });

  const classes = style();

  return (
    <>
      <TableCell className={classes.nowrap}>
        <Autocomplete
          autoComplete={true}
          renderInput={(params) => (
            <TextField
              {...params}
              className={classes.TextField}
              variant="outlined"
              fullWidth
              onChange={handleAutocompleteChange}
              label="Select die name"
            />
          )}
          options={[{}, ...options]}
          onChange={handleChangeAnswer}
          value={die || {}}
          getOptionLabel={(option) => option.name || ""}
          getOptionSelected={(option, value) => option.name === value.name}
          filterOptions={filterOptions}
        />
      </TableCell>

      {existsInFlow(build, "Backgrind") && (
        <TableCell className={classes.nowrap}>
          <TextField
            className={classes.withoutLabel + " " + classes.thickness}
            type="string"
            variant="outlined"
            fullWidth
            value={die.incomingWaferThick || ""}
            InputLabelProps={{ shrink: true }}
            onChange={handleChangeThick}
            helperText={"between 100 and 1000"}
            error={isInvalid()}
            InputProps={{
              endAdornment: <InputAdornment position="end">Um</InputAdornment>,
            }}
          />
        </TableCell>
      )}
    </>
  );
};
