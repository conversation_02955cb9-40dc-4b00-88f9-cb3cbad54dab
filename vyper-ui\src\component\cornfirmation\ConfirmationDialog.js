import React, { useState } from "react";
import DialogActions from "@material-ui/core/DialogActions";
import { Button, Dialog, DialogContent, DialogTitle } from "@material-ui/core";
import DialogContentText from "@material-ui/core/DialogContentText";
import makeStyles from "@material-ui/core/styles/makeStyles";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
}));

export const ConfirmationDialog = ({ children }) => {
  const [yes, setYes] = useState();
  const [no, setNo] = useState();
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [yesText, setYesText] = useState("");
  const [noText, setNoText] = useState("");
  const [open, setOpen] = useState(false);
  const [yesProps, setYesProps] = useState({});
  const [noProps, setNoProps] = useState({});

  const handleOpen = ({
    title = "Confirmation Dialog",
    message = "Are you sure?",
    yesText = "Yes",
    noText = "No",
    yesProps = { variant: "contained", color: "primary" },
    noProps = { variant: "outlined", color: "primary" },
    onYes = () => {},
    onNo = () => {},
  }) => {
    setTitle(title);
    setMessage(message);
    setYesText(yesText);
    setNoText(noText);
    setYesProps(yesProps);
    setNoProps(noProps);
    setYes(() => onYes);
    setNo(() => onNo);
    setOpen(true);
  };

  const classes = useStyles();

  const handleClose = () => {
    setOpen(false);
  };

  const handleYes = () => {
    yes();
    handleClose();
  };

  const handleNo = () => {
    no();
    handleClose();
  };

  return (
    <ConfirmationDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="xl">
        <DialogTitle className={classes.title}>{title}</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>{message}</DialogContentText>
        </DialogContent>

        <DialogActions>
          <Button {...noProps} onClick={handleNo}>
            {noText}
          </Button>
          <Button {...yesProps} onClick={handleYes}>
            {yesText}
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </ConfirmationDialogContext.Provider>
  );
};

export const ConfirmationDialogContext = React.createContext(null);
