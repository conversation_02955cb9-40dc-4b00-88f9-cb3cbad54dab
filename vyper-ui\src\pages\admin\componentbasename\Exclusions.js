import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import DataGrid from "../../../component/universal/DataGrid/DataGrid";
import {
  gridActionDeleteRecord,
  gridActionEditRecord,
} from "src/component/universal";
import AddBoxIcon from "@material-ui/icons/AddBox";
import { QuestionDialogContext } from "src/component/question/QuestionDialog";
import { noop } from "src/component/vyper/noop";
import { BaseNameExclusionDao } from "src/dao/BaseNameExclusionDao";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";

const useStyles = makeStyles({
  root: {},
});

export const Exclusions = ({}) => {
  const { showQuestionDialog } = useContext(QuestionDialogContext);
  const { open: showConfirm } = useContext(ConfirmationDialogContext);

  const [data, setData] = useState([]);

  const dao = new BaseNameExclusionDao();

  useEffect(() => {
    refresh().catch(noop);
  }, []);

  const refresh = () => {
    return dao.list().then((json) => setData(json));
  };

  const columns = [{ field: "baseName", title: "Base Name" }];

  const handleAdd = () => {
    showQuestionDialog({
      type: "text",
      title: "Add Base Name",
      description: "Enter the new base name to exclude from grouping.",
      onSave: (answer) => {
        dao.create(answer).then(() => refresh());
      },
    });
  };

  const handleEdit = (event, row) => {
    showQuestionDialog({
      type: "text",
      title: "Edit Base Name",
      description: "Update the base name.",
      value: row.baseName,
      onSave: (answer) => {
        dao.update(row.baseName, answer).then(() => refresh());
      },
    });
  };

  const handleDelete = (event, row) => {
    showConfirm({
      title: "Confirmation Dialog",
      message: `Do you want to delete the base name exclusion: ${row.baseName}?`,
      onYes: () => {
        dao.deleteByBaseName(row.baseName).then(() => refresh());
      },
    });
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <p>
        Note: Exclusions are base names that should not be grouped together when
        showing the user a list of component values. These are stored in the
        component_base_name_no_reduce table.
      </p>

      <DataGrid
        title="Exclusions"
        data={data}
        columns={columns}
        actions={[
          gridActionEditRecord(handleEdit),
          gridActionDeleteRecord(handleDelete),
          {
            icon: () => <AddBoxIcon />,
            isFreeAction: true,
            onClick: handleAdd,
          },
        ]}
      />
    </div>
  );
};
