import React, { useContext, useEffect, useState } from "react";
import { useComponentMap } from "src/api/useComponentMap";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";

export const ComponentMap = ({ children }) => {
  const { list } = useComponentMap();
  const { open: openError } = useContext(ErrorDialogContext);

  const [componentMaps, setComponentMaps] = useState([]);

  // load the component maps at mount
  useEffect(() => {
    handleRefresh();
  }, []);

  // refresh the component maps
  const handleRefresh = () => {
    list()
      .then(setComponentMaps)
      .catch((e) => openError({ title: "Fetch Component Maps", error: e }));
  };

  // find a component map by name
  const handleFindByName = (name) =>
    componentMaps.find((cm) => cm.name === name);

  return (
    <ComponentMapContext.Provider
      value={{
        componentMaps: componentMaps,
        findComponentMapByName: handleFindByName,
        refreshComponentMaps: handleRefresh,
      }}
    >
      <div>{children}</div>
    </ComponentMapContext.Provider>
  );
};

export const ComponentMapContext = React.createContext(null);
