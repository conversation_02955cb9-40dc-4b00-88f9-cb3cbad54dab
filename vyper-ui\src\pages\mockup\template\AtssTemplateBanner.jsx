import React from "react";
import PropTypes from "prop-types";
import { Alert, AlertTitle } from "@material-ui/lab";

/**
 * Show a banner for ATSS Templates.
 *
 * @param {object} build - The build object
 * @return {JSX.Element}
 * @constructor
 */
export const AtssTemplateBanner = ({ build }) => {
  const material = build.templateSource.atssMaterial;
  const facility = build.templateSource.atssFacility;
  const status = build.templateSource.atssStatus;

  return (
    <Alert severity="success">
      <AlertTitle>
        The flow for this build is based on the ATSS spec:
        <strong>
          {facility} / {material} / {status}
        </strong>
        .
      </AlertTitle>
    </Alert>
  );
};

AtssTemplateBanner.propTypes = {
  build: PropTypes.object.isRequired,
};
