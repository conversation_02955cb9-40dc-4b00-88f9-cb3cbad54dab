import IconButton from "@material-ui/core/IconButton";
import { makeStyles } from "@material-ui/core/styles";
import Tooltip from "@material-ui/core/Tooltip";
import HelpIcon from "@material-ui/icons/Help";
import HelpOutlineIcon from "@material-ui/icons/HelpOutline";
import Alert from "@material-ui/lab/Alert";
import React, { useState } from "react";

const useStyles = makeStyles(() => ({
  // Help Icon CSS
  helpIcon: {
    marginLeft: "1rem",
  },
}));

/**
 *
 * @param {object} children
 * @param {boolean} children.show - Show instructions by default
 * @param {string} children.title - Title of the Instructions
 * @param {Array<string>} children.instructions - The array of instructions to show in order
 */
const Instructions = ({ show, title, instructions }) => {
  const classes = useStyles();

  const [visible, setVisible] = useState(show);
  const toggleVisibility = () => setVisible((v) => !v);

  return (
    <div>
      <hr />

      <Tooltip title="Show/Hide Instructions">
        <IconButton
          className={classes.helpIcon}
          aria-label="Show/Hide"
          onClick={toggleVisibility}
        >
          {/* controls which icon will appear when instruction visibility changes */}
          {visible ? <HelpOutlineIcon /> : <HelpIcon />}
        </IconButton>
      </Tooltip>

      {visible && (
        <Alert color="success">
          <h4>{title}</h4>
          <ol>
            {instructions.map((item, n) => (
              <li key={n}>{item}</li>
            ))}
          </ol>
        </Alert>
      )}
    </div>
  );
};

export default Instructions;
