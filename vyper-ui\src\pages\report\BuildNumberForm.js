import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import { Button, TextField } from "@material-ui/core";
import React, { useEffect } from "react";

export const BuildNumberForm = ({ onSubmit }) => {
  // handle the build number form
  const [buildNumber, setBuildNumber] = useLocalStorage(
    "build_number_form.build_number",
    ""
  );
  const handleBuildNumberChange = (e) => setBuildNumber(e.target.value);

  // if there is a build number in the query params, use it
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const buildNumber = params.get("buildNumber");
    if (buildNumber != null) {
      setBuildNumber(buildNumber);
      onSubmit(buildNumber);
    }
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (buildNumber != null) onSubmit(buildNumber);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <TextField
          label="Enter the build number."
          variant="outlined"
          value={buildNumber}
          onChange={handleBuildNumberChange}
        />
      </div>
      <div>
        <Button type="submit" color="primary" variant="contained">
          Run
        </Button>
      </div>
    </form>
  );
};
