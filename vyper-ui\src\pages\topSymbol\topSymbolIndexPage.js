import React from "react";
import { HorizontalTabs } from "../../component/tab/HorizontalTabs";
import { TopSymbol } from "./TopSymbol/TopSymbol";
import { CustMaxChar } from "./CustMaxChar/CustMaxChar";

export const TopsideIndexPage = () => {
  const tabs = [
    {
      label: "Topside Symbol",
      control: <TopSymbol />,
    },

    { label: "Cust Max Characters", control: <CustMaxChar /> },
  ];

  return (
    <div>
      <HorizontalTabs key="topside.tab" tabs={tabs} />
    </div>
  );
};
