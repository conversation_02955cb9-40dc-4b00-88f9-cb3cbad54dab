import moment from "moment";
import PropTypes from "prop-types";
import React from "react";
import { TravelerHeaderRow } from "./TravelerHeaderRow";

/**
 * Returns the traveler header
 * @param {TravelerHeader} header - the travelers header
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerHeaders({ header }) {
  return (
    <>
      <TravelerHeaderRow
        title1="Current Date / Time"
        value1={moment(header.currentDateTime).format("MM/DD/YYYY, h:mm:ss A")}
        title2="Pin"
        value2={header.pin}
      />
      <br />
      <TravelerHeaderRow
        title1="Old Material"
        value1={header.oldMaterial}
        title2="PKG"
        value2={header.pkg}
      />
      <br />
      <TravelerHeaderRow
        title1="Spec Device"
        value1={header.specDevice}
        title2="Package Group"
        value2={header.packageGroup}
      />
      <br />
      <TravelerHeaderRow
        title1="A/T"
        value1={header.facility}
        title2="SPQ"
        value2={header.spq}
      />
      <br />
      <TravelerHeaderRow
        title1="Revision"
        value1={header.revision}
        title2="MOQ"
        value2={header.moq}
      />
      <br />
      <TravelerHeaderRow
        title1="SBE"
        value1={header.sbe}
        title2="Industry Sector"
        value2={header.industrySector}
      />
      <br />
      <TravelerHeaderRow
        title1="SBE-1"
        value1={header.sbe1}
        title2="Customer"
        value2={header.customer}
      />
      <br />
      <TravelerHeaderRow
        title1="SBE-2"
        value1={header.sbe2}
        title2="Cust Part Name"
        value2={header.custPartName}
      />
      <br />
      <TravelerHeaderRow
        title1="SAP Material"
        value1={header.sapMaterial}
        title2="Cust Print Name"
        value2={header.custPrintName}
      />
      <br />
      <TravelerHeaderRow
        title1="SAP Base Material"
        value1={header.sapBaseName}
        title2="Cust Print Revision"
        value2={header.custPrintRevision}
      />
      <br />
      <TravelerHeaderRow
        title1="Niche"
        value1={header.niche}
        title2="Type 4"
        value2={header.type4}
      />
      <br />
      <TravelerHeaderRow 
       title1="Created Date / Time"
       value1={moment(header?.createdDateTime).format("MM/DD/YYYY, h:mm:ss A")}
       title2="PRESP" 
       value2={header.presp} />
      <br />
    </>
  );
}

TravelerHeaders.propTypes = {
  header: PropTypes.object.isRequired,
};
