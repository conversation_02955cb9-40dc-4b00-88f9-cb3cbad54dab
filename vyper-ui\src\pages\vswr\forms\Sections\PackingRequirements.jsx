import React, { useMemo, useState, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import TextField from "@material-ui/core/TextField";
import { FormControl, InputLabel, Select, MenuItem } from "@material-ui/core";
import {
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@material-ui/core";
import { BASE_FETCH_OPTIONS_URL } from "../FormConstants";

const stickerTypeOptions = [
  "SWR",
  "None-Waived (QA waiver required before shipment - add e-waiver number)",
  "None-PDC stocking for new APLs /RTMs only (will ship without sticker as PW01)",
];

const fetchData = (uri, setFormState, defaultValue) => {
  fetch(`${BASE_FETCH_OPTIONS_URL}${uri}`)
    .then((response) => response.json())
    .then(setFormState)
    .catch(() => {
      setFormState(defaultValue);
    });
};

const PackingRequirements = (props) => {
  const { classes, formState, setFormState, readOnly = false } = props;
  // const [stickerTypeOptions, setStickerTypeOptions] = useState([]);
  const [finishedGoodsDispoOptions, setFinishedGoodsDispoOptions] = useState(
    []
  );
  const [waferSkeletonOptions, setWaferSkeletonOptions] = useState([]);

  useEffect(() => {
    // fetchData('/', setStickerTypeOptions, []);
    fetchData("/finishedGoodOptions", setFinishedGoodsDispoOptions, []);
    fetchData("/waferSkeletonOptions", setWaferSkeletonOptions, []);
  }, []);

  const handleChange = (evt) => {
    if (readOnly) {
      return;
    }
    const { name, value } = evt.target;
    if (value !== undefined) {
      setFormState((prevState) => {
        return {
          ...prevState,
          [name]: value,
        };
      });
    }
  };

  const defaultFieldProps = useMemo(() => {
    return {
      InputLabelProps: {
        className: classes.textField,
      },
      disabled: readOnly,
      style: readOnly ? { background: "#DCDCDC" } : {},
      required: true,
      variant: "outlined",
      color: "secondary",
      onChange: handleChange,
    };
  }, []);

  return (
    <Paper elevation={24} className={classes.paper}>
      <Typography variant="h6">Packing Requirements</Typography>
      <Grid>
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6}>
            <FormControl
              required
              fullWidth
              error={!formState.stickerType}
              color="secondary"
              variant="outlined"
            >
              <InputLabel className={classes.textField}>
                Sticker Type
              </InputLabel>
              <Select
                {...defaultFieldProps}
                name={"stickerType"}
                onChange={handleChange}
                value={formState.stickerType}
              >
                {stickerTypeOptions.map((item, i) => (
                  <MenuItem key={i} value={item}>
                    {item}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              error={
                stickerTypeOptions[1] === formState.stickerType &&
                !formState.eWaiver
              }
              value={formState.eWaiver}
              disabled={
                readOnly || stickerTypeOptions[1] !== formState.stickerType
              }
              style={{
                background:
                  readOnly || stickerTypeOptions[1] !== formState.stickerType
                    ? "#DCDCDC"
                    : "#FFFFFF",
              }}
              name={"eWaiver"}
              label="e-Waiver Number"
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6}>
            <FormControl
              required
              fullWidth
              error={!formState.finishedGoodsDispo}
              color="secondary"
              variant="outlined"
            >
              <InputLabel className={classes.textField}>
                Disposition of Partial Finished Goods
              </InputLabel>
              <Select
                {...defaultFieldProps}
                name={"finishedGoodsDispo"}
                onChange={handleChange}
                value={formState.finishedGoodsDispo}
              >
                {finishedGoodsDispoOptions.map((item, i) => (
                  <MenuItem key={i} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <FormControl
              required
              fullWidth
              error={!formState.waferSkeleton}
              color="secondary"
              variant="outlined"
            >
              <InputLabel className={classes.textField}>
                Disposition of wafer skeletons (unpicked die)
              </InputLabel>
              <Select
                {...defaultFieldProps}
                name={"waferSkeleton"}
                onChange={handleChange}
                value={formState.waferSkeleton}
              >
                {waferSkeletonOptions.map((item, i) => (
                  <MenuItem key={i} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              error={
                "RETURN" === formState.waferSkeleton && !formState.plantCode
              }
              value={formState.plantCode}
              disabled={readOnly || "RETURN" !== formState.waferSkeleton}
              style={{
                background:
                  readOnly || "RETURN" !== formState.waferSkeleton
                    ? "#DCDCDC"
                    : "#FFFFFF",
              }}
              name={"plantCode"}
              label="Plant Code"
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <FormControl color={"secondary"} component="fieldset">
              <FormLabel component="legend">
                Is this RMR/Retest Material?
              </FormLabel>
              <RadioGroup
                row
                name="isRetestRMR"
                value={formState.isRetestRMR}
                onChange={handleChange}
              >
                <FormControlLabel
                  value={"no"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="No"
                />
                <FormControlLabel
                  value={"yes"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="Yes"
                />
              </RadioGroup>
            </FormControl>
          </Grid>
          <Grid item xs>
            <FormControl color={"secondary"} component="fieldset">
              <FormLabel component="legend">
                Will these units be stocked in PDC for unrestricted sale to
                customer?
              </FormLabel>
              <RadioGroup
                row
                name="pdcUnrestrictedSale"
                value={formState.pdcUnrestrictedSale}
                onChange={handleChange}
              >
                <FormControlLabel
                  value={"no"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="No"
                />
                <FormControlLabel
                  value={"yes"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="Yes"
                />
              </RadioGroup>
            </FormControl>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};
export default PackingRequirements;
