import React, { useEffect } from "react";
import PropTypes from "prop-types";
import MaterialTable from "material-table";
import Refresh from "@material-ui/icons/Refresh";
import { default as tableIcons } from "./icons";
import { tableConfig, styleOptions } from "./defaults";
import { refreshData } from "./pageable";

const ServerDataGrid = ({
  actions = [],
  columns = [],
  detailPanel = [],
  handleSelect,
  pageable = false,
  selectable = false,
  timestamp = null,
  title = "",
  url,
  maxBodyHeight,
  pageSize,
  pageSizeOptions,
  paging,
  data = (query) =>
    new Promise((resolve, reject) => {
      if (JSON.stringify(paging) !== undefined && !paging) {
        query.page = 0;
        query.pageSize = 10000;
      }
      refreshData(url, query, resolve, reject, pageable);
    }),
}) => {
  const tableRef = React.createRef();
  const refreshAction = [
    {
      icon: () => <Refresh />,
      tooltip: "Refresh Data",
      isFreeAction: true,
      onClick: () => tableRef.current && tableRef.current.onQueryChange(),
    },
  ];

  useEffect(() => {
    tableRef.current && tableRef.current.onQueryChange();
  }, [url, timestamp]);

  return (
    <MaterialTable
      minRows={tableConfig.minRows}
      actions={[...refreshAction, ...actions]}
      columns={columns}
      data={data}
      detailPanel={detailPanel}
      icons={tableIcons}
      onSelectionChange={selectable ? (rows) => handleSelect(rows) : null}
      options={{
        search: false,
        ...styleOptions,
        ...{ selection: selectable },
        ...{ filtering: tableConfig.filtering },
        ...{ paging: false },
        ...{ pageSize: pageSize ? pageSize : tableConfig.defaultPageSize },
        ...{
          pageSizeOptions: pageSizeOptions
            ? pageSizeOptions
            : tableConfig.defaultPageSizeOptions,
        },
        ...{
          paging:
            JSON.stringify(paging) !== undefined
              ? paging
              : tableConfig.defaultPaging,
        },
        //TO DO: customStyles object
        maxBodyHeight: maxBodyHeight
          ? maxBodyHeight
          : JSON.stringify(paging) !== undefined && !paging
          ? undefined
          : "600px",
      }}
      tableRef={tableRef}
      title={title}
    />
  );
};

ServerDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  detailPanel: PropTypes.any,
  handleSelect: PropTypes.func,
  pageable: PropTypes.bool,
  selectable: PropTypes.bool,
  timestamp: PropTypes.string,
  title: PropTypes.any,
  url: PropTypes.string.isRequired,
};

export default ServerDataGrid;
