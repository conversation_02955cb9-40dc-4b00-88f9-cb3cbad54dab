import React, { useContext, useEffect, useState } from "react";
import Button from "@material-ui/core/Button";
import {
  bulkUpdateTaskFunction,
  fetchAssignments,
  fetchGroups,
  refreshTaskAssignments,
} from "src/component/api/taskService2";
import Autocomplete from "@material-ui/lab/Autocomplete";
import CircularProgress from "@material-ui/core/CircularProgress";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@material-ui/core";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { logError } from "src/pages/functions/logError";

const getUniqueTaskUuids = (groupsTasks) => {
  let taskUuids = new Set();
  groupsTasks.forEach((groupInfo) => {
    groupInfo.forEach((taskInfo) => {
      const { taskUuid, stateName, current, complete } = taskInfo;
      if (current && !complete && stateName !== "final approved") {
        taskUuids.add(taskUuid);
      }
    });
  });
  return [...taskUuids];
};

const bulkUpdate = (uniqueTasks) => {
  return refreshTaskAssignments(uniqueTasks);
};

/**
 * @typedef {object} BulkUpdateItemStatus
 * @property {string} taskUuid
 * @property {boolean} success
 * @property {string} error
 */

/**
 * @param {object} props
 * @param {boolean} props.extGroup
 * @param {(groupName: string) => Array<string>} props.getGroupUserIds
 * @param {Array<string>} props.groupNames
 * @param {CSSStyleDeclaration} props.style
 * @param {string} props.title
 */
const UpdateTaskGroupsButton = (props) => {
  const {
    extGroup = false,
    getGroupUserIds,
    groupNames = [],
    style = {},
    title = "Update Tasks",
  } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [groups, setGroups] = useState(groupNames);
  const [selected, setSelected] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const { open: openAlert } = useContext(AlertDialogContext);

  useEffect(() => {
    if (groupNames.length === 0) {
      fetchGroups().then(setGroups).catch(logError);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * @param {Array<string>} uniqueTasks
   */
  const bulkUpdateExt = (uniqueTasks) => {
    if (uniqueTasks.length) {
      const fnctName = selected[0];
      const userIdList = getGroupUserIds(fnctName);
      return bulkUpdateTaskFunction(uniqueTasks, fnctName, userIdList);
    } else {
      // No tasks to update, return dummy response
      return Promise.resolve(null);
    }
  };

  const onUpdateComplete = (data) => {
    let alertStatus = {
      title: "Success",
      message: (
        <pre>{`Tasks with the following groups have been updated \n ${selected.join(
          "\n"
        )}`}</pre>
      ),
    };

    openAlert(alertStatus);
    setIsDisabled(false);
  };

  /**
   * @param {object} data
   */
  const onUpdateCompleteExt = (data) => {
    let alertStatus = {};
    const itemStatuses = data.value;
    const allCount = itemStatuses.length;
    const successCount = itemStatuses.filter((item) => item.success).length;
    if (successCount !== allCount) {
      alertStatus = {
        title: "Partial Success",
        message: (
          <pre>{`${successCount} of ${allCount} tasks for the group ${groupName} have been updated`}</pre>
        ),
      };
    } else {
      alertStatus = {
        title: "Success",
        message: (
          <pre>{`Tasks for the group ${groupName} have been updated`}</pre>
        ),
      };
    }
    openAlert(alertStatus);
    setIsDisabled(false);
  };

  const handleOpen = () => {
    setSelected([]);
    setIsOpen(true);
  };
  const handleClose = () => {
    setIsOpen(false);
  };

  const handleSubmit = () => {
    if (extGroup) {
      const group = selected[0];
      const groupsTasksProms = [
        fetchAssignments(`&fnctName=${group}`).then((json) => json.value),
      ];
      Promise.all(groupsTasksProms)
        .then(getUniqueTaskUuids)
        .then(bulkUpdateExt)
        .then(onUpdateCompleteExt)
        .catch(logError);
      handleClose();
    } else {
      setIsDisabled(true);
      const groupsTasksProms = selected.map((group) => {
        return fetchAssignments(`&fnctName=${group}`).then(
          (json) => json.value
        );
      });
      Promise.all(groupsTasksProms)
        .then(getUniqueTaskUuids)
        .then(bulkUpdate)
        .then(onUpdateComplete)
        .catch(logError);
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={isOpen} onClose={handleClose} fullWidth maxWidth="sm">
        <DialogTitle>{title}</DialogTitle>

        <DialogContent>
          {extGroup && <div>Please select only 1 group:</div>}
          <Autocomplete
            multiple
            options={groups}
            value={selected}
            onChange={(evt, value) => setSelected(value)}
            getOptionLabel={(option) => option}
            renderInput={(params) => (
              <TextField {...params} label="Groups" variant="outlined" />
            )}
          />
        </DialogContent>

        <DialogActions>
          <Button
            disabled={
              (extGroup && selected.length != 1) ||
              (!extGroup && !selected.length)
            }
            variant="contained"
            color="secondary"
            onClick={handleSubmit}
          >
            Submit
          </Button>
          <Button variant="contained" color="primary" onClick={handleClose}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      <Button
        endIcon={isDisabled && <CircularProgress color="inherit" />}
        disabled={isDisabled}
        variant="contained"
        color="secondary"
        style={style}
        onClick={handleOpen}
      >
        Update tasks
      </Button>
    </>
  );
};
export default UpdateTaskGroupsButton;
