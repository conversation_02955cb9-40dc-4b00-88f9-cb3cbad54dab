import PropTypes from "prop-types";
import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";

/**
 * Displays a cell for the reference row.
 *
 * @param {*} item - The item
 * @param {*} as - The component to use to display the data
 * @return {JSX.Element}
 * @constructor
 */
export function ReferenceCell2({ item, as: Component }) {
  return (
    <DataCell source="VYPER">
      <Component item={item} />
    </DataCell>
  );
}

ReferenceCell2.propTypes = {
  item: PropTypes.any.isRequired,
  as: PropTypes.any.isRequired,
};
