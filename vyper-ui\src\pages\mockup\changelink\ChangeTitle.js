import TextField from "@material-ui/core/TextField";
import React, { useEffect, useState } from "react";

export const ChangeTitle = ({ changeNumber, disabled }) => {
  if (changeNumber == null || changeNumber == undefined) return null;

  const [changeTitle, setChangeTitle] = useState();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (
      changeNumber == null ||
      changeNumber === "" ||
      changeNumber == undefined
    ) {
      return;
    }
    setLoading(true);
    fetch(
      `/vyper/v1/changelink/change/findByChangeNumber?changeNumber=${changeNumber}`
    )
      .then((response) => {
        response.json().then((json) => setChangeTitle(json?.changeTitle));
      })
      .catch((e) => console.log(e))
      .finally(() => setLoading(false));
  }, [changeNumber]);

  return (
    <div>
      <TextField
        disabled={disabled}
        variant="outlined"
        fullWidth
        inputProps={{ readOnly: true }}
        label="Change Title"
        value={changeTitle || ""}
        style={{ minWidth: "20em" }}
      ></TextField>
    </div>
  );
};
