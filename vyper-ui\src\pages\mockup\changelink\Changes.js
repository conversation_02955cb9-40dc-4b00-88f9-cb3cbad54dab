import React from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { VyperLink } from "src/pages/mockup/VyperLink.js";

export const Changes = ({ changes, onClick, canEdit }) => {
  return (
    <>
      {changes.map((change, n) => (
        <DataCell key={n} source={change?.source}>
          <VyperLink onClick={onClick} canEdit={canEdit}>
            {change.object?.changeNumber || "click to select"}
          </VyperLink>
        </DataCell>
      ))}
    </>
  );
};
