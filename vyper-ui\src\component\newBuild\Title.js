import PropTypes from "prop-types";
import React from "react";

/**
 * Displays the title for the new build dialog.
 *
 * @param string buildNumber The build number
 * @returns {JSX.Element}
 * @constructor
 */
export const Title = ({ buildNumber }) => {
  return (
    <>
      {buildNumber == null ? "New Build Dialog" : `Update Build ${buildNumber}`}
    </>
  );
};

Title.propTypes = {
  buildNumber: PropTypes.string,
};
