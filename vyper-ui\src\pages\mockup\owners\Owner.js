import { Chip } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import PersonIcon from "@material-ui/icons/Person";
import PropTypes from "prop-types";
import React from "react";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles((theme) => ({
  root: {
    display: "inline",
    marginRight: theme.spacing(2),
  },
}));

/**
 * Display a chip for the owner
 *
 * @param owner
 * @param onRemove
 * @returns {JSX.Element}
 * @constructor
 */
export const Owner = ({ owner, onRemove }) => {
  const classes = useStyles();

  const label = externalUse
    ? `${owner.username}`
    : `${owner.username} / ${owner.userid}`;

  return (
    <div className={classes.root}>
      <Chip
        variant="outlined"
        color="primary"
        size="small"
        icon={<PersonIcon />}
        label={label}
        onDelete={() => onRemove(owner)}
      />
    </div>
  );
};

Owner.propTypes = {
  owner: PropTypes.object.isRequired,
  onRemove: PropTypes.func.isRequired,
};
