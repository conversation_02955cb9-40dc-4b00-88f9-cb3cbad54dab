import TableBody from "@material-ui/core/TableBody";
import { DieRow } from "./DieRow";
import React from "react";

export const DieRows = ({
  dieTypes,
  facility,
  instances,
  numCols,
  onChangeDie,
  onAddPriority,
  onRemoveDie,
  onChangeType,
  build,
}) => {
  return (
    <TableBody>
      {instances.map((instance, n) => (
        <DieRow
          facility={facility}
          key={n}
          rowIndex={n}
          instance={instance}
          numCols={numCols}
          onChangeDie={onChangeDie}
          onAddPriority={onAddPriority}
          onRemoveDie={onRemoveDie}
          onChangeType={onChangeType}
          build={build}
        />
      ))}
    </TableBody>
  );
};
