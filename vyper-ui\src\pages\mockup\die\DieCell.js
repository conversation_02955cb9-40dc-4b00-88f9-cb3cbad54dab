import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { DieView } from "./DieView";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";

export const DieCell = ({ vyper, build, onClick }) => {
  const { canEditDie } = useContext(HelperContext);
  const canEdit = canEditDie(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Dies")
  )
    return null;

  return (
    <DataCell source={build.dies?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        <DieView instances={build.dies.dieInstances} build={build} />
      </VyperLink>
    </DataCell>
  );
};
