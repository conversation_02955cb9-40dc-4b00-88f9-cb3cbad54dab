import React from "react";
import PropTypes from "prop-types";
import Button from "@material-ui/core/Button";
import SaveIcon from "@material-ui/icons/Save";
import SendIcon from "@material-ui/icons/Send";
import NavigateNextIcon from "@material-ui/icons/NavigateNext";

export const BaseButton = ({
  color = "default",
  disabled = false,
  handleClick,
  icon = null,
  label,
  variant = "contained",
}) =>
  disabled ? (
    <span style={{ cursor: "not-allowed" }}>
      <Button
        color={color}
        disabled
        onClick={handleClick}
        role="button"
        startIcon={icon}
        variant={variant}
      >
        {label}
      </Button>
    </span>
  ) : (
    <Button
      color={color}
      onClick={handleClick}
      role="button"
      startIcon={icon}
      variant={variant}
    >
      {label}
    </Button>
  );

BaseButton.propTypes = {
  color: PropTypes.string,
  disabled: PropTypes.bool,
  handleClick: PropTypes.func,
  icon: PropTypes.node,
  label: PropTypes.string.isRequired,
  variant: PropTypes.string,
};

export const PrimaryButton = ({ disabled, icon, label, handleClick }) => {
  return (
    <BaseButton
      color="primary"
      disabled={disabled}
      handleClick={handleClick}
      label={label}
      icon={icon}
      variant="contained"
    />
  );
};

PrimaryButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
  label: PropTypes.string.isRequired,
};

export const SecondaryButton = ({ disabled, icon, label, handleClick }) => {
  return (
    <BaseButton
      color="secondary"
      disabled={disabled}
      handleClick={handleClick}
      label={label}
      icon={icon}
      variant="contained"
    />
  );
};

SecondaryButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
  label: PropTypes.string.isRequired,
};

export const NeutralButton = ({ disabled, icon, label, handleClick }) => {
  return (
    <BaseButton
      color="default"
      disabled={disabled}
      handleClick={handleClick}
      label={label}
      icon={icon}
      variant="contained"
    />
  );
};

NeutralButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
  label: PropTypes.string.isRequired,
};

export const DangerButton = ({ disabled, icon, label, handleClick }) => {
  return (
    <BaseButton
      color="primary"
      disabled={disabled}
      handleClick={handleClick}
      label={label}
      icon={icon}
      variant="contained"
    />
  );
};

DangerButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
  label: PropTypes.string.isRequired,
};

export const SaveButton = ({ disabled, handleClick }) => {
  return (
    <BaseButton
      color="secondary"
      variant="contained"
      disabled={disabled}
      handleClick={handleClick}
      label={"Save"}
      icon={<SaveIcon />}
    />
  );
};

SaveButton.propTypes = {
  disabled: PropTypes.bool,
};

export const SubmitButton = ({ disabled, handleClick }) => {
  return (
    <BaseButton
      color="primary"
      variant="contained"
      disabled={disabled}
      handleClick={handleClick}
      label={"Save & Submit"}
      icon={<SendIcon />}
    />
  );
};

SubmitButton.propTypes = {
  disabled: PropTypes.bool,
};

export const CancelButton = ({ disabled, icon, handleClick }) => {
  return (
    <BaseButton
      color="default"
      variant="outlined"
      disabled={disabled}
      handleClick={handleClick}
      label={"Cancel"}
      icon={icon}
    />
  );
};

CancelButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
};

export const OutlinedButton = ({
  disabled,
  icon,
  handleClick,
  color,
  label,
}) => {
  return (
    <BaseButton
      color={color}
      variant="outlined"
      disabled={disabled}
      handleClick={handleClick}
      label={label}
      icon={icon}
    />
  );
};

CancelButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
};

export const NextButton = ({ disabled, handleClick }) => {
  return (
    <BaseButton
      color="secondary"
      variant="outlined"
      disabled={disabled}
      handleClick={handleClick}
      label={"Next"}
      icon={<NavigateNextIcon />}
    />
  );
};

NextButton.propTypes = {
  disabled: PropTypes.bool,
};

export const CloseButton = ({ disabled, icon, handleClick }) => {
  return (
    <BaseButton
      color="default"
      variant="outlined"
      disabled={disabled}
      handleClick={handleClick}
      label={"Close"}
      icon={icon}
    />
  );
};

CloseButton.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.node,
};
