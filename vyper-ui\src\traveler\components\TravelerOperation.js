import PropTypes from "prop-types";
import React from "react";
import { TravelerComponent } from "./TravelerComponent";
import { useTravelerStyles } from "./travelerStyles";

/**
 * return the operation row for the traveler
 * @param {TravelerOperation} - operation - The traveler's operation.
 * @returns {JSX.Element}
 * @constructor
 */

export function TravelerOperation({ operation }) {
  const classes = useTravelerStyles();
  const name = classes[operation.validStatus];
  return (
    <>
      <span className={name}>{operation.name}</span>
      <br />
      {operation.components.map((component, n) => (
        <TravelerComponent key={n} component={component} />
      ))}
      <br />
    </>
  );
}

TravelerOperation.propTypes = {
  operation: PropTypes.object.isRequired,
};
