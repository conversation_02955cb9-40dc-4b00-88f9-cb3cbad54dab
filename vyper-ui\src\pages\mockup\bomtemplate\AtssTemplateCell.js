import React from "react";
import PropTypes from "prop-types";
import { DataCell } from "src/component/datacell/DataCell";
import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles(() => ({
  link: {
    textDecoration: "none",
    color: "rgb(85, 26, 139)",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
      color: "red",
    },
  },
  highlight: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));

/**
 * description
 * @param {object} vyper
 * @param {object} build
 * @return {JSX.Element}
 * @constructor
 */
export const AtssTemplateCell = ({ vyper, build }) => {
  const classes = useStyles();

  return (
    <DataCell source={build.bomTemplate.source}>
      <div className={classes.highlight}>ATSS Template</div>
      <div>{`${build.templateSource.atssMaterial} ${build.templateSource.atssFacility} ${build.templateSource.atssStatus}`}</div>
    </DataCell>
  );
};

AtssTemplateCell.propTypes = {
  vyper: PropTypes.object.isRequired,
  build: PropTypes.object.isRequired,
};
