import { MenuItem, TextField } from "@material-ui/core";
import React from "react";
import { Experimental, New } from "./BuildTypes";

const BuildtypeSelect = ({ onChange, value }) => {
  const buildTypes = [Experimental, New];

  return (
    <TextField
      variant="outlined"
      select
      id="buildType"
      label="Select the Build Type"
      fullWidth
      value={value}
      onChange={(e) => onChange(e.target.value)}
    >
      {buildTypes.map((buildType) => (
        <MenuItem key={buildType} value={buildType}>
          {buildType}
        </MenuItem>
      ))}
    </TextField>
  );
};

export default BuildtypeSelect;
