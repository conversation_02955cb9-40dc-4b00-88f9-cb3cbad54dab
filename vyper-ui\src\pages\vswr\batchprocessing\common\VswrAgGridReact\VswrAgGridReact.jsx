import React from "react";
import { AgGridReact } from "ag-grid-react";

const VswrAgGridReact = React.forwardRef((props, ref) => {
  let { defaultColDef, ...otherProps } = props;

  return (
    <div
      className={"ti-server-ag-grid ag-theme-alpine"}
      style={{ width: "100%" }}
    >
      <AgGridReact
        ref={ref}
        defaultColDef={{
          sortable: true,
          resizable: true,
          filter: "agTextColumnFilter",
          suppressMenu: true,
          floatingFilter: true,
          ...defaultColDef,
        }}
        enableCellTextSelection
        rowSelection={"multiple"}
        domLayout={"autoHeight"}
        {...otherProps}
      />
    </div>
  );
});
export default VswrAgGridReact;
