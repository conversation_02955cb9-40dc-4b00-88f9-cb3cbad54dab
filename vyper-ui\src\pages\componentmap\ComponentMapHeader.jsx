import Button from "@material-ui/core/Button";
import React from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
  root: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  buttons: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    gap: "1em",
  },
});
/**
 * Show a header of the component name, and a group of buttons.
 *
 * @param name
 * @param canNew
 * @param canCancel
 * @param canSave
 * @param onNew
 * @param onCancel
 * @return {JSX.Element}
 * @constructor
 */
export const ComponentMapHeader = ({
  name,
  canNew,
  canCancel,
  canSave,
  onNew,
  onCancel,
}) => {
  const classes = useStyles();

  return (
    <h3 className={classes.root}>
      <div>{name}</div>
      <div className={classes.buttons}>
        <Button
          variant="contained"
          color="primary"
          type="button"
          onClick={onNew}
          disabled={!canNew}
        >
          New
        </Button>
        <Button
          variant="outlined"
          color="primary"
          type="button"
          onClick={onCancel}
          disabled={!canCancel}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          type="submit"
          disabled={!canSave}
        >
          Save
        </Button>
      </div>
    </h3>
  );
};

ComponentMapHeader.propTypes = {
  name: PropTypes.string.isRequired,
  onNew: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  canNew: PropTypes.bool.isRequired,
  canSave: PropTypes.bool.isRequired,
  canCancel: PropTypes.bool.isRequired,
};
