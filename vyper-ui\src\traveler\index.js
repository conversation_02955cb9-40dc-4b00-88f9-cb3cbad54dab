export { Traveler } from "./components/Traveler";

/**
 * Traveler
 * @typedef Traveler
 * @property {boolean} dryRun - True if this is a dry run, false to actually make the changes.
 * @property {string} submitter - The badge number of the person submitting the traveler.
 * @property {string} vyperBuildNumber - The build number of the traveler
 * @property {TravelerFacility} facility - The facility information
 * @property {TravelerMaterial} material - The material information
 * @property {TravelerHeader} header - The traveler's header
 * @property {TravelerSubflow[]} subFlows - The traveler's subflows
 */

/**
 * @typedef TravelerFacility
 * @property {string} PlantType
 * @property {string} PlantLoc
 * @property {string} PlantName
 * @property {string} PlantCode
 * @property {string} PDBFacility
 */

/**
 * @typedef TravelerMaterial
 * @property {string} Automotive
 * @property {string} FlowName
 * @property {string} Isolation
 * @property {string} Material
 * @property {string} MCMChipCount
 * @property {string} MOQ
 * @property {string} MSL
 * @property {string} MultiChipModule
 * @property {string} OldMaterial
 * @property {string} PackageDesignator
 * @property {string} PackageGroup
 * @property {string} PackagePin
 * @property {string} PackingConfig
 * @property {string} SBE
 * @property {string} SBE1
 * @property {string} SBE2
 * @property {string} SPQ
 */

/**
 * @typedef TravelerHeader
 * @property {string} id
 * @property {string} currentDateTime
 * @property {string} oldMaterial
 * @property {string} specDevice
 * @property {string} at
 * @property {string} revision
 * @property {string} sbe
 * @property {string} sbe1
 * @property {string} sbe2
 * @property {string} sapMaterial
 * @property {string} sapBaseName
 * @property {string} niche
 * @property {string} presp
 * @property {string} pins
 * @property {string} pkg
 * @property {string} packageGroup
 * @property {number} spq
 * @property {number} moq
 * @property {string} industrySector
 * @property {string} customer
 * @property {string} custPartName
 * @property {string} custPrintName
 * @property {string} custPrintRevision
 * @property {string} type4
 */

/**
 * TravelerSubflow
 * @typedef TravelerSubflow
 * @property {string} type - The subflow type
 * @property {TravelerOperation[]} operations - The subflow's operations.
 * @property {boolean} validStatus - true if the subflow is valid in ATSS.
 */

/**
 * The  Traveler Operation
 * @typedef TravelerOperation
 * @property {string} name - The operation's name.
 * @property {TravelerComponent[]} components - The operation's components.
 * @property {boolean} validStatus - true if the subflow is valid in ATSS.
 */

/**
 * The  Traveler Component
 * @typedef TravelerComponent
 * @property {string} name - The component's name.
 * @property {string|number|null} value - The component's value.
 * @property {number} priority - The component's priority.
 * @property {TravelerAttribute[]} attributes - The component's attributes
 * @property {string|null} paragraph - The paragraph text.
 * @property {boolean} validStatus - true if the subflow is valid in ATSS.
 */

/**
 * @typedef TravelerAttribute
 * @property {string} name - The attribute name
 * @property {string} value - The attribute value
 * @property {boolean} validStatus - true if the attribute is valid.
 * @property {boolean} validStatus - true if the subflow is valid in ATSS.
 */
