import CircularProgress from "@material-ui/core/CircularProgress";
import TextField from "@material-ui/core/TextField";
import { Autocomplete } from "@material-ui/lab";
import React, { useEffect, useState } from "react";

export const AutocompleteTargetFacility = ({
    facility,
    onChangeFacility,
    disabled
}) => {
    const [facilities, setFacilities] = useState([]);
    const [facilityOptions, setFacilityOptions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [search, setSearch] = useState(facility);

    const handleInputChange = (e, v) => {
        setSearch(v);
        setFacilityOptions(facilities.filter((elem) => elem.toLowerCase().includes(v.toLowerCase())));
    };

    useEffect(() => {
        setLoading(true);

        fetch(`/vyper/v1/atssmassupload/facilities`)
            .then((response) => response.json())
            .then((json) => {
                setFacilities(json.map((elem) => elem.facilityAt));
                setFacilityOptions(json.map((elem) => elem.facilityAt));
            })
            .catch((e) => console.log(e))
            .finally(() => setLoading(false));
    }, []);

    return (
        <Autocomplete
            fullWidth
            disabled={disabled}
            open={open}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            renderInput={(params) =>
                <TextField
                    {...params}
                    disabled={disabled}
                    variant="outlined"
                    fullWidth
                    label="Target Facility"
                    InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <React.Fragment>
                                {loading ? (
                                    <CircularProgress color="inherit" size={20} />
                                ) : null}
                                {params.InputProps.endAdornment}
                            </React.Fragment>
                        ),
                    }}
                />
            }
            options={facilityOptions}
            loading={loading}
            inputValue={search}
            onInputChange={handleInputChange}
            value={facility || ""}
            onChange={(e, v) => onChangeFacility(v)}
            style={{ minWidth: "20rem" }}
        />
    );
};
