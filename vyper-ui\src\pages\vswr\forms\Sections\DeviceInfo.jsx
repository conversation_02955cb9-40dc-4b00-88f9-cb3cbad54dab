import React, { useState, useMemo, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import TextField from "@material-ui/core/TextField";
import { FormControl, InputLabel, Select, MenuItem } from "@material-ui/core";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";

import { BASE_FETCH_OPTIONS_URL } from "../FormConstants";

const fetchData = (uri, setFormState, defaultValue) => {
  fetch(`${BASE_FETCH_OPTIONS_URL}${uri}`)
    .then((response) => response.json())
    .then(setFormState)
    .catch(() => {
      setFormState(defaultValue);
    });
};

const DeviceInfo = (props) => {
  const { classes, formState, setFormState, readOnly = false } = props;

  const [offloadInfoOption, setOffloadInfoOptions] = useState([]);

  useEffect(() => {
    fetchData(`/offloadInfoOptions`, setOffloadInfoOptions, []);
  }, []);

  const handleChange = (evt) => {
    if (readOnly) {
      return;
    }
    const { name, value } = evt.target;
    setFormState((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const defaultFieldProps = useMemo(() => {
    return {
      InputLabelProps: {
        className: classes.textField,
      },
      fullWidth: true,
      style: { background: "#DCDCDC" },
      disabled: true,
      variant: "outlined",
      color: "secondary",
      onChange: handleChange,
    };
  }, []);

  return (
    <Paper elevation={24} className={classes.paper}>
      <Typography variant="h6">Device Information </Typography>
      <Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.sapMaterial}
              name={"sapMaterial"}
              label="SAP Material"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.sapBaseMaterial}
              name={"sapBaseMaterial"}
              label="SAP Base Material"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.specDevice}
              name={"specDevice"}
              label="Spec Device"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.sbe}
              name={"sbe"}
              label="SBE"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.sbe1}
              name={"sbe1"}
              label="SBE-1"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.sbe2}
              name={"sbe2"}
              label="SBE-2"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.industrySector}
              name={"industrySector"}
              label="Industry Sector"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.pin}
              name={"pin"}
              label="Pin"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.pkg}
              name={"pkg"}
              label="Pkg"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.pkgGroup}
              name={"pkgGroup"}
              label="Pkg Group"
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              style={readOnly ? { background: "#DCDCDC" } : {}}
              disabled={readOnly}
              error={!formState.buildQuantity}
              value={formState.buildQuantity}
              required
              name={"buildQuantity"}
              label="Build Quantity"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.profitCenter}
              name={"profitCenter"}
              label="Profit Center (WWID)"
            />
          </Grid>
          <Grid item xs>
            <FormControl
              required
              fullWidth
              error={!formState.offloadInfo}
              color="secondary"
              variant="outlined"
            >
              <InputLabel className={classes.textField}>
                Offload Info
              </InputLabel>
              <Select
                {...defaultFieldProps}
                style={readOnly ? { background: "#DCDCDC" } : {}}
                disabled={readOnly}
                name={"offloadInfo"}
                onChange={handleChange}
                required
                value={formState.offloadInfo}
              >
                {offloadInfoOption.map((item, i) => (
                  <MenuItem key={i} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.apl}
              name={"apl"}
              label="APL"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.iso}
              name={"iso"}
              label="ISO"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.mcm}
              name={"mcm"}
              label="MCM"
            />
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};
export default DeviceInfo;
