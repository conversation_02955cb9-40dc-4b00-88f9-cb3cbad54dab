import React from "react";
import PropTypes from "prop-types";
import { DataCell } from "src/component/datacell/DataCell";
import { HasBomTemplate } from "src/pages/mockup/bomtemplate/HasBomTemplate";
import { Development } from "src/pages/mockup/bomtemplate/Development";
import { makeStyles } from "@material-ui/styles";
import { Link } from "react-router-dom";

const useStyles = makeStyles(() => ({
  link: {
    textDecoration: "none",
    color: "rgb(85, 26, 139)",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
      color: "red",
    },
  },
  highlight: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));

/**
 * description
 * @param {object} vyper
 * @param {object} build
 * @return {JSX.Element}
 * @constructor
 */
export const SimilarTemplateCell = ({ vyper, build }) => {
  const classes = useStyles();

  const similar = build.templateSource?.similarPkgNiche;
  const name = build.bomTemplate.object?.name || "";

  return (
    <DataCell source={build.bomTemplate.source}>
      <HasBomTemplate bomTemplate={build.bomTemplate} build={build} />
      <Development bomTemplate={build.bomTemplate} />

      <div>
        <div className={classes.highlight}>
          Similar Niche Template: {similar}
        </div>
        <Link
          className={classes.link}
          to={`/projects/${vyper.vyperNumber}/builds/${build.buildNumber}/bomtemplate`}
        >
          {name.split(",").map((line, key) => (
            <div key={key}>{line}</div>
          ))}
        </Link>
      </div>
    </DataCell>
  );
};

SimilarTemplateCell.propTypes = {
  vyper: PropTypes.object.isRequired,
  build: PropTypes.object.isRequired,
};
