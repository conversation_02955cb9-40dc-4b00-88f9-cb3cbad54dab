import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../../mockup/RowPrefix";
import { DataCell } from "src/component/datacell/DataCell";

export function VscnOldMaterialRow({ vscns }) {
  return (
    <TableRow hover>
      <RowPrefix help="old material" title="Old Material Name" required />
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <DataCell key={n}>
            <span>{vscn.material?.object?.OldMaterial}</span>
          </DataCell>
        </TableCell>
      ))}
    </TableRow>
  );
}

VscnOldMaterialRow.propTypes = {
  vscns: PropTypes.array.isRequired,
};
