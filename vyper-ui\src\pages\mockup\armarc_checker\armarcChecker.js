/**
 * Returns an array of the list of armarc check messages for a build
 * @param build
 * @returns {string[]}
 */
export const getMessages = (build) => {
  return ["Leadframe", "Mount Compound", "Mold Compound", "Wire"]
    .map((cName) => build.components.find((c) => c.name === cName))
    .flatMap((c) => c?.instances ?? [])
    .flatMap((p) => p?.priorities ?? [])
    .map((p) => p?.armarcCheckMessage)
    .filter((m) => m != null);
};
