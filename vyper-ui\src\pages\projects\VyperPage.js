import React, { useContext, useEffect } from "react";
import { RouteManager, RouteNotFound } from "src/component/common";
import { VyperProjectPage } from "src/pages/vyper/builds/VyperProjectPage";
import { Route, Switch, useParams } from "react-router-dom";
import { noop } from "src/component/vyper/noop";
import { VyperBuildPage } from "src/pages/projects/builds/VyperBuildPage";
import { DataModelsContext } from "src/DataModel";
import { VyperPraPage } from "src/pages/projects/pras/VyperPraPage";
import { VyperVscnPage } from "./vscns/VyperVscnPage";
import { VscnSelectPage } from "./vscns/select/VscnSelectPage";

export const VyperPage = () => {
  const { vyperDao } = useContext(DataModelsContext);

  const { vyperNumber } = useParams();

  // retrieve the vyper - store in the cache
  useEffect(() => {
    vyperDao.findByVyperNumber(vyperNumber).catch(noop);
  }, [vyperNumber]);

  const params = { vyperNumber };

  return (
    <Switch>
      <Route
        exact
        path="/projects/:vyperNumber"
        render={() => {
          return <VyperProjectPage {...params} />;
        }}
      />

      <Route
        path="/projects/:vyperNumber/builds/:buildNumber"
        render={() => {
          return <VyperBuildPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/pras/:praNumber"
        render={() => {
          return <VyperPraPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/pras"
        render={() => {
          return <VyperPraPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/vscns/:vscnNumber"
        component={VyperVscnPage}
      />

      <Route
        exact
        path="/projects/:vyperNumber/vscns/:vscnNumber/selection"
        component={VscnSelectPage}
      />

      <RouteManager component={RouteNotFound} />
    </Switch>
  );
};
