import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { ChangeCell } from "./ChangeCell";
import { ChangeDialogContext } from "./ChangeDialog";

export const ChangeRow = ({ vyper, builds, onChange }) => {
  const { open } = useContext(ChangeDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeLinkChange = (changes, build) => {
    return buildDao
      .changeChangelinkChange(vyper.vyperNumber, build.buildNumber, changes)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleOpen = (build) => {
    open({
      rows: build.changelink.changes.map((c) => c.object),
      onSave: (rows) => handleChangeLinkChange(rows, build),
    });
  };

  return (
    <TableRow hover>
      <RowPrefix help="change" title="Change Number(s)" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <ChangeCell
            vyper={vyper}
            build={build}
            onClick={() => handleOpen(build)}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
