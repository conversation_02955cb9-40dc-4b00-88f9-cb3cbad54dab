import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React from "react";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { VyperLink } from "../../mockup/VyperLink";
import { useHistory } from "react-router-dom";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
  center: {
    display: "flex",
    alignItems: "center",
    flexDirection: "column",
  },
});

export const VscnSelectionRow = ({ vyper, vscns, build }) => {
  const classes = useStyles();
  const history = useHistory();

  return (
    <TableRow hover>
      <RowPrefix
        help="packSelectComponents"
        title="Pack Select Components"
        required
      />
      {vscns.map((vscn, n) => (
        <TableCell key={n} className={classes.center}>
          {vscn.changedComponentsGroup?.includes(
            `VYPER_${vscn.facility?.object?.PDBFacility}_PACK`
          ) ? (
            <VyperLink
              onClick={(e) => {
                history.push(
                  `/projects/${vscn.vyperNumber}/vscns/${vscn.vscnNumber}/selection`
                );
              }}
            >
              Link to select components
            </VyperLink>
          ) : (
            <p className={classes.center}>Not Applicable</p>
          )}
        </TableCell>
      ))}
    </TableRow>
  );
};
