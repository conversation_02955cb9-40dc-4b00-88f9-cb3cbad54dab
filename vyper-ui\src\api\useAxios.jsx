import axios from "axios";
import { useContext } from "react";
import { SpinnerContext } from "src/component/Spinner";

export const useAxios = () => {
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const instance = axios.create();

  instance.interceptors.request.use(
    function (config) {
      showSpinner();
      return config;
    },
    function (error) {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    function (response) {
      hideSpinner();
      return response;
    },
    function (error) {
      hideSpinner();
      // todo: future use - handle errors here
      return Promise.reject(error);
    }
  );

  return { instance };
};
