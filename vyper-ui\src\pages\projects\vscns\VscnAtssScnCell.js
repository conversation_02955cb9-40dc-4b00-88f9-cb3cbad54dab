import PropTypes from "prop-types"
import React, {useState} from "react"
import { Button} from "@material-ui/core"
import {DataCell} from "../../../component/datacell/DataCell"
import {VyperLink} from "../../mockup/VyperLink"
import {AtssScnDialog} from "./AtssScnDialog"

export function VscnAtssScnCell({item, onClickRefresh}) {

    const [open, setOpen] = useState(false);

    function handleOpen() {
        setOpen(true)
    }

    function handleClose() {
        setOpen(false)
    }

    const scnsCreated = item.atssScns || [];

    return <div>
             {scnsCreated.map( (scnCreated,ind) =>{
                return <div>
                            <DataCell source={null}>
                                 <VyperLink onClick={handleOpen}>{  (scnCreated.scnId != "" && scnCreated.scnId != null)? scnCreated.scnId : scnCreated?.rejectReason ||'Not created'}</VyperLink>
                                 <AtssScnDialog open={open} atssScn={scnCreated} onClose={handleClose}/>
                                 { (scnCreated.scnId != "" && scnCreated.scnId != null) && <Button variant="contained" color="primary" onClick={onClickRefresh}>Refresh status</Button> }
                             </DataCell>
                        </div>
             })}
    </div>
}

VscnAtssScnCell.propTypes = {
    item: PropTypes.any.isRequired,
    onClickRefresh: PropTypes.func.isRequired
}