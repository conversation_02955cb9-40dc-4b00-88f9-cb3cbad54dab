import { DaoBase } from "src/component/fetch/DaoBase";

export class Announcement<PERSON>ao extends DaoBase {
  constructor(params) {
    super({
      name: "AnnouncementDao",
      url: "/vyper/v1/announcements",
      ...params,
    });
  }

  create(announcement) {
    return this.handleFetch("create", "", "POST", announcement);
  }

  read(id) {
    return this.handleFetch("read", `/${id}`, "GET");
  }

  update(announcement) {
    return this.handleFetch(
      "update",
      `/${announcement.id}`,
      "POST",
      announcement
    );
  }

  delete(id) {
    return this.handleFetch("delete", `/${id}`, "DELETE");
  }

  list(page = 0, size = 1000, sort = "id,asc") {
    return this.handleFetch(
      "list",
      `/?page=${page}&size=${size}&sort=${sort}`,
      "GET"
    );
  }

  active(page = 0, size = 1000, sort = "id,asc") {
    return this.handleFetch(
      "active",
      `/active?page=${page}&size=${size}&sort=${sort}`,
      "GET"
    );
  }
}
