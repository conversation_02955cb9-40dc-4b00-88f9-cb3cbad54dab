import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import { Button, Grid } from "@material-ui/core";
import DialogActions from "@material-ui/core/DialogActions";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  gridItem: {
    marginBottom: theme.spacing(2),
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  button: {
    width: "100px",
  },
}));

export const CopyFromDialog = ({
  open,
  onClose,
  onClickAtss,
  onClickVyper,
  onClickArmarc,
}) => {
  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle className={classes.title}>
        Copy From Selector 2222222
      </DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent style={{ marginTop: "2em" }}>
        <Grid container>
          <Grid className={classes.gridItem} item xs={3}>
            <Button
              className={classes.button}
              color="primary"
              variant="contained"
              onClick={onClickArmarc}
            >
              ARM/ARC
            </Button>
          </Grid>
          <Grid className={classes.gridItem} item xs={9}>
            Copy from an existing ARM/ARC.
          </Grid>

          <Grid className={classes.gridItem} item xs={3}>
            <Button
              className={classes.button}
              color="primary"
              variant="contained"
              onClick={onClickAtss}
            >
              ATSS
            </Button>
          </Grid>
          <Grid className={classes.gridItem} item xs={9}>
            Copy from an existing ATSS.
          </Grid>

          <Grid className={classes.gridItem} item xs={3}>
            <Button
              className={classes.button}
              color="primary"
              variant="contained"
              onClick={onClickVyper}
            >
              VYPER
            </Button>
          </Grid>
          <Grid className={classes.gridItem} item xs={9}>
            Copy from an existing VYPER.
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined" color="primary">
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};
