import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import React, { useEffect, useState } from "react";
import { TravelerItem } from "./TravelerItem";

export const TravelerBodyTable = ({ build }) => {
  // build the traveler rows
  const [travelerItems, setTravelerItems] = useState([]);
  useEffect(() => {
    const travelerItems = [];

    build?.traveler?.operations.map((operation) => {
      if (operation.components.length === 0) {
        travelerItems.push({
          operation: operation.name,
          componentName: null,
          componentValue: null,
          attributeName: null,
          attributeValue: null,
          paragraph: null,
        });
      } else {
        operation.components.map((component) => {
          const hasAttributes = component.attributes.length > 0;
          const hasParagraph = component.paragraph != null;

          if (hasAttributes) {
            component.attributes.map((attribute) => {
              travelerItems.push({
                operation: operation.name,
                componentName: component.name,
                componentValue: component.value || "",
                attributeName: attribute.name,
                attributeValue: attribute.value,
                paragraph: null,
              });
            });
          }

          if (hasParagraph) {
            travelerItems.push({
              operation: operation.name,
              componentName: component.name,
              componentValue: component.value || "",
              attributeName: null,
              attributeValue: null,
              paragraph: component.paragraph,
            });
          }

          if (!hasAttributes && !hasParagraph) {
            travelerItems.push({
              operation: operation.name,
              componentName: component.name,
              componentValue: component.value || "",
              attributeName: null,
              attributeValue: null,
              paragraph: null,
            });
          }
        });
      }
    });

    setTravelerItems(travelerItems);
  }, [build]);

  return (
    <Table size="small">
      <TableHead>
        <TableRow hover>
          <TableCell>
            <strong>Operation</strong>
          </TableCell>
          <TableCell>
            <strong>Component Name</strong>
          </TableCell>
          <TableCell>
            <strong>Component Value</strong>
          </TableCell>
          <TableCell>
            <strong>Attribute Name</strong>
          </TableCell>
          <TableCell>
            <strong>Attribute Value</strong>
          </TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {travelerItems.map((travelerItem, n) => (
          <TravelerItem key={n} travelerItem={travelerItem} />
        ))}
      </TableBody>
    </Table>
  );
};
