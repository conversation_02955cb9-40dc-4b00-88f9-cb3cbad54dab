import React, { useState } from "react";
import { Dialog } from "@material-ui/core";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import { makeStyles } from "@material-ui/core/styles";
import { logError } from "src/pages/functions/logError";

const useStyles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    paddingTop: 4,
    paddingBottom: 4,
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "#ffffff",
  },
  messages: {
    paddingBottom: "0.5em",
  },
}));

export const ErrorDialog = ({ children }) => {
  const [title, setTitle] = useState("Error Dialog");
  const [messages, setMessages] = useState([]);
  const [open, setOpen] = useState(false);
  const classes = useStyles();

  const handleOpen = ({ title = "Error Dialog", error }) => {
    logError(error);

    setTitle(title);

    if (error == null) {
      setMessages(["An error occurred."]);
    } else if (error instanceof Response) {
      setMessages([error.status, error.statusText]);

      // task service embeds error message in the text of the response, as a json object.
      // if a message exists, then add it to the dialog.
      // VYPER-1760
      error.text().then((t) => {
        if (t == null) {
          return;
        }

        if (t.startsWith("{")) {
          const o = JSON.parse(t);
          if (o.message) {
            setMessages([error.status, error.statusText, o.message]);
          }
        }
      });
    } else if (error.message) {
      setMessages([error.message]);
    } else {
      setMessages([error.toString()]);
    }

    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <ErrorDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle className={classes.title}>{title}</DialogTitle>
        <DialogContent>
          {messages.map((message, n) => (
            <div className={classes.messages} key={n}>
              {message}
            </div>
          ))}
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" color="primary" onClick={handleClose}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </ErrorDialogContext.Provider>
  );
};

export const ErrorDialogContext = React.createContext(null);
