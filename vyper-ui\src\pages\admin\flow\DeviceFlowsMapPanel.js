import React, { useState, useContext, useEffect } from "react";
import { DataGrid, VyperMultiSelect } from "../../../component/universal";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { logError } from "../../functions/logError";
import { DeviceFlowMapDao } from "../../../dao/DeviceFlowMapDao";
import { AlertDialogErrorHandler } from "../../../component/fetch/DaoBase";
import { Checkbox, Select, MenuItem } from "@material-ui/core";

export function DeviceFlowsMapPanel() {
  const [data, setData] = useState([]);
  const { open: openAlert } = useContext(AlertDialogContext);

  const deviceFlowMapDao = new DeviceFlowMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
  });

  useEffect(() => {
    refresh().catch(logError);
  }, []);

  const flowData = data
    .filter((flow) => flow.flowName != "TKY")
    .map((flow) => {
      return { id: flow.id, value: flow.flowName };
    });

  const lookupFlowName = (flowIds) => {
    if (!flowIds) return ["None"];
    const flowNames = flowIds
      .map((flowId) => flowData.filter((flow) => flow.id === flowId)[0].value)
      .join(", ");
    return flowNames.length === 0 ? ["None"] : flowNames;
  };

  const lookupFlowIds = (flowNames) => {
    return flowNames.map(
      (flowName) => flowData.filter((flow) => flow.value === flowName)[0].id
    );
  };

  const refresh = () => {
    return deviceFlowMapDao
      .list(0, 1000)
      .then((json) => {
        setData(json);
      })
      .catch(logError);
  };

  const handleFlowSelectionChange = (rowData, field, value) => {
    console.log("Changed data ", rowData, field, value);
    let updatedRow;
    setData(
      data.map((d) => {
        if (d.id === rowData.id) {
          if (field === "predecessorFlow") {
            d.predecessorFlows = lookupFlowIds(rowData.predecessorFlows);
          } else if (field === "successorFlows") {
            d.successorFlows = lookupFlowIds(rowData.successorFlows);
          } else {
            d[field] = !d[field];
          }
          updatedRow = d;
          return d;
        } else {
          return d;
        }
      })
    );

    if (field === "default") {
      handleRowUpdate(updatedRow);
    }
  };

  const columns = [
    {
      field: "id",
      title: "id",
      hidden: true,
    },

    {
      field: "flowName",
      title: "FLOW NAME",
      validate: (rowData) => {
        return rowData.flowName === "" ? "Flow name cannot be blank" : "";
      },
    },
    {
      field: "flowDescription",
      title: "FLOW DESCRIPTION",
      validate: (rowData) => {
        return rowData.flowDescription === ""
          ? "flow description cannot be blank"
          : "";
      },
    },
    {
      field: "default",
      title: "Default",
      editComponent: ({ value, onChange, rowData }) => (
        <Checkbox
          color="secondary"
          checked={rowData.default === true}
          onChange={(event) =>
            onChange(rowData.default === true ? false : true)
          }
        />
      ),
      render: (rowData) => {
        return (
          <div>
            <Checkbox
              color="secondary"
              checked={rowData.default === true}
              onChange={() => handleFlowSelectionChange(rowData, "default")}
            />
          </div>
        );
      },
      cellStyle: {
        maxWidth: 1000,
      },
    },
    {
      field: "scsFlow",
      title: "SCS FLOW",
      validate: (rowData) => {
        return rowData.scsFlow === "" ? "scs flow cannot be blank" : "";
      },
    },
    {
      field: "templateFlow",
      title: "TEMPLATE FLOW",
      render: (rowData) => {
        return <div>{rowData.templateFlows.join(", ")}</div>;
      },
      validate: (rowData) => {
        return rowData.templateFlow === ""
          ? "template flow cannot be blank"
          : "";
      },
    },
    {
      field: "predecessorFlow",
      title: "PREDECESSOR",
      editComponent: ({ value, onChange, rowData }) => (
        <Select
          name="predecessorSelection"
          value={[value]}
          defaultValue={[9999]}
          multiple={true}
          onChange={(evt) =>
            handleFlowSelectionChange(
              rowData,
              "predecessorFlow",
              evt.target.value
            )
          }
        >
          {flowData.map((flow) => (
            <MenuItem key={flow.id} value={flow.id}>
              {flow.value}
            </MenuItem>
          ))}
          <MenuItem key={9999} value={9999}>
            {"None"}
          </MenuItem>
        </Select>
      ),
      render: (rowData) => {
        return <div>{lookupFlowName(rowData.predecessorFlows)} </div>;
      },
      validate: (rowData) => {
        return rowData.predecessorFlow === ""
          ? "predecessor flow cannot be blank"
          : "";
      },
    },
    {
      field: "successorFlow",
      title: "SUCCESSOR",
      editComponent: ({ value, onChange, rowData }) => (
        <Select
          name="successorFlowSelection"
          value={value ? value.value : value}
          defaultValue={"9999"}
          onChange={(evt) => onChange(evt.target.value)}
        >
          {flowData.map((flow) => (
            <MenuItem key={flow.id} value={flow.id}>
              {flow.value}
            </MenuItem>
          ))}
          <MenuItem key={9999} value={9999}>
            {"None"}
          </MenuItem>
        </Select>
      ),
      render: (rowData) => {
        return <div>{lookupFlowName(rowData.successorFlows)} </div>;
      },
      validate: (rowData) => {
        return rowData.successorFlow === ""
          ? "successor flow cannot be blank"
          : "";
      },
    },
  ];

  function handleRowAdd(newData) {
    return new Promise((resolve, reject) => {
      console.log("Added -> ", newData);
      deviceFlowMapDao
        .create(newData)
        .then((deviceFlowMap) => {
          setData([...data, deviceFlowMap]);
          resolve();
        })
        .catch(() => reject());
    });
  }

  function handleRowUpdate(newData) {
    return new Promise((resolve, reject) => {
      console.log("Updated data ->", newData);

      deviceFlowMapDao
        .update(newData)
        .then((deviceFlowMap) => {
          setData(
            data.map((tnm) =>
              tnm.id === deviceFlowMap.id ? deviceFlowMap : tnm
            )
          );
          resolve();
        })
        .catch(() => reject());
    });
  }

  function handleRowDelete(oldData) {
    return new Promise((resolve, reject) => {
      deviceFlowMapDao
        .delete(oldData.id)
        .then(() => {
          setData(data.filter((tnm) => tnm.id !== oldData.id));
          resolve();
        })
        .catch(() => reject());
    });
  }

  return (
    <div>
      <DataGrid
        title="Device Flow Map"
        columns={columns}
        data={data}
        editable={true}
        onRowAdd={handleRowAdd}
        onRowUpdate={handleRowUpdate}
        onRowDelete={handleRowDelete}
        options={{
          search: false,
          pageSize: 10,
          pageSizeOptions: [5, 10, 20, 50, 100, 200],
        }}
        initialState={{ columnVisibility: { id: false } }}
      />
    </div>
  );
}
