import React from "react";
import { Box, Checkbox, TextField } from "@material-ui/core";

export const SymbolCust = ({ cust, onChange, canIgnoreAnother, length }) => {
  /*
    ignored | canIgnoreAnother | valueEmpty ||  enabled
      F         F                  F              F
      F         F                  T              F
      F         T                  F              F
      F         T                  T              T
      T         F                  F              T
      T         F                  T              T
      T         T                  F              T
      T         T                  T              T
 */

  const ignored = cust.ignoreBlank === "Y";
  const valueEmpty = cust.value === "" || !cust.value;
  const canCheck = ignored || (canIgnoreAnother && valueEmpty);

  const helperText =
    length == null
      ? "max length not configure for this symbol"
      : `${cust.value?.length} of ${length} chars.`;

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "flex-start",
        alignItems: "baseline",
      }}
    >
      <TextField
        fullWidth
        variant="outlined"
        label={cust.name}
        name="cust"
        value={cust.value}
        onChange={(e) => onChange({ ...cust, value: e.target.value })}
        error={
          cust.value?.length === 0 || (length && cust.value?.length > length)
        }
        InputLabelProps={{ shrink: true }}
        disabled={cust.ignoreBlank === "Y"}
        helperText={helperText}
      />

      <Checkbox
        color="secondary"
        checked={cust.ignoreBlank === "Y"}
        onChange={(e) => {
          onChange({ ...cust, ignoreBlank: e.target.checked ? "Y" : "N" });
        }}
        title="Check to indicate that this value is intentionally blank."
        disabled={!canCheck}
      />
    </Box>
  );
};
