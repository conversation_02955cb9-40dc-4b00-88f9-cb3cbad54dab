import React, { useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import Button from "@material-ui/core/Button";
import DialogActions from "@material-ui/core/DialogActions";

export const ViewDialog = ({ children }) => {
  const styles = makeStyles((theme) => ({
    closeButton: {
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    autocomplete: {
      minWidth: "13rem",
    },
    dialog: {
      padding: "3rem",
      margin: "3rem",
      minWidth: "400px",
    },
    preview: {
      backgroundColor: "#EBEBE3",
      border: "1px solid black",
      overflowY: "scroll",
      height: "300px",
      width: "100%",
      fontFamily: "monospace",
    },
    previewgrid: {},
  }));

  const [open, setOpen] = useState(false);

  const [parameters, setParameters] = useState();

  const handleOpen = (parameters) => {
    setParameters(parameters);
    setOpen(true);
  };

  const [content, setContent] = useState();

  const handleClose = () => {
    setParameters(undefined);
    setOpen(false);
  };

  const handleEdit = () => {
    setOpen(false);
    parameters?.handleEdit(parameters?.build);
  };
  const classes = styles();

  return (
    <ViewDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
        <DialogTitle>{parameters?.title || "View Test Program"}</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <pre className={classes.preview}>{parameters?.content}</pre>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleEdit} variant="outlined" color="primary">
            Edit
          </Button>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </ViewDialogContext.Provider>
  );
};

export const ViewDialogContext = React.createContext(null);
