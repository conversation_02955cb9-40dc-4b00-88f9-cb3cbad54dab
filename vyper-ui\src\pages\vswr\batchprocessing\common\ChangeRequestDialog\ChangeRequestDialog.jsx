import React, { useState } from "react";
import Button from "@material-ui/core/Button";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
} from "@material-ui/core";
import CircularProgress from "@material-ui/core/CircularProgress";

import SaveIcon from "@material-ui/icons/Save";
import { Alert } from "@material-ui/lab";
import { useSwrsRequestUpdater } from "./queries";

const ChangeRequestDialog = (props) => {
  let { onOpenRequest, triggerButtonLabel, onSuccessfulChange } = props;
  const [openDialog, setOpenDialog] = useState(false);
  const [changeReason, setChangeReason] = useState("");
  const swrsRequestUpdater = useSwrsRequestUpdater({
    onSuccess: () => {
      onCloseDialog();
      onSuccessfulChange();
    },
  });

  const onCloseDialog = () => {
    setOpenDialog(false);
    setChangeReason("");
    swrsRequestUpdater.reset();
  };

  return (
    <>
      <Button
        style={{ width: "100%" }}
        variant="contained"
        startIcon={<SaveIcon />}
        onClick={() => {
          onOpenRequest()
            .then((data) => {
              setOpenDialog({ data });
            })
            .catch((e) => {
              alert(e);
            });
        }}
      >
        {triggerButtonLabel}
      </Button>
      <Dialog open={openDialog} onClose={onCloseDialog}>
        <DialogTitle>{triggerButtonLabel}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Enter the Reason why these changes are being made
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label="Change Reason"
            multiline
            fullWidth
            variant={"outlined"}
            maxRows={4}
            value={changeReason}
            onChange={(e) => {
              let target = e.target;
              setChangeReason(target.value);
            }}
          />
          {swrsRequestUpdater.isError && (
            <Alert severity="error">
              An error occured trying to update the swr(s).
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={onCloseDialog}
            disabled={swrsRequestUpdater.isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              swrsRequestUpdater.mutate({ ...openDialog.data, changeReason });
            }}
            disabled={swrsRequestUpdater.isLoading}
            startIcon={
              swrsRequestUpdater.isLoading && <CircularProgress size={"24px"} />
            }
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
export default ChangeRequestDialog;
