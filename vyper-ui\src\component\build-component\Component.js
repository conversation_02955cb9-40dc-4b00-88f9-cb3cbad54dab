import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext } from "react";
import { VyperLink } from "../../pages/mockup/VyperLink";
import {
  ComponentDialogContext,
  FILTER_MODE_BUILD,
  MODE_COMPONENT,
} from "./ComponentDialog";
import { ComponentView } from "./ComponentView";
import { Experimental } from "../../pages/mockup/buildtype/BuildTypes";

const useStyles = makeStyles({
  column: {
    display: "flex",
    justifyContent: "flex-left",
  },
});

export const Component = ({ build, component, onSaveBuild, canEdit }) => {
  const { openComponentDialog } = useContext(ComponentDialogContext);

  // display the component dialog box
  const handleClick = () => {
    openComponentDialog({
      buildNumber: build.buildNumber,
      material: build.material.object.Material,
      pin: build.material.object.PackagePin,
      pkg: build.material.object.PackageDesignator,
      pkgGroup: build.material.object.PackageGroup,
      facility: build.facility.object.PDBFacility,
      plantCode: build.facility.object.PlantCode,
      component: component,
      enableEngineering: true,
      mode: MODE_COMPONENT,
      filterMode: FILTER_MODE_BUILD,
      buildType: build.buildtype,
      onSave: (updatedComponent, revertPgs) =>
        onSaveBuild(updatedComponent, build, revertPgs),
    });
  };

  const classes = useStyles();

  if (component == null) {
    return <div>&nbsp;</div>;
  }

  return (
    <div>
      <div className={classes.column}>
        <VyperLink onClick={handleClick} canEdit={canEdit}>
          <ComponentView component={component} />
        </VyperLink>
      </div>
    </div>
  );
};
