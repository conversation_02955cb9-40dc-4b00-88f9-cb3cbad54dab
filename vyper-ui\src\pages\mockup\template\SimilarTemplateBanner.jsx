import React from "react";
import PropTypes from "prop-types";
import { Alert, AlertTitle } from "@material-ui/lab";

/**
 * Show a banner for ATSS Templates.
 *
 * @param {object} build - The build object
 * @return {JSX.Element}
 * @constructor
 */
export const SimilarTemplateBanner = ({ build }) => {
  const similarPkgNiche = build.templateSource.similarPkgNiche;

  return (
    <Alert severity="success">
      <AlertTitle>
        The flow for this build is based on the similar package niche:{" "}
        <strong>{similarPkgNiche}</strong>.
      </AlertTitle>
    </Alert>
  );
};

SimilarTemplateBanner.propTypes = {
  build: PropTypes.object.isRequired,
};
