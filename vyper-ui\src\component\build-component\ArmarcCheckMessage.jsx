import React from "react";
import { Toolt<PERSON>, Zoom } from "@material-ui/core";
import PriorityHighIcon from "@material-ui/icons/PriorityHigh";

export const ArmarcCheckMessage = ({ armarcCheckMessage }) => {
  if (armarcCheckMessage == null) {
    return null;
  }

  return (
    <div>
      <Tooltip TransitionComponent={Zoom} title={armarcCheckMessage}>
        <PriorityHighIcon fontSize="small" />
      </Tooltip>
    </div>
  );
};
