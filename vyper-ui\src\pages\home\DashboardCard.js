import React, { useContext } from "react";
import { useHistory } from "react-router-dom";
import { FetchContext } from "../../component/fetch/VyperFetch";
import { Card, CardActionArea, CircularProgress } from "@material-ui/core";
import Typography from "@material-ui/core/Typography";
import NavigateNextIcon from "@material-ui/icons/NavigateNext";
import makeStyles from "@material-ui/core/styles/makeStyles";

const cardEdge = "9"; // unit=rem

const useStyles = makeStyles((theme) => ({
  root: {},
  card: {
    width: `${cardEdge * 2}rem`,
    height: `${cardEdge}rem`,
    marginLeft: theme.spacing(3),
    marginRight: theme.spacing(3),
    marginBottom: theme.spacing(3),
  },
  cardActionArea: {
    display: "flex",
    justifyContent: "flex-start",
    backgroundColor: "rgb(240,240,240)",
    color: "rgb(0,123,255)",
    height: `${cardEdge * 0.2}rem`,
    padding: theme.spacing(1),
  },
  cardContent: {
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
    height: `${cardEdge * 0.8}rem`,
  },
  pullLeft: {
    marginRight: "auto",
  },
  cardIcon: {
    fontSize: "3rem",
    margin: theme.spacing(2),
  },
  countContainer: {
    display: "flex",
    flexDirection: "column",
    margin: theme.spacing(2),
  },
  count: {
    display: "flex",
    alignItems: "flex-end",
    justifyContent: "center",
  },
  countUnit: {
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "center",
  },
  countLoadingCircularProgress: {
    padding: theme.spacing(1),
  },
}));

export const DashboardCard = ({ card, count, isLoading }) => {
  const history = useHistory();
  const { vpost } = useContext(FetchContext);

  const handleTitleClick = () => {
    if (card.action.to != null) {
      history.push(card.action.to);
    } else if (card.action.fn != null) {
      card.action.fn({ history, vpost });
    }
  };

  const IconComponent = card.iconComponent;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Card className={classes.card}>
        <div className={classes.cardContent}>
          <div className={classes.pullLeft}>
            <IconComponent className={classes.cardIcon} />
          </div>

          <div className={classes.countContainer}>
            {isLoading ? (
              <CircularProgress
                className={classes.countLoadingCircularProgress}
              />
            ) : null}

            {!isLoading && count ? (
              <>
                <div className={classes.count}>
                  {" "}
                  <Typography variant="h5">{count}</Typography>
                </div>
                <div className={classes.countUnit}>
                  <Typography variant="body2">{card.countUnit}</Typography>
                </div>
              </>
            ) : null}
          </div>
        </div>

        <CardActionArea
          className={classes.cardActionArea}
          onClick={handleTitleClick}
        >
          <Typography variant="body2">{card.text}</Typography>
          <NavigateNextIcon style={{ fontSize: "1rem" }} />
        </CardActionArea>
      </Card>
    </div>
  );
};
