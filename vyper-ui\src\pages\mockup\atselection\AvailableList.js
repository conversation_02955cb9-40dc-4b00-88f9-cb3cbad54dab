import makeStyles from "@material-ui/core/styles/makeStyles";
import React from "react";

const useStyles = makeStyles({
  selectColumn: {
    marginRight: "1rem",
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  select: {
    width: "100%",
    fontSize: "1rem",
    borderColor: "#cccccc",
    height: "200px",
    maxHeight: "300px",
  },
  option: {
    paddingRight: "1rem",
  },
});

export const AvailableList = ({ title, options = [], value, onClick }) => {
  const handleClick = (e) => onClick(e.target.value);

  const classes = useStyles();

  return (
    <div className={classes.selectColumn}>
      <h3>{title}</h3>
      <select
        className={classes.select}
        size="10"
        onChange={handleClick}
        value={value}
      >
        {options.map((option) => (
          <option
            className={classes.option}
            key={option.value}
            value={option.value}
          >
            {option.engineering === "Y"
              ? `${option.value} [engineering]`
              : `${option.value}`}
          </option>
        ))}
      </select>
    </div>
  );
};
