import React, { useContext } from "react";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../vyper/FormStatus";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { HelperContext } from "src/component/helper/Helpers";

export const TurnkeyCell = ({ vyper, build, onClick }) => {
  const { canEditTurnkey } = useContext(HelperContext);
  const canEdit = canEditTurnkey(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Turnkey")
  )
    return null;

  return (
    <DataCell source={build.turnkey?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        {build.turnkey.value || "click to select"}
      </VyperLink>
    </DataCell>
  );
};
