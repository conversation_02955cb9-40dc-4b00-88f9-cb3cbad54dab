import React, { useState } from "react";
import Dialog from "@material-ui/core/Dialog";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { DataGrid } from "../universal";
import { gridActionSelectRecord } from "../universal/DataGrid/gridActions";
import { Button, DialogActions } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  paper: {
    minWidth: "1000px",
  },
  root: {
    marginBottom: theme.spacing(3),
  },
}));

export const SingleSelectionDialog = ({ children }) => {
  const classes = useStyles();

  const [data, setData] = useState([]);
  const [columns, setColumns] = useState();
  const [title, setTitle] = useState();
  const [handleSelect, setHandleSelect] = useState();
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setData([]);
    setColumns();
    setTitle();
    setOpen(false);
  };

  const onClickSelect = (event, rowData) => {
    setOpen(false);
    handleSelect(event, rowData);
  };

  const handleOpen = ({
    handleSelect,
    data = [],
    columns,
    title = "Select Supported Items",
  }) => {
    setData(data);
    setColumns(columns);
    setTitle(title);
    setHandleSelect(() => handleSelect);
    setOpen(true);
  };

  return (
    <SingleSelectionDialogContext.Provider
      value={{
        openSingleSelectionDialog: handleOpen,
      }}
    >
      <Dialog
        open={open}
        onClose={handleClose}
        classes={{ paper: classes.paper }}
        maxWidth="xl"
      >
        <DataGrid
          title={title}
          data={data}
          columns={columns}
          actions={[gridActionSelectRecord(onClickSelect)]}
          options={{
            search: false,
            pageSize: 10,
            pageSizeOptions: [5, 10, 20, 50],
          }}
        />
        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </SingleSelectionDialogContext.Provider>
  );
};

export const SingleSelectionDialogContext = React.createContext(null);
