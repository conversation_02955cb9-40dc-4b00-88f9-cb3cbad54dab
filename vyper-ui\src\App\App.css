body {
    background-color: #f7f7f7; /* http://brand.ti.com/pages/bel-color.shtml */
    overscroll-behavior-y: none;
}

a {
    text-decoration: none;
}

*,
:before,
:after {
    box-sizing: border-box;
}

.react-grid-HeaderCell {
    text-align: center;
    padding: 0px !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    background-color: rgb(204, 0, 0);
    color: white;
    vertical-align: middle;
}

.react-grid-Canvas {
    overflow-y: auto !important;
}

.row-highlight > div > div {
    background-color: #ffcccc !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

.deviceAction-highlight > div > div {
    background-color: #e0e0e0 !important;
    text-align: center !important;
    color: #333333 !important;
}

.deviceAction-highlight label.react-grid-checkbox-label {
    display:none;
}

.data-grid-cell > div {
    height: inherit !important;
}

.data-grid-cell > div > div {
    height: inherit !important;
}

.react-grid-Cell__value div {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.pred-cell-highlight > div > div:nth-of-type(8) {
    background-color: rgb(204, 0, 0) !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

.pred-cell-yellow > div > div:nth-of-type(8) {
    background-color: #faef4f !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

.duplicated-results > div > div {
    background-color: #b9e5fb !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

.legend-box {
    padding: 0px 8px 5px 8px;
}

.legend-cell {
    display: inline-block;
    border: 1px solid black;
    width: 20px;
    height: 1.3em;
    text-align: center;
    vertical-align: middle;
}

.duplicated-cell {
    background-color: #b9e5fb;
    color: white;
}

.predecessor-cell {
    background-color: rgb(204, 0, 0);
    color: white;
}

.legend-text {
    padding-left: 5px;
}

.qual-memo-rexid-row-highlight > div > div {
    background-color: #aaaaaa !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

.qual-memo-hidden-row > div > div {
    background-color: #e0e0e0 !important;
    font-family: "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

#qbsGrid div.react-grid-Row div.react-grid-Cell:nth-of-type(n+8) {
    padding-left: 0px ;
    padding-right: 0px;
    line-height: 2.5;
    text-align: center;
}

#qualMemoGrid div.react-grid-Row div.react-grid-Cell:nth-of-type(n+8) {
    padding-left: 0px ;
    padding-right: 0px;
    line-height: 2.5;
    text-align: center;
}

#reqGrid div.react-grid-Row div.react-grid-Cell div.rdg-cell-expand {
    display: none;
}

#qvGrid div.react-grid-Row div.react-grid-Cell:nth-of-type(n+1) {
    padding-left: 0px ;
    padding-right: 0px;
    line-height: 2.5;
    text-align: center;
}

#resultsGrid div.react-grid-Row div.react-grid-Cell div.rdg-cell-expand {
    display: none;
}

.worksheetSpace {
    max-width: auto;
    height: 120px;
}