import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React from "react";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { VscnPackConfigCell } from "./VscnPackConfigCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const VscnPackConfigRow = ({ vyper, vscns, build, onChange }) => {
  const classes = useStyles();

  return (
    <TableRow hover>
      <RowPrefix help="packConfig" title="Pack Config" required />
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <VscnPackConfigCell
            vyper={vyper}
            build={build}
            vscn={vscn}
            onChange={onChange}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
