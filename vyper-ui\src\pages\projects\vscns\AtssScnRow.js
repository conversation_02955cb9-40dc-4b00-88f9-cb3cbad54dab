import TableCell from "@material-ui/core/TableCell"
import TableRow from "@material-ui/core/TableRow"
import PropTypes from "prop-types"
import React from "react"
import {RowPrefix} from "../../mockup/RowPrefix"
import {VscnAtssScnCell} from "./VscnAtssScnCell"

export function AtssScnRow({items, onClickRefresh}) {

    return <TableRow hover>
        <RowPrefix help="ATSS SCN" title="ATSS SCN" required/>
        {items.map((item, n) =>
            <TableCell key={n}>
                <VscnAtssScnCell item={item} onClickRefresh = {onClickRefresh}/>
            </TableCell>
        )}
    </TableRow>

}

AtssScnRow.propTypes = {
    items: PropTypes.array.isRequired,
    onClickRefresh: PropTypes.func.isRequired
}