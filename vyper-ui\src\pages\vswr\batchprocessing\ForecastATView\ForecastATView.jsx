import React, { useContext, useRef } from "react";
import { useParams } from "react-router-dom";
import PageHeader from "../common/PageHeader";
import CircularProgress from "@material-ui/core/CircularProgress";
import { columnDefs } from "./config";
import { useAtViewGetter } from "./queries";
import VswrAgGridReact from "../common/VswrAgGridReact";
import { ForbiddenErrorPage } from "../common/HttpErrorPage";
import Button from "@material-ui/core/Button";
import GetAppIcon from "@material-ui/icons/GetApp";
import {
  AuthContext,
  VSWR_ROLE_GROUPS,
  ADMINISTRATIVE_LEVEL,
} from "src/component/common/auth";
const { AT, BTH } = VSWR_ROLE_GROUPS;
const { ADMIN } = ADMINISTRATIVE_LEVEL;

const ForecastATView = () => {
  let { swrId } = useParams();
  let atViewGetter = useAtViewGetter(swrId);
  const { authUser } = useContext(AuthContext);
  const gridRef = useRef();
  let userVswrSecurityRole = authUser?.vswrSecurity?.roleCategory;
  let userVswrAdministrativeLevel = authUser?.vswrSecurity?.administrativeLevel;

  let hasAdminAccess = userVswrSecurityRole === ADMIN;
  let hasAtAccess = userVswrSecurityRole === AT;
  let hasBthAccess = userVswrSecurityRole === BTH;

  if (!(hasAtAccess || hasBthAccess || hasAdminAccess)) {
    return <ForbiddenErrorPage />;
  }

  return (
    <div className="App BatchContainer">
      <PageHeader
        headerTitle={"Forecasted SWRs (AT View)" + (swrId ? ` - ${swrId}` : "")}
      />
      {atViewGetter.isLoading ? (
        <CircularProgress />
      ) : (
        <div>
          <Button
            onClick={() => {
              gridRef.current.api.exportDataAsCsv();
            }}
          >
            <GetAppIcon /> Download To Excel
          </Button>

          <VswrAgGridReact
            ref={gridRef}
            rowData={atViewGetter.data}
            columnDefs={columnDefs}
          />
        </div>
      )}
    </div>
  );
};
export default ForecastATView;
