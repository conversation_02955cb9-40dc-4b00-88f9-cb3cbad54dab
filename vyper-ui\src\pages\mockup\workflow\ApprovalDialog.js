import { Button, DialogActions } from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import MenuItem from "@material-ui/core/MenuItem";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TextField from "@material-ui/core/TextField";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "white",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    padding: theme.spacing(1),
  },
  required: {
    color: "red",
    fontSize: "1.5rem",
  },
}));

/**
 * called when the user clicks approve on the ApprovalDialog.
 * @callback ApprovalDialog~onApprove
 * @param {string} group - The approving group.
 * @param {string|null} comment - the approval comment, or null if no comment.
 */

/**
 * called when the user clicks cancel on the ApprovalDialog.
 * @callback ApprovalDialog~onClose
 */

/**
 * Display the A/T approval dialog.
 * @param {boolean} open - set true/false to show/hide the dialog
 * @param {string[]} approvalGroups - The list of approval groups
 * @param {ApprovalDialog~onApprove} onApprove - callback called when the use clicks approve
 * @param {ApprovalDialog~onClose} onClose - callback called when the user clicks cancel
 * @returns {JSX.Element}
 * @function
 */
export function ApprovalDialog({ open, approvalGroups, onApprove, onClose }) {
  const [group, setGroup] = useState("");
  const [comment, setComment] = useState("");

  // reset group and comment when the dialog opens
  useEffect(() => {
    if (open) {
      setGroup("");
      setComment("");
    }
  }, [open]);

  const handleChangeGroup = (e) => setGroup(e.target.value);
  const handleChangeComment = (e) => setComment(e.target.value);
  const handleClickApprove = () => onApprove(group, comment || null);

  // enable the save button when the group is set
  const canSave = group !== "";

  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className={classes.title}>Action: Approve</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <div>
          <span className={classes.required}>*</span>
          <span>Approval group:</span>
        </div>

        <TextField
          autoFocus
          fullWidth
          select
          margin="dense"
          name="group"
          value={group}
          onChange={handleChangeGroup}
          variant="outlined"
        >
          {approvalGroups.map((g) => (
            <MenuItem key={g} value={g}>
              {g}
            </MenuItem>
          ))}
        </TextField>

        <div>
          <span>(Optional) Type in reason for Approval</span>
        </div>

        <TextField
          fullWidth
          multiline
          minRows={5}
          margin="dense"
          name="comment"
          value={comment}
          onChange={handleChangeComment}
          variant="outlined"
        />
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="primary" onClick={onClose}>
          Cancel
        </Button>

        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={!canSave}
          onClick={handleClickApprove}
        >
          Approve
        </Button>
      </DialogActions>
    </Dialog>
  );
}

ApprovalDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  approvalGroups: PropTypes.array.isRequired,
  onApprove: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};
