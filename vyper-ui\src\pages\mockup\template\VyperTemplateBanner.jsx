import React from "react";
import PropTypes from "prop-types";
import { Alert, AlertTitle } from "@material-ui/lab";

/**
 * Show a banner for ATSS Templates.
 *
 * @param {object} build - The build object
 * @return {JSX.Element}
 * @constructor
 */
export const VyperTemplateBanner = ({ build }) => {
  const vyperBuildNumber = build.templateSource.vyperBuildNumber;

  return (
    <Alert severity="success">
      <AlertTitle>
        The flow for this build is based on the Vyper build:
        <strong>{vyperBuildNumber}</strong>.
      </AlertTitle>
    </Alert>
  );
};

VyperTemplateBanner.propTypes = {
  build: PropTypes.object.isRequired,
};
