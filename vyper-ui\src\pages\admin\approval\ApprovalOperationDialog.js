import {
  Button,
  Dialog,
  <PERSON>alogActions,
  <PERSON>alogContent,
  <PERSON>alogT<PERSON>le,
  TextField,
} from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import { makeStyles } from "@material-ui/core/styles";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import { noop } from "../../../component/vyper/noop";
import { DataModelsContext } from "../../../DataModel";

const useStyles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    paddingTop: 4,
    paddingBottom: 4,
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
}));

const groupTexts = ["_PREBOND", "_BOND", "_FINISH", "_TEST", "_PACK"];

/**
 * Dialog for editing Approval Operations
 *
 * @param open
 * @param approvalOperation
 * @param onUpdate
 * @param onClose
 * @returns {JSX.Element}
 * @function
 */
export const ApprovalOperationDialog = ({
  open,
  approvalOperation,
  onUpdate,
  onClose,
}) => {
  const [data, setData] = useState({});
  const { operationDao, operations } = useContext(DataModelsContext);

  // load the list of operations
  useEffect(() => {
    operationDao.list().catch(noop);
  }, []);

  // when we open the dialog, copy the approval operation to our form data
  useEffect(() => {
    if (!open) {
      return;
    }

    setData(
      approvalOperation || {
        id: undefined,
        operation: "",
        groupText: "",
      }
    );
  }, [open]);

  const handleChange = (e) => {
    setData((d) => ({ ...d, [e.target.name]: e.target.value }));
  };

  const handleClickUpdate = () => onUpdate(data);

  const canUpdate = data.operation != null && data.groupText != null;

  const classes = useStyles();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl">
      <DialogTitle className={classes.title}>
        Edit Approval Operation
      </DialogTitle>

      <IconButton className={classes.closeButton} onClick={onClose}>
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <TextField
          autoFocus
          select
          variant="outlined"
          margin="dense"
          id="operation"
          name="operation"
          label="Operation"
          fullWidth
          value={data?.operation}
          onChange={handleChange}
        >
          {operations.map((o) => (
            <option key={o} value={o}>
              {o}
            </option>
          ))}
        </TextField>

        <TextField
          select
          variant="outlined"
          margin="dense"
          id="groupText"
          name="groupText"
          label="groupText"
          fullWidth
          value={data?.groupText}
          onChange={handleChange}
        >
          {groupTexts.map((gt) => (
            <option key={gt} value={gt}>
              {gt}
            </option>
          ))}
        </TextField>
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="secondary" onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          disabled={!canUpdate}
          onClick={handleClickUpdate}
        >
          Update
        </Button>
      </DialogActions>
    </Dialog>
  );
};

ApprovalOperationDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  approvalOperation: PropTypes.object.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};
