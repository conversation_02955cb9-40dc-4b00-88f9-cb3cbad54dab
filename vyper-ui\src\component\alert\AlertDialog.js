import React, { useState } from "react";
import DialogActions from "@material-ui/core/DialogActions";
import { Button, Dialog, DialogContent, DialogTitle } from "@material-ui/core";
import DialogContentText from "@material-ui/core/DialogContentText";
import { makeStyles } from "@material-ui/styles";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import List from "@material-ui/core/List";
import { ListItem, ListItemIcon, ListItemText } from "@material-ui/core";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";

const useStyles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
}));

export const AlertDialog = ({ children }) => {
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [blockers, setBlockers] = useState([]);
  const [open, setOpen] = useState(false);
  const [childComponent, setChildComponent] = useState();
  const classes = useStyles();

  const handleOpen = ({
    title = "Alert Dialog",
    message = "",
    blockers = [],
    childComponent,
  }) => {
    setTitle(title);
    setMessage(message);
    setBlockers(blockers);
    setOpen(true);
    setChildComponent(childComponent);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <AlertDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="xl">
        <DialogTitle className={classes.title}>{title}</DialogTitle>

        <IconButton className={classes.closeButton} onClick={handleClose}>
          <CloseIcon />
        </IconButton>

        <DialogContent>
          {blockers.length > 0 && (
            <div>
              <div>
                You are unable to perform that action. Here are the reasons:
              </div>
              <List>
                {blockers.map((text) => (
                  <ListItem key={text}>
                    <ListItemIcon>
                      <ArrowRightAltIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText>{text}</ListItemText>
                  </ListItem>
                ))}
              </List>
            </div>
          )}
          {childComponent}
          <DialogContentText component="div">{message}</DialogContentText>
        </DialogContent>

        <DialogActions>
          <Button variant="contained" color="primary" onClick={handleClose}>
            OK
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </AlertDialogContext.Provider>
  );
};

export const AlertDialogContext = React.createContext(null);
