import { makeStyles } from "@material-ui/core";
import React, { useContext, useEffect, useState } from "react";
import { useHistory, useParams } from "react-router-dom";
import { FetchContext } from "../../component/fetch/VyperFetch";
import { useQueryParams } from "../../hooks/useQuery";
import { Traveler } from "../index";
import { TravelerForm } from "./components/TravelerForm";

const useStyles = makeStyles(() => ({
  root: {},
}));

/**
 * The page displays all travelers - build, pra, vscn, ...
 * @return {JSX.Element}
 * @constructor
 */
export function VyperTravelerPage() {
  // VBUILD0111022-0008 PRA0111022-0006 VSCN0111022-0012

  const [traveler, setTraveler] = useState(null);
  const { vget } = useContext(FetchContext);
  const history = useHistory();
  const { number } = useParams();
  const { type, variant, hideForm } = useQueryParams({ variant: "VYPER" });
  const classes = useStyles();

  // set showForm to true if the hideForm query param isn't in the url
  const showForm = hideForm == null;

  // if we have a number, type and variant, fetch the traveler
  useEffect(() => {
    if (type == null || number == null || variant == null) {
      return;
    }

    vget(
      `/vyper/v1/traveler/${number}?type=${type}&variant=${variant}`,
      setTraveler
    );
  }, [number, type, variant]);

  // the user selected a new number. redirect to the page
  function handleSelectNumber(type, number, variant) {
    // must have a number. type and variant are optional
    if (number === "") {
      return;
    }

    history.push(`/traveler/${number}?type=${type}&variant=${variant}`);
  }

  return (
    <div className={classes.root}>
      {showForm && (
        <div>
          <TravelerForm onSelect={handleSelectNumber} />
          <hr />
        </div>
      )}

      <div>
        <Traveler traveler={traveler} number={number} />
      </div>
    </div>
  );
}
