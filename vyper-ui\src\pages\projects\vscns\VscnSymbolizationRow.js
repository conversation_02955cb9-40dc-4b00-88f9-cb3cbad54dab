import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { VscnSymbolCell } from "./VscnSymbolCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const VscnSymbolizationRow = ({
  vyper,
  vscns,
  onChange,
  showSameness,
}) => {
  const { vscnDao } = useContext(DataModelsContext);
  const classes = useStyles();

  const samenessSymbolization = () => {
    return vscns
      .map((vscn) =>
        vscn.symbolization?.symbols
          .map((symbol) => symbol.object.name)
          .join(",")
      )
      .every((name, index, array) => name === array[0]);
  };

  const handleSave = (vscn, location, name, picture, ecat, custs) => {
    return vscnDao
      .changeSymbolization(
        vscn.vscnNumber,
        { location, name, picture },
        custs,
        ecat
      )
      .then((json) => onChange(json))
      .catch(noop);
  };

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessSymbolization(),
      })}
      hover
    >
      <RowPrefix help="symbolization" title="Symbolization" required />
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <VscnSymbolCell vyper={vyper} vscn={vscn} onSave={handleSave} />
        </TableCell>
      ))}
    </TableRow>
  );
};
