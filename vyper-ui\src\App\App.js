import React, { useEffect, useState } from "react";
import {
  AuthContextWrapper,
  theme as DefaultTheme,
  useAuth,
} from "../component/common";
import { AppToolbarUtil, ThemeUtil, UserUtil } from "@ti/simba-common-util";
import {
  createGenerateClassName,
  createTheme,
  MuiThemeProvider,
  StylesProvider,
} from "@material-ui/core/styles";
import { BrowserRouter as Router } from "react-router-dom";
import { VyperFetch } from "../component/fetch/VyperFetch";
import { Spinner } from "../component/Spinner";
import Routes from "../Routes";
import config from "../buildEnvironment";
import { Dialogs } from "../Dialogs";
import ScrollToTop from "../component/scrolltotop/ScrollToTop";
import typography from "../component/common/theme/typography";
import overrides from "../component/common/theme/overrides";
import props from "../component/common/theme/props";
import { ComponentMap } from "../component/componentmap/ComponentMap";
import { ApprovalOperation } from "../component/approvaloperation/ApprovalOperation";
import { SimbaTheme } from "../SimbaTheme";
import "./App.css";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Helper } from "src/component/helper/Helpers";
import { PraWorkFlowHelpers } from "src/pages/mockup/workflow/PraWorkflow/PraWorkFlowHelpers";
import vyperTheme from "src/vypertheme";
import { appToolbarConfig } from "./appToolbarConfig";
import { ValidateExternalUser } from "src/component/externaluser/ValidateExternalUser";
import { ErrorDialog } from "src/component/error/ErrorDialog";

const { basePath } = config;

const generateClassName = createGenerateClassName({
  productionPrefix: "vyper",
  disableGlobal: true,
  seed: "vyper",
});

const useStyles = makeStyles((theme) => ({
  page: {
    flexGrow: 1,
    padding: theme.spacing(4),
  },
}));

const App = () => {
  // Load user authorization information
  const [authLoading, authSuccess, authUser] = useAuth();

  // Hook for detecting user profile changes (to switch theme)
  const [theme, setTheme] = useState(DefaultTheme);

  useEffect(() => {
    // Subscribe to user profile information from Simba common util
    const subscription = UserUtil.getUserProfile$().subscribe((userProfile) => {
      const paletteName = userProfile.theme
        ? userProfile.theme.name
        : ThemeUtil.getDefaultPaletteName();

      const palette = ThemeUtil.getPalette(paletteName);

      const newTheme = createTheme({
        palette,
        typography,
        overrides: {
          ...overrides,
          ...vyperTheme(paletteName),
        },
        // eslint-disable-next-line no-undef
        props,
      });
      setTheme(newTheme);
    });
    return () => subscription.unsubscribe();
  }, []);

  // Set application toolbar when App mounts
  useEffect(() => {
    AppToolbarUtil.setAppToolbarConfig(
      appToolbarConfig({ authUser, basePath })
    );
  }, [authUser]);

  const classes = useStyles();

  // Load still in progress or failed
  if (authLoading || !authSuccess) {
    return null;
  }

  return (
    <SimbaTheme id="App" prefix="Vyper">
      <AuthContextWrapper authUser={authUser} authSuccess={authSuccess}>
        <ValidateExternalUser>
          <MuiThemeProvider theme={theme}>
            <ErrorDialog>
              <Spinner>
                <VyperFetch>
                  <Helper>
                    <PraWorkFlowHelpers>
                      <StylesProvider generateClassName={generateClassName}>
                        <ComponentMap>
                          <ApprovalOperation>
                            <Dialogs>
                              <Router basename={basePath}>
                                <ScrollToTop />
                                <div className={classes.page}>
                                  <Routes />
                                </div>
                              </Router>
                            </Dialogs>
                          </ApprovalOperation>
                        </ComponentMap>
                      </StylesProvider>
                    </PraWorkFlowHelpers>
                  </Helper>
                </VyperFetch>
              </Spinner>
            </ErrorDialog>
          </MuiThemeProvider>
        </ValidateExternalUser>
      </AuthContextWrapper>
    </SimbaTheme>
  );
};

export default App;
