import React from "react";
import Backdrop from "@material-ui/core/Backdrop";
import CircularProgress from "@material-ui/core/CircularProgress";
import { makeStyles } from "@material-ui/core/styles";
import { Grid } from "@material-ui/core";

export function Spinner() {
  return <CircularProgress color="primary" disableShrink />;
}

export function OverlaySpinner() {
  const useStyles = makeStyles((theme) => ({
    backdrop: {
      zIndex: theme.zIndex.drawer + 1,
      color: "#fff",
    },
  }));
  const classes = useStyles();
  return (
    <Backdrop className={classes.backdrop} open>
      <CircularProgress color="inherit" />
    </Backdrop>
  );
}

export function CenterSpinner() {
  return (
    <Grid container justify="center" alignItems="center">
      <CircularProgress color="primary" disableShrink />
    </Grid>
  );
}
