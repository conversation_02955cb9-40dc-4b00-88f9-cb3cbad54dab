/**
 * Get a list of all of the component names that are required for the vyper project
 *
 * @param builds
 * @returns {any[]|any[]}
 */
export const requiredComponentsByVyperBuilds = (builds) => {
  return [
    ...new Set(builds.map((build) => requiredComponentsByBuild(build))),
  ].sort();
};

/**
 * get a list of the required components for a single build
 *
 * @param build The build
 * @returns list of component names
 */
export const requiredComponentsByBuild = (build) => {
  let result =
    build.requiredComponents == null
      ? [
          "MB Diagram",
          "Leadframe",
          "Mount Compound",
          "Mold Compound",
          "Wire",
          "MSL",
          "CUST1",
        ]
      : [...new Set(build.requiredComponents.componentNames)].sort();

  result.push("Backgrind Required");
  if (build.backgrind?.backgrindVal === "YES") {
    result.push("Backgrind Value");
  }

  return result;
};
