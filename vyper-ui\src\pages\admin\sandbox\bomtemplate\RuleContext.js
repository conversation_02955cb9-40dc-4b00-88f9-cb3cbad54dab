import React from "react";
import { ResponsiveGrid } from "./ResponsiveGrid";

export const RuleContext = ({ ruleContext }) => {
  if (null == ruleContext) return null;
  const items = Object.entries(ruleContext)
    .map((pair) => ({ title: pair[0], value: pair[1].comment }))
    .sort((a, b) => {
      if (a.title < b.title) return -1;
      if (a.title > b.title) return 1;
      return 0;
    });
  return <ResponsiveGrid items={items} columns={1} />;
};
