import React, { useContext, useMemo } from "react";

import { ValidatedOperation } from "../../select/traveler/ValidatedOperation";
import { AuthContext } from "../../../component/common";
import { ApprovedText } from "src/pages/select/traveler/ApprovedText";

const checkIsInGroup = (compName, userRoles, reqCheckedGroups) => {
  const groupRequired = Object.keys(reqCheckedGroups).find((group) =>
    reqCheckedGroups[group].includes(compName)
  );
  return userRoles.some(
    (role) => role.groupName !== undefined && role.groupName === groupRequired
  );
};

const defaultVO = {
  isValidatable: true,
  isGroup: false,

  validatedOperation: {
    username: "",
    userid: "",
    when: "",
  },
  checked: false,
  showWarning: true,
};

const PraComponentCheck = (props) => {
  const {
    compName,
    pra,
    onClickValidate,
    approvedGroup,
    reqCheckedGroups,
    praTaskAssignments,
  } = props;
  const { authUser } = useContext(AuthContext);

  const isAlreadyApproved = useMemo(() => {
    if (Object.keys(reqCheckedGroups).length === 0 || !praTaskAssignments) {
      return false;
    }

    const parentGroup = Object.keys(reqCheckedGroups).find((group) => {
      return reqCheckedGroups[group].includes(compName);
    });

    return praTaskAssignments.some((assignment) => {
      const { current, fnctName, complete, branchName } = assignment;
      return (
        current &&
        complete &&
        branchName.includes("approve") &&
        fnctName === parentGroup
      );
    });
  }, [reqCheckedGroups, praTaskAssignments]);

  const isFinalState = useMemo(() => {
    if (!praTaskAssignments) {
      return false;
    }
    // if no current assignments, task is in final state
    return praTaskAssignments.every((assignment) => !assignment.current);
  }, [praTaskAssignments]);

  const isVerified = useMemo(() => {
    return pra.verifiers
      .filter((verifier) => verifier.name === compName)
      .every(
        (verifier) =>
          verifier.status === "FULLY_VERIFIED" ||
          (verifier.source === "SOURCE_PGS" &&
            verifier.status === "PARTIALLY_VERIFIED")
      );
  }, [pra.verifiers]);

  const vo = useMemo(() => {
    if (!pra || authUser.name === "") {
      return defaultVO;
    }
    const { username, userid, when, checked } =
      pra.validatedComponents[compName];
    const enabledUnapproved = checkIsInGroup(
      compName,
      authUser.roles,
      reqCheckedGroups
    );
    const newVO = { ...defaultVO };

    newVO.isValidatable =
      pra.state === "PRA_DRAFT" &&
      isVerified &&
      !isAlreadyApproved &&
      !isFinalState;
    newVO.enabledUnapproved = enabledUnapproved;
    newVO.validatedOperation = {
      username: username,
      userid: userid,
      when: when,
    };
    newVO.checked = checked;
    newVO.showWarning = !checked;

    return newVO;
  }, [authUser, reqCheckedGroups, isVerified, isAlreadyApproved, isFinalState]);

  const handleClickValidate = () => {
    onClickValidate(pra.vyperNumber, pra.praNumber, compName, !vo.checked);
  };

  return (
    <>
      <ValidatedOperation vo={vo} onClickValidate={handleClickValidate}>
        {pra.validatedComponents[compName].parentOperation}
      </ValidatedOperation>
      <br />
      <ApprovedText approvedGroup={approvedGroup} />
    </>
  );
};
export default PraComponentCheck;
