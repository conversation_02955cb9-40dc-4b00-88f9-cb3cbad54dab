import makeStyles from "@material-ui/core/styles/makeStyles";
import React from "react";
import { PraSourceIcon } from "src/component/sourceicon/PraSourceIcon";

const useStyles = makeStyles({
  root: {},
  goodVerified: {
    backgroundColor: "hsla(128, 65%, 30%, 1)",
    alignItems: "center",
    display: "flex",
    paddingRight: "1px",
    paddingLeft: "1px",
    paddingTop: "1px",
  },
  badVerified: {
    backgroundColor: "hsla(0, 65%, 45%, 1)",
    alignItems: "center",
    display: "flex",
    paddingRight: "1px",
    paddingLeft: "1px",
    paddingTop: "1px",
  },
});

// the list of icon names in the order they will appear - must match VerifierSource.java
const PRA_SOURCE_ICON_NAMES = [
  "SOURCE_PGS",
  "SOURCE_PAVV_COMPONENT",
  "SOURCE_ARMARC",
  "SOURCE_ATSS_GLOBAL",
  "SOURCE_ATSS_AT",
  "SOURCE_ATSS_BOM",
  "SOURCE_DIE",
  "SOURCE_ATSS_QUALIFIED",
];

const PraSourceIcons = ({ pra, title }) => {
  const classes = useStyles();

  // get the verifiers
  const verifiers = pra.verifiers[title] || [];

  return (
    <div className={classes.root}>
      {PRA_SOURCE_ICON_NAMES.map((name) => (
        <PraSourceIcon
          key={name}
          verifiers={verifiers}
          sourceName={name}
          title={title}
        />
      ))}
    </div>
  );
};

export default PraSourceIcons;
