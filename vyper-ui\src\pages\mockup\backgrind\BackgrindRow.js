import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { samenessDifference } from "src/component/sameness/sameness";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { BackgrindCell } from "./BackgrindCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const BackgrindRow = ({ vyper, builds, onChange, showSameness }) => {
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeBackgrind = (
    isBackgrindValReq,
    backgrindSelected,
    build
  ) => {
    return buildDao
      .changeBackgrind(
        vyper.vyperNumber,
        build.buildNumber,
        isBackgrindValReq,
        backgrindSelected
      )
      .then((json) => {
        onChange(json);
        return json;
      })
      .catch(noop);
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessDifference(builds),
      })}
      hover
    >
      <RowPrefix help="backgrind" title="Backgrind" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <BackgrindCell
            key={n}
            vyper={vyper}
            build={build}
            onSave={(isBackgrindValReq, backgrindSelected) => {
              return handleChangeBackgrind(
                isBackgrindValReq,
                backgrindSelected,
                build
              );
            }}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
