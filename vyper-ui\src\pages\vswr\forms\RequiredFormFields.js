export const REQUIRED_GENERAL_INFO_FIELDS = [
  { title: "Title", name: "title" },
  // {title: 'SWR Type', name: 'swrType'},
  //     {title: 'Priority', name: 'priority'},
  //     {title: 'VBuild ID', name: 'vbuildID'},
  //     {title: 'PurchaseOrder', name: 'purchaseOrder'},
  //     {title: 'Line Item', name: 'lineItem'},
  //     {title: 'IO', name: 'io'}
];

export const REQUIRED_DEVICE_INFO_FIELDS = [
  { title: "Build Quantity", name: "buildQuantity" },
  { title: "Offload Info", name: "offloadInfo" },
];

export const REQUIRED_DIE_INFO_VALS = [{ title: "Die Lot", name: "dieLots" }];

export const REQUIRED_DIE_LOT_INFO_VALS = [
  { title: "Die Lot", name: "dieLot" },
  { title: "Delivery Note", name: "deliveryNote" },
  { title: "QTY To Ship", name: "qtyToShip" },
  { title: "Date Shipped", name: "dateShipped" },
  { title: "Sap Way bill", name: "sapWaybill" },
  { title: "Material Ship Status", name: "matShipStatus" },
  { title: "Plant", name: "plant" },
];

export const REQUIRED_PACKING_REQUIREMENTS_FIELDS = [
  { title: "Sticker Type", name: "stickerType" },
  { title: "eWaiver Number", name: "eWaiver" },
  { title: "RMR/Retest Material", name: "isRetestRMR" },
  {
    title: "Disposition of Partial Finished Goods",
    name: "finishedGoodsDispo",
  },
  { title: "Disposition of Wafer Skeleton", name: "waferSkeleton" },
  { title: "Plant Code", name: "plantCode" },
  { title: "PDC Unrestricted Sale", name: "pdcUnrestrictedSale" },
];

export const REQUIRED_SHIPPING_INFO_FIELDS = [
  { title: "Attention", name: "attention" },
  { title: "Mail Station", name: "mailStation" },
  { title: "Plant", name: "plant" },
  { title: "Address", name: "address" },
  { title: "Quantity", name: "quantity" },
  { title: "State Of Finish", name: "stateOfFinish" },
  { title: "Ship Device Name", name: "shipDeviceName" },
];
