import MenuItem from "@material-ui/core/MenuItem";
import React from "react";
import { TextField } from "@material-ui/core";

const symbolFilters = [
  {
    id: "FACILITY_PKG",
    name: "Facility and Package",
  },
  {
    id: "PKG",
    name: "Package",
  },
  {
    id: "ALL",
    name: "All ",
  },
];

export const SymbolFilter = ({ filter = "", onChange, buildType }) => {

  const isMinorChange = buildType === "Minor Change";

  return (
    <TextField
      fullWidth
      select
      variant="outlined"
      label="Filter"
      name="filter"
      value={filter}
      onChange={onChange}
      disabled={isMinorChange}
      error={!filter || filter.length === 0}
      InputLabelProps={{ shrink: true }}
    >
      {symbolFilters.map((f) => (
        <MenuItem key={f.id} value={f.id}>
          {f.name}
        </MenuItem>
      ))}
    </TextField>
  );
};
