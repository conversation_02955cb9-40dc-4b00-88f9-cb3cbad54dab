import { DaoBase } from "src/component/fetch/DaoBase";
import { New, MinorChange } from "../pages/mockup/buildtype/BuildTypes";

export class PkgNicheBomTemplateDao extends DaoBase {
  constructor(params) {
    super({
      name: "PkgNicheBomTemplateDao",
      url: "/vyper/v1/packageNiche",
      ...params,
    });
  }

  fetchPkgNichesSupported(buildType) {
    return this.handleFetch("list", `/all`, "GET").then((data) => {
      const filteredData = (buildType === New || buildType === MinorChange)
      ? data.filter(
        (template) => template.flowPreferenceCode === "A")
        : data; 
      return filteredData;
    });
  }

  findBomTemplatesSupportedByPkgNiche(packageNiche) {
    return this.handleFetch(
      "findBomTemplate",
      `/?packageNiche=${packageNiche}`,
      "GET"
    );
  }
}
