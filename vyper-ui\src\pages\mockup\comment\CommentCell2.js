import PropTypes from "prop-types";
import React, { useState } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { CommentsDialog2 } from "./CommentsDialog2";

/**
 * @callback CommentCell2~onGetComments - retrieves the item's comments
 * @param {*} item - The item
 * @return {CommentsDialog2~Comment[]} - The comments
 */

/**
 * @callback CommentCell2~onSave - Called to save the comment
 * @param {*} item - The item
 * @param {string} comment - The comment to save
 * @return {CommentsDialog2~Comment[]} - The comments
 */

/**
 * Display the comment row's cell.
 *
 * @param {*} item - The item to display
 * @param {CommentCell2~onGetComments} onGetComments - Called to get the list of comments
 * @param {CommentCell2~onSave} onSave - Called to save the comment
 * @return {JSX.Element}
 * @constructor
 */
export function CommentCell2({ item, onGetComments, onSave }) {
  const [open, setOpen] = useState(false);

  function handleOpen() {
    setOpen(true);
  }

  function handleClose() {
    setOpen(false);
  }

  function handleSave(comment) {
    onSave(item, comment);
    setOpen(false);
  }

  const comments = onGetComments(item);

  // build the display message
  let message;
  if (comments.length === 1) {
    message = "1 comment";
  } else {
    message = `${comments.length} comments`;
  }

  return (
    <DataCell source={null}>
      <VyperLink onClick={handleOpen}>{message}</VyperLink>
      <CommentsDialog2
        open={open}
        comments={comments}
        onClose={handleClose}
        onSave={handleSave}
      />
    </DataCell>
  );
}

CommentCell2.propTypes = {
  item: PropTypes.any.isRequired,
  onGetComments: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
};
