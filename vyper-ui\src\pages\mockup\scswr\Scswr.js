import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import Button from "@material-ui/core/Button";
import Tooltip from "@material-ui/core/Tooltip";
import buildEnvironment from "../../../buildEnvironment";

export const Scswr = ({ vyper, build }) => {
  const copyToClipboard = (text) => {
    // https://30secondsofcode.org/browser#copytoclipboard

    let el = document.createElement("textarea");
    el.value = text;
    el.setAttribute("readonly", "");
    el.style.position = "absolute";
    el.style.left = "-9999px";
    document.body.appendChild(el);
    let selected =
      document.getSelection().rangeCount > 0
        ? document.getSelection().getRangeAt(0)
        : false;
    el.select();
    document.execCommand("copy");
    document.body.removeChild(el);
    if (selected) {
      document.getSelection().removeAllRanges();
      document.getSelection().addRange(selected);
    }
  };

  // build a url to the select page for this build

  let url = window.location.protocol + "//" + window.location.hostname;
  if (window.location.port != null) {
    url += ":" + window.location.port;
  }

  url += buildEnvironment.basePath;
  if (!url.endsWith("/")) url += "/";
  url += `projects/${vyper?.vyperNumber}/builds/${build?.buildNumber}/plain`;

  return (
    <DataCell source={null}>
      <Tooltip
        title="Copy the URL of this device's traveler to the clipboard."
        placement="top"
      >
        <Button
          color="primary"
          variant="contained"
          size="small"
          onClick={() => copyToClipboard(url)}
        >
          Copy
        </Button>
      </Tooltip>
    </DataCell>
  );
};
