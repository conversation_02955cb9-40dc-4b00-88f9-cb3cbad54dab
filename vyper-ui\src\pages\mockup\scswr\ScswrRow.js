import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import React from "react";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { Scswr } from "./Scswr";

export const ScswrRow = ({ vyper, builds }) => {
  return (
    <TableRow hover>
      <RowPrefix help="url" title="SWR URL" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <Scswr key={n} vyper={vyper} build={build} />
        </TableCell>
      ))}
    </TableRow>
  );
};
