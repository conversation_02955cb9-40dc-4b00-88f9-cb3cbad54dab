import { DaoBase } from "src/component/fetch/DaoBase";

export class <PERSON>ceFlowMapDao extends DaoBase {
  constructor(params) {
    super({
      name: "<PERSON>ce<PERSON>lowMapDao",
      url: "/vyper/v1/config/flow",
      ...params,
    });
  }

  list() {
    return this.handleFetch("list", `/`, "GET");
  }

  search(page, size) {
    return this.handleFetch(
      "search",
      `/search?page=${page}&size=${size}`,
      "GET"
    );
  }

  create(deviceFlowMap) {
    return this.handleFetch("create", ``, "POST", deviceFlowMap);
  }

  update(deviceFlowMap) {
    return this.handleFetch("update", ``, "PUT", deviceFlowMap);
  }

  delete(id) {
    return this.handleFetch("delete", `/${id}`, "DELETE");
  }
}
