import { DaoBase } from "../component/fetch/DaoBase";
import { noop } from "../component/vyper/noop";

export class OverridesDao extends DaoBase {
  constructor(params) {
    super({ name: "OverrideDao", url: "/vyper/v1/overrides", ...params });
    this.pra = params.pra || {};
    this.setPra = params.setPra || noop;
  }

  buildState(vyperNumber, buildNumber, buildState) {
    return this.handleFetch("buildState", `/buildstate`, "POST", {
      vyperNumber,
      buildNumber,
      buildState,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }
}
