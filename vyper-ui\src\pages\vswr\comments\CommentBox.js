import React from "react";
import { Grid, Typography, Paper, Tooltip } from "@material-ui/core";
import { UsernameAvatar } from "./UsernameAvatar";

export const CommentBox = ({ comment, i }) => {
  return (
    <Paper
      style={{
        padding: "10px 30px 10px ",
        marginTop: 10,
        backgroundColor: "#FAF9F6",
      }}
      key={i}
    >
      <Grid container wrap="nowrap" spacing={1} key={i}>
        <Grid item>
          <UsernameAvatar userName={comment.userName} />
        </Grid>
        <Grid item xs zeroMinWidth>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <div style={{ float: "left" }}>
              <h4 style={{ margin: 0, textAlign: "left" }}>
                {comment.userName}
              </h4>
            </div>
            <div style={{ float: "left" }}>
              <p style={{ textAlign: "right", color: "gray" }}>
                Operation Category: {comment.operation || "General"}
              </p>
            </div>
          </div>
          <p style={{ textAlign: "left" }}>{comment.comment}</p>
          <Tooltip title={comment.dttm} placement="top-start" arrow>
            <p style={{ textAlign: "left", color: "gray" }}>
              posted {comment.dttm}
            </p>
          </Tooltip>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default CommentBox;
