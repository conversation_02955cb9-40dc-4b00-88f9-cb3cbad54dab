import React, { useEffect, useState } from "react";
import { MenuItem, Select } from "@material-ui/core";
import { FlowTemplateDetailPanel } from "src/pages/admin/sandbox/bomtemplate/FlowTemplateDetailPanel";

export const FlowTemplateDetailsPanel = ({
  flowTemplateDetails,
  ruleContext,
}) => {
  const [index, setIndex] = useState(`0`);

  if (flowTemplateDetails == null) return null;
  if (ruleContext == null) return null;

  return (
    <div>
      <h3>
        Select the template: &nbsp;
        <Select value={index} onChange={(e) => setIndex(e.target.value)}>
          {flowTemplateDetails.map((detail, n) => (
            <MenuItem
              key={n}
              value={`${n}`}
            >{`${detail.templateName} (${detail.templateType})`}</MenuItem>
          ))}
        </Select>
      </h3>

      <br />
      <br />

      {flowTemplateDetails.length > 0 ? (
        <FlowTemplateDetailPanel
          flowTemplateDetail={flowTemplateDetails[index]}
          ruleContext={ruleContext}
        />
      ) : null}
    </div>
  );
};
