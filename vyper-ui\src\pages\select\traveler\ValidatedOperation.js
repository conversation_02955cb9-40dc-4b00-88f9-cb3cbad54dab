import { Checkbox } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import clsx from "clsx";
import React from "react";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles({
  errorRow: {
    backgroundColor: "hsla(0, 100%, 75%, 1)",
  },
  availableCheckbox: {
    color: "red",
  },
});

/**
 * This function verifies that the current user is allowed to approve the operation.
 *
 * @param group The group string. must be lowercase.
 * @param approvalOperation
 * @param buildFacility
 * @param sbe
 * @param sbe1
 * @param operation The object. may be null/undefined
 * @returns {boolean|*}
 */
export const checkIsInGroup = (
  group,
  approvalOperation,
  buildFacility,
  sbe,
  sbe1,
  operation
) => {
  // handle test operations.
  if (approvalOperation?.groupText === "_TEST_") {
    // match with facility_sbe_sbe1
    let text = `_${buildFacility}_TEST_${sbe}_${sbe1}`.toLowerCase();
    if (group.includes(text)) {
      return true;
    }

    // match with facility_sbe
    text = `_${buildFacility}_TEST_${sbe}`.toLowerCase();
    return group.includes(text);
  }

  // handle engineering operations - they are approved by the SCP group
  if (operation?.engineering === "Y") {
    return group.endsWith("_scp");
  }

  // match the pre-bond, bond, finish, pack groups
  return (
    group.includes(approvalOperation?.groupText?.toLowerCase()) &&
    group.includes(`_${buildFacility}_`.toLowerCase())
  );
};

/**
 * @typedef {object} ApprovalOperation
 * @property {number} id - the database record id
 * @property {string} operation - the operation name
 * @property {string} groupText - The approval's group group_text
 *
 */

/**
 * @typedef {object} ValidateOperationResult
 * @property {boolean} isEditable  - True is the build state is an editable state.
 * @property {ApprovalOperation} approvalOperation - the approval operation that matches the operation.
 * @property {boolean} isValidatable - true if the operation is validatable ((is engineering or has group_text), and not engineering deleted).
 * @property {string[]} groupNames - The list of group names associated with the current user.
 * @property {boolean} isGroup - True if the current user is allowed to approve the operation.
 * @property {boolean} enabled - True if the checkbox should be enabled, false for disabled.
 * @property {boolean} checked - true if the operation is already validated.
 * @property {boolean} showWarning true if the warning message should be displayed.
 * @property {object} validatedOperation - The same data passed into the function
 * @property {object} operation - The same data passed into the function
 */

/**
 * Creates a data structure with information on what the currently logged in user can do to the operation
 *
 * @param {string} buildState - The build's state
 * @param {string} buildFacility - The PDB Facility name
 * @param {string} sbe - The device's business SBE
 * @param {string} sbe1 - The device's business SBE1
 * @param {object} operation - The current operation
 * @param {string} operation.name - The operation's name
 * @param {object} authUser - The logged-in user
 * @param {string} authUser.uid - the user's AID or XID
 * @param {string} authUser.name - The user's name
 * @param {string[]} authUser.roles - The list of roles
 * @param {object[]} operations - The list of operations in the traveler
 *
 * @param validatedOperation
 * @param findApprovalOperationByOperation
 * @param unApprovedGroups
 * @returns {ValidateOperationResult}
 */
export const validateOperation = ({
  buildState,
  buildFacility,
  sbe,
  sbe1,
  operation,
  authUser,
  validatedOperation,
  findApprovalOperationByOperation,
  operations = [],
  unApprovedGroups = [],
}) => {
  // is the build editable (state = NEW,AT_REVIEW_CHANGE)
  const isEditable =
    buildState === "DRAFT" ||
    buildState === "AT_REVIEW_CHANGE" ||
    buildState === "REWORK";

  // is the operation validatable (group != null and not engineering)
  const approvalOperation = findApprovalOperationByOperation(operation?.name);

  // determine if this operation is duplicated in the traveler. of so, disable the checkbox
  const isDuplicated =
    operations.filter((o) => o.name === operation?.name).length > 1;

  // determine if the checkbox is enabled or disabled
  const isValidatable =
    (operation.engineering === "Y" || approvalOperation?.groupText != null) &&
    !operation.engineeringDeleted &&
    !isDuplicated;

  // can the authorized user approve the operation?
  // one of my groups contains the groupText, and also contains the facility name
  // get the rolesvc group names for the current user
  const groupNames = authUser.roles
    .filter((role) => role.groupName != null)
    .map((role) => role.groupName);

  // is the operation's group one of the user's group
  const isGroup = groupNames
    .map((group) => group.toLowerCase())
    .some((group) =>
      checkIsInGroup(
        group,
        approvalOperation,
        buildFacility,
        sbe,
        sbe1,
        operation
      )
    );

  // enable the checkbox
  const enabled = isEditable && isGroup;

  // is the checkbox checked
  const checked = validatedOperation?.when != null;

  // show the warning if the box is enabled, but not checked
  const showWarning = !checked && enabled;

  // editable and group and unapproved
  const isUnapproved = unApprovedGroups.some((group) =>
    group.includes(approvalOperation?.groupText)
  );
  const enabledUnapproved = enabled && isUnapproved;

  return {
    isEditable,
    approvalOperation,
    isValidatable,
    groupNames,
    isGroup,
    enabled,
    checked,
    showWarning,
    validatedOperation,
    operation,
    isDuplicated,
    enabledUnapproved,
  };
};

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////

export const NON_VALIDATABLE_OPERATIONS = [
  "HEADER",
  "FOOTER",
  "Device Reference",
];

export const ValidatedOperation = ({
  vo,
  operation,
  onClickValidate,
  children,
}) => {
  // if the operation is non-validatable, then don't show the checkbox / checked message
  if (NON_VALIDATABLE_OPERATIONS.includes(operation?.name)) {
    return <span>{children}</span>;
  }

  return vo.isValidatable ? (
    <EnabledCheckbox
      vo={vo}
      operation={operation}
      onClickValidate={onClickValidate}
    >
      {children}
    </EnabledCheckbox>
  ) : (
    <DisabledCheckbox vo={vo}>{children}</DisabledCheckbox>
  );
};

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////

const EnabledCheckbox = ({ vo, operation, onClickValidate, children }) => {
  const enabledUnapproved = vo.enabledUnapproved;
  const checked = vo.checked;
  const showWarning = vo.showWarning;
  const validatedOperation = vo.validatedOperation;

  const classes = useStyles();

  return (
    <span>
      <Checkbox
        className={clsx({ [classes.availableCheckbox]: enabledUnapproved })}
        checked={checked}
        disabled={!enabledUnapproved}
        onClick={() => onClickValidate(operation)}
      />

      {children}

      {checked ? (
        <CheckedByMessage validatedOperation={validatedOperation} />
      ) : null}

      {showWarning ? <WarningMessage /> : null}
    </span>
  );
};

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////

const CheckedByMessage = ({ validatedOperation }) => {
  if (externalUse) {
    return <span> - [{`Checked by ${validatedOperation?.username}`}]</span>;
  }
  return (
    <span>
      {" "}
      - [
      {`Checked by ${validatedOperation?.username}/${validatedOperation?.userid}`}
      ]
    </span>
  );
};

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////

const WarningMessage = () => {
  const classes = useStyles();
  return (
    <span>
      {" "}
      - [
      <span className={classes.errorRow}>Check to validate the operation</span>]
    </span>
  );
};

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////

const DisabledCheckbox = ({ vo, children }) => {
  const checked = vo.checked;
  const validatedOperation = vo.validatedOperation;
  const classes = useStyles();

  return (
    <span>
      <Checkbox
        className={clsx({ [classes.availableCheckbox]: vo.isGroup })}
        checked={vo.checked}
        disabled
      />
      {children}
      {checked ? (
        <CheckedByMessage validatedOperation={validatedOperation} />
      ) : null}
    </span>
  );
};
