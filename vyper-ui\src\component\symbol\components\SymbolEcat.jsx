import React from "react";
import { MenuItem, TextField } from "@material-ui/core";

const ecats = [
  "G4",
  "G3",
  "G2",
  "G1",
  "E6",
  "E5",
  "E4",
  "E3",
  "E2",
  "E1",
  "E0",
  "NA",
  "TBD",
];

export const SymbolEcat = ({ ecat = "", onChange }) => {
  return (
    <TextField
      fullWidth
      select
      variant="outlined"
      label="ECAT"
      name="ecat"
      value={ecat}
      onChange={onChange}
      error={!ecat || ecat.length === 0}
      InputLabelProps={{ shrink: true }}
    >
      {ecats.map((e) => (
        <MenuItem key={e} value={e}>
          {e}
        </MenuItem>
      ))}
    </TextField>
  );
};
