import React, { useContext, useEffect, useState } from "react";
import { MenuItem, Paper, TextField } from "@material-ui/core";
import Button from "@material-ui/core/Button";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Alert } from "@material-ui/lab";
import DescriptionTwoToneIcon from "@material-ui/icons/DescriptionTwoTone";
import pgs160 from "src/component/sourceicon/crown_160.png";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";

const useStyles = makeStyles({
  form: {
    display: "flex",
    justifyContent: "space-around",
    alignItems: "center",
    padding: ".5rem",
    marginTop: "1rem",
    border: "1px solid red",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
  },
  paper: {
    margin: "1rem",
  },
});

export const PgsSandbox = () => {
  const { facilityDao, plantCodes, sandboxDao } = useContext(DataModelsContext);

  const [device, setDevice] = useLocalStorage("playground.pgs.device", null);
  const [plantCode, setPlantCode] = useLocalStorage(
    "playground.pgs.plantCode",
    null
  );

  const [result, setResult] = useState({});

  const names = [
    "Bond",
    "Flux",
    "Leadframe",
    "MB Diagram",
    "Mold Compound",
    "Mount Compound",
    "Solder Ball",
    "Substrate",
    "Wire",
  ];

  useEffect(() => {
    facilityDao.loadPlantCodes().catch(noop);
  }, []);

  const handleChangeDevice = (e) => setDevice(e.target.value);

  const handleChangePlantCode = (e) => setPlantCode(e.target.value);

  const buttonDisabled =
    device == null || device === "" || plantCode == null || plantCode === "";

  const handleSubmit = () => {
    sandboxDao
      .pgs(device, plantCode, names)
      .then((json) => setResult(json))
      .catch(noop);
  };

  const classes = useStyles();

  return (
    <div>
      <br />

      <Alert severity="success">
        This form allows you to view the data returned from PGS, as well as how
        Vyper loaded that data into the components.
        <ul>
          <li>The components below show how Vyper parsed the data from PGS.</li>
          <li>At the bottom is the raw data returns from PGS.</li>
        </ul>
      </Alert>

      <form className={classes.form}>
        <div>
          <TextField
            label="Device"
            value={device || ""}
            onChange={handleChangeDevice}
          />
        </div>

        <div>
          <TextField
            label="Facility A/T"
            select
            value={plantCode || ""}
            onChange={handleChangePlantCode}
          >
            {plantCodes.map((plantCode) => (
              <MenuItem key={plantCode.facilityAt} value={plantCode.plantCode}>
                {plantCode.facilityAt}
              </MenuItem>
            ))}
          </TextField>
        </div>

        <div>
          <Button
            disabled={buttonDisabled}
            color="primary"
            variant="contained"
            onClick={handleSubmit}
          >
            Go
          </Button>
        </div>
      </form>

      <Paper>
        <div className={classes.paper}>
          {names.map((name, n) => (
            <div key={n}>
              <Component name={name} components={result?.build?.components} />
            </div>
          ))}

          <hr />

          <div>
            <h3>
              <img height="14px" src={pgs160} alt="pgs" /> PGS Results
            </h3>
            <pre>{JSON.stringify(result?.build?.pgs, null, "\t")}</pre>
          </div>
        </div>
      </Paper>
    </div>
  );
};

const Component = ({ name, components }) => (
  <div>
    <hr />
    <h3>
      <DescriptionTwoToneIcon /> {name}
    </h3>
    <pre>
      {JSON.stringify(
        components?.find((c) => c.name === name),
        null,
        "\t"
      )}
    </pre>
  </div>
);
