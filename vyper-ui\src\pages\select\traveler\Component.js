import React from "react";
import { ComponentNameDisplay } from "src/component/component/ComponentNameDisplay";
import { Rework } from "src/pages/select/traveler/Rework";
import { SourceIcon } from "../../../component/sourceicon/SourceIcon";
import VyperButton from "../../../component/VyperButton";
import Attributes from "./Attributes";
import { ComponentButtons } from "./buttons/ComponentButtons";
import { DiagramApproval } from "./DiagramApproval";
import { rightPad } from "./Padding";
import Paragraph from "./Paragraph";
import { custx } from "src/component/symbol/custx";

export const Component = ({
  vyper,
  build,
  options,
  disabled,
  operation,
  component,
  onEditName,
  onEditValue,
  onAdd,
  onRemove,
  priority,
  reworkedTraveler,
  onSelectDiagramApproval,
  checkDisableComponent,
}) => {
  const getComponentValue = () => {
    // if(component.required === "OPTIONAL" && !component.value && disabled){
    //     return '[select]'
    // }
    return component.value || "[select]";
  };
  const checkComponentError = () => {
    // There is no error if optional and left blank
    if (component.required === "OPTIONAL" && !component.value && disabled) {
      return false;
    }
    return component.value == null;
  };

  const value = getComponentValue();
  const space = 25 - value.length;
  const checkDisabled = () => {
    // Check the source
    let source = component.sourceValue.system.name
      ? component.sourceValue.system.name.toLowerCase()
      : "";
    switch (source) {
      case "oss":
        return disabled || source === "oss";
      case "locked_vyper":
        return disabled || source === "locked_vyper";

      default:
        return disabled;
    }
  };

  let priorityString = "0" + priority;

  // handle the special case - custx, ignore blank
  const ignoreBlank =
    custx.includes(component.name) &&
    build.components.find((c) => c.name === component.name)?.instances?.[0]
      ?.priorities?.[0]?.object?.ignoreBlank === "Y";

  return options.component === false ? null : (
    <div>
      &nbsp;&nbsp;&nbsp;
      <ComponentButtons
        vyper={vyper}
        build={build}
        options={options}
        disabled={checkDisabled()}
        operation={operation}
        component={component}
        onAdd={onAdd}
        onEdit={onEditName}
        onRemove={onRemove}
      />
      <span>
        <SourceIcon
          source={component.sourceName}
          heading={"This component was added by"}
        />
        {rightPad(component.name, 30)}:&nbsp;
        <SourceIcon
          source={component.sourceValue}
          heading={"This value was selected by"}
        />
        {ignoreBlank ? (
          <span>intentionally blank</span>
        ) : (
          <span>
            <VyperButton
              disabled={checkDisableComponent}
              onClick={() => onEditValue(operation, component)}
            >
              <ComponentNameDisplay
                error={checkComponentError()}
                name={value}
                engineering={component.engineering}
              />
            </VyperButton>
            <span>{rightPad(" ", space)}</span>
            <span>{priorityString}</span>
          </span>
        )}
        {options.rejection && (
          <Rework
            reworkedTraveler={reworkedTraveler}
            operation={operation}
            component={component}
            priority={priority}
            build={build}
          />
        )}
        <DiagramApproval
          build={build}
          component={component}
          value={value}
          operation={operation}
          onSelect={onSelectDiagramApproval}
        />
        <br />
        <Attributes attributes={component.attributes} options={options} />
        <Paragraph paragraph={component.paragraph} options={options} />
      </span>
    </div>
  );
};
