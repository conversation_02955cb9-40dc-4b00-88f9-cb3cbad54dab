import { useAxios } from "src/api/useAxios";
import { useCallback } from "react";

export const useWireMetalApi = () => {
  const { instance } = useAxios();

  const search = useCallback(
    (page = 0, size = 1000, config = {}) =>
      instance
        .get(`/vyper/v1/wiremetal/search?page=${page}&size=${size}`, config)
        .then((result) => result.data),
    []
  );

  const link = useCallback(
    (armarcValues, camsValues, config = {}) =>
      instance
        .post(`/vyper/v1/wiremetal/link`, { armarcValues, camsValues }, config)
        .then((result) => result.data),
    []
  );

  const unlink = useCallback(
    (ids, config = {}) =>
      instance
        .post(`/vyper/v1/wiremetal/unlink`, { ids }, config)
        .then((result) => result.data),
    []
  );

  return { search, link, unlink };
};
