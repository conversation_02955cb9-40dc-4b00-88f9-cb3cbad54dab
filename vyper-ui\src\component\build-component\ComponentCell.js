import React, { useContext } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { HelperContext } from "src/component/helper/Helpers";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../pages/vyper/FormStatus";
import { Component } from "./Component";

export const ComponentCell = ({
  vyper,
  build,
  title,
  onSaveBuild,
  onSavePra,
}) => {
  const { canEditComponent } = useContext(HelperContext);

  const canEdit = canEditComponent(vyper, build, title);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, title)
  ) {
    return null;
  }

  let component = build.components.find((c) => c.name === title);

  // if this is the mount compound row, and we have a flux component, use the flux component.
  if (title === "Mount Compound") {
    const c = build.components.find((c) => c.name === "Flux");
    if (c) {
      component = c;
    }
  }

  return (
    <DataCell source={component?.source}>
      <Component
        build={build}
        component={component}
        onSaveBuild={onSaveBuild}
        onSavePra={onSavePra}
        canEdit={canEdit}
      />
    </DataCell>
  );
};
