import React, { useContext, useState } from "react";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import {
  AlertDialogError<PERSON>andler,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { SpinnerContext } from "src/component/Spinner";
import { AuditDao } from "src/dao/AuditDao";
import { BuildDao } from "src/dao/BuildDao";
import { CompareDao } from "src/dao/CompareDao";
import { ComponentDao } from "src/dao/ComponentDao";
import { EslDao } from "src/dao/EslDao";
import { FacilityDao } from "src/dao/FacilityDao";
import { OperationDao } from "src/dao/OperationDao";
import { SandboxDao } from "src/dao/SandboxDao";
import { VyperDao } from "src/dao/VyperDao";
import { ApprovalOperationDao } from "./dao/ApprovalOperationDao";
import { PraDao } from "./dao/PraDao";
import { TravelerNameMapDao } from "./dao/TravelerNameMapDao";
import { VscnDao } from "./dao/VscnDao";
import { PkgNicheBomTemplateDao } from "./dao/PkgNicheBomTemplateDao";

export const DataModels = ({ children }) => {
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const [vyper, setVyper] = useState();
  const vyperDao = new VyperDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    vyper,
    setVyper,
  });

  const [build, setBuild] = useState();
  const buildDao = new BuildDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    build,
    setBuild,
  });

  const [pra, setPra] = useState();
  const praDao = new PraDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    pra,
    setPra,
  });

  const [components, setComponents] = useState([]);
  const componentDao = new ComponentDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    components,
    setComponents,
  });

  const [operations, setOperations] = useState([]);
  const operationDao = new OperationDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    operations,
    setOperations,
  });

  const [audits, setAudits] = useState([]);
  const auditDao = new AuditDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    audits,
    setAudits,
  });

  const [facilities, setFacilities] = useState([]);
  const [plantCodes, setPlantCodes] = useState([]);
  const facilityDao = new FacilityDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    facilities,
    setFacilities,
    plantCodes,
    setPlantCodes,
  });

  const [esls, setEsls] = useState([]);
  const eslDao = new EslDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    esls,
    setEsls,
  });

  const [vscn, setVscn] = useState();
  const vscnDao = new VscnDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    vscn,
    setVscn,
  });

  const compareDao = new CompareDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const sandboxDao = new SandboxDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const approvalOperationDao = new ApprovalOperationDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const travelerNameMapDao = new TravelerNameMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const pkgNicheBomTemplateDao = new PkgNicheBomTemplateDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  return (
    <DataModelsContext.Provider
      value={{
        vyperDao,
        vyper,
        buildDao,
        build,
        componentDao,
        components,
        operationDao,
        operations,
        auditDao,
        audits,
        facilityDao,
        facilities,
        plantCodes,
        eslDao,
        esls,
        compareDao,
        sandboxDao,
        praDao,
        approvalOperationDao,
        travelerNameMapDao,
        vscnDao,
        vscn,
        pkgNicheBomTemplateDao,
      }}
    >
      {children}
    </DataModelsContext.Provider>
  );
};

export const DataModelsContext = React.createContext(null);
