import Button from "@material-ui/core/Button";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { DieCell } from "./DieCell";
import { DieNumberCell } from "./DieNumberCell";
import { TypeCell } from "./TypeCell";

/**
 * Display a row of die information
 *
 * @param facility
 * @param instance
 * @param rowIndex
 * @param onChangeDie
 * @param onAddPriority
 * @param onRemoveDie
 * @param onChangeType
 * @returns {unknown[]}
 * @constructor
 */
export const DieRow = ({
  facility,
  instance,
  rowIndex,
  onChangeDie,
  onAddPriority,
  onRemoveDie,
  onChangeType,
  build,
}) => {
  let key = 0;

  const handleAddPriority = () => onAddPriority(rowIndex);

  const handleChange = (e) => onChangeType(rowIndex, e.target.value);
  const totalPriorities = instance.dies.length;

  return instance.dies.map((die, priority) => (
    <TableRow hover key={priority}>
      <TypeCell key={key++} instance={instance} onTypeChange={handleChange} />
      <DieNumberCell key={key++} rowIndex={rowIndex} />
      <TableCell>{priority + 1}</TableCell>
      <DieCell
        key={key++}
        facility={facility}
        die={die}
        rowIndex={rowIndex}
        colIndex={priority}
        onChangeDie={onChangeDie}
        onRemoveDie={onRemoveDie}
        build={build}
      />
      <TableCell>
        <Button
          variant="contained"
          color="primary"
          onClick={() => onRemoveDie(rowIndex, priority)}
        >
          Remove Die
        </Button>
      </TableCell>
      {totalPriorities === priority + 1 ? (
        <TableCell key={key++}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleAddPriority}
          >
            + Priority
          </Button>
        </TableCell>
      ) : (
        <></>
      )}
    </TableRow>
  ));
};

DieRow.propTypes = {
  facility: PropTypes.string.isRequired,
  instance: PropTypes.object.isRequired,
  rowIndex: PropTypes.number.isRequired,
  onChangeDie: PropTypes.func.isRequired,
  onAddPriority: PropTypes.func.isRequired,
  onRemoveDie: PropTypes.func.isRequired,
  onChangeType: PropTypes.func.isRequired,
};
