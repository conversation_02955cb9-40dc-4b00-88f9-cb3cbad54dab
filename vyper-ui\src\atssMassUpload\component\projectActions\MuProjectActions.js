import React, { useContext, useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON> } from "@material-ui/core";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { makeStyles, withStyles } from "@material-ui/core/styles";
import useSnackbar from "../../../hooks/Snackbar";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";
import { AuthContext } from "src/component/common/auth";
import AddIcon from "@material-ui/icons/Add";
import ActivateIcon from "@material-ui/icons/AddBox";
import ApproveIcon from "@material-ui/icons/AssignmentTurnedIn";
import ReturnIcon from "@material-ui/icons/AssignmentReturn";
import SubmitIcon from "@material-ui/icons/AssignmentInd";

import {
  fetchAllTasks,
  submitProjectToAtGroup,
  updateTask,
  approveTask,
  processTasks,
  approveOrRejectTheTask,
} from "./ProjectTasksUtility";
import { ApprovalDialog } from "../../../pages/mockup/workflow/ApprovalDialog";
import { ReworkDialog } from "../../../pages/mockup/workflow/ReworkDialog";
import TaskStatusDialog from "../../../pages/mockup/workflow/TaskStatusDialog";

const useStyles = makeStyles((theme) => ({
  actions: {
    display: "flex",
    flexWrap: "wrap",
    marginTop: "0.5rem",
    marginBottom: "1rem",
    gap: "1rem",
  },
}));

export const MuProjectActions = ({
  gridRef,
  projectInfo,
  projectDevices,
  gotoUploadPage,
  seeCamsDiff,
  redirectToMassReview,
  refreshData,
  setProjectInfo = () => {},
}) => {
  const classes = useStyles();
  const { vpost, vget } = useContext(FetchContext);
  const { enqueueErrorSnackbar } = useSnackbar();
  const { open: openAlert } = useContext(AlertDialogContext);
  const { open: openError } = useContext(ErrorDialogContext);
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { authUser } = useContext(AuthContext);
  const [tasks, setTasks] = useState([]);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showReworkDialog, setShowReworkDialog] = useState(false);
  const [taskStatusDialog, setTaskStatusDialog] = useState(false);

  /**
   * If the project changes, get the approvals
   */
  useEffect(() => {
    if (
      authUser.uid === "" ||
      projectInfo?.projNumber === "" ||
      projectInfo?.projNumber === undefined
    ) {
      return;
    }

    fetchAllTasks(projectInfo, authUser, setTasks).catch((err) =>
      console.log(err)
    );
  }, [authUser, projectInfo?.projNumber]);

  // Identify user level tasks
  const {
    unApprovedGroups,
    approvedGroups,
    isFrozen,
    unApprovedGroupsAsObjects,
    taskUser,
    isApprover,
  } = processTasks(tasks, authUser);

  const isUserOwner = projectInfo.ownerId === authUser.uid;
  const isActiveAtssTravelerEnabled =
    isUserOwner && projectInfo.projStatus === "AT APPROVED";
  const isSubmitEnabled =
    isUserOwner && projectInfo?.projStatus === "REF VALIDATED" && projectDevices?.length > 0;
  const isApproveEnabled = isApprover && projectInfo.projStatus === "AT REVIEW";
  const isRejectEnabled =
    (isApprover || isUserOwner) && projectInfo.projStatus === "AT REVIEW";
  const isApprovalHistoryEnabled = projectInfo?.praState === "PRA_APPROVED" && projectInfo?.projStatus !== "DRAFT";

  /**
   * Create working traveler creation for the selected material
   */
  const createWorkingTraveler = () => {
    const selectedDevices = gridRef.current.api.getSelectedRows();
    if (selectedDevices.length === 0) {
      enqueueErrorSnackbar("Please select at least one device for processing!");
      return;
    }

    if (
      projectInfo.projId === undefined ||
      projectInfo.projId === "" ||
      projectInfo.refSpecId === ""
    ) {
      enqueueErrorSnackbar(
        "Reference Traveler is missing. Cannot create ATSS Traveler"
      );
      return;
    }

    const requestWorkingForm = {
      projectId: projectInfo.projId,
      action: "WORKING_ATSS",
      projectDevices: selectedDevices,
    };

    vpost(
      `/vyper/v1/atssmassupload/traveler/create`,
      requestWorkingForm,
      () => {
        refreshData();
        openAlert({
          title: "Action completed",
          message:
            "ATSS Traveler creation request has been submitted for \n " +
            selectedDevices.map((row) => row.specDevice + "\n"),
        });
      }
    );
  };

  /**
    Revert Project Approval to make the changes and resubmit
  */
  const revertApproval = () => {
    openConfirmation({
        title: "Approval cancellation",
        message: "Are you sure to cancel the approval and resubmit ?",
        yesText: "Yes",
        noText: "No",
        onYes: () => {
            handleWorkflowChange("REF VALIDATED");
        },
      })
  }

  const getValidationResult = (device) => {};

  /**
    Check Change link is populated and ready status
  */
  const runActivationChecks = (projectInfo, materials) => {
    if (projectInfo?.praState !== "PRA_APPROVED") {
      enqueueErrorSnackbar("Reference Traveler PRA is not Approved yet");
      return;
    }

    // Validate each material is part of the CMS
    const hasErrors = materials
      .map((material) => getValidationResult(material))
      .filter((isSuccess) => !isSuccess);

    return hasErrors.length > 0;
  };

  const handleWorkflowChange = (status) => {
    const projUpdated = {
      projNumber: projectInfo.projNumber,
      projName: projectInfo.projName,
      projType: projectInfo.projType,
      status: status,
    };

    vpost(
      `/vyper/v1/atssmassupload/project/projNumber/${projectInfo.projNumber}/workflow`,
      projUpdated,
      () => {
        setProjectInfo({ ...projectInfo, status: status });
        openAlert({
          title: "Action completed",
          message:
            "ATSS Mass upload project " +
            projectInfo.projNumber +
            " moved to " + status,
        });
        refreshData();
      }
    );
  };

  const createProjectDeviceVscns = () => {
    return vpost(
      `/vyper/v1/atssmassupload/project/${projectInfo?.projNumber}/createVscns`,
      {
        vyperNumber: "VYPER" + projectInfo?.refVyperBuildNumber.split("-")[0].slice("VBUILD".length),
        buildNumber: projectInfo?.refVyperBuildNumber,
        praNumber: projectInfo?.refVyperPraNumber,
      },
      (response) => {
        openAlert({
          title: "Action completed (VSCN Step)",
          message:
          "The VSCNs were created for ATSS Mass upload project " + projectInfo?.projNumber
        });
      },
      (error) => {
        openError({ error, title: "Error while creating VSCNs for devices" });
      }
    );
  };

  /**
      Submit Project for AT Group Review
    */
  const submitToAt = () => {
    // Create VSCNs for all project devices
    createProjectDeviceVscns()
    .then(() => {
      submitProjectToAtGroup(projectInfo, projectDevices, authUser, "Submit")
      .then(() => handleWorkflowChange("AT REVIEW"))
      .catch((error) => {
        openError({ error, title: "Submit for Approval Error" });
      });
    })
    .catch((error) => {
      openError({ error, title: "Submit for Approval Error" });
    })
  };
  /**
    Request for ATSS Active traveler creation
    The rules must be satisfied before we can push to ATSS
  */
  const activateScn = () => {
    const selectedDevices = gridRef.current.api.getSelectedRows();
    if (selectedDevices.length === 0) {
      enqueueErrorSnackbar(
        "Please select at least one material for processing!"
      );
      return;
    }

    if (
      projectInfo.projId === undefined ||
      projectInfo.projId === "" ||
      projectInfo.refSpecId === ""
    ) {
      enqueueErrorSnackbar(
        "Reference Traveler is missing. Cannot create ATSS Traveler"
      );
      return;
    }

    const isMaterialReadyForActivation = runActivationChecks(
      projectInfo,
      selectedDevices
    );
    if (!isMaterialReadyForActivation) {
      openAlert({
        title: "Unable to perform the action",
        message: "Some validation checks failed for ATSS activation",
      });
      return;
    }

    const requestWorkingForm = {
      projectId: projectInfo.projId,
      action: "ACTIVE_ATSS",
      projectDevices: selectedDevices,
    };

    vpost(
      `/vyper/v1/atssmassupload/traveler/create`,
      requestWorkingForm,
      () => {
        openAlert({
          title: "Action completed",
          message:
            "ATSS Traveler creation request has been submitted for \n " +
            selectedDevices.map((row) => row.specDevice + "\n"),
        });
      }
    );
  };

  const approveTask = (group, comment) => {
    approveOrRejectTheTask(
      tasks,
      authUser,
      "Approve",
      comment,
      null,
      group
    ).then((data) => {
      setShowApprovalDialog(false);
      refreshData();
    });
  };

  const rejectTask = (group, reason, comment) => {
    approveOrRejectTheTask(
      tasks,
      authUser,
      "Reject",
      comment,
      reason,
      group
    ).then((data) => {
      setShowReworkDialog(false);
      handleWorkflowChange("REF VALIDATED");
    });
  };

  const refreshDeviceScnStatusByProject = () => {
    return vpost(`/vyper/v1/atssmassupload/project/${projectInfo?.projNumber}/devices/refreshScnStatus`,
      {
        projId: projectInfo?.projId
      },
      (data) => {
        return data;
      }
    );
  }

  const refresh = () => {
    refreshDeviceScnStatusByProject().then((data) => {
      refreshData(data);
    });
  }

  return (
    <div className={classes.actions}>
      <ApprovalDialog
        open={showApprovalDialog}
        approvalGroups={unApprovedGroups}
        onApprove={approveTask}
        onClose={() => {
          setShowApprovalDialog(false);
        }}
      />
      <ReworkDialog
        open={showReworkDialog}
        reworkGroups={unApprovedGroups}
        onRework={rejectTask}
        onClose={() => setShowReworkDialog(false)}
      />
      <TaskStatusDialog
        open={taskStatusDialog}
        contextKey={`VSCN_Approval~MUProjectNumber~${projectInfo.projNumber}`}
        handleClose={() => setTaskStatusDialog(false)}
      />
      { isUserOwner && projectInfo?.projStatus !== "AT REVIEW" &&
        <Button
          variant="contained"
          color="primary"
          onClick={createWorkingTraveler}
          startIcon={<AddIcon />}
        >
          Working SCN
        </Button>
      }
      
      {isActiveAtssTravelerEnabled && (
        <Button
          variant="contained"
          color="primary"
          onClick={activateScn}
          startIcon={<ActivateIcon />}
        >
          Activate SCN
        </Button>
      )}
      {isSubmitEnabled && (
        <Button
          variant="contained"
          color="primary"
          onClick={submitToAt}
          startIcon={<SubmitIcon />}
        >
          Submit
        </Button>
      )}
      {isApproveEnabled && (
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowApprovalDialog(true)}
          startIcon={<ApproveIcon />}
        >
          Approve
        </Button>
      )}
      {isRejectEnabled && isApprover && (
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowReworkDialog(true)}
          startIcon={<ReturnIcon />}
        >
          { "Reject" }
        </Button>
      )}
      {isRejectEnabled && !isApprover && (
          <Button
            variant="contained"
            color="primary"
            onClick={revertApproval}
            startIcon={<ReturnIcon />}
          >
            {"Rework"}
          </Button>
      )}
      <Button
        variant="contained"
        color="primary"
        onClick={redirectToMassReview}
      >
        Mass Review
      </Button>
      <Button variant="contained" color="primary" onClick={seeCamsDiff}>
        CAMS Diff
      </Button>
      { isApprovalHistoryEnabled &&
          <Button
            variant="contained"
            color="primary"
            onClick={() => setTaskStatusDialog(true)}
          >
            Approval History
          </Button>
      }

      {isActiveAtssTravelerEnabled && (
          <Button
            variant="contained"
            color="primary"
            onClick={revertApproval}
            startIcon={<ReturnIcon />}
          >
            Revert Approval
          </Button>
        )}
      <Button
        variant="contained"
        color="secondary"
        onClick={gotoUploadPage}
      >
        Back to Upload
      </Button>
      <Button
        variant="contained"
        color="secondary"
        onClick={refresh}
      >
        Refresh
      </Button>

    </div>
  );
};
