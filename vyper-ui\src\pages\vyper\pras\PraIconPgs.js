import PropTypes from "prop-types";
import React from "react";
import pgs160 from "src/component/sourceicon/crown_160.png";
import { choosePraIconStyle } from "src/pages/vyper/pras/praHelpers";

/**
 * Display the PGS icon
 *
 * @param verifier
 * @param hover
 * @param onClick
 * @returns {JSX.Element}
 * @function
 */
export const PraIconPgs = ({ verifier, hover, onClick }) => {
  const base = {
    padding: "3px",
    cursor: "pointer",
  };

  const style = choosePraIconStyle(verifier.status, base);

  // hack to adjust pgs icon size for firefox browser
  // seems that the problem has gone away, so it may have been a firefox bug
  // no longer needed but i'm keeping this here just in case.
  //
  // const isFirefox = typeof InstallTrigger !== 'undefined';
  // const size = (isFirefox) ? 24 : 24;
  // console.log('isFirefox', isFirefox)

  return (
    <img
      style={style}
      src={pgs160}
      alt="pgs"
      height={24}
      width={24}
      onClick={onClick}
      title={hover}
    />
  );
};

PraIconPgs.propTypes = {
  verifier: PropTypes.object.isRequired,
  hover: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
};
