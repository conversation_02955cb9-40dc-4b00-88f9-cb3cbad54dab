import { requiredComponentsByBuild } from "src/component/required/requiredComponents";
import { custx } from "src/component/symbol/custx";
import { MinorChang<PERSON>, New } from "src/pages/mockup/buildtype/BuildTypes";

// build state
// needs to match com.ti.specteam.vyper.build.model.BuildState.java
const STATE_NEW = "DRAFT";
const STATE_AT_REVIEW_CHANGE = "AT_REVIEW_CHANGE";
const STATE_BU_REVIEW_CHANGE = "BU_REVIEW_CHANGE";
const STATE_FINAL_APPROVED = "FINAL_APPROVED";
const STATE_REWORK = "REWORK";
const STATE_CANCELED = "CANCELED";

export const hasMaterial = (build) => {
  return build.material.object?.Material != null;
};

export const hasFacility = (build) => {
  return build.facility.object?.PDBFacility != null;
};

export const hasPackageNiche = (build) => {
  return build.packageNiche?.name != null;
};

export const hasBomTemplate = (build) => {
  if (build.templateSource?.templateType !== "DEVICE_PKGNICHE") {
    return true;
  }

  const hasGlobal = build.bomTemplate?.object?.name?.includes("(GLOBAL)");
  const hasBom = build.bomTemplate?.object?.name?.includes("(BOM)");
  return hasGlobal && hasBom;
};

export const existsInFlow = (build, rowName) => {
  const flowRows = build.buildFlow?.flowRows;
  if (flowRows && flowRows.length > 0) {
    return (
      flowRows.filter((flowRowConfig) => rowName === flowRowConfig.opnName)
        .length > 0
    );
  }
  return true;
};

export const stateIsNew = (build) => {
  return build.state === STATE_NEW || build.state === "NEW";
};

export const stateIsRework = (build) => {
  return build.state === STATE_REWORK;
};

export const descriptionIsSet = (build) => {
  return build.description != null;
};

export const currentUserIsOwner = (vyper, authUser) => {
  return vyper.owners.some((usr) => usr.userid === authUser.uid);
};

export const dieIsSet = (build) => {
  return existsInFlow(build, "Dies") && existsInFlow(build, "Backgrind")
    ? build.dies.dieInstances[0]?.dies[0]?.name != null
    : true;
};

export const diesHaveThicknesses = (build) => {
  return existsInFlow(build, "Dies") && existsInFlow(build, "Backgrind")
    ? build.dies.dieInstances.every((i) =>
        i.dies.every((d) => d.incomingWaferThick != null)
      )
    : true;
};

export const testIsSet = (build) => {
  return existsInFlow(build, "Test")
    ? build.turnkey.value !== "TKY" || build.test.content != null
    : true;
};

export const symbolizationIsSet = (build) => {
  return existsInFlow(build, "Symbolization")
    ? build.symbolization.symbols[0]?.object?.name != null
    : true;
};

export const eslIsSet = (build) => {
  return existsInFlow(build, "ESL") ? build.esl.object.value != null : true;
};

export const turnKeyIsSet = (build) => {
  return existsInFlow(build, "Turnkey") ? build.turnkey.value != null : true;
};

export const packConfigIsSet = (build) => {
  return existsInFlow(build, "Pack Config")
    ? build.packConfig.object.value != null
    : true;
};

export const dryBakeIsSet = (build) => {
  return existsInFlow(build, "Dry Bake")
    ? build.dryBake.object.value != null
    : true;
};

export const isBackgrindRequiredIsSet = (build) => {
  return existsInFlow(build, "Backgrind")
    ? build.backgrind?.backgrindVal != null
    : true;
};

export const isBackgrindSelectedReqSet = (build) => {
  return build.backgrind?.backgrindVal === "YES" &&
    existsInFlow(build, "Backgrind")
    ? build.backgrind?.backgrindSelected != null &&
        build.backgrind?.backgrindSelected?.length !== 0
    : true;
};

export const waferSawMethodIsSet = (build) => {
  return existsInFlow(build, "Wafer Saw Method")
    ? build.waferSawMethod?.object?.value != null
    : true;
};

export const submitBlockers = (build, vyper, authUser) => {
  const blockers = [];
  if (!(stateIsNew(build) || stateIsRework(build))) {
    blockers.push("State is not new or reject.");
  }
  if (!descriptionIsSet(build)) {
    blockers.push("A build description is not set.");
  }
  if (!currentUserIsOwner(vyper, authUser)) {
    blockers.push("You are not an owner.");
  }
  if (!hasMaterial(build)) {
    blockers.push("The material has not been set.");
  }
  if (!hasFacility(build)) {
    blockers.push("The facility has not been set.");
  }
  if (!hasPackageNiche(build)) {
    blockers.push("The package niche has not been set.");
  }
  if (!hasBomTemplate(build)) {
    blockers.push("The Bill of Process Template has not been set.");
  }
  if (!dieIsSet(build)) {
    blockers.push("Die has not been set.");
  }
  if (!diesHaveThicknesses(build)) {
    blockers.push("Die is missing incoming wafer thickness.");
  }
  if (!testIsSet(build)) {
    blockers.push("Test information has not been uploaded.");
  }

  build.components
    .filter((c) => requiredComponentsByBuild(build).includes(c.name))
    .filter(
      (c) =>
        (c.instances?.[0]?.priorities?.[0]?.object?.name == null ||
          c.instances?.[0]?.priorities?.[0]?.object?.name === "") &&
        existsInFlow(build, c.name)
    )
    .forEach((c) => blockers.push(`Component ${c.name} has not been set.`));

  if (!symbolizationIsSet(build)) {
    blockers.push("The symbolization has not been set.");
  }
  if (!eslIsSet(build)) {
    blockers.push("The ESL has not been set.");
  }
  if (!turnKeyIsSet(build)) {
    blockers.push("The turnkey has not been set.");
  }
  if (!packConfigIsSet(build)) {
    blockers.push("The pack config has not been set.");
  }
  if (!dryBakeIsSet(build)) {
    blockers.push("The dry bake has not been set.");
  }
  if (!isBackgrindRequiredIsSet(build)) {
    blockers.push("Backgrind Required has not been set.");
  }
  if (isBackgrindRequiredIsSet(build) && !isBackgrindSelectedReqSet(build)) {
    blockers.push("Backgrind Value has not been set.");
  }
  if (!waferSawMethodIsSet(build)) {
    blockers.push("Wafer Saw Method has not been set.");
  }

  // custX
  // find the custx components
  // filter out any that are not found
  // filter out any that have values set
  // filter out any that have ignoreBlank set to Y
  // remaining components need their values set.

  custx
    .map((name) => build.components.find((c) => c.name === name))
    .filter((c) => c != null)
    .filter((c) => !c.instances?.[0]?.priorities?.[0]?.object?.name)
    .filter(
      (c) => c.instances?.[0]?.priorities?.[0]?.object?.ignoreBlank !== "Y"
    )
    .forEach((c) => blockers.push(`${c.name} has not been set.`));

  // ecat

  build.components
    .filter((c) => "ECAT" === c.name)
    .filter(
      (c) =>
        c.instances?.[0]?.priorities?.[0]?.object?.name == null ||
        c.instances?.[0]?.priorities?.[0]?.object?.name === ""
    )
    .map((c) => `${c.name} has not been set.`)
    .forEach((text) => blockers.push(text));

  // VYPER-1696
  // If build type is NEW, then don't submit any component values with "PLACEHOLDER" text

  const placeholder = "PLACEHOLDER";

  if (build.buildtype === New || build.buildtype === MinorChange) {
    build.components.forEach((c) => {
      c.instances
        .flatMap((i) => i.priorities)
        .filter(
          (p) =>
            p.object.name?.toUpperCase()?.includes(placeholder) ||
            p.object.PartNumber?.toUpperCase()?.includes(placeholder)
        )
        .forEach(() =>
          blockers.push(
            `Placeholder value for ${c.name} is not allowed for ${build.buildtype} buildType.`
          )
        );
    });
  }

  return blockers;
};
