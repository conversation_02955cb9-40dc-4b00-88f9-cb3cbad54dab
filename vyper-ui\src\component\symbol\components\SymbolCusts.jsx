import React from "react";
import { SymbolCust } from "src/component/symbol/components/SymbolCust";

export const SymbolCusts = ({ custs, onChange, lengths }) => {
  const handleChange = (cust) => {
    onChange(custs.map((c) => (c.name === cust.name ? cust : c)));
  };

  const num = custs.length;
  const numIgnored = custs.reduce(
    (acc, cur) => acc + (cur.ignoreBlank === "Y" ? 1 : 0),
    0
  );

  const canIgnoreAnother = num - numIgnored > 1;

  return (
    <>
      {custs.map((cust) => (
        <div key={cust.name} style={{ marginTop: "16px" }}>
          <SymbolCust
            cust={cust}
            canIgnoreAnother={canIgnoreAnother}
            onChange={handleChange}
            length={lengths[cust.name]}
          />
        </div>
      ))}
    </>
  );
};
