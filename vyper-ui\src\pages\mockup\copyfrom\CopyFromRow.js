import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext, useState } from "react";
import { samenessDifference } from "src/component/sameness/sameness";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { CopyFromCell } from "./CopyFromCell";
import { CopyFromDialog } from "src/pages/mockup/copyfrom/CopyFromDialog";
import { ArmarcDialog } from "../../../component/armarc/ArmarcDialog";
import { VyperCopyDialog } from "../../../component/vypercopy/VyperCopyDialog";
import { AtssTravelerDialog } from "src/component/atss/AtssTravelerDialog";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const CopyFromRow = ({ vyper, builds, onChange, showSameness }) => {
  const { buildDao } = useContext(DataModelsContext);
  const [openCopyFrom, setOpenCopyFrom] = useState(false);
  const [openArmarc, setOpenArmarc] = useState(false);
  const [openAtss, setOpenAtss] = useState(false);
  const [openVyper, setOpenVyper] = useState(false);
  const [build, setBuild] = useState();
  const classes = useStyles();

  const handleClick = (build) => {
    setBuild(build);
    setOpenCopyFrom(true);
  };

  const handleClickArmarc = () => {
    setOpenArmarc(true);
    setOpenCopyFrom(false);
  };

  const handleClickAtss = () => {
    setOpenAtss(true);
    setOpenCopyFrom(false);
  };

  const handleClickVyper = () => {
    setOpenVyper(true);
    setOpenCopyFrom(false);
  };

  const handleSaveArmarc = (armarc, build) => {
    // convert the Armarc entity to the ArmarcObject
    const armarcObject = {
      autocomplete: armarc.autocomplete,
      jobid: armarc.jobId,
      submitttime: armarc.submitTime,
      lastchangetime: armarc.lastChangeTime,
      queue: armarc.queue,
      device: armarc.device,
      requesttype: armarc.requestType,
      automotive: armarc.automotive,
      diestatus: armarc.dieStatus,
      atsite: armarc.atSite,
      pkg: armarc.pkg,
      pkgtype: armarc.packageType,
      rulesfile: armarc.rulesFile,
      submitter: armarc.submitter,
      pkgreviewer: armarc.packageReviewer,
      atreviewer: armarc.atReviewer,
      pkgdisposition: armarc.packageDisposition,
      atdisposition: armarc.atDisposition,
      leadframe: armarc.leadframe,
      lfpref: armarc.lfPref,
      pin: armarc.pin,
      mbedge: armarc.mbEdge,
      wiredia: armarc.wireDiameter,
      wiretype: armarc.wireType,
      pesignoff: armarc.peSignoff,
      qasignoff: armarc.qaSignoff,
      pkggroup: armarc.packageGroup,
      processtech: armarc.processTech,
      pkgrequestid: armarc.packageRequestID,
      bomset: armarc.bomSet,
      mountcompound: armarc.mountCompound,
      moldcompound: armarc.moldCompound,
      msl: armarc.msl,
      processstack: armarc.processStack,
      bumpstack: armarc.bumpStack,
      bumptech: armarc.bumpTech,
      bumptype: armarc.bumpType,
      sitech: armarc.siTech,
    };

    return buildDao
      .changeArmarc(vyper.vyperNumber, build.buildNumber, armarcObject)
      .then((json) => onChange(json))
      .then(() => setOpenArmarc(false))
      .catch(noop);
  };

  const handleSaveAtss = (material, facilityAt, status) => {
    return buildDao
      .changeAtss(vyper.vyperNumber, build.buildNumber, {
        material,
        facilityAt,
        status,
      })
      .then((json) => onChange(json))
      .then(() => setOpenAtss(false))
      .catch(noop);
  };

  const handleSaveVyper = (srcBuildNumber) => {
    return buildDao
      .changeVyper(vyper.vyperNumber, build.buildNumber, srcBuildNumber)
      .then((json) => onChange(json))
      .then(() => setOpenVyper(false))
      .catch(noop);
  };

  return (
    <>
      <TableRow
        className={clsx({
          [classes.difference]: showSameness && !samenessDifference(builds),
        })}
        hover
      >
        <RowPrefix help="copyfrom" title="Copy From" />
        {builds.map((build, n) => (
          <TableCell key={n}>
            <CopyFromCell vyper={vyper} build={build} onClick={handleClick} />
          </TableCell>
        ))}
      </TableRow>

      <CopyFromDialog
        open={openCopyFrom}
        onClose={() => setOpenCopyFrom(false)}
        onClickArmarc={handleClickArmarc}
        onClickAtss={handleClickAtss}
        onClickVyper={handleClickVyper}
      />

      {build && (
        <ArmarcDialog
          open={openArmarc}
          onClose={() => setOpenArmarc(false)}
          onSave={handleSaveArmarc}
          build={build}
        />
      )}

      {build && (
        <AtssTravelerDialog
          open={openAtss}
          defaultSpecDevice={build?.material?.object?.OldMaterial}
          defaultFacilityAt={build?.facility?.object?.PDBFacility}
          defaultStatus="ACTIVE"
          onClose={() => setOpenAtss(false)}
          onSelect={handleSaveAtss}
          title="Copy From ATSS"
        />
      )}

      {build && (
        <VyperCopyDialog
          open={openVyper}
          onClose={() => setOpenVyper(false)}
          onSave={handleSaveVyper}
          build={build}
        />
      )}
    </>
  );
};
