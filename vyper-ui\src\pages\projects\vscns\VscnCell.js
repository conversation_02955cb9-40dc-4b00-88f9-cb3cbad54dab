import PropTypes from "prop-types";
import React, { useContext } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { VscnDisplay } from "./VscnDisplay";
import { AtSelectionDialogContext } from "../../mockup/atselection/AtSelectionDialog";
import { ComponentMapContext } from "../../../component/componentmap/ComponentMap";
import { DataModelsContext } from "../../../DataModel";
import { noop } from "src/component/vyper/noop";

/**
 * Display the values for a PRA component
 *
 * @param vscn - The VSCN object
 * @param name - The component name
 * @param onSave - Callback for saving component value updates
 * @returns {JSX.Element}
 * @function
 */
export const VscnCell = ({ vscn, name, onChange, canEdit, build }) => {
  const { showSelectionDialog } = useContext(AtSelectionDialogContext);
  const { componentMaps } = useContext(ComponentMapContext);
  const { vscnDao } = useContext(DataModelsContext);

  // get the verifiers for the component
  const verifiers = vscn.verifiers.filter((verifier) => verifier.name === name);

  const component = vscn.components.find(
    (component) => component.name === name
  );

  let values = [];
  // get the component values
  if (component?.instances[0]?.priorities != undefined) {
    values = Object.keys(component?.instances[0]?.priorities)
      .map((key) => component?.instances[0]?.priorities[key])
      .map((priority) => priority?.object?.name);
  }

  // get the verifiers that match the value
  const valueVerifiers = (verifiers, value) =>
    verifiers.filter((verifier) => verifier.value === value);

  // get the engineering for the value
  const getEngineering = (verifiers, value) =>
    verifiers.find((verifier) => verifier.value === value)?.engineering || "N";

  // the user clicked the link for the value. show the dialog
  // noinspection JSUnusedLocalSymbols\
  const handleClick = () => {
    // if this is an eng component, change to the vyper component name

    let cName = name;
    const componentMap = componentMaps.find(
      (cm) => cm.engineeringAtssComponentName === cName
    );
    if (componentMap != null) {
      cName = componentMap.name;
    }

    const component = vscn.components.find(
      (component) => component.name === cName
    );

    // get the selected items
    const selectionItems = Object.keys(component?.instances[0]?.priorities)
      .map((key) => component?.instances[0]?.priorities[key])
      .map((priority) => {
        const obj = {
          engineering: priority.engineering,
          source: priority.source,
          value: priority.object?.name,
        };
        return obj;
      });

    // get the supplier part number / wire
    let supplierOrWire = vscn.components
      .find((c) => c.name === cName)
      ?.instances?.[0]?.priorities?.[0]?.object?.name?.replace(
        "Placeholder Supplier #: ",
        ""
      );

    // show the a/t selection dialog
    showSelectionDialog({
      selection: {},
      build,
      name: cName,
      selectionItems,
      supplierOrWire,
      disableAddEngineering: true,
      allowParentChildDies: true,
      praNumber: vscn.praNumber,
      onSave: (selection) => {
        return vscnDao
          .changeComponents(
            vscn.vyperNumber,
            vscn.vscnNumber,
            selection.name,
            selection.items
          )
          .then((json) => onChange(json))
          .catch(noop);
      },
    });
  };

  return (
    <DataCell source={null}>
      {values.map((value) => (
        <div key={value}>
          <VscnDisplay
            vscn={vscn}
            name={name}
            value={value}
            verifiers={valueVerifiers(verifiers, value)}
            engineering={getEngineering(verifiers, value)}
            onClick={handleClick}
            canEdit={canEdit}
          />
        </div>
      ))}
    </DataCell>
  );
};

VscnCell.propTypes = {
  vscn: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
  canEdit: PropTypes.func.isRequired,
  build: PropTypes.object.isRequired,
};
