import React, { useState } from "react";
import Paper from "@material-ui/core/Paper";
import TextField from "@material-ui/core/TextField";
import Button from "@material-ui/core/Button";
import Grid from "@material-ui/core/Grid";

import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles((theme) => ({
  paper: {
    width: "max-content",
    marginTop: "30px",
    padding: "20px",
  },
}));

const SimpleSubmit = (props) => {
  const { label, handleSubmit } = props;
  const classes = useStyles();
  const [submitText, setSubmitText] = useState("");

  return (
    <Grid
      container
      spacing={0}
      direction="column"
      alignItems="center"
      justifyContent="flex-start"
    >
      <Paper elevation={24} className={classes.paper}>
        <Grid
          container
          spacing={3}
          direction="row"
          alignItems="center"
          justifyContent="flex-start"
        >
          <Grid item>
            <TextField
              fullWidth
              required={true}
              variant="outlined"
              value={submitText}
              onChange={(e) => setSubmitText(e.target.value)}
              name={"submitText"}
              label={label}
            />
          </Grid>
          <Grid item>
            <Button
              disabled={submitText.trim().length === 0}
              onClick={() => handleSubmit(submitText)}
              variant="contained"
              color="secondary"
            >
              Submit {label}
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  );
};
export default SimpleSubmit;
