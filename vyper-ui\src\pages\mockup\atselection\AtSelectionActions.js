import React from "react";
import Button from "@material-ui/core/Button";
import makeStyles from "@material-ui/core/styles/makeStyles";
import ArrowUpwardIcon from "@material-ui/icons/ArrowUpward";
import ArrowDownwardIcon from "@material-ui/icons/ArrowDownward";
import ArrowForwardIcon from "@material-ui/icons/ArrowForward";
import SubdirectoryArrowRightIcon from "@material-ui/icons/SubdirectoryArrowRight";
import DeleteIcon from "@material-ui/icons/Delete";

const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  actionButton: {
    margin: "1rem",
    width: theme.spacing(16),
  },
}));

/**
 * The component shows a column of buttons used for the A/T selection actions
 *
 * @param canSelect
 * @param onSelect
 * @param canUnSelect
 * @param onUnSelect
 * @param canReplace
 * @param onReplace
 * @param canMoveUp
 * @param onMoveUp
 * @param canMoveDown
 * @param onMoveDown
 * @param canHaveMultiplePriorities
 * @returns {JSX.Element}
 * @constructor
 */
export const AtSelectionActions = ({
  canSelect,
  onSelect,
  canUnSelect,
  onUnSelect,
  canReplace,
  onReplace,
  canMoveUp,
  onMoveUp,
  canMoveDown,
  onMoveDown,
  canHaveMultiplePriorities,
}) => {
  // determine if the add button is enabled or disabled
  const disableAdd = !canSelect || !canHaveMultiplePriorities;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <h3>Actions</h3>

      <Button
        className={classes.actionButton}
        variant="contained"
        color="primary"
        startIcon={<ArrowForwardIcon />}
        disabled={!canReplace}
        onClick={onReplace}
      >
        Replace
      </Button>

      <Button
        className={classes.actionButton}
        variant="contained"
        color="primary"
        startIcon={<SubdirectoryArrowRightIcon />}
        disabled={disableAdd}
        onClick={onSelect}
      >
        Add
      </Button>

      <Button
        className={classes.actionButton}
        variant="contained"
        color="primary"
        startIcon={<DeleteIcon />}
        disabled={!canUnSelect}
        onClick={onUnSelect}
      >
        Remove
      </Button>

      <Button
        className={classes.actionButton}
        variant="contained"
        color="primary"
        startIcon={<ArrowUpwardIcon />}
        disabled={!canMoveUp}
        onClick={onMoveUp}
      >
        Up
      </Button>

      <Button
        className={classes.actionButton}
        variant="contained"
        color="primary"
        startIcon={<ArrowDownwardIcon />}
        disabled={!canMoveDown}
        onClick={onMoveDown}
      >
        Down
      </Button>
    </div>
  );
};
