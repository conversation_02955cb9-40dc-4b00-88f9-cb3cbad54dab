import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { hasFacility, hasMaterial } from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";

export const CopyFromCell = ({ vyper, build, onClick }) => {
  const { canEditCopyFrom } = useContext(HelperContext);
  const canEdit = canEditCopyFrom(vyper, build);

  if (!hasMaterial(build) || !hasFacility(build)) return null;

  let text;
  switch (build.vyperCopiedFrom.type) {
    case "ARMARC":
      text = build.vyperCopiedFrom.type + ": " + build.armarc.object.jobid;
      break;

    case "ATSS":
      text =
        build.vyperCopiedFrom.type +
        ": " +
        build.atss.object.facilityAt +
        " " +
        build.atss.object.material +
        " " +
        build.atss.object.status;
      break;

    case "VYPER":
      text =
        build.vyperCopiedFrom.type +
        ": " +
        build.vyperCopiedFrom?.facility?.object?.PDBFacility +
        " " +
        build.vyperCopiedFrom?.material?.object?.Material +
        " " +
        build.vyperCopiedFrom?.vyperBuildNumber;
      break;

    default:
      text = "click to select";
      break;
  }

  //         <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
  return (
    <>
      <DataCell source={build.vyperCopiedFrom?.sourceGlobal}>
        <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
          {text}
        </VyperLink>
      </DataCell>
    </>
  );
};
