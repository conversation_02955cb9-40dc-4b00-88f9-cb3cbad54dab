import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import { MaterialAutocomplete } from "../../autocomplete/components/MaterialAutocomplete";
import PropTypes from "prop-types";

export const SelectMaterialDialog = ({
  material,
  setMaterial,
  open,
  setOpen,
  onSave,
  title,
  label = "Select OPN/Material Name",
}) => {
  const styles = makeStyles((theme) => ({
    closeButton: {
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
  }));

  const classes = styles();

  const handleClose = () => {
    setOpen(false);
  };

  const handleSelect = ({ materialAttributes }) => {
    setMaterial(materialAttributes);
  };

  // determine if save button is enabled / disabled
  const disabled = material == null;

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle>{title}</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={handleClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <DialogContentText>{label}</DialogContentText>
        <MaterialAutocomplete
          variant="outlined"
          defaultMaterial={material}
          onSelect={handleSelect}
        />
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} variant="outlined" color="primary">
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={onSave}
          variant="contained"
          color="primary"
          disabled={disabled}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

SelectMaterialDialog.propTypes = {
  material: PropTypes.object.isRequired,
  setMaterial: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  title: PropTypes.string,
  label: PropTypes.string,
};
