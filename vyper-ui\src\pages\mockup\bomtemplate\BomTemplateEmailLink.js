import React, { useContext } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { Button } from "@material-ui/core";
import { QuestionDialogContext } from "../../../component/question/QuestionDialog";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";

const useStyles = makeStyles({
  root: {
    marginTop: "0.5rem",
  },
  link: {
    textDecoration: "underline",
    textColor: "blue",
    cursor: "pointer",
  },
});

export const BomTemplateEmailLink = ({
  vyperNumber,
  buildNumber,
  bomTemplate,
}) => {
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { open: openAlert } = useContext(AlertDialogContext);
  const { vpost } = useContext(FetchContext);

  const name = bomTemplate.object.name || "";
  const numGlobal = (name.match(/\(GLOBAL\)/g) || []).length;
  const numBom = (name.match(/\(BOM\)/g) || []).length;
  const numAt = (name.match(/\(AT\)/g) || []).length;

  const show = numGlobal !== 1 || numBom !== 1 || numAt > 1;

  const handleClick = () => {
    openConfirmation({
      title: "Request Bill of Process Template Review",
      message: `This action will send an email to the Bill of Process Template team asking them to review and update this template. Are you sure you want to do this?`,
      yesText: "Yes",
      noText: "No",
      onYes: () =>
        vpost(
          "/vyper/v1/vyper/bomtemplate/review",
          {
            vyperNumber: vyperNumber,
            buildNumber: buildNumber,
          },
          () => {
            openAlert({
              title: "Success",
              message: "The email was successfully sent",
            });
          }
        ),
    });
  };

  const classes = useStyles();

  if (show) {
    return (
      <div className={classes.root}>
        <Button
          size="small"
          color="primary"
          variant="contained"
          onClick={handleClick}
        >
          Request Review
        </Button>
      </div>
    );
  } else {
    return null;
  }
};
