import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { QuestionDialogContext } from "../../../component/question/QuestionDialog";
import { samenessFacility } from "../../../component/sameness/sameness";
import { FacilityCell } from "./FacilityCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

/**
 * Create the row to display the facility information
 *
 * @param vyper
 * @param builds
 * @param onChange
 * @param showSameness
 * @returns {JSX.Element}
 * @constructor
 */
export const FacilityRow = ({ vyper, builds, onChange, showSameness }) => {
  const { showQuestionDialog } = useContext(QuestionDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeFacility = (facility, build) => {
    return buildDao
      .changeFacility(vyper.vyperNumber, build.buildNumber, facility)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    const format = (name, facility) => `${name} (${facility.PlantCode})`;

    const names =
      build.material.object.FlowName?.flatMap((flowName) => {
        const names = [];
        const pos = flowName.indexOf(">>");
        if (-1 === pos) {
          names.push(flowName.trim());
        } else {
          names.push(flowName.substr(0, pos).trim());
          names.push(flowName.substr(pos + 2).trim());
        }
        return names;
      })
        .filter((name) => !name.includes("[MFF]"))
        .map((name) => name.replaceAll("(ASY)", "").trim())
        .map((name) => name.replaceAll("(AS1)", "").trim())
        .map((name) => name.replaceAll("(SAW)", "").trim())
        .map((name) => name.replaceAll("[TKY]", "").trim())
        .map((name) => {
          const facility = build.material.facilities.find(
            (f) => f.PDBFacility === name
          );
          return format(name, facility);
        })
        .filter((name, index, names) => names.indexOf(name) === index) || // removes duplicates
      [];
    const namer = (facility) =>
      `${facility.PDBFacility} (${facility.PlantCode})`;

    if (names.length === 0) {
      names.push(...build.material.facilities.map((f) => namer(f)));
    }

    showQuestionDialog({
      type: "select",
      title: "Select Facility",
      description: `Select the facility for the ${build.material.object.Material}. The facilities in the list below are authorized to build this material in the ${build.material.object.PackagePin}${build.material.object.PackageDesignator} package.`,
      value: null,
      multiline: false,
      rows: 10,
      options: names,
      onSave: (name) => {
        const facility = build.material.facilities.find(
          (f) => name === format(f.PDBFacility, f)
        );
        handleChangeFacility(facility.PDBFacility, build);
      },
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessFacility(builds),
      })}
      hover
    >
      <RowPrefix help="facility" title="Facility" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <FacilityCell
            key={n}
            vyper={vyper}
            build={build}
            onClick={handleClick}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
