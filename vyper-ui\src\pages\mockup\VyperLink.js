import PropTypes from "prop-types";
import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { NavLink } from "react-router-dom";

const useStyles = makeStyles({
  link: {
    textDecoration: "underline",
    cursor: "pointer",
    "&:hover": {
      color: "red",
    },
  },
});

/**
 * Show data as text, or show it as a html link
 *
 * @param onClick
 * @param canEdit
 * @param children
 * @returns {JSX.Element}
 * @constructor
 */
export const VyperLink = ({ onClick, canEdit = true, children }) => {
  const classes = useStyles();

  if (canEdit) {
    return (
      <a className={classes.link} onClick={onClick}>
        {children}
      </a>
    );
  } else {
    return <span>{children}</span>;
  }
};

VyperLink.propTypes = {
  onClick: PropTypes.func.isRequired,
  canEdit: PropTypes.bool,
  children: PropTypes.any,
};

export const VyperNavLink = ({ to, canEdit = true, children }) => {
  const classes = useStyles();

  if (canEdit) {
    return (
      <NavLink className={classes.link} to={to}>
        {children}
      </NavLink>
    );
  } else {
    return <span>{children}</span>;
  }
};
