import { makeStyles } from "@material-ui/core/styles";
import { IconButton, Paper, Tab, Tabs } from "@material-ui/core";
import React, { useEffect } from "react";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { TabPanel } from "../../pages/admin/sandbox/TabPanel";
import HelpIcon from "@material-ui/icons/Help";

const useStyles = makeStyles((theme) => ({
  root: {},
  panels: {
    marginTop: theme.spacing(3),
  },
  tab: {
    // minWidth: 150
  },
  bar: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
}));

export const HorizontalTabs = ({ storageKey, tabs, helpUrl = null }) => {
  // store selected tab's index in storage
  const [value, setValue] = useLocalStorage(storageKey, 0);
  const handleChange = (event, newValue) => setValue(newValue);

  // if there is a default value, use it
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const name = params.get("name");
    let tabIndex = tabs.findIndex((tab) => tab.label === name);
    if (-1 !== tabIndex) setValue(tabIndex);
  }, []);

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Paper>
        <div className={classes.bar}>
          <Tabs
            onChange={handleChange}
            className={classes.tab}
            indicatorColor="primary"
            textColor="primary"
            value={value || 0}
          >
            {tabs.map((tab, n) => (
              <Tab key={n} label={tab.label} />
            ))}
          </Tabs>

          {helpUrl == null ? null : (
            <IconButton target="_blank" href={helpUrl}>
              <HelpIcon />
            </IconButton>
          )}
        </div>
      </Paper>

      <div className={classes.panels}>
        {tabs.map((tab, n) => (
          <TabPanel key={n} value={value} index={n}>
            {tab.control}
          </TabPanel>
        ))}
      </div>
    </div>
  );
};
