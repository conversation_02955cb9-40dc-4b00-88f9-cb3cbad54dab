import { DaoBase } from "src/component/fetch/DaoBase";
import produce from "immer";
import { noop } from "src/component/vyper/noop";

export class VyperDao extends DaoBase {
  constructor(params) {
    super({ name: "VyperDao", url: "/vyper/v1/vyper", ...params });
    this.vyper = params.vyper || [];
    this.setVyper = params.setVyper || noop;
  }

  delete(vyperNumber) {
    return this.handleFetch("delete", `/${vyperNumber}`, "DELETE");
  }

  reload(vyperNumber) {
    return findByVyperNumber(vyperNumber, true);
  }

  findByVyperNumber(vyperNumber, ignoreCache = false) {
    if (!ignoreCache && this.vyper?.vyperNumber === vyperNumber) {
      return Promise.resolve(this.vyper);
    }

    return this.handleFetch(
      "findByVyperNumber",
      `/findByVyperNumber/${vyperNumber}`,
      "GET"
    )
      .then((json) =>
        produce(json, (draft) => {
          draft.builds = []; // todo: remove when .builds is deleted
        })
      )
      .then((json) => {
        this.setVyper(json);
        return json;
      });
  }

  findByBuildNumber(buildNumber) {
    if (this.vyper?.vyperNumber === buildNumber.substr(0, 12)) {
      return Promise.resolve(this.vyper);
    }

    return this.handleFetch(
      "findByBuildNumber",
      `/findByVyperNumber/${buildNumber.substr(0, 12)}`,
      "GET"
    )
      .then((json) =>
        produce(json, (draft) => {
          draft.builds = [];
        })
      )
      .then((json) => {
        this.setVyper(json);
        return json;
      });
  }

  updateTitle(vyperNumber, title) {
    return this.handleFetch("updateTitle", `/title`, "POST", {
      vyperNumber,
      title,
    }).then((json) => {
      this.setVyper(json);
      return json;
    });
  }


  addOwner(data) {
    return this.handleFetch(
      "addOwner",
      "/owner/add",
      "POST",
      data
    ).then((json) => {
      this.setVyper(json);
      return json;
    });
  }


  removeOwner(vyperNumber, userid) {
    return this.handleFetch("removeOwner", `/owner/remove`, "POST", {
      vyperNumber,
      userid,
    }).then((json) => {
      this.setVyper(json);
      return json;
    });
  }

  addBuild(
    mode,
    vyperNumber,
    buildNumber,
    flowData,
    description,
    buildType,
    copyBuildNumber,
    symbolChoice
  ) {
    if (flowData.length === 1) {
      const flow = flowData[0];
      const newBuild = {
        mode: mode,
        vyperNumber: vyperNumber,
        buildNumber: buildNumber,
        buildFlow: flow.flowName,
        material: flow.device.Material,
        facility: flow.facility.PDBFacility,
        multiBuild: flow.isMultiBuild,
        specDevice: flow.specDevice,
        description,
        buildType,
        copyBuildNumber,
        templateSourceForm: flow.templateSource,
      };
      return this.handleFetch("addbuild", `/addbuild`, "POST", newBuild).then(
        (json) => {
          return json; // returns the build object
        }
      );
    } else {
      const mfFlows = flowData.map((flow) => {
        return {
          mode: mode,
          vyperNumber: vyperNumber,
          buildNumber: buildNumber,
          buildFlow: flow.flowName,
          material: flow.device.Material,
          facility: flow.facility.PDBFacility,
          multiBuild: flow.isMultiBuild,
          specDevice: flow.specDevice,
          description,
          buildType,
          copyBuildNumber,
          symbolChoice,
          templateSourceForm: flow.templateSource,
        };
      });

      return this.handleFetch(
        "addMFFBuild",
        `/addmffbuild`,
        "POST",
        mfFlows
      ).then((json) => {
        return json; // returns all the builds
      });
    }
  }
}
