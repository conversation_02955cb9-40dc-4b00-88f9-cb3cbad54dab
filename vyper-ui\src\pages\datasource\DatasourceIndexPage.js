import React from "react";
import { HorizontalTabs } from "../../component/tab/HorizontalTabs";
import { ArmArcPanel } from "./ArmArcPanel";
import { ChangePanel } from "./ChangePanel";
import { PcnPanel } from "./PcnPanel";

export const DatasourceIndexPage = () => {
  const tabs = [
    {
      label: "ArmArc",
      control: <ArmArcPanel />,
    },
    {
      label: "Changelink Change",
      control: <ChangePanel />,
    },
    {
      label: "Changelink PCNs",
      control: <PcnPanel />,
    },
  ].sort((a, b) => {
    if (a.label < b.label) return -1;
    if (a.label > b.label) return 1;
    return 0;
  });

  return (
    <div>
      <HorizontalTabs storageKey="datasource.tab.value" tabs={tabs} />
    </div>
  );
};
