import { Button, DialogActions, Typography } from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Grid from "@material-ui/core/Grid";
import CloseIcon from "@material-ui/icons/Close";
import Alert from "@material-ui/lab/Alert";

import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";

export function AtssScnStatusDialog({ open, atssScn, onClose }) {
  const atssScnIdCreated = atssScn.scnId != "" && atssScn.scnId != null;
  const atssScnAlerts = atssScn.atssFieldErrors || [];

  const useStyles = makeStyles((theme) => ({
    closeButton: {
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    dialogActions: {
      display: "flex",
      justifyContent: "space-between",
      width: "100%",
      margin: "0em 1em 1em 1em",
    },
  }));

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <DialogTitle>SCN : {atssScn?.scnId || "NA"}</DialogTitle>

      <DialogContent>
        <Grid container spacing={2}>
          <Grid item xs={4}>
            Status: {atssScn?.rejectReason || "Created"}
          </Grid>
          {atssScnIdCreated && (
            <Grid item xs={4}>
              <Button
                disabled={!atssScnIdCreated}
                onClick={onClose}
                variant="contained"
                color="primary"
              >
                Close
              </Button>
            </Grid>
          )}
        </Grid>
        {atssScnAlerts.map((fieldError) => (
          <Grid container spacing={1}>
            <Grid item xs={8}>
              <Alert severity="error">
                [{fieldError.field}] {fieldError.error}
              </Alert>
            </Grid>
          </Grid>
        ))}
      </DialogContent>

      <DialogActions>
        <div className={useStyles.dialogActions}>
          <Button onClick={onClose} variant="outlined" color="secondary">
            Close
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
}

AtssScnStatusDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  atssScn: PropTypes.any.isRequired,
  onClose: PropTypes.func.isRequired,
};
