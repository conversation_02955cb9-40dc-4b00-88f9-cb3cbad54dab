import React, { useState, useContext, useEffect } from "react";
import { Typo<PERSON>, But<PERSON> } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TiServerAgGrid, { exportGridAsExcel } from "../../lib/TiServerAgGrid";
import IconButton from "@material-ui/core/IconButton";
import VisibilityIcon from "@material-ui/icons/Visibility";
import { Link, useHistory } from "react-router-dom";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import { PersistentFilters } from "src/pages/devices/PersistentFilters.js";
import { CreateProjectDialog } from "../component/newProject/CreateProjectDialog";
import { FullFlowSpin } from "../component/newProject/ProjectTypes";
import { FetchContext } from "../../../src/component/fetch/VyperFetch";

const useStyles = makeStyles((theme) => ({
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "0.5rem",
  },

  buttonGroup: {
    fontSize: "1.25rem",
    fontWeight: "bold",
    display: "flex",
    gap: "0.7rem",
  },
  deviceGridStyle: {
      '& .ag-header-row': {
          backgroundColor : theme.palette.primary.main,
      }
    },
}));

const atssStatusMapping = { ACTIVE: "A", WORKING: "W" };

let body = document.body,
  html = document.documentElement;
let height =
  Math.max(
    body.scrollHeight,
    body.offsetHeight,
    html.clientHeight,
    html.scrollHeight,
    html.offsetHeight
  ) - 200;

export const AtssMassUploadPage = () => {
  const actionRenderer = (node) => {
    const rowData = node.data;
    if (rowData !== undefined) {
      return (
        <IconButton
          onClick={() => {
            history.push({
              pathname: `/atssmassupload/project/${rowData.projNumber}/specchanges`,
            });
          }}
          color="secondary"
        >
          <VisibilityIcon style={{ fontSize: "1rem" }} />
        </IconButton>
      );
    }
    return <></>;
  };

  const projNumberRenderer = (node) => {
    const rowData = node.data;
    if (rowData !== undefined) {
      return (
        <Link to={`/atssmassupload/project/${rowData.projNumber}/specchanges`}>
          {rowData.projNumber}
        </Link>
      );
    }
    return <></>;
  };

  const buildNumberRenderer = (node) => {
    const rowData = node.data;
    if (rowData !== undefined) {
      const vyperNumber =
        "VYPER" +
        rowData.refVyperBuildNumber?.split("-")[0].slice("VBUILD".length);
      return (
        <Link
          to={`/projects/${vyperNumber}/builds/${rowData.refVyperBuildNumber}/selection`}
        >
          {rowData.refVyperBuildNumber}
        </Link>
      );
    }
    return <></>;
  };

  const praNumberRenderer = (node) => {
    const rowData = node.data;
    if (rowData !== undefined) {
      const vyperNumber =
        "VYPER" + rowData.refVyperPraNumber?.split("-")[0].slice("PRA".length);
      return (
        <Link to={`/projects/${vyperNumber}/pras/${rowData.refVyperPraNumber}`}>
          {rowData.refVyperPraNumber}
        </Link>
      );
    }
    return <></>;
  };

  const defaultColDef = {
    filterParams: {
      suppressAndOrCondition: true,
      filterOptions: [
        {
          displayKey: "contains",
          displayName: "Contains",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "=",
          displayName: "Equal",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "<>",
          displayName: "Not Equal",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "<",
          displayName: "Less Than",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: ">",
          displayName: "Greater Than",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "<=",
          displayName: "Less Than or Equal",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: ">=",
          displayName: "Greater Than or Equal",
          predicate: () => {}, //needed to use display key and name
        },
      ],
    },
  };

  const columns = [
    {
      headerName: "Actions",
      cellRenderer: actionRenderer,
      filter: false,
      sortable: false,
      width: 90,
    },
    {
      headerName: "Project Number",
      field: "projNumber",
      cellRenderer: projNumberRenderer,
      linkCellRendererParams: {
        links: (params) => {
          /** @type {ResultRowData} */
          const data = params.data;
          const label = `${data.projNumber}`;
          const url =
            location.origin +
            `/atssmassupload/project/${data.projNumber}/specchanges`;
          return { label, url };
        },
      },
      cellStyle: {
        fontSize: 14,
      },
    },
    { headerName: "Project Name", field: "projName" },
    { headerName: "Category", field: "projType" },
    { headerName: "Status", field: "projStatus" },
    { headerName: "Target Facility", field: "facilityAt" },
    { headerName: "Reference Type", field: "refType" },
    { headerName: "Ref Spec Device", field: "refSpecDevice" },
    { headerName: "Ref Facility", field: "refFacilityAt" },
    { headerName: "Ref Status", field: "refStatus" },
    {
      headerName: "Ref Vyper Build",
      field: "refVyperBuildNumber",
      cellRenderer: buildNumberRenderer,
      linkCellRendererParams: {
        links: (params) => {
          /** @type {ResultRowData} */
          const data = params.data;
          console.log(data);
          const label = `${data.refVyperBuildNumber}`;
          const vyperNumber =
            "VYPER" +
            data.refVyperBuildNumber?.split("-")[0].slice("VBUILD".length);
          const url =
            location.origin +
            `/vyper/projects/${vyperNumber}/builds/${data.refVyperBuildNumber}/selection`;
          return { label, url };
        },
      },
      cellStyle: {
        fontSize: 14,
      },
    },
    {
      headerName: "PRA Number",
      field: "refVyperPraNumber",
      cellRenderer: praNumberRenderer,
      linkCellRendererParams: {
        links: (params) => {
          /** @type {ResultRowData} */
          const data = params.data;
          console.log(data);
          const label = `${data.refVyperPraNumber}`;
          const vyperNumber =
            "VYPER" + data.refVyperPraNumber?.split("-")[0].slice("PRA".length);
          const url =
            location.origin +
            `/vyper/projects/${vyperNumber}/pras/${data.refVyperPraNumber}`;
          return { label, url };
        },
      },
      cellStyle: {
        fontSize: 14,
      },
    },
    { headerName: "PRA State", field: "praState" },
    { headerName: "CMS Number", field: "cmsNumber" },
    { headerName: "Owner", field: "ownerId" },
    { headerName: "Created by", field: "createdBy" },
    {
      headerName: "Created on",
      field: "createdDttm",
    },
    { headerName: "Updated by", field: "updatedBy" },
    {
      headerName: "Last updated on",
      field: "updatedDttm",
    },
  ];

  const url = "/vyper/v1/atssmassupload/projects";
  const { vpost } = useContext(FetchContext);
  const history = useHistory();

  const [filterState, setFilterState] = useLocalStorage(
    "tableFilterStates.massUploadProjectsFilterState",
    {}
  );
  const [sortState, setSortState] = useLocalStorage(
    "tableFilterStates.massUploadProjectsSortState",
    []
  );

  const defaultSortColDef = {
    state: [{ colId: "createdDttm", sort: "desc", sortIndex: 0 }],
  };

  const classes = useStyles();

  const [gridRef, setGridRef] = useState(null);
  const [hasTableLoaded, setHasTableLoaded] = useState(false);

  const filters = Object.keys(filterState);
  const hasPersistentFilters = Object.keys(filterState).length > 0;
  const hasPersistentSort =
    sortState.length > 0 || Object.keys(sortState).length > 0;

  const onFirstDataRendered = (api) => {
    setGridRef(api);
    setHasTableLoaded(true);
  };

  const exportAsExcel = () => {
    const api = gridRef.api;
    const columnApi = gridRef.columnApi;
    exportGridAsExcel(api, columnApi, {
      columnFilter: (column) => {
        return column.getColDef().headerName !== "Actions";
      },
      fileName: "All_Projects_ATSS_Mass_Upload.xlsx",
    });
  };

  const [openCreateProjectDialog, setOpenCreateProjectDialog] = useState(false);
  const [projectName, setProjectName] = useState();
  const [projectType, setProjectType] = useState(FullFlowSpin);
  const [targetFacility, setTargetFacility] = useState();
  const [atssSpecDevice, setAtssSpecDevice] = useState();
  const [atssFacility, setAtssFacility] = useState();
  const [atssStatus, setAtssStatus] = useState();
  const [cmsNumber, setCmsNumber] = useState();

  useEffect(() => {
    setTargetFacility(atssFacility);
  }, [atssFacility]);

  const handleClickCreateProject = () => {
    setOpenCreateProjectDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenCreateProjectDialog(false);
    resetValues();
  };

  const handleChangeProjectName = (e) => {
    setProjectName(e.target.value);
  };

  const handleProjectTypeChange = (value) => {
    setProjectType(value);
  };

  const resetValues = () => {
    setProjectName();
    setTargetFacility();
    setAtssSpecDevice();
    setAtssFacility();
    setAtssStatus();
    setCmsNumber();
  };

  const handleClickSave = () => {
    const newProject = {
      projName: projectName,
      projType: projectType,
      targetFacility: targetFacility,
      specDevice: atssSpecDevice,
      facilityAt: atssFacility,
      refStatus: atssStatusMapping[atssStatus],
      cmsNumber,
    };

    vpost(`/vyper/v1/atssmassupload/projects`, newProject, (projectInfo) => {
      history.push({
        pathname: `/atssmassupload/project/${projectInfo.projNumber}/specchanges`,
      });
    });

    handleCloseDialog();
  };

  return (
    <div>
      <div className={classes.header}>
        <Typography
          variant="h5"
          style={{
            fontWeight: "bold",
          }}
        >
          All Projects
        </Typography>
        <div className={classes.buttonGroup}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleClickCreateProject}
          >
            Create Project
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              gridRef.api.setFilterModel({});
            }}
            color="secondary"
          >
            Clear filters
          </Button>
          <Button variant="contained" onClick={exportAsExcel} color="secondary">
            Export as Excel
          </Button>
        </div>
      </div>

      <div
        style={{ display: "flex", flexDirection: "row", alignItems: "center" }}
      >
        {hasPersistentFilters && (
          <div style={{ marginRight: "5px" }}>Persistent Filters</div>
        )}
        {filters?.map((filter, n) => (
          <PersistentFilters
            key={n}
            filter={filter}
            value={filterState[`${filter}`]["filter"]}
            columns={columns}
          />
        ))}
      </div>

      <TiServerAgGrid
        url={url}
        defaultColDef={defaultColDef}
        columnDefs={columns}
        style={{ height }}
        classNames={`ag-theme-alpine ${classes.deviceGridStyle}`}
        defaultPageSize={20}
        paginationSelectOptions={[5, 10, 20, 50, 100, 200]}
        getFilterModel={hasTableLoaded && setFilterState}
        getSortModel={hasTableLoaded && setSortState}
        onFirstDataRendered={onFirstDataRendered}
        initialFilterModel={filterState}
        initialSortModel={hasPersistentSort ? sortState : defaultSortColDef}
      />

      <CreateProjectDialog
        open={openCreateProjectDialog}
        projectName={projectName}
        projectType={projectType}
        targetFacility={targetFacility}
        atssSpecDevice={atssSpecDevice}
        atssFacility={atssFacility}
        atssStatus={atssStatus}
        cmsNumber={cmsNumber}
        handleChangeProjectName={handleChangeProjectName}
        handleProjectTypeChange={handleProjectTypeChange}
        onChangeTargetFacility={setTargetFacility}
        onChangeAtssSpecDevice={setAtssSpecDevice}
        onChangeAtssFacility={setAtssFacility}
        onChangeAtssStatus={setAtssStatus}
        handleChangeCMSNumber={setCmsNumber}
        handleCloseDialog={handleCloseDialog}
        handleClickSave={handleClickSave}
      />
    </div>
  );
};
