import React, { useState } from "react";
import Button from "@material-ui/core/Button";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core/";

import DieSelect from "./DieSelect";

const DieLotStatusDialog = (props) => {
  const {
    classes,
    isOpen,
    handleCancel,
    handleSave,
    defaultDieLotData = {},
    material,
    plant,
    dieLocationOptions,
  } = props;
  const [dieLotData, setDieLotData] = useState({});

  const handleOnSaveClick = () => {
    handleSave({ ...dieLotData });
    setDieLotData({});
  };

  return (
    <Dialog fullWidth maxWidth="xl" open={isOpen}>
      <DialogTitle>Die Lot Information</DialogTitle>
      <DialogContent>
        <DieSelect
          classes={classes}
          setDieLotData={setDieLotData}
          material={material}
          plant={plant}
          dieLocationOptions={dieLocationOptions}
        />
      </DialogContent>
      <DialogActions>
        <Button
          style={{ marginRight: "20px" }}
          variant="contained"
          onClick={handleCancel}
          color="primary"
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          disabled={Object.keys(dieLotData).length === 0}
          onClick={handleOnSaveClick}
          color="secondary"
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};
export default DieLotStatusDialog;
