import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import TextField from "@material-ui/core/TextField";
import Autocomplete, {
  createFilterOptions,
} from "@material-ui/lab/Autocomplete";
import { FetchContext } from "../../component/fetch/VyperFetch";

/**
 * an autocomplete widget for materials.
 * we start with a default Material Attributes (object of { Material:"", ...})
 * and return (through onSelect callback) the Material Attributes
 *
 * @param string label - The label for the component
 * @param string defaultMaterial - The initial material
 * @param refreshInterval - The interval for debouncing the input
 * @param variant The Material Attribute input's attribute
 * @param onSelect - The callback function
 * @param rest - optional attributes to add to the Autocomplete control
 * @returns {JSX.Element}
 */
export const MaterialAutocomplete = ({
  label = "Device",
  defaultMaterial = "",
  refreshInterval = 500,
  variant = "standard",
  onSelect,
  ...rest
}) => {
  const { vget } = useContext(FetchContext);
  const [material, setMaterial] = useState(defaultMaterial);
  const [pgsData, setPgsData] = useState();
  const [materials, setMaterials] = useState([]);
  const [materialAttributes, setMaterialAttributes] = useState([]);
  const [timer, setTimer] = useState();

  // when the material changes (the user types characters), wait for a debounce delay, then refresh the pgs data
  useEffect(() => {
    if (timer != null) clearTimeout(timer);
    setTimer(window.setTimeout(() => refresh(), refreshInterval));
  }, [material]);

  // If the material has changed, query pgs for the material and facility information
  const refresh = () => {
    if (typeof material !== "string") return;
    const device = material?.trim();
    if (device == null || device === "") return;
    vget(`/vyper/v1/autocomplete/material?search=${device}`, setPgsData);
  };

  // when the pgs data is loaded, parse the unique materials out of the pgs data
  useEffect(() => {
    if (pgsData == null) return;

    const materials = pgsData.items
      .filter((item) => item.type === "Device")
      .map((item) => item.attrs)
      .map((item) => item.Material)
      .sort()
      .filter((item, index, items) => items.indexOf(item) === index); // removes duplicates

    setMaterials(materials);
  }, [pgsData]);

  // when the materials change, parse the material attributes + package niche out of the gs data
  useEffect(() => {
    if (pgsData == null || materials == null) return;

    const materialAttributes = Object.keys(pgsData.metadata.refObjects)
      .map((key) => pgsData.metadata.refObjects[key])
      .filter((ro) => ro.type === "Material")
      .map((ro) => ro.attrs)
      .filter((attrs) => materials.includes(attrs.Material))

      // add the package niche(s) as a CSV attribute
      .map((attrs) => {
        const nicheMapping = pgsData.items
          .filter((item) => item.type === "Device")
          .filter((item) => item.attrs.Material === attrs.Material)
          .map((item) => {
            return {
              PackageNiche: item.attrs.PackageNiche,
              PlantCode: item.attrs.AssemblyPlantCode,
            };
          });
        return { ...attrs, PackageNicheMapping: nicheMapping };
      });
    setMaterialAttributes(materialAttributes);
  }, [pgsData, materials]);

  const handleSelect = (material) => {
    if (!material) return;

    // fetching the facilities for the material selected
    // FlowName contains array of flow with site names
    const materialFlowFacilities = Object.keys(pgsData.metadata.refObjects)
      .map((key) => pgsData.metadata.refObjects[key])
      .filter((ro) => ro.type === "Material")
      .map((ro) => ro.attrs)
      .filter((attrs) => attrs.Material === material.Material)
      .flatMap((attr) => attr.FlowName)
      .reduce((flowArr, flow) => {
        const flowFacilities = flow
          ?.split(">> ")
          ?.map((fac) => fac?.split(" ")[0]);
        flowFacilities && flowArr.push(...flowFacilities);
        return flowArr;
      }, []);

    // materialFlowFacilities is an array of strings of facility (PDB) names.
    // these values are configured in scs (and are pulled in via pgs).

    // facilities this is the list of facilities (PGS Attr objects) that the device may be built.
    let facilities = pgsData.items
      .filter((item) => item.type === "Device")
      .filter((item) => item.attrs?.Material === material.Material)
      .flatMap((item) => {
        let targets = item.rels?.DeviceAssySite?.targets;
        if (targets != undefined && targets != null) {
          return targets;
        }
        return [];
      })
      .map((target) => (target == null ? null : target.idRef))
      .filter((idRef, index, idRefs) => idRefs.indexOf(idRef) === index) // removes duplicates
      .map((id) => {
        let refObject = pgsData.metadata?.refObjects?.[id];
        if (refObject != null && refObject != undefined) {
          return refObject.attrs;
        }
        return null;
      })
      .filter((attr) => !!attr);

    // Removing OBSOLETE and INACTIVE facilities
    facilities = facilities.filter(
      (facility) => !["OBSOLETE", "INACTIVE"].includes(facility.PDBFacility)
    );

    if (materialFlowFacilities.length > 0) {
      const pdbFacilityList = facilities.map(
        (facility) => facility.PDBFacility
      );
      const missingFacilityList = materialFlowFacilities.filter(
        (materialFlowFacility) =>
          !pdbFacilityList.includes(materialFlowFacility)
      );

      if (missingFacilityList.length > 0) {
        missingFacilityList.forEach((missingFacility) => {
          vget(
            `/vyper/v1/bdw/plantDetails?facility=${missingFacility}`,
            (bdwPlant) => {
              const missingFacilityInfo = {
                PDBFacility: bdwPlant.pdbFacility,
                PlantCode: bdwPlant.plantCode,
                PlantLoc: bdwPlant.plantLoc,
                PlantName: bdwPlant.plantName,
                PlantType: bdwPlant.plantType,
              };

              facilities.push(missingFacilityInfo);
            }
          );
        });
      }
    }

    onSelect({
      material: material.Material,
      facilities,
      materialAttributes: material,
      pgsData,
    });
  };

  const filterOptions = createFilterOptions({
    trim: true,
  });

  return (
    <Autocomplete
      {...rest}
      value={defaultMaterial}
      options={materialAttributes}
      renderInput={(params) => (
        <TextField {...params} variant={variant} label={label} />
      )}
      onInputChange={(e, v) => {
        if (v !== "") setMaterial(v);
      }}
      onChange={(e, material) => handleSelect(material)}
      getOptionLabel={(option) => option.Material || ""}
      filterOptions={filterOptions}
      getOptionSelected={(option, value) =>
        option.Material.toUpperCase() === value.Material?.toUpperCase()
      }
    />
  );
};

MaterialAutocomplete.propTypes = {
  label: PropTypes.string,
  defaultMaterial: PropTypes.object,
  refreshInterval: PropTypes.number,
  variant: PropTypes.string,
  onSelect: PropTypes.func.isRequired,
};
