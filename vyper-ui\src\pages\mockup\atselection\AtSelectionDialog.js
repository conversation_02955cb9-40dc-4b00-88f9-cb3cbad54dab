/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { ComponentMapContext } from "../../../component/componentmap/ComponentMap";
import { AtAttributes } from "./AtAttributes";
import { AtEngineeringForm } from "./AtEngineeringForm";
import { AtSelectionActions } from "./AtSelectionActions";
import { AvailableList } from "./AvailableList";
import { AtSelectionButtons } from "./AtSelectionButtons";
import { FilteredAutocomplete } from "src/component/build-component/FilteredAutocomplete";
import produce from "immer";
import { Experimental } from "../buildtype/BuildTypes";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  form: {
    display: "flex",
    justifyContent: "space-between",
    paddingLeft: "1rem",
    paddingRight: "1rem",
  },
  select: {
    width: "100%",
    fontSize: "1rem",
    borderColor: "#cccccc",
    height: "300px",
    maxHeight: "300px",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  bom: {
    color: "black",
    padding: "1rem",
    margin: "1rem",
    backgroundColor: "#eeeeee",
    borderRadius: "0.25rem",
  },
  highlight: {
    marginTop: "1em",
    padding: 7,
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));

export const AtSelectionDialog = ({ children }) => {
  const emptyFunctionObject = {
    fn: () => {},
  };

  // show or hide the dialog
  const [open, setOpen] = useState(false);

  // the component name (string) Leadframe, Die, ...
  const [name, setName] = useState();

  // callback function to use when user clicks save
  const [onSave, setOnSave] = useState(emptyFunctionObject);

  // the selected items (array of SelectionItems)
  const [selectionItems, setSelectionItems] = useState([]);

  const [supplierOrWire, setSupplierOrWire] = useState();

  const [allowParentChildDies, setAllowParentChildDies] = useState(false);

  const [disableAddEngineering, setDisableAddEngineering] = useState(false);

  // the available the user has clicked (SelectionItem object)
  const [selectedComponent, setSelectedComponent] = useState();

  // the selected value the user has clicked (SelectionItem Object)
  const [availableComponent, setAvailableComponent] = useState();

  // the component value used to show the attributes
  const [attributeComponent, setAttributeComponent] = useState();

  // the engineering component value (string)
  const [engineering, setEngineering] = useState();

  //use api call for finding max length

  const { vget } = useContext(FetchContext);

  // the component value is cust and cannot exceed this length, maxLens is an array
  const [maxLens, setMaxLens] = useState([]);

  const { open: openAlertDialog } = useContext(AlertDialogContext);

  const [build, setBuild] = useState();
  const [praNumber, setPraNumber] = useState();

  // store the input parameters, and show the dialog
  const handleOpen = ({
    build,
    name,
    selectionItems,
    supplierOrWire,
    onSave,
    disableAddEngineering,
    allowParentChildDies,
    praNumber,
  }) => {
    setBuild(build);
    setName(name);
    setSelectionItems(selectionItems);
    setSupplierOrWire(supplierOrWire);
    setOnSave({ fn: onSave });
    setOpen(true);
    setDisableAddEngineering(disableAddEngineering);
    setAllowParentChildDies(allowParentChildDies);
    setPraNumber(praNumber);
  };

  // reset the parameters and close the dialog
  const handleClose = () => {
    setName(undefined);
    setOnSave(emptyFunctionObject);
    setEngineering(undefined);
    setOpen(false);
    setSelectionItems([]);
    setSelectedComponent(undefined);
    setAvailableComponent(undefined);
    setAttributeComponent(undefined);
  };

  // call the save callback, then close the form
  const handleSave = () => {
    if (name.match(/^.*CUST.*$/)) {
      var warningDialog = "";
      //before saving check if were in a cust saving window and its not too long
      const isValid = selectionItems.every((value) => {
        const maxLen = maxLens.find((maxLen) => maxLen.custFieldName === name);
        if (maxLen == null || value == null) return true;
        warningDialog =
          'Custom field "' +
          value.value +
          '" too long, found ' +
          value.value.length +
          " characters. Expected only " +
          maxLen.maxLen +
          " characters. If you believe this to be an error please contact your symbol SME.";
        return value.value.length <= maxLen.maxLen;
      });

      if (!isValid) {
        openAlertDialog({
          title: "Custom length dialog",
          message: warningDialog,
        });
        return;
      }
    }
    onSave.fn({ name: name, items: selectionItems });
    handleClose();
  };

  const facilityAt = build?.facility?.object?.PDBFacility;
  const pkgGroup = build?.material?.object?.PackageGroup;
  const pkg = build?.material?.object?.PackageDesignator;
  const pin = build?.material?.object?.PackagePin;
  const symbolization = build?.symbolization?.symbols[0]?.object?.name;

  // user click a available value
  const handleClickAvailable = (componentValue) => {
    setAvailableComponent(componentValue);
    setAttributeComponent(componentValue);
  };

  // user clicks a selected value
  const handleClickSelected = (componentValue) => {
    setSelectedComponent(componentValue);
    setAttributeComponent(componentValue);
  };

  // handle enabling/disabling the select and unselect action buttons
  const canSelect = availableComponent != null;
  const canUnSelect = selectedComponent != null;
  const canReplace = availableComponent != null;

  // create the priority object, with handling for special components
  const makePriorityObject = (name, value) => {
    if (name === "MB Diagram" && value.includes(" ")) {
      const [number] = value.split(" ");
      return number;
    } else {
      return value;
    }
  };

  // user clicked the select button
  const handleSelect = () => {
    if (availableComponent != null) {
      setSelectionItems([
        ...selectionItems,
        {
          value: makePriorityObject(componentMap?.name, availableComponent),
          engineering: "N",
        },
      ]);
    }
  };

  // user clicked the un-select button
  const handleUnSelect = () => {
    if (selectedComponent != null) {
      setSelectionItems(
        selectionItems.filter((c) => c.value !== selectedComponent)
      );
    }
  };

  // user clicked the replace button
  const handleReplace = () => {
    if (availableComponent != null) {
      setSelectionItems([
        {
          value: makePriorityObject(componentMap?.name, availableComponent),
          engineering: "N",
        },
      ]);
    }
  };

  // user clicked the add engineering component button
  const handleAddEngineering = () => {
    if (engineering !== null) {
      setSelectionItems([
        ...selectionItems,
        { value: engineering, engineering: "Y" },
      ]);
    }
  };
  // check for cust max length
  useEffect(() => {
    if (
      symbolization == null ||
      pkg == null ||
      facilityAt == null ||
      pin == null
    )
      return;
    // Encode the parameters before constructing the URL
    const encodedPkg = encodeURIComponent(pkg);
    const encodedFacility = encodeURIComponent(facilityAt);
    const encodedSymbolName = encodeURIComponent(symbolization);
    const encodedPin = encodeURIComponent(pin);

    vget(
      `/vyper/v1/topsidecust/findCustMaxLen?pkg=${encodedPkg}&facility=${encodedFacility}&symbol_name=${encodedSymbolName}&pin_count=${encodedPin}`,
      (json) => {
        setMaxLens(json);
      }
    );
  }, [symbolization, pkg, facilityAt, pin]);

  const { findComponentMapByName } = useContext(ComponentMapContext);

  const componentMap = findComponentMapByName(name);
  // handle adjusting of the priorities

  const selectedIndex = selectionItems.findIndex(
    (s) => s.value === selectedComponent
  );
  const canMoveUp = selectedIndex > 0;
  const canMoveDown =
    selectedIndex < selectionItems.length - 1 && selectedIndex !== -1;
  const onMoveUp = () => swap(selectedIndex, selectedIndex - 1);
  const onMoveDown = () => swap(selectedIndex, selectedIndex + 1);
  const swap = (x1, x2) => {
    setSelectionItems(
      produce(selectionItems, (draft) => {
        const temp = draft[x1];
        draft[x1] = draft[x2];
        draft[x2] = temp;
      })
    );
  };

  const canHaveMultiplePriorities =
    componentMap?.allowMultiplePriorities == null ||
    componentMap?.allowMultiplePriorities === "YES";

  const classes = useStyles();

  const isBom = componentMap?.querySelected?.includes("BOM");

  let title = `Component: ${name}`;
  if (componentMap?.name !== componentMap?.atssComponentName) {
    title += ` (ATSS Component: ${componentMap?.atssComponentName})`;
  }

  return (
    <AtSelectionDialogContext.Provider
      value={{
        showSelectionDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="lg">
        <DialogTitle
          classes={{
            root: classes.title,
          }}
        >
          {title}
        </DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        {isBom ? (
          <div className={classes.bom}>
            The available components list comes from the ATSS bom for pin ={" "}
            {pin}, package = {pkg}, facility = {facilityAt}.
          </div>
        ) : null}

        <div className={classes.form}>
          <div>
            <FilteredAutocomplete
              mode="querySelected"
              title="Available Components"
              componentMap={componentMap}
              componentName={name}
              facility={facilityAt}
              pkg={pkg}
              pin={pin}
              pkgGroup={pkgGroup}
              supplierOrWire={supplierOrWire}
              onChange={handleClickAvailable}
              allowParentChildDies={allowParentChildDies}
              praNumber={praNumber}
            />

            {canHaveMultiplePriorities ? null : (
              <div className={classes.highlight}>
                {componentMap?.name} can have only 1 priority.
              </div>
            )}
          </div>

          <div>
            <AtAttributes
              facilityAt={facilityAt}
              name={name}
              value={attributeComponent}
            />

            <br />
            <br />
            <br />
            <hr />
            {disableAddEngineering === false &&
              componentMap?.engineeringAtssComponentName != null &&
              (name === "MB Diagram"
                ? build?.buildtype === Experimental
                : true) && (
                <AtEngineeringForm
                  value={engineering}
                  onChange={setEngineering}
                  onAdd={handleAddEngineering}
                />
              )}
          </div>

          <AtSelectionActions
            canSelect={canSelect}
            onSelect={handleSelect}
            canUnSelect={canUnSelect}
            onUnSelect={handleUnSelect}
            canReplace={canReplace}
            onReplace={handleReplace}
            canMoveUp={canMoveUp}
            onMoveUp={onMoveUp}
            canMoveDown={canMoveDown}
            onMoveDown={onMoveDown}
            canHaveMultiplePriorities={canHaveMultiplePriorities}
          />

          <AvailableList
            title="Selected"
            value={selectedComponent}
            options={selectionItems}
            onClick={handleClickSelected}
          />
        </div>

        <br />
        <br />

        <AtSelectionButtons onClose={handleClose} onSave={handleSave} />
      </Dialog>

      {children}
    </AtSelectionDialogContext.Provider>
  );
};

export const AtSelectionDialogContext = React.createContext(null);
