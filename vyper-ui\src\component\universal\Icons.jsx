import React, { forwardRef } from "react";

import EditIcon from "@material-ui/icons/Edit";
import DeleteIcon from "@material-ui/icons/Delete";
import GetAppIcon from "@material-ui/icons/GetApp";
import SaveIcon from "@material-ui/icons/Save";
import AddBoxRoundedIcon from "@material-ui/icons/AddBoxRounded";
import ObsoleteIcon from "@material-ui/icons/SignalCellularNoSim";
import CommentIcon from "@material-ui/icons/Comment";
import VisibilityIcon from "@material-ui/icons/Visibility";
import HighlightOffOutlinedIcon from "@material-ui/icons/HighlightOffOutlined";
import IconButton from "@material-ui/core/IconButton";
import AddBoxIcon from "@material-ui/icons/AddBox";
import AddBox from "@material-ui/icons/AddBox";
import IndeterminateCheckBoxIcon from "@material-ui/icons/IndeterminateCheckBox";
import FolderIcon from "@material-ui/icons/FolderOpenTwoTone";
import Refresh from "@material-ui/icons/Refresh";
import { BlurCircular, Memory } from "@material-ui/icons";
import FileCopyIcon from "@material-ui/icons/FileCopy";
import ClearIcon from "@material-ui/icons/Clear";
import Clear from "@material-ui/icons/Clear";
import ReplayIcon from "@material-ui/icons/Replay";
import ArrowDownward from "@material-ui/icons/ArrowDownward";
import Check from "@material-ui/icons/Check";
import ChevronLeft from "@material-ui/icons/ChevronLeft";
import ChevronRight from "@material-ui/icons/ChevronRight";
import FirstPage from "@material-ui/icons/FirstPage";
import LastPage from "@material-ui/icons/LastPage";
import Remove from "@material-ui/icons/Remove";
import SaveAlt from "@material-ui/icons/SaveAlt";
import Search from "@material-ui/icons/Search";
import ViewColumn from "@material-ui/icons/ViewColumn";
import CheckBoxOutlineBlank from "@material-ui/icons/CheckBoxOutlineBlank";
import FilterListIcon from "@material-ui/icons/FilterList";
import BlockIcon from "@material-ui/icons/Block";
import Help from "@material-ui/icons/Help";
import { Button } from "@material-ui/core";

const iconStyle1 = {
  smallIcon: {
    fontSize: "1rem",
  },
};

export const tableIcons = {
  Checkbox: forwardRef((props, ref) => (
    <CheckBoxOutlineBlank {...props} ref={ref} />
  )),
  Add: forwardRef((props, ref) => <AddBox {...props} ref={ref} />),
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => (
    <IconButton color="secondary">
      <DeleteIcon {...props} ref={ref} style={iconStyle1.smallIcon} />
    </IconButton>
  )),
  Download: forwardRef((props, ref) => (
    <IconButton color="secondary">
      <GetAppIcon {...props} ref={ref} style={iconStyle1.smallIcon} />
    </IconButton>
  )),
  DetailPanel: forwardRef((props, ref) => <></>),
  Edit: forwardRef((props, ref) => (
    <IconButton color="secondary">
      <EditIcon {...props} ref={ref} style={iconStyle1.smallIcon} />
    </IconButton>
  )),
  Export: forwardRef((props, ref) => (
    <SaveAlt {...props} ref={ref} fontSize="small" />
  )),
  Filter: forwardRef((props, ref) => (
    <FilterListIcon {...props} ref={ref} style={iconStyle1.smallIcon} />
  )),
  FirstPage: forwardRef((props, ref) => (
    <FirstPage {...props} ref={ref} fontSize="small" />
  )),
  LastPage: forwardRef((props, ref) => (
    <LastPage {...props} ref={ref} fontSize="small" />
  )),
  NextPage: forwardRef((props, ref) => (
    <ChevronRight {...props} ref={ref} fontSize="small" />
  )),
  PreviousPage: forwardRef((props, ref) => (
    <ChevronLeft {...props} ref={ref} fontSize="small" />
  )),
  Refresh: forwardRef((props, ref) => (
    <Refresh {...props} ref={ref} fontSize="small" />
  )),
  ResetSearch: forwardRef((props, ref) => (
    <Clear {...props} ref={ref} fontSize="small" />
  )),
  Search: forwardRef((props, ref) => (
    <Search {...props} ref={ref} fontSize="small" />
  )),
  SortArrow: forwardRef((props, ref) => (
    <ArrowDownward {...props} ref={ref} fontSize="small" />
  )),
  ThirdStateCheck: forwardRef((props, ref) => (
    <Remove {...props} ref={ref} fontSize="small" />
  )),
  ViewColumn: forwardRef((props, ref) => (
    <ViewColumn {...props} ref={ref} fontSize="small" />
  )),
};

const iconStyle = {
  smallIcon: {
    fontSize: "20px",
    padding: "2px",
  },
};

export const EditRecordIcon = () => {
  return (
    <IconButton color="secondary">
      <EditIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const CancelRecordIcon = () => {
  return <HighlightOffOutlinedIcon style={iconStyle.smallIcon} />;
};

export const ObsoleteRecordIcon = () => {
  return (
    <IconButton color="secondary">
      <ObsoleteIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const DeleteRecordIcon = () => {
  return (
    <IconButton color="secondary">
      <DeleteIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const DownloadRecordIcon = () => {
  return (
    <IconButton color="secondary">
      <GetAppIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const SaveRecordIcon = () => {
  return <SaveIcon style={iconStyle.smallIcon} />;
};

export const PlusIcon = () => {
  return <AddBoxRoundedIcon fontSize="small" />;
};

export const PlusRecordIcon = () => {
  return (
    <IconButton color="secondary">
      <AddBoxRoundedIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const ViewCommentIcon = () => {
  return (
    <IconButton color="secondary">
      <CommentIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const ViewRecordIcon = () => {
  return (
    <IconButton color="secondary">
      <VisibilityIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const ViewCommentWhiteIcon = () => {
  return <CommentIcon fontSize="small" />;
};

export const ViewIcon = () => {
  return <VisibilityIcon fontSize="small" />;
};

export const AddComponent = () => {
  return <AddBoxIcon style={iconStyle.smallIcon} />;
};

export const RemoveComponent = () => {
  return <IndeterminateCheckBoxIcon style={iconStyle.smallIcon} />;
};

export const YellowCloseFolder = () => {
  return (
    <FolderIcon
      style={{
        color: "#ffc800",
        smallIcon: {
          fontSize: "20px",
          padding: "3px",
        },
      }}
    />
  );
};
export const PinkCloseFolder = () => {
  return (
    <FolderIcon
      style={{
        color: "#ff75ff",
        smallIcon: {
          fontSize: "20px",
          padding: "3px",
        },
      }}
    />
  );
};
export const RedCloseFolder = () => {
  return (
    <FolderIcon
      style={{
        color: "#ff0000",
        smallIcon: {
          fontSize: "20px",
          padding: "3px",
        },
      }}
    />
  );
};

export const MemoryIcon = () => {
  return <Memory style={iconStyle.smallIcon} />;
};

export const BlurCircularIcon = () => {
  return <BlurCircular style={iconStyle.smallIcon} />;
};

export const AddRowIcon = () => {
  return (
    <IconButton color="secondary">
      <AddBoxIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};
export const HelpIcon = () => {
  return <Help style={{ fontSize: "25px" }} />;
};

export const CopyIcon = () => {
  return (
    <IconButton color="secondary">
      <FileCopyIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const RemoveIcon = () => {
  return (
    <IconButton color="secondary">
      <ClearIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const UndoIcon = () => {
  return (
    <IconButton color="secondary">
      <ReplayIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const RebuildIcon = () => {
  return (
    <IconButton color="secondary">
      <Refresh style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const RejectRowIcon = () => {
  return (
    <IconButton color="secondary">
      <BlockIcon style={iconStyle.smallIcon} />
    </IconButton>
  );
};

export const SelectRecordIcon = () => {
  return (
    <Button color="secondary" style={{ fontSize: "14px" }}>
      Select
    </Button>
  );
};
