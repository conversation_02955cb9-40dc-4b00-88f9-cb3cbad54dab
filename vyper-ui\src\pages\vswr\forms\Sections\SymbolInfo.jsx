import React from "react";
import Grid from "@material-ui/core/Grid";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";

const SymbolInfo = (props) => {
  const { classes, symbolData } = props;

  const Symbol = () => {
    if (!symbolData) {
      return "";
    }

    let symbol = "";

    symbolData.forEach((component, i) => {
      const { name, value, paragraph, attributes } = component;
      symbol += `${name}:\t${value}\n`;
      if (paragraph) {
        symbol += `${paragraph}\n`;
      }
      attributes.forEach(({ name, value }, i) => {
        symbol += `\t\t${name}:\t${value}\n`;
      });
    });
    return <pre>{symbol}</pre>;
  };

  return (
    <Paper elevation={24} className={classes.paper}>
      <Typography variant="h6">Symbol Information </Typography>
      <Grid>
        <Symbol />
      </Grid>
    </Paper>
  );
};
export default SymbolInfo;
