import Tooltip from "@material-ui/core/Tooltip";
import { IconButton } from "@material-ui/core";
import React from "react";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles(() => ({
  icon: {
    marginRight: ".5rem",
  },
}));

export const TestButton = ({ tooltipTitle, enabled, icon, onClick }) => {
  const classes = useStyles();

  const Icon = icon;

  return (
    <Tooltip title={tooltipTitle} placement="top" arrow>
      <span>
        <IconButton
          className={classes.icon}
          fontSize="small"
          color="primary"
          disabled={!enabled}
          onClick={onClick}
        >
          <Icon />
        </IconButton>
      </span>
    </Tooltip>
  );
};
