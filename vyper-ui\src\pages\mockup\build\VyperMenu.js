import { Menu, MenuItem } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import MenuIcon from "@material-ui/icons/Menu";
import PropTypes from "prop-types";
import React from "react";

const useStyles = makeStyles({
  menu: {
    cursor: "pointer",
    color: "red",
  },
});

/**
 *
 * Shows a hamburger icon, and when clicked it builds and shows the menu.
 *
 * @param items
 * @param onMenu
 * @returns {JSX.Element}
 * @constructor
 */
export const VyperMenu = ({ items, onMenu }) => {
  // reference to the dom location to parent the menu
  const [anchor, setAnchor] = React.useState(null);

  const classes = useStyles();

  const handleClose = () => {
    setAnchor(null);
  };

  const handleClickMenu = (action) => {
    handleClose();
    onMenu(action);
  };

  return (
    <div>
      <MenuIcon
        className={classes.menu}
        color="primary"
        onClick={(event) => setAnchor(event.currentTarget)}
      />
      <Menu
        anchorEl={anchor}
        keepMounted
        open={Boolean(anchor)}
        onClose={handleClose}
      >
        {items.map((item, n) => {
          if (item.enabled) {
            return (
              <MenuItem key={n} onClick={() => handleClickMenu(item.id)}>
                {item.title}
              </MenuItem>
            );
          } else {
            return null;
          }
        })}
      </Menu>
    </div>
  );
};

VyperMenu.propTypes = {
  items: PropTypes.array.isRequired,
  onMenu: PropTypes.func.isRequired,
};
