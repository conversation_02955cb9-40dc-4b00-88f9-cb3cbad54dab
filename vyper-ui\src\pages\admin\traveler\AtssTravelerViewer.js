import {
  <PERSON><PERSON>,
  FormControlLabel,
  FormGroup,
  makeStyles,
} from "@material-ui/core";
import React, { useContext, useEffect, useState } from "react";
import { BuildNumberAutocomplete } from "../../../autocomplete/components/BuildNumberAutocomplete";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { Traveler } from "../../../traveler";

const useStyles = makeStyles(() => ({
  root: {},
  widget: {
    width: "16rem",
  },
  valid: {
    backgroundColor: "lightgreen",
  },
  invalid: {
    backgroundColor: "pink",
  },
}));

/**
 * Allows the user to view the ATSS-Compatible generated traveler.
 *
 * @returns {JSX.Element}
 * @function
 */
export function AtssTravelerViewer() {
  const [number, setNumber] = useState("");
  const [traveler, setTraveler] = useState();
  const { vget } = useContext(FetchContext);

  useEffect(() => {
    if (number === "") {
      return;
    }

    handleClickShow();
  }, []);

  function handleClickShow() {
    if (number === "") {
      return;
    }

    vget(`/vyper/v1/traveler/${number}?variant=ATSS`, setTraveler);
  }

  const enabled = number !== "";

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <h3>ATSS Traveler Viewer</h3>

      <p>
        This form allows you to view the traveler that will get sent to ATSS
        when the VSCN is created.
      </p>

      <form className={classes.form}>
        <FormGroup row>
          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                variant="outlined"
                defaultNumber={number}
                onSelect={(number) => setNumber(number)}
              />
            }
          />
        </FormGroup>

        <Button
          variant="contained"
          color="primary"
          disabled={!enabled}
          onClick={handleClickShow}
        >
          Show
        </Button>
      </form>

      <hr />

      <div>
        <Traveler traveler={traveler} number={number} />
      </div>
    </div>
  );
}
