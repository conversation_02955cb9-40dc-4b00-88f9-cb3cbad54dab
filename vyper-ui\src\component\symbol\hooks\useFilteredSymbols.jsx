import { useEffect, useState } from "react";
import { filteredSymbolsApi } from "src/component/symbol/api/symbolsApi";

/**
 * Fetch and store the symbols for a facility and at.
 * @param {string} filter - The filter mode. FACILITY_PKG, or PKG, or ALL
 * @param {string} pkg - The package designator
 * @param {string} facilityAt - The facility A/T site
 * @return {{filteredSymbols: Symbol[], filteredSymbolObjects:SymbolObject[]}}
 */
export const useFilteredSymbols = (filter, pkg, facilityAt) => {
  const [filteredSymbols, setFilteredSymbols] = useState(
    /** @type {Symbol[]} */ []
  );

  /**
   * Given a package and facility, return the symbols assigned to it.
   *
   * @param {string} filter - The filter
   * @param {string} pkg - The package designator
   * @param {string} facilityAt the pdb facility name
   * @return {Promise<List<Symbol>>} a promise that resolves to a list of symbols
   */
  useEffect(() => {
    if (pkg == null || facilityAt == null || filter == null) {
      return;
    }

    filteredSymbolsApi(filter, pkg, facilityAt)
      .then((symbols) => setFilteredSymbols(symbols))
      .catch(console.log);
  }, [filter, pkg, facilityAt]);

  // parse out the symbol objects into its own array
  const filteredSymbolObjects = filteredSymbols.map((s) => s.object);

  return { filteredSymbols, filteredSymbolObjects };
};
