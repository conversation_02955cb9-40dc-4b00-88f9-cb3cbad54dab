import React, { useState } from "react";

import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS

const selectOptionsDefault = [20, 50, 100];

/**
 * @typedef {object} FilterCellRendererProps
 * @param {Function} onSelectChange
 * @param {Number} firstRow
 * @param {Number} lastRow
 * @param {Number} totalRows
 * @param {Number} currPage
 * @param {Number} totalPages
 * @property {number} [prop.defaultPageSize] Default page size on initial render.
 */
const TiPagination = (props) => {
  const {
    onSelectChange = null,

    firstRow = "?",
    lastRow = "?",
    totalRows = "?",
    currPage = "?",
    totalPages = "?",

    onFirstPageClick = null,
    onPrevPageClick = null,
    onNextPageClick = null,
    onLastPageClick = null,

    defaultPageSize = null,
  } = props;
  const selectOptions =
    props.selectOptions && props.selectOptions.length > 0
      ? props.selectOptions
      : selectOptionsDefault;

  const [pageSize, setPageSize] = useState(defaultPageSize || selectOptions[0]);

  const onSelect = (evt) => {
    onSelectChange(evt);
    setPageSize(evt.target.value);
  };

  const PageSelect = () => {
    let options = selectOptions.map((elem, i) => {
      return (
        <option className="page-size-select-option" value={elem} key={i}>
          {elem}
        </option>
      );
    });
    return (
      <select className="page-size-select" value={pageSize} onChange={onSelect}>
        {options}
      </select>
    );
  };

  return (
    <div className="ag-paging-panel" id="ti-paging">
      <span className="page-size-select-panel">
        <span id="select-text">{"Page size:"}</span>
        <PageSelect />
      </span>

      <span className="ag-paging-row-summary-panel">
        <span id="ti-first-row" className="ag-paging-row-summary-panel-number">
          {firstRow}
        </span>
        <span id="ti-to">{" to "}</span>
        <span id="ti-last-row" className="ag-paging-row-summary-panel-number">
          {lastRow}
        </span>
        <span id="ti-of">{" of "}</span>
        <span id="ti-row-count" className="ag-paging-row-summary-panel-number">
          {totalRows}
        </span>
      </span>

      <span className="ag-paging-page-summary-panel">
        <button
          className="ag-paging-button"
          disabled={currPage <= 1}
          onClick={onFirstPageClick}
        >
          <span className="ag-icon ag-icon-first" />
        </button>
        <button
          className="ag-paging-button"
          disabled={currPage <= 1}
          onClick={onPrevPageClick}
        >
          <span className="ag-icon ag-icon-previous" />
        </button>

        <span className="ag-paging-description">
          <span id="ti-start-page">{"Page "}</span>
          <span id="ti-start-page-number" className="ag-paging-number">
            {currPage}
          </span>
          <span id="ti-of-page">{" of "}</span>
          <span id="ti-of-page-number" className="ag-paging-number">
            {totalPages}
          </span>
        </span>

        <button
          className="ag-paging-button"
          disabled={currPage >= totalPages}
          onClick={onNextPageClick}
        >
          <span className="ag-icon ag-icon-next" />
        </button>
        <button
          className="ag-paging-button"
          disabled={currPage >= totalPages}
          onClick={onLastPageClick}
        >
          <span className="ag-icon ag-icon-last" />
        </button>
      </span>
    </div>
  );
};
export default TiPagination;
