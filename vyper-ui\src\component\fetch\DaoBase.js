/**
 * Build the options for the fetch function.
 *
 * @param method The HTTP method
 * @param body The body of the call, or null.
 * @returns {{headers: {Accept: string, "Cache-Control": string, Pragma: string, "Content-Type": string}, cache: string, method, credentials: string}}
 */
export const fetchData = (method, body) => {
  const data = {
    method: method,
    credentials: "include",
    cache: "no-cache",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Pragma: "no-cache",
      "Cache-Control": "no-cache",
    },
  };

  if (body != null) {
    data.body = JSON.stringify(body);
  }

  return data;
};

/////////////////////////////////////////////////////////////////////////////////////////////////
// ignore the error message
export class Noop<PERSON><PERSON>r<PERSON><PERSON><PERSON> {
  execute(message) {}
}

/////////////////////////////////////////////////////////////////////////////////////////////////
// log the error message to the console
export class ConsoleErrorHandler {
  execute(message) {
    console.log(message);
  }
}

/////////////////////////////////////////////////////////////////////////////////////////////////
// show the error message in an alert
export class AlertErrorHandler {
  execute(message) {
    alert(message);
  }
}

/////////////////////////////////////////////////////////////////////////////////////////////////
// show the Vyper AlertDialog
export class AlertDialogErrorHandler {
  /**
   *
   * @param open The AlertDialogContext's open function
   */
  constructor(open) {
    this.open = open;
  }

  execute(message) {
    this.open({
      title: "Error",
      message: message,
    });
  }
}

/////////////////////////////////////////////////////////////////////////////////////////////////

export class NoopLoadingHandler {
  increment() {}

  decrement() {}
}

/////////////////////////////////////////////////////////////////////////////////////////////////

export class ConsoleLoadingHandler {
  constructor() {
    this.count = 0;
  }

  increment() {
    this.count++;
    console.log("increment", this.count);
    return this.count;
  }

  decrement() {
    this.count--;
    console.log("decrement", this.count);
    return this.count;
  }
}

/////////////////////////////////////////////////////////////////////////////////////////////////
// use Vyper's SpinnerContext

export class SpinnerLoadingHandler {
  constructor(showSpinner, hideSpinner) {
    this.showSpinner = showSpinner;
    this.hideSpinner = hideSpinner;
  }

  increment() {
    this.showSpinner();
  }

  decrement() {
    this.hideSpinner();
  }
}

/////////////////////////////////////////////////////////////////////////////////////////////////

export class DaoBase {
  /**
   *
   * @param name The name of the dao
   * @param url The base url for the endpoint
   * @param errorHandler The error handler. defaults to ConsoleErrorHandler
   * @param enableLogs true of show logs on browser console
   * @param loadingHandler The loading handler. defaults to console loading handler.
   */
  constructor({
    name,
    url,
    errorHandler = new ConsoleErrorHandler(),
    enableLogs = false,
    loadingHandler = new ConsoleLoadingHandler(),
  }) {
    this.name = name;
    this.url = url;
    this.errorHandler = errorHandler;
    this.enableLogs = enableLogs;
    this.loadingHandler = loadingHandler;
  }

  log(...args) {
    if (this.enableLogs) {
      console.log(this.name, ...args);
    }
  }

  /**
   *
   * @param name The name of the function - used for logging
   * @param uri The uri of the endpoint the url + uri becomes the full URL
   * @param method GET, POST, DELETE
   * @param body The body of the POST, or undefined if there is no body.
   * @returns {Promise<any>}
   */
  handleFetch(name, uri, method, body = undefined) {
    this.log(name, uri, method, body, `${this.url}${uri}`);
    this.loadingHandler.increment();
    return fetch(`${this.url}${uri}`, fetchData(method, body))
      .then(
        (response) => {
          this.loadingHandler.decrement();
          return response;
        },
        (response) => {
          this.loadingHandler.decrement();
          return Promise.reject(response);
        }
      )
      .then((response) =>
        response.status === 204 ? response : response.json()
      )
      .then(
        (json) => {
          this.log(`${name} success - returned`, json);
          return json;
        },
        (response) => {
          this.log(`${name} failed - returned`, response);
          return this.handleError(response);
        }
      );
  }

  handleError(param1) {
    if (param1 instanceof Response) {
      return param1.json().then((json) => {
        this.errorHandler.execute(
          `Error ${param1.status} ${param1.statusText}\n\n${json.message}`
        );
        return Promise.reject(
          `Error ${param1.status} ${param1.statusText}\n\n${json.message}`
        );
      });
    } else {
      alert(`A error occurred. ${param1.toString()}`);
      return Promise.reject(`A error occurred. ${param1.toString()}`);
    }
  }
}
