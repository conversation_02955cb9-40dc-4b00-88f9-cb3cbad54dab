import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

/**
 * @typedef {object} Pra - Pra object
 * @property {number} id - The version number
 * @property {string} state - The state.
 * @property {string} vyperNumber - The Vyper number
 * @property {string} praNumber - The PRA number
 * @property {string} buildNumber - The build number
 * @property {string} description - The description.
 * @property {Material} material - The material
 * @property {Facility} facility - The facility
 * @property {object} PackageNiche - The package niche
 * @property {object[]} components - The components
 * @property {CommentsDialog~Comment[]} comments - The comments
 * @property {object} validatedComponents - The validated components
 * @property {object} diagramApprovals - The MB Diagram approvals
 * @property {object[]} diagramApprovals - The MB Diagram approvals
 * @property {object[]} verifiers - The verifiers
 */

export class PraDao extends DaoBase {
  constructor(params) {
    super({ name: "<PERSON>ra<PERSON><PERSON>", url: "/vyper/v1/pra", ...params });
    this.pra = params.pra || {};
    this.setPra = params.setPra || noop;
  }

  addPra(vyperNumber, buildNumber) {
    return this.handleFetch("addPra", `/addpra`, "POST", {
      vyperNumber,
      buildNumber,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  findByPraNumber(praNumber) {
    return this.handleFetch(
      "findByPraNumber",
      `/findPraByPraNumber/${praNumber}`,
      "GET"
    ).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  findAllByVyperNumber(vyperNumber) {
    return this.handleFetch(
      "findAllByVyperNumber",
      `/findAllByVyperNumber/${vyperNumber}`,
      "GET"
    ).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  changeDescription(vyperNumber, praNumber, description) {
    return this.handleFetch("changeDescription", `/description`, "POST", {
      vyperNumber,
      praNumber,
      description,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  addComment(
    vyperNumber,
    praNumber,
    username,
    userid,
    when,
    comment,
    operation
  ) {
    return this.handleFetch("addComment", `/add/comment`, "POST", {
      vyperNumber,
      praNumber,
      username,
      userid,
      when,
      comment,
      operation,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  countByBuildNumber(buildNumber) {
    return this.handleFetch(
      "countByBuildNumber",
      `/countByBuildNumber/${buildNumber}`,
      "GET"
    ).then((json) => {
      return json;
    });
  }

  findAllBuildNumbersByVyperNumber(vyperNumber) {
    return this.handleFetch(
      "findAllBuildNumbersByVyperNumber",
      `/findAllBuildNumbersByVyperNumber/${vyperNumber}`,
      "GET"
    ).then((json) => {
      return json;
    });
  }

  approvePra(vyperNumber, praNumber) {
    return this.handleFetch("approvePra", `/approve`, "POST", {
      vyperNumber,
      praNumber,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  buReworkPra(vyperNumber, praNumber) {
    return this.handleFetch("buReworkPra", `/buReworkPra`, "POST", {
      vyperNumber,
      praNumber,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  deletePra(vyperNumber, praNumber) {
    return this.handleFetch("deletePra", `/delete`, "POST", {
      vyperNumber,
      praNumber,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  refreshPra(vyperNumber, praNumber) {
    return this.handleFetch("refreshPra", `/refresh`, "POST", {
      vyperNumber,
      praNumber,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  refreshPraDieAttributes(vyperNumber, praNumber) {
    return this.handleFetch("refreshPra", `/refreshdies`, "POST", {
      vyperNumber,
      praNumber,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  changeComponents(vyperNumber, praNumber, name, oldValue, newValue) {
    return this.handleFetch("changePraComponents", `/components`, "POST", {
      vyperNumber,
      praNumber,
      name,
      oldValue,
      newValue,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  updatePra(pra) {
    return this.handleFetch("updatePra", `/update`, "POST", pra).then(
      (json) => {
        this.setPra(json);
        return json;
      }
    );
  }

  refreshPraMbDiagrams(vyperNumber, praNumber) {
    return this.handleFetch(
      "refreshPraMbDiagrams",
      `/refreshdiagrams`,
      "POST",
      { vyperNumber, praNumber }
    ).then((json) => {
      this.setPra(json);
      return json;
    });
  }

  toggleChecked(vyperNumber, praNumber, componentName, checked) {
    return this.handleFetch("toggleChecked", `/toggleChecked`, "POST", {
      vyperNumber,
      praNumber,
      componentName,
      checked,
    }).then((json) => {
      this.setPra(json);
      return json;
    });
  }
}
