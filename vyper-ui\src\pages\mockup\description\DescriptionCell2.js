import PropTypes from "prop-types";
import React, { useState } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { DescriptionDialog } from "./DescriptionDialog";

/**
 * @callback DescriptionCell2~onDescription Callback for get the item's description
 * @param {Vscn} vscn - The vscn object
 * @return {string} - The description
 */

/**
 * @callback DescriptionCell2~onEditable Callback for get the item's description can-be-edited value
 * @param {Vscn} vscn - The vscn object
 * @return {boolean} - true if the description is allowed to be edited.
 */

/**
 * @callback DescriptionCell2~onSave Callback for saving the description
 * @param {Vscn} vscn - The vscn object
 * @param {string} description - The description to save
 */

/**
 * Displays the clickable description in the cell.
 *
 * @param {*} item - The current item.
 * @param {DescriptionCell2~onDescription} onDescription - callback to retrieve the item's description.
 * @param {DescriptionCell2~onEditable} onEditable - callback to determine if the item description is editable.
 * @param {DescriptionCell2~onSave} onSave - callback to save the item
 * @returns {JSX.Element}
 * @constructor
 */
export function DescriptionCell2({ item, onDescription, onEditable, onSave }) {
  const [open, setOpen] = useState(false);

  const description = onDescription(item);

  function handleSave(text) {
    onSave(item, text);
    setOpen(false);
  }

  function handleClose() {
    setOpen(false);
  }

  function handleOpen() {
    setOpen(true);
  }

  return (
    <DataCell source={null}>
      <VyperLink onClick={handleOpen}>
        {description || "click to select"}
      </VyperLink>
      <DescriptionDialog
        open={open}
        initialDescription={description}
        onSave={handleSave}
        onClose={handleClose}
      />
    </DataCell>
  );
}

DescriptionCell2.propTypes = {
  item: PropTypes.object.isRequired,
  onDescription: PropTypes.func.isRequired,
  onEditable: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
};
