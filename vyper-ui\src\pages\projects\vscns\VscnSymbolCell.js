import React, { useState } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { VyperLink } from "../../mockup/VyperLink";
import { SymbolDialog } from "src/component/symbol/SymbolDialog";
import { custx } from "src/component/symbol/custx";

export const VscnSymbolCell = ({ vscn, onSave }) => {
  const [open, setOpen] = useState(false);
  const canEdit = vscn?.state === "VSCN_DRAFT";

  if (vscn == null) {
    return null;
  }

  const symbol = vscn.symbolization.symbols[0];

  const text =
    symbol?.object?.name == null
      ? "click to select symbol"
      : `${symbol.object.location} = ${symbol.object.name}`;

  // get the ecat
  const ecatValue = vscn.components.find((c) => c.name === "ECAT")?.instances[0]
    ?.priorities[0]?.object?.name;
  const ecatText = `ECAT = ${ecatValue || "click to select"}`;

  // get the custs
  const custs = [];
  custx.map((name) => {
    const component = vscn.components.find((c) => c.name === name);
    if (component != null) {
      const value = component.instances[0]?.priorities[0]?.object?.name;
      const ignoreBlank =
        component.instances[0]?.priorities[0]?.object?.ignoreBlank;
      custs.push({ name, value, ignoreBlank });
    }
  });

  const symbolName = vscn.symbolization.symbols[0]?.object?.name;

  return (
    <DataCell source={symbol?.source}>
      <VyperLink onClick={() => setOpen(true)} canEdit={canEdit}>
        <div>{text}</div>
        <div>{ecatText}</div>
        {custs.map((cust) => (
          <div key={cust.name}>
            {cust.name} = {cust.value}{" "}
            {(cust.value == null || cust.value === "") &&
            cust.ignoreBlank === "Y"
              ? "- intentional blank -"
              : ""}
          </div>
        ))}
      </VyperLink>

      <SymbolDialog
        open={open}
        onClose={() => setOpen(false)}
        facilityAt={vscn.facility.object.PDBFacility}
        pkg={vscn.material.object.PackageDesignator}
        pin={vscn.material.object.PackagePin}
        defaultName={symbolName}
        defaultEcat={ecatValue}
        defaultCusts={custs}
        onSave={(location, name, picture, ecat, custs) =>
          onSave(vscn, location, name, picture, ecat, custs).then(() =>
            setOpen(false)
          )
        }
      />
    </DataCell>
  );
};
