import React from "react";
import Autocomplete from "@material-ui/lab/Autocomplete";
import TextField from "@material-ui/core/TextField";
import { useTravelerFacility } from "src/component/atss/hooks/useTravelerFacility";

export const AtssTravelerFacilitySelect = ({
  specDevice = "",
  facilityAt = "",
  onChange,
  showSelectedText,
}) => {
  const [value, setValue] = React.useState(specDevice);

  // search is a blank string so we get the entire list of facilities, and not a filtered list
  const { options } = useTravelerFacility(specDevice, "");

  const enabled = !!specDevice;

  return (
    <Autocomplete
      disabled={!enabled}
      inputValue={value}
      onInputChange={(e, v) => setValue(v)}
      value={facilityAt || ""}
      onChange={(e, v) => onChange(v)}
      style={{ minWidth: "10em" }}
      options={
        options.includes(facilityAt) ? options : [...options, facilityAt]
      }
      renderInput={(params) => (
        <TextField
          {...params}
          label="Facility"
          variant="outlined"
          InputLabelProps={{ shrink: true }}
          helperText={
            showSelectedText
              ? !facilityAt
                ? "not selected"
                : `selected: ${facilityAt}`
              : ""
          }
          size="small"
        />
      )}
      getOptionLabel={(o) => o || ""}
      openOnFocus
      autoHighlight
    />
  );
};
