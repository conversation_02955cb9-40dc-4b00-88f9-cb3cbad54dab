import "whatwg-fetch";
import "ti-fetch";

import config from "../../buildEnvironment";

// Set default configuration for ti-fetch
fetch.configure({
  gatewayUrl: config.envApiGatewayUrl, // modify for your environment
  isSecure: true,
});

// Messages to users by response status
const userErrorMessageHttp401 = "You are not logged in. Please login first.";
const userErrorMessageHttp403 =
  "You do not have enough privileges to do that transaction.";
const userErrorMessageGeneric = "System has encountered an unexpected error";
// Utility variables
const headers = {
  read: new Headers({
    // "Pragma": "no-cache",
    "Cache-Control": "no-cache",
    // "X-ZUUL-TI-SECRET": "dcda53e2-cd65-4cd4-be1b-279579baa648", //FIX_ME
    // "X-ZUUL-TI-UID": "a0274225" //FIX_ME
  }),
  write: new Headers({
    "Content-Type": "application/json",
    Accept: "application/json",
    // "Pragma": "no-cache",
    "Cache-Control": "no-cache",
    // "X-ZUUL-TI-SECRET": "dcda53e2-cd65-4cd4-be1b-279579baa648", //FIX_ME
    // "X-ZUUL-TI-UID": "a0274225" //FIX_ME
  }),
};

/**
 * Utility function for simplified fetch call with built-in error notification.
 */
function simpleFetch(url, options, successCallback, errorCallback) {
  fetch(url, options)
    .then((response) => {
      return response
        .json()
        .then((json) => {
          return {
            status: response.status,
            body: json,
          };
        })
        .catch((error) => {
          // If JSON parsing of the response hits a snag, just return the response itself
          return {
            status: response.status,
            body: response,
          };
        });
    })
    .then((response) => {
      if (
        (response.status >= 200 && response.status < 300) ||
        response.status === 400
      ) {
        // If status is ~200 or 400 (validation error return), then proceed to callback
        if (typeof successCallback === "function") {
          successCallback(response.status, response.body);
        }
      } else if (response.status === 401) {
        // If redirect 401 is detected, return a user-friendly message or just log it
        console.debug(userErrorMessageHttp401);
      } else if (response.status === 403) {
        // If not enough privileges, return a user-friendly message
        console.debug(userErrorMessageHttp403);
      } else if (!!response.error) {
        // Start assuming that response resulted in an error
        // First, check if an error is returned
        var error = new Error(response.error.message);
        error.status = response.status;
        error.response = response;
        throw error;
      } else {
        // If all else fails, return a generic error message
        var defaulterror = new Error(userErrorMessageGeneric);
        defaulterror.status = response.status;
        defaulterror.response = response;
        throw defaulterror;
      }
    })
    .catch((error) => {
      //errorModal(error.message) // Display a modal with the error message
      if (typeof errorHandler === "function") {
        errorCallback(error); // Execute additional instructions for error handling
      }
    });
}

export function fetchGet(url, successCallback, errorCallback) {
  return simpleFetch(
    url,
    {
      method: "GET",
      headers: headers.read,
      credentials: "include",
    },
    successCallback,
    errorCallback
  );
}

export function fetchPostMultipart(
  url,
  formData,
  successCallback,
  errorCallback
) {
  return simpleFetch(
    url,
    {
      method: "POST",
      body: formData,
      headers: new Headers({
        Accept: "application/json",
      }),
      credentials: "include",
    },
    successCallback,
    errorCallback
  );
}

export function fetchPost(url, data, successCallback, errorCallback) {
  data = data || "";
  data = typeof data === "object" ? JSON.stringify(data) : data;
  return simpleFetch(
    url,
    {
      method: "POST",
      body: data,
      headers: headers.write,
      credentials: "include",
    },
    successCallback,
    errorCallback
  );
}

export function fetchPut(url, data, successCallback, errorCallback) {
  data = data || "";
  data = typeof data === "object" ? JSON.stringify(data) : data;
  return simpleFetch(
    url,
    {
      method: "PUT",
      body: data,
      headers: headers.write,
      credentials: "include",
    },
    successCallback,
    errorCallback
  );
}

export function fetchDelete(url, successCallback, errorCallback) {
  return simpleFetch(
    url,
    {
      method: "DELETE",
      headers: headers.write,
      credentials: "include",
    },
    successCallback,
    errorCallback
  );
}

export default {
  fetchGet,
  fetchPostMultipart,
  fetchPost,
  fetchPut,
  fetchDelete,
};
