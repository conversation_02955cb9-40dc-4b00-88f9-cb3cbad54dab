import React, { useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Button from "@material-ui/core/Button";
import DialogActions from "@material-ui/core/DialogActions";
import { DataGrid } from "../../../component/universal";
import { Checkbox } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialog: {
    padding: "3rem",
    margin: "3rem",
  },
}));

export const PcnDialog = ({ children }) => {
  const [rows, setRows] = useState([]); // the selected pcn objects
  const [onSave, setOnSave] = useState();
  const [open, setOpen] = useState(false);

  const handleOpen = ({ rows, onSave }) => {
    setRows(rows);
    setOnSave(() => onSave);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = () => {
    typeof onSave === "function" && onSave(rows);
    handleClose();
  };

  const contains = (change) =>
    rows.some((r) => r.pcnNumber === change.pcnNumber);

  const handleChange = (row) => {
    if (contains(row)) {
      setRows(rows.filter((r) => r.pcnNumber !== row.pcnNumber));
    } else {
      setRows(() => [...rows, row]);
    }
  };

  const columns = [
    {
      field: "selected",
      title: "Select",
      filtering: false,
      sorting: false,
      render: (rowData) => {
        return (
          <div>
            <Checkbox
              color="secondary"
              checked={contains(rowData)}
              onChange={() => handleChange(rowData)}
            />
          </div>
        );
      },
    },
    { field: "pcnNumber", title: "PCN Number" },
    { field: "projectName", title: "Project Name" },
    { field: "pcnTitle", title: "PCN Title" },
    { field: "groupName", title: "Group Name" },
  ];

  const classes = useStyles();

  return (
    <PcnDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="xl">
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DataGrid
          title="Select PCNs"
          url={`/vyper/v1/changelink/pcn/search`}
          columns={columns}
          pageable
          pageSize={20}
          pageSizeOptions={[5, 10, 20, 50, 100, 200]}
          actions={[
            {
              icon: () => <CloseIcon />,
              tooltip: "Close",
              isFreeAction: true,
              onClick: handleClose,
            },
          ]}
        />

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </PcnDialogContext.Provider>
  );
};

export const PcnDialogContext = React.createContext(null);
