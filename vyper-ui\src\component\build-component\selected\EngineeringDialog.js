import React, { useState } from "react";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import TextField from "@material-ui/core/TextField";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";

export const EngineeringDialog = ({ open, onClose, onSave }) => {
  const [value, setValue] = useState();
  const handleChangeValue = (e) => setValue(e.target.value);
  const handleSave = () => {
    onSave(value);
    setValue(undefined);
  };

  return (
    <Dialog open={open} onClose={() => onClose()}>
      <DialogTitle>Add Engineering Component</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Use this dialog to add a new supplier material that is not available,
          or it is not approved for the A/T's BOM.
        </DialogContentText>
        <TextField
          variant="outlined"
          autoFocus
          margin="dense"
          label="Supplier Material Description"
          type="text"
          fullWidth
          value={value || ""}
          onChange={handleChangeValue}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary" variant="outlined">
          Cancel
        </Button>
        <Button onClick={handleSave} color="primary" variant="contained">
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};
