// noinspection JSUnresolvedVariable,JSCheckFunctionSignatures

import makeStyles from "@material-ui/core/styles/makeStyles";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import React, { useContext, useEffect, useState } from "react";
import useSnackbar from "../../../hooks/Snackbar";
import { BackToBuildFormLink } from "src/component/backbutton/BackToBuildFormLink";
import { DataModelsContext } from "src/DataModel";
import { DescriptionRow } from "src/pages/mockup/description/DescriptionRow";
import { ScswrControlNumberRow } from "src/pages/mockup/scswrcontrolnumber/ScswrControlNumberRow";
import { BuildPageTitle } from "src/pages/vyper/BuildPageTitle";
import { ComponentRow } from "../../../component/build-component/ComponentRow";
import { submitForApproval } from "../../functions/submitForApproval";
import { BackgrindRow } from "../../mockup/backgrind/BackgrindRow";
import { BomTemplateRow } from "../../mockup/bomtemplate/BomTemplateRow";
import { BuildNumberRow } from "../../mockup/build/BuildNumberRow";
import { BuildTypeRow } from "../../mockup/buildtype/BuildTypeRow";
import { ChangeRow } from "../../mockup/changelink/ChangeRow";
import { PcnRow } from "../../mockup/changelink/PcnRow";
import { BuildCommentRow } from "../../mockup/comment/BuildCommentRow";
import { CopyFromRow } from "../../mockup/copyfrom/CopyFromRow";
import { DieRow } from "../../mockup/die/DieRow";
import { DryBakeRow } from "../../mockup/drybake/DryBakeRow";
import { EslRow } from "../../mockup/esl/EslRow";
import { FacilityRow } from "../../mockup/facility/FacilityRow";
import { MaterialRow } from "../../mockup/material/MaterialRow";
import { PackageNicheRow } from "../../mockup/packageniche/PackageNicheRow";
import { PackConfigRow } from "../../mockup/packconfig/PackConfigRow";
import { ScswrRow } from "../../mockup/scswr/ScswrRow";
import { SymbolizationRow } from "../../mockup/symbol/SymbolizationRow";
import { TestRow } from "../../mockup/test/TestRow";
import { TurnkeyRow } from "../../mockup/turnkey/TurnkeyRow";
import { WaferSawMethodRow } from "../../mockup/wafersawmethod/WaferSawMethodRow";
import { WorkFlowRow } from "../../mockup/workflow/WorkFlowRow";
import { BuildFlowRow } from "../../mockup/flow/BuildFlowRow";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { ConfirmationDialogContext } from "src/component/cornfirmation/ConfirmationDialog";
import { useHistory } from "react-router-dom";
import { rejectTask } from "src/pages/mockup/workflow/approvalHelpers";

const useStyles = makeStyles(() => ({
  root: {},
  table: {},
  tab: {
    position: "fixed",
  },
}));

export const VyperBuildFormPage = ({ vyperNumber, buildNumber }) => {
  const { open: openConfirm } = useContext(ConfirmationDialogContext);
  const { vyper, buildDao, praDao, build } = useContext(DataModelsContext);
  const [builds, setBuilds] = useState([]);
  const [buildsWithPras, setBuildsWithPras] = useState([]);
  const { enqueueErrorSnackbar } = useSnackbar();
  const { open: openError } = useContext(ErrorDialogContext);
  const classes = useStyles();
  const history = useHistory();

  // if vyper number changes, updates the builds
  useEffect(() => {
    reloadBuild();
    reloadBuildsWithPras();
  }, [vyperNumber]);

  const reloadBuild = () => {
    return buildDao
      .findByBuildNumber(buildNumber)
      .then((json) => setBuilds([json]))
      .catch((error) => {
        openError({ error, title: "Loading Build Error" });
      });
  };

  const reloadBuildsWithPras = () => {
    praDao
      .findAllBuildNumbersByVyperNumber(vyperNumber)
      .then((buildNumbers) => {
        setBuildsWithPras(buildNumbers);
      })
      .catch((error) => {
        openError({ error, title: "Loading Builds Error" });
      });
  };

  const handleChangeBuild = (updatedBuild) => setBuilds([updatedBuild]);

  const handleSubmitForApproval = (action, build) => {
    return submitForApproval(vyper, build, action, buildDao)
      .then(handleChangeBuild)
      .catch((error) => {
        enqueueErrorSnackbar("An error occurred.");
        openError({ error, title: "Submit for Approval Error" });
      });
  };

  const handleDeleteBuild = (action, build) =>
    openConfirm({
      title: "Delete Build",
      message: `Are you sure you want to delete ${build.buildNumber}?`,
      onYes: () => deleteTheBuild(action, build),
    });

  const deleteTheBuild = (action, build) => {
    // notify the api of the deletion
    buildDao
      .changeWorkflow(
        vyper.vyperNumber,
        build.buildNumber,
        action.toLowerCase(),
        "BU_OWNER"
      )
      .then(() => {
        // redirect to the project page
        history.push(`/projects/${vyper.vyperNumber}`);
      })
      .catch((err) => openError({ title: "Delete Build", error: err }));
  };

  const handleChangeWorkflow = (action, build, context) => {
    switch (action.toLowerCase()) {
      case "rework":
        return handleAtRework(build, context);

      case "bu rework":
        return handleBuRework(build, context);

      default:
        return buildDao
          .changeWorkflow(
            vyper.vyperNumber,
            build.buildNumber,
            action.toLowerCase(),
            "BU_OWNER"
          )
          .then(handleChangeBuild)
          .catch((err) => openError({ title: `${action} Error`, error: err }));
    }
  };

  const handleAtRework = (build, context) => {
    // rework the task
    return rejectTask(
      context.tasks,
      context.authUser,
      context.comment,
      context.reason,
      context.group
    )
      .then(() => {
        // update the workflow
        return buildDao.changeWorkflow(
          vyper.vyperNumber,
          build.buildNumber,
          "rework",
          context.group,
          context.reason
        );
      })
      .then(handleChangeBuild)
      .catch((err) => openError({ title: `Rework Error`, error: err }));
  };

  const handleBuRework = (build, context) => {
    // rework the task
    return buildDao
      .changeWorkflow(
        vyper.vyperNumber,
        build.buildNumber,
        "bu rework",
        context.group,
        context.reason
      )
      .then(handleChangeBuild)
      .catch((err) => openError({ title: `Rework Error`, error: err }));
  };

  /**
   * Call the api to make a change to the build's description
   * @param description String The text of the description
   * @param build Build the current build.
   */
  const handleDescriptionChange = (description, build) => {
    return buildDao
      .changeDescription(vyper.vyperNumber, build.buildNumber, description)
      .then((json) => handleChangeBuild(json))
      .catch((error) => {
        enqueueErrorSnackbar("An error occurred.");
        openError({ error, title: "Change Description Error" });
      });
  };

  const handleScswrControlNumberChange = (scswrcontrolnumber, build) => {
    return buildDao
      .changeScswrControlNumber(
        vyper.vyperNumber,
        build.buildNumber,
        scswrcontrolnumber
      )
      .then((json) => handleChangeBuild(json))
      .catch((error) => {
        openError({ error, title: "Change SCSWR Number Error" });
      });
  };

  const handleAddComment = (comment, build) => {
    return buildDao
      .addComment(
        vyper.vyperNumber,
        build.buildNumber,
        comment.who.username,
        comment.who.userid,
        comment.when,
        comment.text,
        comment.operation
      )
      .then((json) => handleChangeBuild(json))
      .catch((error) => {
        openError({ error, title: "Change Comment Error" });
      });
  };
  /////////////////////////////////////////////////////////////////////////////////////////

  const showSameness = false;

  if (vyper == null || builds == null || builds.length === 0) return null;

  const filteredBuilds = [builds[0]];

  const defaultHideFlag =
    filteredBuilds[0].buildFlow?.flowRows?.length > 0 || false;
  const showRows = {};
  [
    "Dies",
    "Probe",
    "Bump",
    "Wafer Saw Method",
    "Leadframe",
    "Mount Compound",
    "MB Diagram",
    "Wire",
    "Mold Compound",
    "Symbolization",
    "Test",
    "Pack Config",
    "MSL",
    "Dry Bake",
    "ESL",
    "Turnkey",
    "Backgrind",
  ].forEach((flowName) => {
    showRows["" + flowName] = !defaultHideFlag;
  });

  if (filteredBuilds[0].buildFlow?.flowRows?.length > 0) {
    const buildRowsDefinition = filteredBuilds[0].buildFlow.flowRows;
    buildRowsDefinition.forEach((flowRowConfig) => {
      showRows[flowRowConfig.opnName] = true;
    });
  }

  return (
    <div className={classes.root}>
      <BackToBuildFormLink
        vyperNumber={vyperNumber}
        build={build}
        className={classes.tab}
      />

      <BuildPageTitle build={builds[0]} title="Build Form" />

      <Table className={classes.table} size="small">
        <TableBody>
          <BuildNumberRow
            vyper={vyper}
            vyperNumber={vyperNumber}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            buildsWithPras={buildsWithPras}
          />
          <BuildFlowRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <WorkFlowRow
            vyper={vyper}
            builds={filteredBuilds}
            setBuilds={setBuilds}
            onChange={handleChangeWorkflow}
            onSubmit={handleSubmitForApproval}
            onDelete={handleDeleteBuild}
          />
          <BuildTypeRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <DescriptionRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
            handleRowChange={handleDescriptionChange}
          />
          <ScswrControlNumberRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
            handleRowChange={handleScswrControlNumberChange}
          />
          <BuildCommentRow
            vyper={vyper}
            builds={filteredBuilds}
            handleAddComment={handleAddComment}
            showSameness={showSameness}
          />
          <MaterialRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <FacilityRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          {showRows["Backgrind"] && (
            <BackgrindRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          <CopyFromRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <PackageNicheRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <BomTemplateRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
          />
          {showRows["Dies"] && (
            <DieRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Wafer Saw Method"] && (
            <WaferSawMethodRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Leadframe"] && (
            <ComponentRow
              title="Leadframe"
              vyper={vyper}
              builds={filteredBuilds}
              onSave={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Mount Compound"] && (
            <ComponentRow
              title="Mount Compound"
              vyper={vyper}
              builds={filteredBuilds}
              onSave={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["MB Diagram"] && (
            <ComponentRow
              title="MB Diagram"
              vyper={vyper}
              builds={filteredBuilds}
              onSave={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Wire"] && (
            <ComponentRow
              title="Wire"
              vyper={vyper}
              builds={filteredBuilds}
              onSave={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Mold Compound"] && (
            <ComponentRow
              title="Mold Compound"
              vyper={vyper}
              builds={filteredBuilds}
              onSave={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Symbolization"] && (
            <SymbolizationRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Test"] && (
            <TestRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Pack Config"] && (
            <PackConfigRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["MSL"] && (
            <ComponentRow
              title="MSL"
              vyper={vyper}
              builds={filteredBuilds}
              onSave={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Dry Bake"] && (
            <DryBakeRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["ESL"] && (
            <EslRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}
          {showRows["Turnkey"] && (
            <TurnkeyRow
              vyper={vyper}
              builds={filteredBuilds}
              onChange={handleChangeBuild}
              showSameness={showSameness}
            />
          )}

          <ChangeRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <PcnRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <ScswrRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
          />
        </TableBody>
      </Table>
    </div>
  );
};
