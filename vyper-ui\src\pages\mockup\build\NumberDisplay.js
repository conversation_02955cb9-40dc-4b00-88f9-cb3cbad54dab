import PropTypes from "prop-types";
import React from "react";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  header: {
    display: "inline",
  },
});

/**
 * Display the number as a colum header.
 *
 * @param number
 * @returns {JSX.Element}
 * @constructor
 */
export const NumberDisplay = ({ number }) => {
  const classes = useStyles();
  return (
    <div>
      <h2 className={classes.header}>{number}</h2>
    </div>
  );
};

NumberDisplay.propTypes = {
  number: PropTypes.string.isRequired,
};
