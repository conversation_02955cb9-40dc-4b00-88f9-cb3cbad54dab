import React from "react";
import { DataGrid } from "../../../component/universal";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  root: {},
});

export const CustMaxChar = () => {
  const columns = [
    { field: "facilityAt", title: "Facility A/T", editable: "never" },
    { field: "pkgGroup", title: "Package Group", editable: "never" },
    { field: "pkg", title: "Package", editable: "never" },
    { field: "pinCount", title: "Pins", editable: "never" },
    { field: "symbolLocation", title: "Location", editable: "never" },
    { field: "symbolName", title: "Symbol Name" },
    { field: "custFieldName", title: "Cust Field Name" },
    { field: "maxLen", title: "Maximum Length" },
  ];

  // post the new symbol name to the server
  const handleUpdate = (newValue, oldValue, rowData, columnDef) => {
    let data = {
      method: "POST",
      credentials: "include",
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Pragma: "no-cache",
        "Cache-Control": "no-cache",
      },
      body: JSON.stringify({
        ...rowData,
        [columnDef.field]: newValue,
      }),
    };

    return new Promise((resolve, reject) => {
      return fetch(`/vyper/v1/topside_cust_max_char/${rowData.id}`, data).then(
        (response) => {
          if (response.ok) {
            rowData[columnDef.field] = newValue;
            return resolve();
          } else {
            return reject();
          }
        }
      );
    });
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Topside Symbols"
        url={`/vyper/v1/topside_cust_max_char/search`}
        actions={[]}
        columns={columns}
        pageable
        editable={true}
        cellEditable={{
          onCellEditApproved: handleUpdate,
        }}
      />
    </div>
  );
};
