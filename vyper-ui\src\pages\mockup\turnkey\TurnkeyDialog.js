import React, { useContext, useState } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { FetchContext } from "src/component/fetch/VyperFetch";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import FormControl from "@material-ui/core/FormControl";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  autocomplete: {
    minWidth: "13rem",
  },
  dialog: {
    padding: "3rem",
    margin: "3rem",
  },
}));

export const TurnkeyDialog = ({ children }) => {
  const emptyFunctionObject = {
    fn: () => {},
  };

  const { vget } = useContext(FetchContext);
  const [value, setValue] = useState();
  const [onSave, setOnSave] = useState(emptyFunctionObject);
  const [open, setOpen] = useState(false);

  const handleOpen = ({ value, onSave }) => {
    setValue(value);
    setOnSave({ fn: onSave });
    setOpen(true);
  };

  const handleClose = () => {
    setValue(undefined);
    setOnSave(emptyFunctionObject);
    setOpen(false);
  };

  const handleSave = () => {
    onSave.fn(value);
    handleClose();
  };

  const [turnkeys, setTurnkeys] = useState(["TKY", "NON-TKY"]);

  const handleChange = (e) => setValue(e.target.value);

  const classes = useStyles();

  return (
    <TurnkeyDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="xl">
        <DialogTitle>Select Turnkey</DialogTitle>

        <DialogContent>
          <DialogContentText>Choose the Turnkey value.</DialogContentText>

          <FormControl
            fullWidth
            variant="outlined"
            className={classes.formControl}
          >
            <Select value={value || ""} onChange={handleChange}>
              {["TKY", "NON-TKY"].map((level) => (
                <MenuItem key={level} value={level}>
                  {level}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </TurnkeyDialogContext.Provider>
  );
};

export const TurnkeyDialogContext = React.createContext(null);
