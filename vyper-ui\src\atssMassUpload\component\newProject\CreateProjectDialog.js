import React, { useEffect, useState, useContext } from "react";
import {
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Description } from "../../../component/newBuild/Description";
import { ProjectType } from "./ProjectType";
import { AutocompleteAtssDevice } from "../../../pages/mockup/fillcomponent/AutocompleteAtssDevice";
import { AutocompleteAtssFacility } from "../../../pages/mockup/fillcomponent/AutocompleteAtssFacility";
import { AutoFetchAtssStatus } from "../../../pages/mockup/fillcomponent/AutoFetchAtssStatus";
import { AutocompleteTargetFacility } from "./AutocompleteTargetFacility";
import { ChangeDialogContext } from "../../../pages/mockup/changelink/ChangeDialog";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { VyperLink } from "../../../pages/mockup/VyperLink";

const useStyles = makeStyles((theme) => ({
  paper: {
    minWidth: "1000px",
  },
  root: {
    minWidth: "1500px",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  flexRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    gap: "10px",
  },
  center: {
    display: "flex",
    justifyContent: "center",
    marginBottom: "0px",
    marginTop: "0px",
  },
}));

export const CreateProjectDialog = ({
  open,
  handleCloseDialog,
  projectName,
  projectType,
  targetFacility,
  onChangeTargetFacility,
  atssSpecDevice,
  atssFacility,
  atssStatus,
  cmsNumber,
  handleChangeProjectName,
  handleProjectTypeChange,
  onChangeAtssSpecDevice,
  onChangeAtssFacility,
  onChangeAtssStatus,
  handleChangeCMSNumber,
  handleClickSave,
}) => {
  const classes = useStyles();

  const checkProjectNameError = () =>
    projectName === "" || !projectName?.trim().length;

  const showSave =
    checkProjectNameError() === false &&
    projectType != undefined &&
    atssSpecDevice != undefined &&
    atssFacility != undefined &&
    atssStatus != undefined &&
    targetFacility != undefined;

  const { vget } = useContext(FetchContext);
  const { open: changeDialogOpen } = useContext(ChangeDialogContext);

  const [changeObj, setChangeObj] = useState(null);

  useEffect(() => {
    if (cmsNumber == undefined || cmsNumber === "") {
      return undefined;
    }

    vget(`/vyper/v1/changelink/change/findByChangeNumber?changeNumber=${cmsNumber}`, (json) => {
      setChangeObj(json);
    });
  }, [cmsNumber]);

  const handleChangeLinkChange = (changes) => {
    handleChangeCMSNumber(changes.length !== 0 ? changes[0].changeNumber : undefined);
  };

  const handleOpen = () => {
    changeDialogOpen({
      multiSelect: false,
      rows: cmsNumber == undefined || cmsNumber === "" ? [] : [changeObj],
      onSave: (rows) => handleChangeLinkChange(rows),
    });
  };

  return (
    <Dialog
      classes={{ paper: classes.paper }}
      open={open}
      fullWidth
      onClose={handleCloseDialog}
    >
      <DialogTitle className={classes.title}>Create Project</DialogTitle>

      <DialogContent>
        <div style={{ overflow: "hidden", height: "100%", width: "100%" }}>
          <div
            style={{
              paddingRight: 17,
              height: "100%",
              width: "100%",
              boxSizing: "content-box",
              overflow: "scroll",
              overflowX: "hidden",
            }}
          >
            <div className={classes.flexRow}>
              <Description
                label="Project Name"
                placeholder="Project Name"
                description={projectName}
                onChange={handleChangeProjectName}
                error={checkProjectNameError()}
              />

              <ProjectType
                projectType={projectType}
                onChange={handleProjectTypeChange}
              />
            </div>

            <h4 className={classes.center}>Reference Traveler</h4>
            <div className={classes.flexRow}>
              <AutocompleteAtssDevice
                device={atssSpecDevice}
                onChangeDevice={onChangeAtssSpecDevice}
              />
              <AutocompleteAtssFacility
                device={atssSpecDevice}
                facility={atssFacility}
                onChangeFacility={onChangeAtssFacility}
              />
              <AutoFetchAtssStatus
                device={atssSpecDevice}
                facility={atssFacility}
                status={atssStatus}
                onChangeStatus={onChangeAtssStatus}
              />
            </div>

            <AutocompleteTargetFacility
              facility={targetFacility}
              onChangeFacility={onChangeTargetFacility}
            />

            <div style={{ display: "flex", flexDirection: "row", gap: "0.2rem", justifyContent: "center", marginTop: "0.5rem" }}>
              <div>
                Changelink CMS Number:
              </div>
              <VyperLink onClick={() => handleOpen()}>
                {cmsNumber || "click to select"}
              </VyperLink>
            </div>
          </div>
        </div>
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="primary" onClick={handleCloseDialog}>
          Cancel
        </Button>
        <Button
          disabled={!showSave}
          variant="contained"
          color="primary"
          onClick={handleClickSave}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};
