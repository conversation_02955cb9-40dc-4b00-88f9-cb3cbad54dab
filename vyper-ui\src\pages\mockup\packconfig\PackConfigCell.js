import React, { useContext, useEffect, useState } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import {
  existsInFlow,
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
} from "../../vyper/FormStatus";
import { HelperContext } from "src/component/helper/Helpers";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { QuestionDialogContext } from "../../../component/question/QuestionDialog";

export const PackConfigCell = ({ vyper, build, onChange }) => {
  const { showQuestionDialog } = useContext(QuestionDialogContext);
  const { buildDao } = useContext(DataModelsContext);
  const [values, setValues] = useState([]);

  useEffect(() => {
    if (vyper == null || build == null) return;

    buildDao
      .listPackConfigs(vyper.vyperNumber, build.buildNumber)
      .then(setValues);
  }, [vyper, build]);

  const handleChangePackConfig = (value, build) => {
    return buildDao
      .changePackConfig(vyper.vyperNumber, build.buildNumber, value)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    showQuestionDialog({
      type: "select",
      title: "Select the Pack Configuration",
      description: `Select the pack config value for this build.`,
      value: build.packConfig?.object?.value,
      multiline: false,
      rows: 10,
      options: values,
      onSave: (value) => handleChangePackConfig(value, build),
    });
  };

  const { canEditPackConfig } = useContext(HelperContext);
  const canEdit = canEditPackConfig(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Pack Config")
  )
    return null;

  return (
    <DataCell source={build.packConfig?.source}>
      <VyperLink onClick={() => handleClick(build)} canEdit={canEdit}>
        {build.packConfig?.object?.value || "click to select"}
      </VyperLink>
    </DataCell>
  );
};
