import React from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  MenuItem,
  TextField,
} from "@material-ui/core";
import DialogContentText from "@material-ui/core/DialogContentText";
import { DateTimePicker, MuiPickersUtilsProvider } from "@material-ui/pickers";
import DateFnsUtils from "@date-io/date-fns";
import { Formik } from "formik";
import * as yup from "yup";
import Button from "@material-ui/core/Button";
import moment from "moment";

export const AnnouncementAdminDialog = ({
  show,
  announcement: _announcement,
  onSave,
  onClose,
}) => {
  return (
    <div>
      <Formik
        initialValues={{
          id: _announcement.id || null,
          teaser: _announcement.teaser || "",
          message: _announcement.message || "",
          author: _announcement.author || "",
          authorName: _announcement.authorName || "",
          startDttm: _announcement.startDttm || moment().toISOString(),
          endDttm: _announcement.endDttm || moment().toISOString(),
          readRequired: _announcement.readRequired || "",
        }}
        validationSchema={yup.object({
          teaser: yup.string().required(),
          message: yup.string().required(),
          author: yup.string().required(),
          authorName: yup.string().required(),
          startDttm: yup.date().required(),
          endDttm: yup.date().required(),
          readRequired: yup.string().required(),
        })}
        onSubmit={(values) => onSave(values)}
      >
        {({
          isSubmitting,
          errors,
          touched,
          handleSubmit,
          setFieldTouched,
          getFieldProps,
          setFieldValue,
          values,
        }) => {
          return (
            <Dialog open={show} onClose={onClose}>
              <DialogTitle>
                {values.id == null
                  ? "Create Announcement"
                  : `Edit Announcement #${values.id}`}
              </DialogTitle>

              <DialogContent>
                <DialogContentText>
                  <Grid container>
                    <Grid item xs={12}>
                      <TextField
                        variant="outlined"
                        fullWidth
                        label="Teaser"
                        name="teaser"
                        {...getFieldProps("teaser")}
                        helperText={
                          touched.teaser && errors.teaser ? errors.teaser : " "
                        }
                        error={errors.teaser != null && touched.teaser != null}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        variant="outlined"
                        fullWidth
                        multiline
                        rows={10}
                        label="Message"
                        name="message"
                        {...getFieldProps("message")}
                        helperText={
                          touched.message && errors.message
                            ? errors.message
                            : " "
                        }
                        error={
                          errors.message != null && touched.message != null
                        }
                      />
                    </Grid>

                    <Grid item xs={6}>
                      <TextField
                        variant="outlined"
                        fullWidth
                        select
                        label="Read Required"
                        name="readRequired"
                        {...getFieldProps("readRequired")}
                        helperText={
                          touched.readRequired && errors.readRequired
                            ? errors.readRequired
                            : " "
                        }
                        error={
                          errors.readRequired != null &&
                          touched.readRequired != null
                        }
                      >
                        <MenuItem value="" />
                        <MenuItem value="YES">YES</MenuItem>
                        <MenuItem value="NO">NO</MenuItem>
                      </TextField>
                    </Grid>

                    <Grid item xs={6}>
                      &nbsp;
                    </Grid>

                    <Grid item xs={6}>
                      <MuiPickersUtilsProvider utils={DateFnsUtils}>
                        <DateTimePicker
                          label="Start Date"
                          ampm={false}
                          value={values.startDttm}
                          onChange={(date) => setFieldValue("startDttm", date)}
                          onBlur={() =>
                            setFieldTouched("startDttm", true, true)
                          }
                          format="yyyy-MM-dd HH:mm:ss"
                          helperText={
                            touched.startDttm && errors.startDttm
                              ? errors.startDttm
                              : " "
                          }
                          error={
                            errors.startDttm != null &&
                            touched.startDttm != null
                          }
                          inputVariant="outlined"
                          emptyLabel="Start Date"
                        />
                      </MuiPickersUtilsProvider>
                    </Grid>

                    <Grid item xs={6}>
                      <MuiPickersUtilsProvider utils={DateFnsUtils}>
                        <DateTimePicker
                          label="End Date"
                          ampm={false}
                          value={values.endDttm}
                          onChange={(date) => setFieldValue("endDttm", date)}
                          onBlur={() => setFieldTouched("endDttm", true, true)}
                          format="yyyy-MM-dd HH:mm:ss"
                          helperText={
                            touched.endDttm && errors.endDttm
                              ? errors.endDttm
                              : " "
                          }
                          error={
                            errors.endDttm != null && touched.endDttm != null
                          }
                          inputVariant="outlined"
                          emptyLabel="End Date"
                        />
                      </MuiPickersUtilsProvider>
                    </Grid>

                    <Grid item xs={6}>
                      <TextField
                        variant="outlined"
                        label="Author Id"
                        name="author"
                        value={values.author}
                        {...getFieldProps("author")}
                        helperText={
                          touched.author && errors.author ? errors.author : " "
                        }
                        error={errors.author != null && touched.author != null}
                      />
                    </Grid>

                    <Grid item xs={6}>
                      <TextField
                        variant="outlined"
                        label="Author Name"
                        name="authorName"
                        value={values.authorName}
                        {...getFieldProps("authorName")}
                        helperText={
                          touched.authorName && errors.authorName
                            ? errors.authorName
                            : " "
                        }
                        error={
                          errors.authorName != null &&
                          touched.authorName != null
                        }
                      />
                    </Grid>
                  </Grid>
                </DialogContentText>
              </DialogContent>

              <DialogActions>
                <Button onClick={onClose} variant="outlined" color="primary">
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmit}
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                >
                  Save
                </Button>
              </DialogActions>
            </Dialog>
          );
        }}
      </Formik>
    </div>
  );
};
