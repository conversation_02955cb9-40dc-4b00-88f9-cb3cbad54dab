import { MenuItem, TextField } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";
import { FullFlowSpin, MinorChange, New } from "./ProjectTypes";

const projectTypes = [FullFlowSpin];

/**
 * Select the build type
 *
 * @param buildType The current build type
 * @param onChange called when the build type changes
 * @returns {JSX.Element|boolean}
 * @constructor
 */
export const ProjectType = ({ projectType, onChange, disabled }) => {
  const handleOnChange = (e) => onChange(e.target.value);

  return (
    <TextField
      disabled={disabled}
      variant="outlined"
      select
      id="projectType"
      label="Select the Project Type"
      fullWidth
      value={projectType || ""}
      onChange={handleOnChange}
    >
      {projectTypes.map((projectType) => (
        <MenuItem key={projectType} value={projectType}>
          {projectType}
        </MenuItem>
      ))}
    </TextField>
  );
};

ProjectType.propTypes = {
  projectType: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};
