import makeStyles from "@material-ui/core/styles/makeStyles";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import { DieHeader } from "./DieHeader";
import { DieFooter } from "./DieFooter";
import { DieRows } from "./DieRows";
import React from "react";

export const Dies = ({
  facility,
  instances,
  onChangeDie,
  onAddPriority,
  onAddDie,
  onRemoveDie,
  onChangeType,
  build,
}) => {
  const styles = makeStyles((theme) => ({
    Table: {
      minWidth: 650,
    },

    TableContainer: {
      marginBottom: theme.spacing(5),
    },
  }));

  // get the maximum number of dies in the instances.
  const numCols =
    instances
      .map((instance) => instance.dies.length)
      .reduce((acc, cur) => Math.max(acc, cur), 0) || 0;

  const classes = styles();

  return (
    <div className={classes.root}>
      <TableContainer className={classes.TableContainer}>
        <Table className={classes.Table} size="small">
          <DieHeader build={build} />

          <DieFooter onAddDie={onAddDie} />

          <DieRows
            facility={facility}
            instances={instances}
            numCols={numCols}
            onChangeDie={onChangeDie}
            onAddPriority={onAddPriority}
            onRemoveDie={onRemoveDie}
            onChangeType={onChangeType}
            build={build}
          />
        </Table>
      </TableContainer>
    </div>
  );
};
