import { useCallback } from "react";
import { useAxios } from "src/api/useAxios";

export const useComponentMap = () => {
  const { instance } = useAxios();

  /**
   * Return the entire list of component maps
   */
  const list = useCallback(() => {
    return instance
      .get("/vyper/v1/componentmap")
      .then((response) => response.data);
  }, []);

  /**
   * Create a new component map
   */
  const create = useCallback((componentMap) => {
    return instance
      .post("/vyper/v1/componentmap", componentMap)
      .then((response) => response.data);
  }, []);

  /**
   * Update an existing component map
   */
  const update = useCallback((id, componentMap) => {
    return instance
      .post(`/vyper/v1/componentmap/${id}`, componentMap)
      .then((response) => response.data);
  }, []);

  // return the functions
  return { list, create, update };
};
