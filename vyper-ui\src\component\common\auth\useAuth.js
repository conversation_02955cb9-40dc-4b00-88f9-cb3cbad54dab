import { useEffect, useState } from "react";
import { UserUtil } from "@ti/simba-common-util";
import axios from "axios";
import useSnackbar from "src/hooks/Snackbar";
import config from "../../../buildEnvironment";

// Hook to retrieve user authorization information.
//
// Makes a fetch call to an API that is behind a Zuul proxy service. The API and Zuul proxy service
// are documented at http://react.itg.ti.com. The proxy service will throw a 401 HTTP response
// if the user is not authenticated. If the user is authenticated, then the API response is an authUser object
// formatted as => {uid: 'a0900000', name: 'Hal 9000', roles: ['MACHINE', 'ADMIN']}
//
// There are a few opinionated components that depend on the authUser object as formatted above.
// If you do not wish to use the authUser object as-is, then modify or remove those components.
//  > AuthContext (common component)
//  > RouteManager (common component)
//  > ProfileSetup (sample component)
//  > Profile (sample component in navigation)

/**
 * @namespace UseAuth
 */

/**
 * @typedef {object} UseAuth~AuthUser
 * @property {string} uid - The logged-in user's id, or an empty string.
 * @property {string} name - The logged-in user's name, or an empty string.
 * @property {string[]} roles - The logged-in user's roles, or an empty array.
 */

/**
 * default user - used while loading, or if there is an error loading the current user.
 * @type {UseAuth~AuthUser}
 */
const authUserDefault = { uid: "", name: "", roles: [] };

/**
 * @typedef {boolean} AuthLoading - True if the user is loading, false if the loading is complete.
 * @typedef {boolean} AuthSuccess - True if the wasn't an error. False if there was an error while loading the user.
 * @typedef {[AuthLoading, AuthSuccess, UseAuth~AuthUser]} UseAuthTuple - return value of the useAuth hook.
 */

/**
 * Returns the currently logged-in user
 * @returns {UseAuthTuple}
 */
function useAuth() {
  // If authLoading is true => the loading process is still in progress.
  const [authLoading, setAuthLoading] = useState(true);
  // If authSuccess is true => loading process has completed and was successful.
  const [authSuccess, setAuthSuccess] = useState(false);
  // If authUser is not null => object containing uid, name and roles.
  const [authUser, setAuthUser] = useState(authUserDefault);

  let { enqueueErrorSnackbar } = useSnackbar();

  // subscribe to the user changes
  useEffect(() => {
    // Subscribe to user information from Simba common util
    const subscription = UserUtil.getUserDetail$().subscribe(handleChangeUser);
    return () => subscription.unsubscribe();
  }, []);

  // when user changes, merge the user with the vswr details
  const handleChangeUser = (userDetails) => {
    // store the simba user.
    // do this now so if the call to vswr's me fails, we still have a user.
    setAuthSuccess(true);
    setAuthLoading(false);
    setAuthUser({
      uid: userDetails.getId(),
      name: userDetails.getName(),
      roles: userDetails.getRoles() || [],
      vswrSecurity: {},
    });

    // Avoid calling vswr roles for ext use
    if (config.externalUse) {
      return;
    }

    // update the user with vswr data
    return axios
      .get(`/vyper/v1/vswr/security/me`)
      .then((res) => res.data)
      .then((vswrSecurity) =>
        setAuthUser((simbaUser) => ({
          uid: simbaUser.uid,
          name: simbaUser.name,
          roles: simbaUser.roles || [],
          vswrSecurity: vswrSecurity,
        }))
      )
      .catch(() =>
        enqueueErrorSnackbar(
          "An error occurred while trying to get VSWR roles.",
          { preventDuplicate: true }
        )
      );
  };

  return [authLoading, authSuccess, authUser];
}

export default useAuth;
