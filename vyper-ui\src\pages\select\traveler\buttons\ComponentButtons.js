import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext } from "react";
import { HelperContext } from "src/component/helper/Helpers";
import { AddRequiredButton } from "./AddRequiredButton";
import { EditRequiredButton } from "./EditRequiredButton";
import { RemoveRequiredButton } from "./RemoveRequiredButton";

const useStyle = makeStyles({
  root: {
    paddingRight: "1em",
    display: "inline",
  },
  enabled: {
    fontSize: "0.75em",
    "&:hover": {
      backgroundColor: "#ccc",
      cursor: "pointer",
    },
  },
  disabled: {
    fontSize: "0.75em",
  },
});

export const ComponentButtons = ({
  vyper,
  build,
  options,
  disabled,
  operation,
  component,
  onAdd,
  onEdit,
  onRemove,
}) => {
  const { currentUserIsSCP } = useContext(HelperContext);

  const isScp = currentUserIsSCP(vyper, build);

  const classes = useStyle();

  if (options.editbutton === false) return null;

  const required = component?.required === "REQUIRED" && !isScp;

  return (
    <div className={classes.root}>
      <EditRequiredButton
        disabled={disabled}
        title="Edit Component"
        required={required}
        onClick={() => onEdit(operation, component)}
      />
      <AddRequiredButton
        disabled={disabled}
        title="Add Component"
        onClick={() => onAdd(operation, component)}
      />
      <RemoveRequiredButton
        disabled={disabled}
        title="Remove Component"
        required={required}
        onClick={() => onRemove(operation, component)}
      />
    </div>
  );
};
