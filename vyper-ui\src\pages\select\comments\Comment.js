import makeStyles from "@material-ui/core/styles/makeStyles";
import Moment from "moment";
import React from "react";
import { dateFormat, formatDate } from "../../../component/dateFormat";

export const Comment = ({ comment }) => {
  const useStyles = makeStyles({
    root: {
      marginBottom: "1rem",
    },
    header: {
      display: "flex",
      justifyContent: "space-between",
      backgroundColor: "#CC0000",
      color: "#FFFFFF",
      padding: "3px",
    },
    username: {},
    userid: {},
    when: {},
    text: {},
  });

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <div className={classes.header}>
        <div className={classes.username}>
          {comment.who.username} / {comment.who.userid}
        </div>
        <div className={classes.when}>{formatDate(comment.when)}</div>
      </div>
      <div className={classes.text}>{comment.text}</div>
    </div>
  );
};
