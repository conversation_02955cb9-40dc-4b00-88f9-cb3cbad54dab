import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { IconButton, Tooltip, Zoom } from "@material-ui/core";
import EditIcon from "@material-ui/icons/Edit";

export const EditRequiredButton = ({ title, disabled, required, onClick }) => {
  const useStyles = makeStyles({
    root: {
      display: "inline",
    },
  });

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Tooltip
        title={title}
        placement="top"
        TransitionComponent={Zoom}
        arrow
        interactive
      >
        <span>
          {required ? (
            <IconButton disabled size="small">
              <EditIcon fontSize="small" />
            </IconButton>
          ) : (
            <IconButton
              disabled={disabled}
              color="primary"
              size="small"
              onClick={onClick}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          )}
        </span>
      </Tooltip>
    </div>
  );
};
