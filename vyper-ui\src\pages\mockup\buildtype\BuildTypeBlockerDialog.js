import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import React, { useContext } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { convertBuildNumbertoVyperNumber } from "../../../component/helper/convertBuildNumbertoVyperNumber";
import { NewBuildDialogContext } from "src/component/newBuild/NewBuildDialog";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
});

export const BuildTypeBlockerDialog = ({
  onAddNewBuild,
  buildNumber,
  open,
  onClose,
  onCloseBuildTypeDialog,
}) => {
  const { openNewBuildDialog } = useContext(NewBuildDialogContext);

  const handleCancel = () => {
    onClose();
    onCloseBuildTypeDialog();
  };

  const handleContinue = () => {
    openNewBuildDialog({
      vyperNumber: convertBuildNumbertoVyperNumber(buildNumber),
      buildNumber: undefined,
      device: undefined,
      facility: undefined,
      multiBuild: false,
      specDevice: undefined,
      description: undefined,
      buildType: undefined,
      symbolChoice: undefined,
      onSave: (
        mode,
        vyperNumber,
        buildNumber,
        material,
        facility,
        multiBuild,
        specDevice,
        description,
        buildType,
        copyBuildNumber,
        symbolChoice
      ) => {
        onClose();
        onCloseBuildTypeDialog();
        return onAddNewBuild(
          mode,
          vyperNumber,
          buildNumber,
          material,
          facility,
          multiBuild,
          specDevice,
          description,
          buildType,
          copyBuildNumber,
          symbolChoice
        );
      },
    });
  };

  const classes = useStyles();

  return (
    <Dialog className={classes.root} open={open} maxWidth="md" fullWidth>
      <DialogTitle className={classes.title}>
        Cannot Perfrom Action: Change BuildType
      </DialogTitle>
      <DialogContent>
        <p>
          Development Template not allowed for Build Type NEW. Continuation with
          change of Build Type to NEW will result in VBUILD creation of new
          VBUILD
        </p>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="primary" onClick={handleCancel}>
          Cancel
        </Button>
        <Button variant="contained" color="primary" onClick={handleContinue}>
          {" "}
          Continue
        </Button>
      </DialogActions>
    </Dialog>
  );
};
