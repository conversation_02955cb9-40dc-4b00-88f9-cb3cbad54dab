import React, { useEffect, useState } from "react";
import { ThemeUtil, UserUtil } from "@ti/simba-common-util";

/**
 * This component wraps the page in a div with a class name that is
 * <prefix>-<theme>. such as <div class="simba-Dark">. This allows you
 * to write custom CSS to handle different themes.
 *
 * @param id
 * @param prefix
 * @param children
 * @returns {JSX.Element}
 * @constructor
 */

export const SimbaTheme = ({ id, prefix = "Simba", children }) => {
  // stores the current profile (Red, Blue, Dark)
  const [paletteName, setPaletteName] = useState();

  // setup listening to profile changes
  useEffect(() => {
    const subscription = UserUtil.getUserProfile$().subscribe((userProfile) => {
      const paletteName = userProfile.theme
        ? userProfile.theme.name
        : ThemeUtil.getDefaultPaletteName();

      setPaletteName(paletteName);
    });
    return () => subscription.unsubscribe();
  }, []);

  return (
    <SimbaThemeContext.Provider
      value={{
        paletteName: paletteName,
      }}
    >
      <div id={id} className={`${prefix}-${paletteName}`}>
        {children}
      </div>
    </SimbaThemeContext.Provider>
  );
};

export const SimbaThemeContext = React.createContext(null);
