import React from "react";
import { Breadcrumbs, Link } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import HomeIcon from "@material-ui/icons/Home";

const useStyles = makeStyles((theme) => ({
  header: {
    paddingBottom: theme.spacing(2),
    fontWeight: "bold",
  },
  link: {
    display: "flex",
  },
  icon: {
    marginRight: theme.spacing(0.5),
    width: 20,
    height: 20,
  },
}));

export const ProjectHeaderLinks = ({ projectHeaderInfo, currentPage }) => {
  const classes = useStyles();

  const handleClick = () => {
    console.log("");
  };

  const baseUrl = "/vyper/atssmassupload/projects";
  const projSpecChangeUrl = `/vyper/atssmassupload/project/${projectHeaderInfo.projNumber}/specchanges`;

  return (
    <div className={classes.header}>
      <Breadcrumbs aria-label="breadcrumb">
        <Link
          color="inherit"
          href={baseUrl}
          onClick={handleClick}
          className={classes.link}
        >
          <HomeIcon className={classes.icon} />
          All projects
        </Link>
        <Link
          color="inherit"
          href={projSpecChangeUrl}
          onClick={handleClick}
          className={classes.link}
        >
          {projectHeaderInfo.projNumber}
        </Link>
        <Link color="textPrimary" onClick={handleClick} aria-current="page" href={currentPage?.link || projSpecChangeUrl}>
          {currentPage?.title || "Spec Changes"}
        </Link>
      </Breadcrumbs>
    </div>
  );
};
