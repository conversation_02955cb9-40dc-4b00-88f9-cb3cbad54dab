import React from "react";
import { BuildForm } from "./form/BuildForm";
import { TravelerForm } from "./form/TravelerForm";
import { AtssForm } from "./form/AtssForm";
import { makeStyles } from "@material-ui/core/styles";
import { HorizontalTabs } from "../../component/tab/HorizontalTabs";

const useStyles = makeStyles((theme) => ({
  tabs: {
    marginBottom: theme.spacing(3),
  },
}));

export const ComparePage = () => {
  const tabs = [
    {
      label: "Build to Build",
      control: <BuildForm />,
    },
    {
      label: "Traveler to Traveler",
      control: <TravelerForm />,
    },
    {
      label: "Traveler to ATSS",
      control: <AtssForm />,
    },
  ];

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <HorizontalTabs
        className={classes.tabs}
        storageKey="compare.tabs"
        tabs={tabs}
      />
    </div>
  );
};
