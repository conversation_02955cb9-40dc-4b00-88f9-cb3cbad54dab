import { makeStyles } from "@material-ui/core/styles";
import { TableCell, TableRow } from "@material-ui/core";
import clsx from "clsx";
import React from "react";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const Row = ({ builds, name, value }) => {
  const values = builds.map((build) => value(build));
  const same = values.every((name, index, array) => name === array[0]);
  const classes = useStyles();

  return (
    <TableRow className={clsx({ [classes.difference]: !same })} hover>
      <TableCell>{name}</TableCell>
      {values.map((v, n) => (
        <TableCell key={n}>{v}</TableCell>
      ))}
    </TableRow>
  );
};
