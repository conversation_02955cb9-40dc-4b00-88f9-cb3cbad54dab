import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "../fetch/VyperFetch";

/**
 * given an approval operation and facility, returns approving group
 * @param {object} ao - the approval operation
 * @param {string} facility - the build facility
 * @returns {string} the approving group
 */
export function determineGroupFromApprovalOperation(ao, facility) {
  if (ao == null || facility == null) {
    return null;
  }

  let group = `VYPER_${facility}_`;

  switch (ao.groupText) {
    case "_PREBOND":
      group += "ASSY_PREBOND";
      break;
    case "_BOND":
      group += "ASSY_BOND";
      break;
    case "_FINISH":
      group += "ASSY_FINISH";
      break;
    //TODO: TEST
    case "_PACK":
      group += "PACK";
      break;
    default:
      return null;
  }

  return group;
}

export const ApprovalOperation = ({ children }) => {
  const { vget } = useContext(FetchContext);

  const [approvalOperations, setApprovalOperations] = useState([]);

  useEffect(() => {
    vget(`/vyper/v1/approvaloperation`, setApprovalOperations);
  }, []);

  const handleFindByOperation = (operation) =>
    approvalOperations.find((ao) => ao.operation === operation);

  return (
    <ApprovalOperationContext.Provider
      value={{
        approvalOperations: approvalOperations,
        findApprovalOperationByOperation: handleFindByOperation,
      }}
    >
      <div>{children}</div>
    </ApprovalOperationContext.Provider>
  );
};

export const ApprovalOperationContext = React.createContext(null);
