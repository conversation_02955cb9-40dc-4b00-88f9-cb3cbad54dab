export const headerItems = (build) => {
  return [
    { title: "Current Date / Time", value: new Date().toLocaleString() },
    { title: "Pin", value: build?.pin },
    { title: "Old Material", value: build?.oldMaterial },
    { title: "PKG", value: build?.pkg },
    { title: "Spec Device", value: build?.specDevice },
    { title: "Package Group", value: build?.pkgGroup },
    { title: "A/T", value: build?.pdbFacility },
    { title: "SPQ", value: build?.spq },
    { title: "Revision", value: "ACTIVE" },
    { title: "MOQ", value: build?.moq },
    { title: "SBE", value: build?.sbe },
    { title: "Industry Sector", value: build?.industrySector },
    { title: "SBE-1", value: build?.sbe1 },
    { title: "Customer", value: "" }, // TODO: implement customer
    { title: "SBE-2", value: build?.sbe2 },
    { title: "Cust Part Name", value: "" }, // TODO: implement cust part name
    { title: "SAP Material", value: build?.sapMaterial },
    { title: "Cust Print Name", value: "" }, // TODO: implement cust print name
    { title: "SAP Base Material", value: build?.sapBaseMaterial },
    { title: "Cust Print Revision", value: "" }, // TODO: implement cust print revision
    { title: "Niche", value: build?.niche },
    { title: "Type 4", value: "" }, // TODO: implement type4
    { title: "PRESP", value: build?.presp },
  ];
};
