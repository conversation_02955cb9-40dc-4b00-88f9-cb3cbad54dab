import { Grid, TableBody } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Table from "@material-ui/core/Table";
import React, { useContext, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import {
  AlertDialogErrorHandler,
  SpinnerLoadingHandler,
} from "../../../component/fetch/DaoBase";
import { SpinnerContext } from "../../../component/Spinner";
import { noop } from "../../../component/vyper/noop";
import { AuditDao } from "../../../dao/AuditDao";
import { DataModelsContext } from "../../../DataModel";
import { logError } from "../../functions/logError";
import { AuditDialog2 } from "../../mockup/audit/AuditDialog2";
import { BuildNumberRow2 } from "../../mockup/build/BuildNumberRow2";
import { CommentRow2 } from "../../mockup/comment/CommentRow2";
import { DescriptionRow2 } from "../../mockup/description/DescriptionRow2";
import { FacilityRow2 } from "../../mockup/facility/FacilityRow2";
import { ReferenceRow2 } from "../../mockup/reference/ReferenceRow2";
import { VyperHeader } from "../../mockup/vyper/VyperHeader";
import { WorkFlowRow2 } from "../../mockup/workflow/WorkFlowRow2";
import { PimRow } from "./PimRow";
import { VscnReferenceCell } from "./VscnReferenceCell";
import { VscnWorkflowCell } from "./VscnWorkflowCell";
import { AtssScnRow } from "./AtssScnRow";
import { VscnRow } from "./VscnRow";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { VscnTestRow } from "./VscnTestRow";
import { VscnPackConfigRow } from "./VscnPackConfigRow";
import { VscnSelectionRow } from "./VscnSelectionRow";
import { VscnSymbolizationRow } from "./VscnSymbolizationRow";
import { VscnMaterialRow } from "./VscnMaterialRow";
import { VscnChangeRow } from "./VscnChangeRow";
import { VscnOldMaterialRow } from "./VscnOldMaterialRow";
import { Owners } from "../../mockup/owners/Owners";
import { AuthContext } from "../../../component/common";

const useStyles = makeStyles((theme) => ({
  root: {},
  table: {
    width: "auto",
  },
  ProjectBox: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "stretch",
    padding: "0.5rem",
    marginBottom: "1rem",
    borderRadius: "1rem",
    paddingLeft: "1rem",
  },
  header: {
    marginTop: theme.spacing(3),
    fontWeight: "bold",
  },
}));

const vscnHelpLink =
  "https://confluence.itg.ti.com/pages/viewpage.action?pageId=630627111";

/**
 * Display the VSCN page.
 * @return {JSX.Element}
 * @constructor
 */
export function VyperVscnPage() {
  const { vyperNumber, vscnNumber } = useParams();
  const { vyper, build, vyperDao, vscnDao, buildDao } =
    useContext(DataModelsContext);

  const { vget } = useContext(FetchContext);
  const { authUser } = useContext(AuthContext);
  const [vscns, setVscns] = useState([]);
  const [changeLinkData, setChangeLinkData] = useState();

  const [openAudit, setOpenAudit] = useState(false);
  const [auditTitle, setAuditTitle] = useState("");
  const [audits, setAudits] = useState([]);

  const classes = useStyles();

  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const currentUserIsOwner = vyper?.owners.some(
    (usr) => usr.userid === authUser?.uid
  );

  const auditDao = new AuditDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
    audits,
    setAudits,
  });

  useEffect(() => {
    vget(
      "/vyper/v1/changelink/change/search?page=0&size=12000",
      setChangeLinkData
    );
  }, []);

  /**
   * if build number changes, reload the vyper
   */
  useEffect(() => {
    if (vscns.length > 0 && vscns[0]?.buildNumber != undefined) {
      reloadBuild().catch(noop);
    }
  }, [vscns]);

  const reloadBuild = () => {
    return buildDao.findByBuildNumber(vscns[0]?.buildNumber).catch(noop);
  };

  /**
   * On page load, retrieve either the 1 vscn, or the list of vscns by vyper number
   * either way, store the vscn(s) as an array
   */
  useEffect(() => {
    // determine which vscns to display, based on the vscn number.
    // if vscnNumber is null/undefined, show all vscns for the vyper
    // if vscnNumber is a PRA number, show all vscns for the pra.
    // if vscnNumber is a VSCN number, show the 1 vscn.

    if (vscnNumber == null) {
      vscnDao
        .findAllByVyperNumber(vyperNumber)
        .then((vscns) => setVscns(vscns))
        .catch(logError);
    } else if (vscnNumber.includes("PRA")) {
      vscnDao
        .findAllByPraNumber(vscnNumber)
        .then((vscns) => setVscns(vscns))
        .catch(logError);
    } else {
      vscnDao
        .findByVscnNumber(vscnNumber)
        .then((vscn) => setVscns([vscn]))
        .catch(logError);
    }
  }, []);

  // sort in descending order
  const filteredVscns = vscns.sort((a, b) => {
    if (a.vscnNumber < b.vscnNumber) {
      return 1;
    } else if (a.vscnNumber > b.vscnNumber) {
      return -1;
    } else {
      return 0;
    }
  });

  const handleChangeVscn = (updatedVscn) => {
    setVscns((vscns) =>
      vscns.map((v) =>
        v.vscnNumber === updatedVscn.vscnNumber ? updatedVscn : v
      )
    );
    return updatedVscn;
  };

  /**
   * save the title
   * @param {string} title - The new title
   */
  function handleChangeTitle(title) {
    vyperDao.updateTitle(vyper.vyperNumber, title).catch(noop);
  }

  /**
   * Return the configuration object for the build number row
   * @param {Vscn} vscn - The vscn object
   * @return {BuildNumberCell2~config}
   */
  function handleGetConfigBuildNumberRow(vscn) {
    return {
      number: vscn.vscnNumber,
      state: vscn.state,
      menuItems: [
        {
          id: "AUDIT",
          title: "Audit",
          enabled: true,
        },
        {
          id: "Refresh VSCN Verifiers",
          title: "Refresh VSCN Verifiers",
          enabled: true,
          internal: true,
        },
      ],
    };
  }

  const handleRefreshVscnVerifiers = (vscnNumber) => {
    vscnDao
      .refreshVscn(vscnNumber)
      .then((vscn) => handleChangeVscn(vscn))
      .catch(logError);
  };

  /**
   * User clicked a menu item.
   * @param {Vscn} vscn - The vscn object
   * @param {string} action - The id of the clicked menu item
   */
  function handleClickBuildNumberRowMenu(vscn, action) {
    switch (action.toUpperCase()) {
      case "AUDIT":
        showAuditDialog(vscn);
        break;
      case "REFRESH VSCN VERIFIERS":
        handleRefreshVscnVerifiers(vscn.vscnNumber);
        break;
    }
  }

  /**
   * Display the audit dialog. fetch the audits, then sort in reverse order
   * @param {Vscn} vscn - The vscn object
   */
  function showAuditDialog(vscn) {
    auditDao
      .findAllByVscnNumber(vscn.vscnNumber)
      .then((audits) => {
        setAudits(audits);
        setAuditTitle(`Audit Log: ${vscn.vscnNumber}`);
        setOpenAudit(true);
      })
      .catch(logError);
  }

  /**
   * Close the audit dialog
   */
  function handleCloseAuditDialog() {
    setAudits([]);
    setAuditTitle("");
    setOpenAudit(false);
  }

  /**
   * Handle clicking of a work flow button.
   * @param {Vscn} vscn - The vscn object
   * @param {string} action - The button that was clicked
   */
  function handleClickWorkFlow(vscn, action) {
    vscnDao
      .changeWorkFlow(vscn.vscnNumber, action)
      .then((updatedVscn) =>
        setVscns(
          vscns.map((v) =>
            v.vscnNumber === updatedVscn.vscnNumber ? updatedVscn : v
          )
        )
      )
      .catch(logError);
  }

  /**
   * returns the description
   * @param {Vscn} vscn - The vscn object
   * @return {string} - the description
   */
  function handleGetTextDescription(vscn) {
    return vscn.description;
  }

  /**
   * returns true if the VSCN allows the description to be changed.
   * @param {Vscn} vscn - The vscn object
   * @return {boolean} - true if editing is allowed
   */
  function handleEditableDescription(vscn) {
    return vscn.state === "VSCN_DRAFT";
  }

  /**
   * Save the description
   * @param {Vscn} vscn - The vscn object
   * @param {string} description - The new description
   */
  function handleSaveDescription(vscn, description) {
    if (vscn.description === description) {
      return;
    }

    vscnDao
      .changeDescription(vscn.vscnNumber, description)
      .then((updatedVscn) =>
        setVscns(
          vscns.map((v) =>
            v.vscnNumber === updatedVscn.vscnNumber ? updatedVscn : v
          )
        )
      )
      .catch(logError);
  }

  /**
   * Return the comments.
   * @param {Vscn} vscn - The vscn object
   * @return {CommentsDialog~Comment[]} - The comments
   */
  function handleGetComments(vscn) {
    return vscn.comments;
  }

  /**
   * Save the comment
   * @param {Vscn} vscn - The vscn object
   * @param {string} comment - The comment
   */
  function handleSaveComment(vscn, comment) {
    vscnDao
      .addComment(vscn.vscnNumber, comment)
      .then((updatedVscn) =>
        setVscns(
          vscns.map((v) =>
            v.vscnNumber === updatedVscn.vscnNumber ? updatedVscn : v
          )
        )
      )
      .catch(logError);
  }

  /**
   * return the facility object
   * @param {Vscn} vscn - The vscn object
   * @return {FacilityObject} The material
   */
  function handleGetFacilityObject(vscn) {
    return vscn.facility.object;
  }

  /**
   * return the material object
   * @param {Vscn} vscn - The vscn object
   * @return {MaterialObject} The material
   */
  function handleGetMaterialObject(vscn) {
    return vscn.material.object;
  }

  /**
   * return the pim setup object
   * @param {Vscn} vscn - The vscn object
   * @return {PimCell~PimSetup}
   */
  function handleGetPimSetup(vscn) {
    return vscn.pimSetup;
  }

  /**
   * user changed the pim setup needed state.
   * @param {Vscn} vscn - The vscn object
   * @param {boolean} state - The new needed state
   */
  function handleChangeNeeded(vscn, state) {
    vscnDao
      .changePimSetupNeededState(vscn.vscnNumber, state)
      .then((updatedVscn) =>
        setVscns(
          vscns.map((v) =>
            v.vscnNumber === updatedVscn.vscnNumber ? updatedVscn : v
          )
        )
      )
      .catch(logError);
  }

  /**
   * user changed the pim setup validated state.
   * @param {Vscn} vscn - The vscn object
   * @param {boolean} state - The new validated state
   */
  function handleChangeValidated(vscn, state) {
    vscnDao
      .changePimSetupValidatedState(vscn.vscnNumber, state)
      .then((updatedVscn) =>
        setVscns(
          vscns.map((v) =>
            v.vscnNumber === updatedVscn.vscnNumber ? updatedVscn : v
          )
        )
      )
      .catch(logError);
  }

  function handleAtssScnRefresh() {}

  // determine the components to display
  // and sort them in the correct order

  const order = {
    Die: 10,
    Leadframe: 20,
    "MB Diagram": 30,
    "Mount Compound": 40,
    Wire: 50,
    "Mold Compound": 60,
    "Topside Symbol": 90,
    CUST1: 100,
    ECAT: 110,
  };

  const showComponents = [
    "Die",
    "Leadframe",
    "MB Diagram",
    "Mount Compound",
    "Wire",
    "Mold Compound",
  ];

  const componentNames = vscns
    .flatMap((vscn) => vscn.components)
    .filter((component) => showComponents.includes(component?.name))
    .map((component) => component.name)
    .filter((item, index, items) => items.indexOf(item) === index)
    .sort((a, b) => {
      let index1 = order[a] || 999;
      let index2 = order[b] || 999;

      if (index1 < index2) {
        return -1;
      } else if (index1 > index2) {
        return 1;
      } else {
        return 0;
      }
    });

  return (
    <div>
      <h1>
        <VyperHeader
          vyper={vyper}
          onChangeTitle={handleChangeTitle}
          helpLink={vscnHelpLink}
        />
      </h1>
      <Grid container className={classes.ProjectBox + " header-box"}>
        <Grid item xs={4}>
          <div className={classes.header}>Owners</div>
          <Owners />
        </Grid>
      </Grid>

      <Table className={classes.table} size="small">
        <TableBody>
          <BuildNumberRow2
            items={filteredVscns}
            help="vscn"
            title="VSCN Number"
            onGetConfig={handleGetConfigBuildNumberRow}
            onClickMenu={handleClickBuildNumberRowMenu}
          />
          <WorkFlowRow2
            vscns={filteredVscns}
            as={VscnWorkflowCell}
            onClick={handleClickWorkFlow}
            currentUserIsOwner={currentUserIsOwner}
          />
          <ReferenceRow2 items={filteredVscns} as={VscnReferenceCell} />
          <DescriptionRow2
            items={filteredVscns}
            onDescription={handleGetTextDescription}
            onEditable={handleEditableDescription}
            onSave={handleSaveDescription}
          />
          <CommentRow2
            items={filteredVscns}
            onGetComments={handleGetComments}
            onSave={handleSaveComment}
          />
          <FacilityRow2
            items={filteredVscns}
            onGetFacilityObject={handleGetFacilityObject}
          />
          <VscnMaterialRow
            vscns={filteredVscns}
            onGetMaterialObject={handleGetMaterialObject}
            onChange={handleChangeVscn}
          />
          <VscnOldMaterialRow vscns={filteredVscns} />
          <PimRow
            items={filteredVscns}
            onGetPimSetup={handleGetPimSetup}
            onChangeNeeded={handleChangeNeeded}
            onChangeValidated={handleChangeValidated}
          />
          <AtssScnRow
            items={filteredVscns}
            onClickRefresh={handleAtssScnRefresh}
          />
          <VscnChangeRow
            data={changeLinkData}
            onChange={handleChangeVscn}
            vscns={filteredVscns}
          />
          {componentNames.map((name) => (
            <VscnRow
              key={name}
              name={name}
              vyper={vyper}
              vscns={filteredVscns}
              onChange={handleChangeVscn}
            />
          ))}
          <VscnSymbolizationRow
            vyper={vyper}
            vscns={filteredVscns}
            onChange={handleChangeVscn}
          />
          <VscnTestRow
            vyper={vyper}
            vscns={filteredVscns}
            onChange={handleChangeVscn}
          />
          <VscnPackConfigRow
            vyper={vyper}
            vscns={filteredVscns}
            build={build}
            onChange={handleChangeVscn}
          />
          <VscnSelectionRow vyper={vyper} vscns={filteredVscns} />
        </TableBody>
      </Table>

      <AuditDialog2
        open={openAudit}
        audits={audits}
        title={auditTitle}
        onClose={handleCloseAuditDialog}
      />
    </div>
  );
}
