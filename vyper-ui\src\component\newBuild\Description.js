import { TextField } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";

/**
 * Show a text box for the description
 *
 * @param description - The description
 * @param onChange - called when the description changes
 * @param error - called if there is a data validation fail error.
 * @returns {JSX.Element}
 * @constructor
 */
export const Description = ({
  label,
  placeholder,
  description,
  onChange,
  error,
}) => {
  return (
    <TextField
      id="description"
      label={label || "Description"}
      placeholder={placeholder || "Description"}
      multiline
      fullWidth
      value={description || ""}
      onChange={onChange}
      variant="outlined"
      error={error}
      helperText={error ? "Required" : " "}
    />
  );
};

Description.propTypes = {
  description: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  error: PropTypes.any,
};
