import React from "react";
import { MenuItem, TextField } from "@material-ui/core";

const symbolLocations = [
  {
    id: "TOP",
    name: "Top side",
  },
  {
    id: "BOTTOM",
    name: "Bottom side",
  },
];

export const SymbolLocation = ({ location = "", onChange, buildType }) => {

  const isMinorChange = buildType === "Minor Change";
  
  return (
    <TextField
      fullWidth
      select
      variant="outlined"
      label="Symbol Location"
      name="location"
      value={location}
      onChange={onChange}
      disabled={isMinorChange}
      error={!location || location.length === 0}
      InputLabelProps={{ shrink: true }}
    >
      {symbolLocations.map((l) => (
        <MenuItem key={l.id} value={l.id}>
          {l.name}
        </MenuItem>
      ))}
    </TextField>
  );
};
