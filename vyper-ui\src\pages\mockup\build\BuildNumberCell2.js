import makeStyles from "@material-ui/core/styles/makeStyles";
import PropTypes from "prop-types";
import React from "react";
import { NumberDisplay } from "./NumberDisplay";
import { StateDisplay } from "./StateDisplay";
import { VyperMenu } from "./VyperMenu";

const useStyles = makeStyles({
  menu: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    whiteSpace: "nowrap",
  },
  element: {
    paddingRight: ".5rem",
  },
  elementMenu: {
    paddingRight: ".5rem",
    paddingTop: 6, // moves the icon down just a little
  },
});

/**
 * @typedef {object} BuildNumberCell2~configMenuItem - The menu item object
 * @property {string} id - The id (action) of the menu
 * @property {string} title - The text to display for the item.
 * @property {boolean} enabled - true to enable, false to disable the item
 */

/**
 * @typedef {object} BuildNumberCell2~config - The header configuration object
 * @property {string} number - the item's number
 * @property {string} state - the item's state
 * @property {BuildNumberCell2~configMenuItem[]} menuItems - The menu items.
 */

/**
 * Callback for when the user clicks on a menu item
 * @callback BuildNumberCell2~onClickMenu
 * @param {*} item - The item to configure.
 * @param {string} action - The action that was clicked.
 */

/**
 * Callback for when the build number header requests the item's configuration
 * @callback BuildNumberCell2~onGetConfig
 * @param {*} item - The item to configure.
 * @return {BuildNumberCell2~config} config - The configuration of the header
 */

/**
 * Display the build number cell.
 * @param {*} item - The item to display
 * @param {BuildNumberCell2~onGetConfig} onGetConfig - The callback function to get the item's configuration
 * @param {BuildNumberCell2~onClickMenu} onClickMenu - Called when the user clicks o menu item.
 * @return {JSX.Element}
 * @constructor
 */
export function BuildNumberCell2({ item, onGetConfig, onClickMenu }) {
  const classes = useStyles();

  const config = onGetConfig(item);

  function handleMenu(action) {
    onClickMenu(item, action);
  }

  return (
    <div className={classes.menu}>
      <div className={classes.element}>
        <NumberDisplay number={config.number} />
      </div>
      <div className={classes.element}>
        <StateDisplay state={config.state} />
      </div>
      <div className={classes.elementMenu}>
        <VyperMenu items={config.menuItems} onMenu={handleMenu} />
      </div>
    </div>
  );
}

BuildNumberCell2.propTypes = {
  item: PropTypes.any.isRequired,
  onGetConfig: PropTypes.func.isRequired,
  onClickMenu: PropTypes.func.isRequired,
};
