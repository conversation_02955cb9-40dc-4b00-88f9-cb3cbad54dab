import React, { useContext, useEffect, useState } from "react";
import ChangeSelectCell from "./ChangeSelectCell";
import { FetchContext } from "../../../component/fetch/VyperFetch";

function AgGridChangeSelectCellEditor(props) {
  const { data, onChange } = props;
  const { vget } = useContext(FetchContext);
  const [changeData, setChangeData] = useState([]);
  useEffect(() => {
    vget(`/vyper/v1/changelink/change/autocomplete/change?material=${data.oldMaterial}`, setChangeData);
  }, []);
  if (!changeData) return <div></div>;
  return (
    <div>
      <ChangeSelectCell
        selectedChange={data.cmsNumber}
        changeData={changeData}
        onClick={onChange}
      />
    </div>
  );
}

export default AgGridChangeSelectCellEditor;
