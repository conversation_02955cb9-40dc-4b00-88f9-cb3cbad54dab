import {
  FormControlLabel,
  Menu<PERSON>tem,
  Switch,
  TextField,
} from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import React from "react";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  table: {
    width: "100%",
  },
  tableContainer: {
    backgroundColor: "#fce09f",
  },
  highlight: {
    marginTop: "1em",
    marginBottom: "1em",
    padding: 7,
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
  helpIcon: {},
});

export const MultiBuild = ({
  multiBuildAtss,
  multiBuild,
  onChangeMultiBuild,
  specDevice,
  onChangeSpecDevice,
  specDevices,
}) => {
  const classes = useStyles();

  return (
    <div>
      {multiBuildAtss ? (
        <div className={classes.highlight}>
          Device is associated with Multi-Build
        </div>
      ) : null}

      <div>
        <FormControlLabel
          value="end"
          control={
            <Switch
              color="secondary"
              checked={multiBuild}
              onChange={onChangeMultiBuild}
            />
          }
          label="Multi-Build. Switch on to select or create a multi build spec (2 or more spec device travelers associated with one material)."
          labelPlacement="end"
        />

        <TextField
          variant="outlined"
          select
          id="specDevices"
          label="Spec Device"
          fullWidth
          value={specDevice || ""}
          onChange={(e) => onChangeSpecDevice(e.target.value)}
          InputLabelProps={{ shrink: true }}
        >
          {specDevices.map((sd) => (
            <MenuItem key={sd} value={sd}>
              {sd}
            </MenuItem>
          ))}
        </TextField>
      </div>
    </div>
  );
};
