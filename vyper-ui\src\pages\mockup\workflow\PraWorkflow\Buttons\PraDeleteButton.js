import React, { useContext, useMemo } from "react";
import { Button } from "@material-ui/core";
import { PraWorkFlowHelpersContext } from "../PraWorkFlowHelpers";
import { DataModelsContext } from "src/DataModel";
import { noop } from "src/component/vyper/noop";
import { useHistory } from "react-router-dom";

export const PraDeleteButton = ({ vyper, pra, onDeletePra }) => {
  const { deleteAction, canUpdatePra } = useContext(PraWorkFlowHelpersContext);
  const { praDao } = useContext(DataModelsContext);
  const history = useHistory();

  const isVisible = useMemo(() => {
    if (pra == undefined || vyper == undefined) {
      return false;
    }
    return canUpdatePra(pra, vyper);
  }, [pra, vyper]);

  const handleClick = () => {
    praDao
      .deletePra(vyper.vyperNumber, pra.praNumber)
      .then((deletedPra) => {
        console.log("PraDeleteButton -> deletedPra: ", deletedPra);
        onDeletePra(deletedPra);
        history.push(`/projects/${vyperNumber}/pras`);
      })
      .catch(noop);
  };

  return (
    isVisible && (
      <Button
        variant="contained"
        color="primary"
        size="small"
        type="button"
        name="action"
        value={deleteAction}
        onClick={handleClick}
      >
        {deleteAction}
      </Button>
    )
  );
};

export default PraDeleteButton;
