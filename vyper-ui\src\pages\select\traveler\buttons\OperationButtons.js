import React, { useContext } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { HelperContext } from "src/component/helper/Helpers";
import { EditRequiredButton } from "./EditRequiredButton";
import { AddRequiredButton } from "./AddRequiredButton";
import { RemoveRequiredButton } from "./RemoveRequiredButton";
import { OperationCommentButton } from "src/pages/select/traveler/buttons/OperationCommentButton";

const useStyles = makeStyles({
  root: {
    paddingRight: "1em",
    display: "inline",
  },
  icon: {
    fontSize: "0.75em",
    "&:hover": {
      backgroundColor: "#ccc",
    },
  },
});

export const OperationButtons = ({
  vyper,
  build,
  disabled,
  options,
  operation,
  onAdd,
  onEdit,
  onRemove,
  onComment,
  vo,
}) => {
  const { currentUserIsSCP } = useContext(HelperContext);

  const classes = useStyles();

  if (options.editbutton === false) return null;

  // can the current user edit this vyper?
  const isChecked = vo.checked;
  const isScp = currentUserIsSCP(vyper, build);
  const isExperimental = build?.buildtype?.toLowerCase() === "experimental";
  const isRequired = operation?.required === "REQUIRED";
  
  const disableAssemblyOperation = (operation?.subflowType === "ASSEMBLY" && build.buildtype === "Minor Change");

  /*
        show delete button
            if not checked
            and if not required
            or (not checked && is scp && is experimental)
     */
  const showDelete =
    (!isChecked && !isRequired) || (!isChecked && isScp && isExperimental);

  return (
    <div className={classes.root}>
      <EditRequiredButton
        disabled={disabled || operation.engineeringDeleted}
        title="Edit Operation"
        required={operation?.required === "REQUIRED" && !isScp}
        onClick={() => onEdit(operation)}
      />

      <AddRequiredButton
        disabled={operation.engineeringDeleted || disableAssemblyOperation}
        title="Add Operation"
        onClick={() => onAdd(operation)}
      />

      <RemoveRequiredButton
        disabled={!showDelete}
        title="Remove Operation"
        required={!showDelete}
        onClick={() => onRemove(operation)}
      />

      <OperationCommentButton
        disabled={disabled || operation.engineeringDeleted}
        title={
          "Edit Operation Instruction: These Manufacturing instructions will print on the traveler " +
          "and are used to communicate any information the manufacturing line may need."
        }
        onClick={() => onComment(operation)}
      />
    </div>
  );
};
