import React from "react";
import { MenuItem, TextField } from "@material-ui/core";
import PropTypes from "prop-types";

export const SymbolChoice = ({
  symbolChoice,
  onChangeSymbolChoice,
  options,
}) => {
  return (
    <div>
      <TextField
        select
        variant="outlined"
        id="symbolization"
        label="Symbolization"
        value={symbolChoice || ""}
        fullWidth
        onChange={onChangeSymbolChoice}
      >
        {options.map((item, n) => (
          <MenuItem key={n} value={item.description}>
            {item.description}
          </MenuItem>
        ))}
      </TextField>
    </div>
  );
};

SymbolChoice.propTypes = {
  symbolChoice: PropTypes.string,
  onChangeSymbolChoice: PropTypes.func,
  options: PropTypes.array,
};
