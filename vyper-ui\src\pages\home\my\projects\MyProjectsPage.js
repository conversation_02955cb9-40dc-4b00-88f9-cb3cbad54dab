import React from "react";
import { Link } from "react-router-dom";
import { DataGrid } from "../../../../component/universal/DataGrid";
import { AuthContext } from "src/component/common/auth";

export const MyProjectsPage = () => {
  const { authUser } = useContext(AuthContext);

  if (authUser.uid === "") return null;

  const columns = [
    {
      title: "Vyper Number",
      field: "vyperNumber",
      render: (rowData) => {
        return (
          <Link to={`/projects/${rowData.vyperNumber}`}>
            {rowData.vyperNumber}
          </Link>
        );
      },
    },
    { title: "Title", field: "title" },
    { title: "Project Type", field: "projectType" },
    { title: "Devices", field: "materials" },
    { title: "Facilities", field: "facilities" },
    { title: "Owner Names", field: "ownerNames" },
    { title: "Owner UserIDs", field: "ownerUserids" },
  ];

  return (
    <div>
      <DataGrid
        title="My Projects"
        url={`/vyper/v1/vyper/project/my?userid=${authUser?.uid}`}
        columns={columns}
        pageable
        pageSize={20}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
      />
    </div>
  );
};
