import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import React from "react";

export const TravelerItem = ({ travelerItem }) => {
  return (
    <TableRow>
      <TableCell>{travelerItem.operation}</TableCell>
      <TableCell>{travelerItem.componentName}</TableCell>
      <TableCell>{travelerItem.componentValue}</TableCell>
      {travelerItem.paragraph == null ? (
        <>
          <TableCell>{travelerItem.attributeName}</TableCell>
          <TableCell>{travelerItem.attributeValue}</TableCell>
        </>
      ) : (
        <TableCell colSpan={2}>
          <pre>{travelerItem.paragraph}</pre>
        </TableCell>
      )}
    </TableRow>
  );
};
