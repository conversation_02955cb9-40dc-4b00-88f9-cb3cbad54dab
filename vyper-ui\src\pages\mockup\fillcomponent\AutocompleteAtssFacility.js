import MenuItem from "@material-ui/core/MenuItem";
import TextField from "@material-ui/core/TextField";
import React, { useEffect, useState } from "react";

export const AutocompleteAtssFacility = ({
  device,
  facility,
  onChangeFacility,
  disabled
}) => {
  const [facilities, setFacilities] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (device == null || device === "") {
      return;
    }

    setLoading(true);

    fetch(`/vyper/v1/vyper/atss/facility/lookup?device=${device}`)
      .then((response) => response.json())
      .then((json) => setFacilities(json))
      .catch((e) => console.log(e))
      .finally(() => setLoading(false));
  }, [device]);

  return (
    <div>
      <TextField
        disabled={disabled}
        variant="outlined"
        fullWidth
        select
        label="Facility"
        value={facility || ""}
        onChange={(e) => onChangeFacility(e.target.value)}
        style={{ minWidth: "20em" }}
      >
        {facilities.map((facility) => (
          <MenuItem key={facility} value={facility}>
            {facility}
          </MenuItem>
        ))}
      </TextField>
    </div>
  );
};
