import PropTypes from "prop-types";
import React from "react";
import { useTravelerStyles } from "./travelerStyles";

/**
 * return the attribute row for the traveler.
 * @param {TravelerAttribute} attribute - The traveler attribute
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerAttribute({ attribute }) {
  const classes = useTravelerStyles();
  const name = classes[attribute.validStatus];
  return (
    <>
      <span className={name}>
        {("".padStart(6, " ") + attribute.name).padEnd(32, " ")}
      </span>
      : <span>{attribute.value || ""}</span>
      <br />
    </>
  );
}

TravelerAttribute.propTypes = {
  attribute: PropTypes.object.isRequired,
};
