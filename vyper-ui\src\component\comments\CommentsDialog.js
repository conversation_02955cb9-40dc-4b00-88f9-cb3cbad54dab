/* eslint-disable */
import React, { useContext, useState, useRef, useEffect } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import {
  Typography,
  Select,
  FormControl,
  InputLabel,
  MenuItem,
} from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import TextField from "@material-ui/core/TextField";
import CommentBox from "./CommentBox";
import { AuthContext } from "src/component/common/auth";
const useStyles = makeStyles((theme) => ({
  textBox: {},
  commentArea: {
    height: "50vh",
    overflowY: "scroll",
    border: "2px solid grey",
    borderRadius: "1%",
    padding: "10px",
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  dialogActions: {
    display: "flex",
    justifyContent: "space-between",
    width: "100%",
    margin: "0em 1em 1em 1em",
  },
}));

export const CommentsDialog = ({ children }) => {
  const { authUser: currentUser } = useContext(AuthContext);
  const generalCommentCategory = "General";

  const [open, setOpen] = useState(false);
  const [parameters, setParameters] = useState();
  const [comments, setComments] = useState([]);
  const [operations, setOperations] = useState([]);
  const [comment, setComment] = useState({
    who: { username: "", userid: "" },
    when: new Date(),
    text: "",
    operation: generalCommentCategory,
  });

  const handleOpen = (parameters) => {
    setParameters(parameters);
    setComments(parameters.comments);
    setOperations(parameters.operations);
    setComment({
      who: { username: currentUser?.name, userid: currentUser?.uid },
      when: new Date(),
      text: "",
      operation: generalCommentCategory,
    });

    scrollToBottom();
    setOpen(true);
  };

  const handleClose = () => {
    setParameters(undefined);
    setComments([]);
    setOperations([]);
    setComment({
      who: comment.who,
      when: new Date(),
      text: "",
      operation: generalCommentCategory,
    });
    setOpen(false);
  };

  const handleSave = () => {
    handleAddComment();
  };

  const handleAddComment = () => {
    setComment({
      who: comment.who,
      when: new Date(),
      text: comment.text,
      operation: comment.operation,
    });

    parameters.onSave(comment);
    setComments([...comments, comment]);

    setComment({
      who: { username: currentUser?.username, userid: currentUser?.userid },
      when: new Date(),
      text: "",
      operation: generalCommentCategory,
    });
  };

  const canSave =
    comment.when != null && comment.text != null && comment.text !== "";

  const classes = useStyles();
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  };
  useEffect(() => {
    scrollToBottom();
  }, [comment]);

  return (
    <CommentsDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="lg">
        <DialogTitle>Comments</DialogTitle>
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <div className={classes.commentArea}>
            {comments.length != 0 ? (
              <>
                {comments.map((c, n) => (
                  <CommentBox c={c} n={n} />
                ))}
              </>
            ) : (
              <Typography variant="h6">No Comments To Display...</Typography>
            )}
            <div ref={messagesEndRef} />
          </div>

          <TextField
            label="Comment"
            value={comment.text || ""}
            onChange={(e) =>
              setComment({
                ...comment,
                text: e.target.value,
              })
            }
            position="fixed"
            fullWidth
            className={classes.textBox}
            multiline
            variant="outlined"
            rows={4}
            placeholder="Enter your comment here."
            autoFocus
            onKeyPress={(ev) => {
              if (ev.key === "Enter") {
                handleSave();
                ev.preventDefault();
              }
            }}
          />
        </DialogContent>

        <DialogActions>
          <div className={classes.dialogActions}>
            <FormControl
              style={{ width: "300px" }}
              variant="outlined"
              sx={{ width: 300 }}
            >
              <InputLabel>Operation</InputLabel>
              <Select
                value={comment.operation}
                label="Operation"
                onChange={(e) =>
                  setComment({
                    ...comment,
                    operation: e.target.value,
                  })
                }
              >
                <MenuItem value={generalCommentCategory}>
                  {generalCommentCategory}
                </MenuItem>

                {operations.map((opName, n) => (
                  <MenuItem value={opName} key={n}>
                    {opName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button
              disabled={!canSave}
              onClick={handleSave}
              variant="contained"
              color="primary"
            >
              Add Comment
            </Button>
          </div>
        </DialogActions>
      </Dialog>

      {children}
    </CommentsDialogContext.Provider>
  );
};

export const CommentsDialogContext = React.createContext(null);
