import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import React from "react";
import { existsInFlow } from "../../pages/vyper/FormStatus";

export const DieHeader = ({ build }) => {
  return (
    <TableHead>
      <TableRow hover>
        <TableCell>Die Type</TableCell>
        <TableCell>Die Sequence</TableCell>
        <TableCell>Priority</TableCell>
        <TableCell>Die Name</TableCell>
        {existsInFlow(build, "Backgrind") && (
          <TableCell>Incoming Wafer Thickness</TableCell>
        )}
        <TableCell>Remove Die</TableCell>
        <TableCell>Add Priority</TableCell>
      </TableRow>
    </TableHead>
  );
};
