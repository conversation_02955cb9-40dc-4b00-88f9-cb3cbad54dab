import React, { useContext } from "react";
import { QuestionLink } from "../../../component/question/QuestionLink";
import { IconButton, makeStyles } from "@material-ui/core";
import HelpIcon from "@material-ui/icons/Help";
import { HelperContext } from "src/component/helper/Helpers";

const useStyles = makeStyles({
  bar: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
});

export const VyperHeader = ({ vyper, onChangeTitle, helpLink }) => {
  const { canEditOwners } = useContext(HelperContext);

  // can the current user edit this vyper?
  const canEdit = canEditOwners(vyper);

  const classes = useStyles();

  if (vyper == null) return null;

  return (
    <div className={classes.bar}>
      <div>
        {vyper.vyperNumber} [{vyper.state}]: &nbsp;
        {canEdit ? (
          <QuestionLink
            title="Edit Project Title"
            description="Please enter the title of the project"
            value={vyper.title}
            onSave={onChangeTitle}
            defaultValue="&lt;enter title&gt;"
          />
        ) : (
          <span>{vyper.title || "untitled"}</span>
        )}
      </div>

      <div>
        <IconButton
          target="_blank"
          href={
            helpLink ||
            "https://confluence.itg.ti.com/display/SimbaInfo/Build+screen"
          }
        >
          <HelpIcon />
        </IconButton>
      </div>
    </div>
  );
};
