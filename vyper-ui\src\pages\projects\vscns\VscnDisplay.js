import {
  makeStyles,
  withStyles,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";
import { PraIcons } from "src/pages/vyper/pras/PraIcons";
import { VscnValue } from "./VscnValue";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
  icons: {
    minWidth: 150,
  },
}));

/**
 * Display the verifier icons and the pra value.
 * @param string name - The component name
 * @param string value - The component value
 * @param engineering - YES/NO values indicating the value is and engineering value
 * @param verifiers - The array of verifiers for the component
 * @param onClick - callback for clicking the value's link
 * @returns {JSX.Element}
 * @function
 */
export const VscnDisplay = ({
  vscn,
  name,
  value,
  engineering,
  verifiers,
  onClick,
  canEdit,
}) => {
  const classes = useStyles();

  const HtmlTooltip = withStyles((theme) => ({
    tooltip: {
      backgroundColor: "#f5f5f9",
      color: "rgba(0, 0, 0, 0.87)",
      fontSize: theme.typography.pxToRem(12),
      border: "2px solid #808080",
    },
  }))(Tooltip);

  const attributes = vscn.components
    .filter((c) => c.name === name)
    .flatMap((c) => c.instances)
    .flatMap((i) => i.priorities)
    .filter((p) => p.object.name === value)
    .map((p) => p.object)
    .find((o) => o != null);

  return (
    <div className={classes.root}>
      <div className={classes.icons}>
        <PraIcons verifiers={verifiers} />
      </div>
      <HtmlTooltip
        arrow
        interactive
        title={
          <React.Fragment>
            <Paper style={{ overflow: "hidden" }}>
              <TableContainer style={{ maxHeight: 440 }}>
                <Table stickyHeader aria-label="sticky table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Attribute Name</TableCell>
                      <TableCell align="right">Attribute Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Object.entries(attributes).map(
                      ([key, value] = attribute) => (
                        <TableRow key={key} hover role="checkbox" tabIndex={-1}>
                          <TableCell component="th" scope="row">
                            {key}
                          </TableCell>
                          <TableCell align="right">{value}</TableCell>
                        </TableRow>
                      )
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </React.Fragment>
        }
      >
        <div>
          {canEdit ? (
            <VscnValue
              name={name}
              value={value}
              engineering={engineering}
              onClick={onClick}
            />
          ) : (
            value
          )}
        </div>
      </HtmlTooltip>
    </div>
  );
};

VscnDisplay.propTypes = {
  name: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  engineering: PropTypes.string.isRequired,
  verifiers: PropTypes.array.isRequired,
  onClick: PropTypes.func.isRequired,
};
