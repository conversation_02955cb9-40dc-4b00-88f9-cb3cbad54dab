import React from "react";
import { VerticalTabs } from "../../component/tab/VerticalTabs";
import useQueryParam from "../../component/queryparams/useQueryParam";
import { Cycletime } from "src/pages/report/Cycletime";
import { RoleSvcUserStatus } from "src/pages/report/RoleSvcUserStatus";

export const ReportIndexPage = () => {
  const [label] = useQueryParam("label", null);

  const tabs = [
    {
      label: "Cycletime",
      control: <Cycletime />,
    },
    {
      label: "RoleSvc Users",
      control: <RoleSvcUserStatus />,
    },
  ];

  // get the selected tab, or default to zero
  let selectedTab = tabs.findIndex((tab) => tab.label === label);
  if (selectedTab === -1) selectedTab = 0;

  return (
    <div>
      <h1>Reports</h1>

      <hr />

      <VerticalTabs
        storageKey="report.tab.value"
        tabs={tabs}
        selectTab={selectedTab}
      />
    </div>
  );
};
