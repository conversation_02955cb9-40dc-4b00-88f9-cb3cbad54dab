import { MenuItem, TextField } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";

/**
 * Display a select component for choosing the facility
 *
 * @param facilities - the list of facilities (array of objects)
 * @param facility - the current facility (object)
 * @param onChange - called when the facility changes
 * @returns {JSX.Element|null}
 * @function
 */
export const Facility = ({ facilities, facility, onChange }) => {
  const handleChange = (e) => onChange(e.target.value);

  return (
    <TextField
      variant="outlined"
      select
      id="facility"
      label="Select the Facility"
      fullWidth
      value={facility}
      onChange={handleChange}
    >
      {facilities.map((f, n) => (
        <MenuItem key={n} value={f}>
          {f.PDBFacility}
        </MenuItem>
      ))}
    </TextField>
  );
};

Facility.propTypes = {
  facilities: PropTypes.array,
  facility: PropTypes.string,
  onChange: PropTypes.func,
};
