import axios from "axios";

/**
 * Retrieve the list of symbols for the package and facilityAt.
 *
 * @param {string} pkg - The package designator
 * @param {string} facilityAt - The facility A/T
 * @param {string} symbolName - The symbolization name
 * @param {string} pinCount - The pin count
 * @return {Promise<LenAndCustName[]>} A promise that resolves to a list of Symbols.
 */
export const custLengthApi = (pkg, facilityAt, symbolName, pinCount) => {
  const url = `/vyper/v1/topsidecust/findCustMaxLen?pkg=${pkg}&facility=${facilityAt}&symbol_name=${symbolName}&pin_count=${pinCount}`;
  return axios.get(url).then((response) => response.data);
};
