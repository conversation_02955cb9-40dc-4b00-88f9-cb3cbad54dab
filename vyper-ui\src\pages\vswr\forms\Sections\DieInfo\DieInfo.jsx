import React, { useState, useRef, useEffect, useMemo } from "react";
import Grid from "@material-ui/core/Grid";
import { AgGridReact } from "ag-grid-react";
import { Edit, Add, Clear } from "@material-ui/icons/";
import IconButton from "@material-ui/core/IconButton";
import Button from "@material-ui/core/Button";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import useSnackbar from "/src/hooks/Snackbar";

import DieLotStatusDialog from "./DieLotStatusDialog";
import { BASE_FETCH_OPTIONS_URL, numberFormatter } from "../../FormConstants";

const defaultColDef = {
  headerClass: "ti-ag-header",
  resizable: true,
  suppressMovable: true,
};

const dieColumnDefs = [
  { headerName: "Sequence", field: "sequence", width: "102px" },
  { headerName: "Priority", field: "priority", width: "90px" },
  {
    headerName: "Matl Master Die Name",
    field: "matlMasterDieName",
    width: "195px",
  },
  { headerName: "Die Rev", field: "dieRev", width: "90px" },
  { headerName: "Die Designator", field: "dieDesignator", width: "140px" },
  { headerName: "Scribe Width", field: "scribeWidth", width: "130px" },
  { headerName: "Die Size", field: "dieSize", width: "275px" },
  { headerName: "FAB Code", field: "fabCode", width: "104px" },
  { headerName: "FAB Technology", field: "fabTechnology", width: "150px" },
  { headerName: "Wafer Diameter", field: "waferDiameter", width: "148px" },
  { headerName: "Wafer Thickness", field: "waferThickness", width: "150px" },
  {
    headerName: "Backgrind Thickness post FAB",
    field: "backgrindThickness",
    width: "250px",
  },
];

const fetchOptionData = (uri, setFormState, defaultValue) => {
  fetch(`${BASE_FETCH_OPTIONS_URL}${uri}`)
    .then((response) => response.json())
    .then(setFormState)
    .catch(() => {
      setFormState(defaultValue);
    });
};

const listContainsObj = (dieLots, obj2) => {
  if (dieLots.length === 0) {
    return false;
  }

  return dieLots.some((obj1) => {
    const k1 = Object.keys(obj1);
    const k2 = Object.keys(obj2);
    if (k1.length !== k2.length) {
      return false;
    }
    return k1.every((key) => obj1[key] === obj2[key]);
  });
};

const DieInfo = (props) => {
  const { classes, dieData, setDieData, readOnly = false } = props;

  const dieInfoGridRef = useRef();

  const [selectedDieData, setSelectedDieData] = useState(-1);

  const [editDieLotIndex, setEditDieLotIndex] = useState(-1);
  const [dieLotData, setDieLotData] = useState([]);

  const [dieDialogOpen, setDieDialogOpen] = useState(false);
  const [dieLotRowData, setDieLotRowData] = useState({});
  const [dieLocationOptions, setDieLocationOptions] = useState([]);

  const { enqueueErrorSnackbar } = useSnackbar();

  const statusLabelFinder = (params) => {
    const { value } = params;
    if (!value || !dieLocationOptions) {
      return "";
    }

    const match = dieLocationOptions.find((dlo) => dlo.value == value);
    return match ? match.label : "";
  };

  const dieLotInfoColumns = useMemo(
    () => [
      { headerName: "Material", field: "matlMasterDieName", width: "130px" },
      { headerName: "Die Lot/SAP Lot", field: "dieLot", width: "148px" },
      {
        headerName: "Material Shipment Status",
        field: "matShipStatus",
        width: "215px",
        valueFormatter: statusLabelFinder,
      },
      { headerName: "Plant", field: "plant", width: "72px" },
      { headerName: "Delivery Note", field: "deliveryNote", width: "133px" },
      {
        headerName: "Qty",
        field: "qtyToShip",
        width: "95px",
        valueFormatter: numberFormatter,
      },
      { headerName: "Location", field: "location", width: "135px" },
      { headerName: "Date Shipped", field: "dateShipped", width: "160px" },
      { headerName: "SAP Waybill#", field: "sapWaybill", width: "128px" },
    ],
    [dieLocationOptions]
  );

  useEffect(() => {
    fetchOptionData("/dieLocation", setDieLocationOptions, []);
  }, []);

  const onRowClicked = (evt) => {
    if (evt.node.rowIndex === selectedDieData) {
      return;
    }
    setSelectedDieData(evt.node.rowIndex);
    setDieLotData(evt.data.dieLots);
  };

  const handleDelete = (rowIndex) => {
    const newDieLotData = [...dieLotData];
    newDieLotData.splice(rowIndex, 1);
    setDieLotData(newDieLotData);
    setDieData((prevDieState) => {
      const newDieInfo = [...prevDieState];
      newDieInfo[selectedDieData].dieLots = newDieLotData;
      return newDieInfo;
    });
  };

  const handleEdit = (rowIndex, rowData) => {
    setDieLotRowData(rowData);
    setEditDieLotIndex(rowIndex);
    setDieDialogOpen(true);
  };

  const handleAdd = () => {
    setEditDieLotIndex(dieLotData.length);
    setDieDialogOpen(true);
  };

  const handleDieLotActions = (row) => {
    const { data, rowIndex } = row;
    if (data) {
      return (
        <div>
          <IconButton onClick={() => handleEdit(rowIndex, data)}>
            <Edit />
          </IconButton>
          <IconButton onClick={() => handleDelete(rowIndex)}>
            <Clear />
          </IconButton>
        </div>
      );
    }
    return <></>;
  };

  const dieLotActionColumn = {
    headerName: "Actions",
    field: "actions",
    width: "90px",
    pinned: "left",
    cellRenderer: handleDieLotActions,
  };

  const handleCancelDialog = () => {
    setDieDialogOpen(false);
  };

  const handleSaveDialog = (dieLotStatus) => {
    if (listContainsObj(dieLotData, dieLotStatus)) {
      enqueueErrorSnackbar("Selected row is already in table.");
    } else {
      const newDieLotData = [...dieLotData];
      newDieLotData[editDieLotIndex] = { ...dieLotStatus };
      setDieLotData(newDieLotData);
      setDieData((prevDieState) => {
        const newDieInfo = [...prevDieState];
        newDieInfo[selectedDieData].dieLots = newDieLotData;
        return newDieInfo;
      });
    }
    setDieDialogOpen(false);
    setEditDieLotIndex(-1);
  };

  const dieLotColumnDefs = useMemo(() => {
    if (readOnly) {
      return dieLotInfoColumns;
    }
    return [dieLotActionColumn, ...dieLotInfoColumns];
  });

  return (
    <Paper elevation={24} className={classes.paper} style={{ width: "100%" }}>
      <DieLotStatusDialog
        classes={classes}
        isOpen={dieDialogOpen}
        handleCancel={handleCancelDialog}
        handleSave={handleSaveDialog}
        defaultDieLotData={dieLotRowData}
        material={dieData?.[selectedDieData]?.matlMasterDieName || ""}
        plant={dieData?.[selectedDieData]?.plant || ""}
        dieLocationOptions={dieLocationOptions}
      />
      <Typography variant="h6">Die Information</Typography>
      <Grid>
        <Grid container spacing={1}>
          <div
            className={"ti-server-ag-grid ag-theme-alpine"}
            style={{ marginBottom: "10px", width: "100%", height: "100%" }}
          >
            <AgGridReact
              ref={dieInfoGridRef}
              rowData={dieData}
              columnDefs={dieColumnDefs}
              domLayout={"autoHeight"}
              defaultColDef={defaultColDef}
              rowSelection={"single"}
              suppressCellSelection={true}
              suppressColumnVirtualisation={true}
              onRowClicked={onRowClicked}
              enableCellTextSelection={true}
            />
          </div>
        </Grid>
      </Grid>
      {selectedDieData > -1 && (
        <Grid>
          <div
            style={{
              margin: "5px 0px 10px 0px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <Typography variant="h6">
              {dieData[selectedDieData]?.matlMasterDieName} Die Lot Information
            </Typography>
            {!readOnly && (
              <Button
                startIcon={<Add />}
                variant="contained"
                color="secondary"
                onClick={handleAdd}
              >
                Add New Die Lot
              </Button>
            )}
          </div>
          <Grid container spacing={1}>
            <div
              className={"ti-server-ag-grid ag-theme-alpine"}
              style={{ marginBottom: "10px", width: "100%", height: "100%" }}
            >
              <AgGridReact
                rowData={dieLotData}
                columnDefs={dieLotColumnDefs}
                domLayout={"autoHeight"}
                defaultColDef={defaultColDef}
                suppressColumnVirtualisation={true}
                suppressCellSelection={true}
                enableCellTextSelection={true}
              />
            </div>
          </Grid>
        </Grid>
      )}
    </Paper>
  );
};
export default DieInfo;
