import { makeStyles } from "@material-ui/core";
import PropTypes from "prop-types";
import React, { useContext } from "react";
import { EngineeringIcon } from "src/component/icons/EngineeringIcon";
import { VyperLink } from "src/pages/mockup/VyperLink";
import { ComponentMapContext } from "../../../component/componentmap/ComponentMap";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
}));

/**
 * Display the PRA component value.
 *
 * @param name The component name
 * @param value The component value
 * @param engineering Y to display the engineering icon, or N to hide it
 * @param onClick
 * @returns {JSX.Element}
 * @function
 */
export const VscnValue = ({ name, value, engineering, onClick }) => {
  const { findComponentMapByName } = useContext(ComponentMapContext);
  const componentMap = findComponentMapByName(name);

  const is74 = null != value.trim().match(/[0-9]{7}-[0-9]{4}/);
  const isEngineering = engineering === "Y";
  const shouldBe74 = componentMap?.valueShouldBe74 === "YES" || false;

  // editable if value is 7-4 (-4 suffix)
  // editable if value is engineering
  // editable if value should be 7-4, but it's not
  const isEditable = true;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <VyperLink
        canEdit={isEditable}
        onClick={() => onClick(value, is74, isEngineering, shouldBe74)}
      >
        {value}
      </VyperLink>
      <EngineeringIcon engineering={engineering} />
    </div>
  );
};

VscnValue.propTypes = {
  name: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  engineering: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
};
