import { DialogContentText } from "@material-ui/core";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import DialogActions from "@material-ui/core/DialogActions";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import CloseIcon from "@material-ui/icons/Close";
import { produce } from "immer";
import React, { useState } from "react";
import { existsInFlow } from "../../pages/vyper/FormStatus";
import { Dies } from "./Dies";

export const DieDialog = ({ children }) => {
  const styles = makeStyles((theme) => ({
    closeButton: {
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    autocomplete: {
      minWidth: "13rem",
    },
    dialog: {
      padding: "3rem",
      margin: "3rem",
    },
  }));

  const [parameters, setParameters] = useState();

  const [instances, setInstances] = useState();

  const [open, setOpen] = useState(false);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const handleOpen = (parameters) => {
    setParameters(parameters);
    setInstances(parameters.instances);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = () => {
    //If buildType is Minor change ask for confimation for Die
    if (parameters?.build.buildtype === "Minor Change") {
      setConfirmDialogOpen(true);
    } else {
      parameters.onSave(instances);
      handleClose();
    }
  };

  const handleConfirm = () => {
    parameters.onSave(instances); //Save the on confirm
    setConfirmDialogOpen(false);
    handleClose();
  };

  const handleCancel = () => {
    setConfirmDialogOpen(false); // Close confirmation dialog
  };

  /**
   * Change an existing die
   * @param rowIndex
   * @param columnIndex
   * @param die
   */
  const handleChangeDie = (rowIndex, columnIndex, die) => {
    setInstances(
      produce(instances, (draft) => {
        draft[rowIndex].dies[columnIndex] = die;
      })
    );
  };

  /**
   * Add another priority
   * @param rowIndex
   */
  const handleAddPriority = (rowIndex) => {
    setInstances(
      produce(instances, (draft) => {
        draft[rowIndex].dies.push({});
      })
    );
  };

  /**
   * Remove the priority die. If no more dies left, remove the instance.
   *
   * @param rowIndex
   * @param columnIndex
   */
  const handleRemovePriority = (rowIndex, columnIndex) => {
    setInstances(
      produce(instances, (draft) => {
        draft[rowIndex].dies.splice(columnIndex, 1);
        if (draft[rowIndex].dies.length === 0) {
          draft.splice(rowIndex, 1);
        }
      })
    );
  };

  /**
   * Add a die instance.
   */
  const handleAddInstance = () => {
    setInstances(
      produce(instances, (draft) => {
        draft.push({ type: "Side-By-Side", dies: [{}] });
      })
    );
  };

  /**
   * Change the die type
   * @param rowIndex
   * @param type
   */
  const handleChangeType = (rowIndex, type) => {
    setInstances(
      produce(instances, (draft) => {
        draft[rowIndex].type = type;
      })
    );
  };
  /**
   * check if wafer thick can be saved
   */
  const isValid = () => {
    return instances?.every((instance) => {
      return instance?.dies?.every((die) => {
        return (
          die.name != null &&
          die.name.trim() !== "" &&
          (existsInFlow(parameters?.build, "Backgrind")
            ? die.incomingWaferThick > 99 && die.incomingWaferThick < 1001
            : true)
        );
      });
    });
  };
  const classes = styles();

  return (
    <DieDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="xl" fullWidth>
        <DialogTitle>Die</DialogTitle>

        <IconButton className={classes.closeButton} onClick={handleClose}>
          <CloseIcon />
        </IconButton>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>
            Use this dialog to add/remove the dies used in the build.
          </DialogContentText>

          <Dies
            facility={parameters?.facility}
            instances={instances}
            onChangeDie={handleChangeDie}
            onAddPriority={handleAddPriority}
            onAddDie={handleAddInstance}
            onRemoveDie={handleRemovePriority}
            onChangeType={handleChangeType}
            build={parameters?.build}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            color="primary"
            disabled={!isValid()}
          >
            Save
          </Button>
        </DialogActions>

        {/* Confirmation Dialog */}
        <Dialog
          open={confirmDialogOpen}
          onClose={handleCancel}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Confirm Die compatibility with M/B Diagram</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {parameters?.build?.components.length > 0 && (
                <>
                <b>
                MB Diagram:{" "}
                {parameters.build.components
                  .find((component) => component.name === "MB Diagram") // Find the MB Diagram component
                  ?.instances.map((instance) =>
                    instance.priorities.map((priority) => priority.object.name)
                  )
                  .flat()
                  .join(", ")}
                
                <br/>
                </b>
                </>
              )}
              {instances?.length > 0 && (
                <>
                <b>
                  Selected Dies:{" "}
                  {instances
                    .flatMap((instance) => instance.dies.map((die) => die.name))
                    .join(", ")}
                  .
                  <br />
                  <br />
                </b>
                </>
              )}
              If there will be any change to the size, scribe, bond pad
              locations, top metal, wire, rotation, or any other attributes in
              the MB diagram, then a Minor Change Vyper cannot be used.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancel} variant="outlined" color="primary">
              Cancel
            </Button>
            <Button onClick={handleConfirm} variant="contained" color="primary">
              Confirm
            </Button>
          </DialogActions>
        </Dialog>
      </Dialog>

      {children}
    </DieDialogContext.Provider>
  );
};

export const DieDialogContext = React.createContext(null);
