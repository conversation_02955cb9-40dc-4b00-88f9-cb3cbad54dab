import React from "react";
import { rightPad } from "./Padding";

const Attributes = ({ options, attributes }) => {
  const prefix = options.editbutton ? "                    " : "         ";

  return options.attribute === false ? null : (
    <>
      {attributes.map((attribute, n) => (
        <span key={n}>{`${prefix}${rightPad(attribute.name, 26)}: ${
          attribute.value
        }\n`}</span>
      ))}
    </>
  );
};

export default Attributes;
