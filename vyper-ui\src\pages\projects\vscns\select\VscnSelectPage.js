import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext, useEffect } from "react";
import { ComponentMapContext } from "src/component/componentmap/ComponentMap";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { useLocalStorage } from "../../../../component/hooks/useLocalStorage";
import { AtSelectionDialogContext } from "../../../mockup/atselection/AtSelectionDialog";
import { VscnBody } from "./VscnBody";
import { useParams } from "react-router-dom";
import { VscnPageTitle } from "./VscnPageTitle";
import { BackToVscnFormLink } from "./BackToVscnFormLink";

const useStyles = makeStyles({
  root: {},
  split: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  comments: {
    paddingBottom: "4rem",
  },
  gridRow: {
    paddingBottom: "1rem",
  },
  optionBar: {
    position: "sticky",
    backgroundColor: "white",
    top: 85,
    paddingTop: "1px",
    zIndex: 10, // the icons have an index of 1, so we keep the optionBar on top
  },
  table: {
    backgroundColor: "#CA8F8F",
  },
  info: {
    color: "red",
  },
});

export const VscnSelectPage = () => {
  const { vyperNumber, vscnNumber } = useParams();
  const { showSelectionDialog } = useContext(AtSelectionDialogContext);
  const { componentMaps } = useContext(ComponentMapContext);
  const { vyperDao, vyper, buildDao, build, vscnDao, vscn, componentDao } =
    useContext(DataModelsContext);

  // manage the options dialog
  const [options, setOptions] = useLocalStorage("traveler.options", {
    attribute: true,
    paragraph: true,
    component: true,
    header: true,
    editbutton: true,
    rejection: true,
    comments: true,
    changelog: true,
  });

  /**
   * If the vyper number changes, get the vyper object
   */
  useEffect(() => {
    reloadVyper().catch(noop);
  }, [vyperNumber]);

  const reloadVyper = () => {
    return vyperDao.findByVyperNumber(vyperNumber).catch(noop);
  };

  /**
   * if build number changes, reload the vyper
   */
  useEffect(() => {
    if (vscn?.buildNumber != undefined) {
      reloadBuild().catch(noop);
    }
  }, [vscn?.buildNumber]);

  const reloadBuild = () => {
    return buildDao.findByBuildNumber(vscn.buildNumber).catch(noop);
  };

  /**
   * if vscn number changes, reload the vyper
   */
  useEffect(() => {
    reloadVscn().catch(noop);
  }, [vscnNumber]);

  const reloadVscn = () => {
    return vscnDao.findByVscnNumber(vscnNumber).catch(noop);
  };

  // edit a component value
  // noinspection JSUnusedLocalSymbol
  const handleEditComponentValue = (operation, component) => {
    // if this is an eng component, change to the vyper component name

    let cName = component.name;
    const componentMap = componentMaps.find(
      (cm) => cm.engineeringAtssComponentName === cName
    );
    if (componentMap != null) {
      cName = componentMap.name;
    }

    const travelerOperation = vscn.traveler.operations.filter(
      (o) => o.subflowType === "PACK" && o.name === operation.name
    )?.[0];

    const selectionItems = travelerOperation?.components
      .filter((c) => c.name === component.name)
      .map((c) => {
        const obj = {
          engineering: c.engineering,
          source: c.sourceValue,
          value: c.value,
        };
        return obj;
      });

    // show the a/t selection dialog
    showSelectionDialog({
      selection: {},
      build: build,
      name: cName,
      selectionItems,
      disableAddEngineering: true,
      onSave: (selection) => {
        return vscnDao
          .changeSelection(
            vscnNumber,
            operation.name,
            selection.name,
            selection.items
          )
          .catch(noop);
      },
    });
  };

  const classes = useStyles();

  if (build == null) {
    return null;
  }

  return (
    <div className={classes.root}>
      <BackToVscnFormLink vyperNumber={vyperNumber} vscn={vscn} />

      <div className={classes.optionBar}>
        <VscnPageTitle
          vscn={vscn}
          showState={true}
          title="Select Component Page"
        />
      </div>

      <h2>Pack Traveler</h2>

      <div>
        <pre>
          <VscnBody
            vscn={vscn}
            vyper={vyper}
            build={build}
            options={options}
            onEditComponentValue={handleEditComponentValue}
          />
        </pre>
      </div>
    </div>
  );
};
