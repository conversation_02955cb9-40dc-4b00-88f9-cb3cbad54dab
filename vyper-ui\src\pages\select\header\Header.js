import React from "react";
import HeaderRow from "./HeaderRow";
import { headerItems } from "src/pages/mockup/traveler/headerItems";

const Header = ({ build, options }) => {
  const items = headerItems(build);
  let rows = [];
  for (let n = 0; n < items.length; n += 2) {
    const item1 = items[n];
    const item2 = items[n + 1];

    rows.push(
      <HeaderRow
        key={n}
        name1={item1.title}
        value1={item1.value}
        name2={item2?.title}
        value2={item2?.value}
      />
    );
  }

  return (
    <>
      {options.header === false ? null : (
        <>
          <span>{build.buildNumber + " Specification"}</span>
          <br />
          <br />
          {rows}
          <br />
        </>
      )}
    </>
  );
};

export default Header;
