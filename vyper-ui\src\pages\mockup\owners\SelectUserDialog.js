import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import TextField from "@material-ui/core/TextField";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import Autocomplete, {
  createFilterOptions,
} from "@material-ui/lab/Autocomplete";
import { Chip, TableContainer, TableRow } from "@material-ui/core";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import Table from "@material-ui/core/Table";
import {
  AlertDialogErrorHandler,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { SpinnerContext } from "src/component/Spinner";
import { LdapDao } from "src/dao/LdapDao";
import { noop } from "src/component/vyper/noop";
import { useDebounce } from "src/component/hooks/useDebounced";
import { grey } from "@material-ui/core/colors";
import { max } from "moment";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  width: {
    width: "50%",
  },
  addListButton: {
    color: grey,
    position: "absolute",
    right: theme.spacing(1),
  }
}));

export const SelectUserDialog = ({ children }) => {

  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const classes = useStyles();

  const [show, setShow] = useState(false);
  const [onSave, setOnSave] = useState();
  const [onClose, setOnClose] = useState();
  const [selected, setSelected] = useState();
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [search, setSearch] = useState("");
  const [results, setResults] = useState({ items: [] });
  const [inputValue, setInputValue] = useState('');



  const searchDebounced = useDebounce(search, 300);

  const ldapDao = new LdapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const handleOpen = ({ onSave, onClose }) => {
    setOnSave(() => onSave);
    setOnClose(() => onClose);
    setShow(true);
    setSelected(null);
    setSearch(undefined);
    setResults({ items: [] });
  };

  const handleClose = () => {
    typeof onClose === "function" && onClose();
    setShow(false);
  };


  const handleSave = () => {
    typeof onSave === "function" && onSave(selectedUsers);
    setSelectedUsers([]);
    handleClose();
  };

  useEffect(() => {
    if (searchDebounced != null && searchDebounced !== "") {
      ldapDao
        .lookup(searchDebounced.trim())
        .then((json) => setResults(json))
        .catch(noop);
    }
  }, [searchDebounced]);

  const filterOptions = createFilterOptions({
    trim: true,
  });

  return (
    <SelectUserDialogContext.Provider
      value={{
        handleOpen: handleOpen,
      }}
    >
      <Dialog open={show} onClose={handleClose} maxWidth="xl" style={{ maxWidth: "60%", marginLeft: "20%"}}>
        <DialogTitle>Select User</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>
            Please enter the user's name or userid.
          </DialogContentText>


        <Autocomplete
          multiple
          options={results.items || []}
          getOptionLabel={(option) => 
            `${option.username ?? ''} / ${option.userid ?? ''}`}
          getOptionSelected={(option, value) => 
            option.userid === value?.userid}
          value={selectedUsers}
          onChange={(event, newValue) => {
              const uniqueUsers = newValue.filter((user, index, self) => 
                index === self.findIndex((u) => u.userid === user.userid));
              setSelectedUsers(uniqueUsers);
           }}
          inputValue={inputValue}
          onInputChange={(event, newInputValue) => 
              setInputValue(newInputValue)
          }
          filterSelectedOptions
          renderTags={(value, getTagProps) => 
            value.map((option, index) => (
              <Chip
                variant="outlined"
                label={`${option.username} / ${option.userid}`} 
                {...getTagProps({ index })}
              />
            ))
          }
          sx={{
            width: '100%',
            '.MultiInputBase-root': {
              flexWrap: 'wrap',
              overflowY: 'auto',
              alignItems: 'flex-start',
              maxHeight: '150px',
          },
          '.MuiAutocomplete-input': {
            minWidth: '150px',
            flexGrow: 1,
          },
          '.MuiAutocomplete-tag': {
              maxWidth: '100%',
              wordBreak: 'break-word',
          },
        }}
          renderInput={(params) => (
            <TextField
              {...params}
              inputProps={{
                ...params.inputProps,
                style: {
                  minWidth: '300px',
                  flexGrow: 1,
                },
              }}
              label="Search users"
              variant="outlined"
              placeholder="Search user"
              onChange={(event) => setSearch(event.target.value)}
              />
            )}
        />
          

        <br />
        <br />


        </DialogContent>


        <DialogActions>
        
          <Button 
            onClick={handleSave}
            variant="contained"
            color="primary"
          >
            Select
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </SelectUserDialogContext.Provider>
  );
};

export const SelectUserDialogContext = React.createContext(null);
