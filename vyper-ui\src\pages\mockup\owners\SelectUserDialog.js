import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import TextField from "@material-ui/core/TextField";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import Autocomplete, {
  createFilterOptions,
} from "@material-ui/lab/Autocomplete";
import { TableContainer, TableRow } from "@material-ui/core";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import Table from "@material-ui/core/Table";
import {
  AlertDialogErrorHandler,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { SpinnerContext } from "src/component/Spinner";
import { LdapDao } from "src/dao/LdapDao";
import { noop } from "src/component/vyper/noop";
import { useDebounce } from "src/component/hooks/useDebounced";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  width: {
    width: "50%",
  },
}));

export const SelectUserDialog = ({ children }) => {
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const classes = useStyles();

  const [show, setShow] = useState(false);
  const [onSave, setOnSave] = useState();
  const [onClose, setOnClose] = useState();
  const [selected, setSelected] = useState(null);
  const [search, setSearch] = useState();
  const [results, setResults] = useState({ items: [] });

  const searchDebounced = useDebounce(search, 300);

  const ldapDao = new LdapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const handleOpen = ({ onSave, onClose }) => {
    setOnSave(() => onSave);
    setOnClose(() => onClose);
    setShow(true);
    setSelected(null);
    setSearch(undefined);
    setResults({ items: [] });
  };

  const handleClose = () => {
    typeof onClose === "function" && onClose();
    setShow(false);
  };

  const handleSave = () => {
    typeof onSave === "function" && onSave(selected);
    handleClose();
  };

  useEffect(() => {
    if (searchDebounced != null && searchDebounced !== "") {
      ldapDao
        .lookup(searchDebounced.trim())
        .then((json) => setResults(json))
        .catch(noop);
    }
  }, [searchDebounced]);

  const filterOptions = createFilterOptions({
    trim: true,
  });

  return (
    <SelectUserDialogContext.Provider
      value={{
        handleOpen: handleOpen,
      }}
    >
      <Dialog open={show} onClose={handleClose} maxWidth="xl">
        <DialogTitle>Select User</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>
            Please enter the user's name or userid.
          </DialogContentText>

          <Autocomplete
            getOptionLabel={(option) => option.username + " / " + option.userid}
            onInputChange={(e, v) => setSearch(v)}
            onChange={(event, value) => setSelected(value)}
            value={selected}
            getOptionSelected={(option, value) =>
              option.userid === value.userid
            }
            options={results.items}
            renderInput={(params) => (
              <TextField
                autoFocus
                {...params}
                label="Search"
                variant="outlined"
              />
            )}
            filterOptions={filterOptions}
          />

          <br />
          <br />

          <DialogContentText>
            The selected user's info will appear below.
          </DialogContentText>

          <TableContainer>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell
                    className={classes.width}
                    variant="head"
                    align="right"
                  >
                    User Name:
                  </TableCell>
                  <TableCell className={classes.width} variant="body">
                    {selected?.username}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell
                    className={classes.width}
                    variant="head"
                    align="right"
                  >
                    UserID:
                  </TableCell>
                  <TableCell className={classes.width} variant="body">
                    {selected?.userid}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell
                    className={classes.width}
                    variant="head"
                    align="right"
                  >
                    EMail:
                  </TableCell>
                  <TableCell className={classes.width} variant="body">
                    {selected?.email}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell
                    className={classes.width}
                    variant="head"
                    align="right"
                  >
                    Phone:
                  </TableCell>
                  <TableCell className={classes.width} variant="body">
                    {selected?.phone}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            color="primary"
            disabled={null == selected}
          >
            Select
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </SelectUserDialogContext.Provider>
  );
};

export const SelectUserDialogContext = React.createContext(null);
