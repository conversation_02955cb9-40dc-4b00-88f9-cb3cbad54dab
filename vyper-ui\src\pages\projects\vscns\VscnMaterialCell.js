import PropTypes from "prop-types";
import React, { useContext, useState } from "react";
import { VyperLink } from "../../mockup/VyperLink";
import { SelectMaterialDialog } from "../../../component/component/SelectMaterialDialog";
import { DataModelsContext } from "src/DataModel";
import { noop } from "src/component/vyper/noop";
import { PraIcons } from "src/pages/vyper/pras/PraIcons";
import { makeStyles } from "@material-ui/styles";
import { DataCell } from "src/component/datacell/DataCell";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
  icons: {
    minWidth: 150,
  },
}));

/**
 * Display the cell of the material object
 * @param {*} vscn - the vscn
 * @param {VscnMaterialCell~onGetMaterialObject} onGetMaterial - callback to get the material object
 * @return {JSX.Element}
 * @constructor
 */
export const VscnMaterialCell = ({ vscn, onGetMaterialObject, onChange }) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [material, setMaterial] = useState(vscn.material?.object);
  const { vscnDao } = useContext(DataModelsContext);

  const verifiers = vscn.verifiers.filter(
    (verifier) => verifier.name === "Material"
  );

  const handleSave = () => {
    vscnDao
      .changeMaterial(vscn.vscnNumber, material.Material)
      .then((json) => onChange(json))
      .catch(noop);

    setOpen(false);
  };

  return (
    <DataCell source={vscn.material?.source}>
      <MaterialDisplay
        material={material.Material}
        verifiers={verifiers}
        setOpen={setOpen}
      />
      <SelectMaterialDialog
        open={open}
        setOpen={setOpen}
        material={material}
        setMaterial={setMaterial}
        onSave={handleSave}
        title="Select OPN/Material Name"
        label="Select OPN/Material Name for the VSCN"
      />
    </DataCell>
  );
};

VscnMaterialCell.propTypes = {
  vscn: PropTypes.object.isRequired,
  onGetMaterialObject: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
};

const MaterialDisplay = ({ material, verifiers, setOpen }) => {
  const classes = useStyles();

  if (material == null) {
    return "click to select";
  }

  return (
    <div className={classes.root}>
      <div className={classes.icons}>
        <PraIcons verifiers={verifiers} />
      </div>
      <div className={classes.root}>
        <VyperLink onClick={(e) => setOpen(true)}>{material}</VyperLink>
      </div>
    </div>
  );
};
