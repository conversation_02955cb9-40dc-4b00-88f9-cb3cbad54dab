import React, { useState, useContext } from "react";
import Grid from "@material-ui/core/Grid";
import Button from "@material-ui/core/Button";
import { AgGridReact } from "ag-grid-react";
import { Edit, Clear } from "@material-ui/icons/";
import IconButton from "@material-ui/core/IconButton";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import ShippingInfoFields from "./ShippingInfoFields";
import { AlertContext } from "../../CreateForm";
import useSnackbar from "/src/hooks/Snackbar";

import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core/";

import {
  findInvalidFields,
  DEFAULT_SHIPPING_INFO_VALS,
} from "../../FormConstants";
import { REQUIRED_SHIPPING_INFO_FIELDS } from "../../RequiredFormFields";

const defaultColDef = {
  headerClass: "ti-ag-header",
  resizable: true,
  suppressMovable: true,
  flex: 2,
};

const shippingInfoColumns = [
  { headerName: "Attention", field: "attention" },
  { headerName: "Mail Station", field: "mailStation" },
  { headerName: "Plant", field: "plant" },
  { headerName: "Address", field: "address" },
  { headerName: "Quantity", field: "quantity" },
  { headerName: "State of Finish", field: "stateOfFinish", flex: 3 },
  { headerName: "Ship Device Name", field: "shipDeviceName", flex: 3 },
];

const ShippingInfo = (props) => {
  const { classes, shippingData, setShippingData, readOnly = false } = props;

  const [selectedRow, setSelectedRow] = useState(-1);
  const [shippingFormState, setShippingFormState] = useState(
    DEFAULT_SHIPPING_INFO_VALS
  );
  const { enqueueErrorSnackbar } = useSnackbar();

  const handleAdd = () => {
    setSelectedRow(shippingData.length);
    setShippingFormState({ ...DEFAULT_SHIPPING_INFO_VALS });
  };

  const handleCancel = () => {
    setSelectedRow(-1);
    setShippingFormState({ ...DEFAULT_SHIPPING_INFO_VALS });
  };

  const handleSave = () => {
    const invalidFields = findInvalidFields(
      shippingFormState,
      REQUIRED_SHIPPING_INFO_FIELDS
    );
    if (invalidFields.length !== 0) {
      enqueueErrorSnackbar(
        `Please complete the following fields before saving ${invalidFields.toString()}`
      );
      return;
    }
    setShippingData((prevState) => {
      const newShippingData = [...prevState];
      newShippingData[selectedRow] = shippingFormState;
      return newShippingData;
    });
    setSelectedRow(-1);
    setShippingFormState({ ...DEFAULT_SHIPPING_INFO_VALS });
  };

  const handleDelete = (rowIndex) => {
    setShippingData((prevState) => {
      const newShippingData = [...prevState];
      newShippingData.splice(rowIndex, 1);
      return newShippingData;
    });
  };

  const handleEdit = (rowIndex, rowData) => {
    setShippingFormState({ ...rowData });
    setSelectedRow(rowIndex);
  };

  const handleActions = (node) => {
    const { data: rowData, rowIndex } = node;
    if (rowData) {
      return (
        <div>
          <IconButton
            disabled={selectedRow !== -1}
            onClick={() => handleEdit(rowIndex, rowData)}
          >
            <Edit />
          </IconButton>
          <IconButton
            disabled={selectedRow !== -1}
            onClick={() => handleDelete(rowIndex)}
          >
            <Clear />
          </IconButton>
        </div>
      );
    }
    return <></>;
  };

  const actionColumn = {
    headerName: "Actions",
    field: "actions",
    width: "90px",
    pinned: "left",
    cellRenderer: handleActions,
  };

  return (
    <Paper elevation={24} className={classes.paper} style={{ width: "100%" }}>
      <Typography variant="h6"> Shipping Information </Typography>
      <Grid>
        <Grid container spacing={1}>
          <div
            className={"ti-server-ag-grid ag-theme-alpine"}
            style={{ marginBottom: "10px", width: "100%", height: "100%" }}
          >
            <AgGridReact
              rowData={shippingData}
              columnDefs={
                readOnly
                  ? shippingInfoColumns
                  : [actionColumn, ...shippingInfoColumns]
              }
              domLayout={"autoHeight"}
              defaultColDef={defaultColDef}
              suppressColumnVirtualisation={true}
              enableCellTextSelection={true}
            />
          </div>
        </Grid>

        <Grid container justifyContent="flex-start" spacing={1}>
          {!readOnly && (
            <Button variant="contained" onClick={handleAdd} color="secondary">
              Add
            </Button>
          )}
        </Grid>

        <Dialog fullWidth maxWidth="xl" open={selectedRow !== -1}>
          <DialogTitle>Shipping Information</DialogTitle>
          <DialogContent>
            <ShippingInfoFields
              formState={shippingFormState}
              setFormState={setShippingFormState}
              classes={classes}
            />
          </DialogContent>
          <DialogActions>
            <Button
              style={{ marginRight: "20px" }}
              variant="contained"
              onClick={handleCancel}
              color="primary"
            >
              Cancel
            </Button>
            <Button variant="contained" onClick={handleSave} color="secondary">
              Save
            </Button>
          </DialogActions>
        </Dialog>
      </Grid>
    </Paper>
  );
};
export default ShippingInfo;
