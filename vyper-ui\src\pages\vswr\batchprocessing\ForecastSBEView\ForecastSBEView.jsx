import React, { useContext } from "react";
import { useParams } from "react-router-dom";
import PageHeader from "../common/PageHeader";
import CircularProgress from "@material-ui/core/CircularProgress";
import { columnDefs } from "./config";
import { useSbeViewGetter } from "./queries";
import VswrAgGridReact from "../common/VswrAgGridReact";
import { ForbiddenErrorPage } from "../common/HttpErrorPage";
import {
  AuthContext,
  VSWR_ROLE_GROUPS,
  ADMINISTRATIVE_LEVEL,
} from "src/component/common/auth";
const { SBE, BTH } = VSWR_ROLE_GROUPS;
const { ADMIN } = ADMINISTRATIVE_LEVEL;

const ForecastSBEView = () => {
  let { swrId } = useParams();
  const { authUser } = useContext(AuthContext);
  let sbeViewGetter = useSbeViewGetter({ swrId, uploadedBy: authUser?.uid });
  let userVswrSecurityRole = authUser?.vswrSecurity?.roleCategory;
  let userVswrAdministrativeLevel = authUser?.vswrSecurity?.administrativeLevel;

  let hasAdminAccess = userVswrAdministrativeLevel === ADMIN;
  let hasSbeAccess = userVswrSecurityRole === SBE;
  let hasBthAccess = userVswrSecurityRole === BTH;

  if (!(hasSbeAccess || hasBthAccess || hasAdminAccess)) {
    return <ForbiddenErrorPage />;
  }

  return (
    <div className="App BatchContainer">
      <PageHeader
        headerTitle={
          "Forecast Revised (SBE View)" + (swrId ? ` - ${swrId}` : "")
        }
      />
      {sbeViewGetter.isLoading ? (
        <CircularProgress />
      ) : (
        <VswrAgGridReact rowData={sbeViewGetter.data} columnDefs={columnDefs} />
      )}
    </div>
  );
};
export default ForecastSBEView;
