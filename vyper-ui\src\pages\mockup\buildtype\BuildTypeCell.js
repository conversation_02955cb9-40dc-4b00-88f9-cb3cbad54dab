import React, { useContext, useState } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { BuildTypeDialog } from "./BuildTypeDialog";
import { HelperContext } from "src/component/helper/Helpers";
import { VyperLink } from "src/pages/mockup/VyperLink";

export const BuildTypeCell = ({ vyper, build, onSubmit, onAddNewBuild }) => {
  const [open, setOpen] = useState(false);

  const [buildtype, setBuildtype] = useState(build.buildtype);

  const styles = makeStyles(() => ({
    link: {
      textDecoration: "none",
      color: "rgb(85, 26, 139)",
      cursor: "pointer",
      "&:hover": {
        textDecoration: "underline",
      },
    },
    buildtypeText: {
      textDecoration: "none",
      color: "rgb(85, 26, 139)",
    },
  }));

  const classes = styles();

  const handleClick = () => {
    setOpen(true);
  };

  const handleSubmit = (value) => {
    onSubmit(value, build);
    setOpen(false);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const { canEditBuildtype } = useContext(HelperContext);
  const canEdit = canEditBuildtype(vyper, build);

  return (
    <DataCell source={null}>
      <VyperLink
        className={classes.link}
        onClick={handleClick}
        canEdit={canEdit}
      >
        {build.buildtype || "click to select"}
      </VyperLink>

      <BuildTypeDialog
        buildtype={buildtype}
        onChange={(value) => setBuildtype(value)}
        open={open}
        onClose={handleClose}
        onSubmit={handleSubmit}
        hasDevelopmentTemplate={build.bomTemplate?.object?.isDevelopment}
        buildNumber={build.buildNumber}
        onAddNewBuild={onAddNewBuild}
      />
    </DataCell>
  );
};
