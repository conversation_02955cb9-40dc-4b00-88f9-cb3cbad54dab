import React, { useMemo } from "react";
import Grid from "@material-ui/core/Grid";
import TextField from "@material-ui/core/TextField";
import {
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@material-ui/core";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";

const AssemblyInfo = (props) => {
  const { classes, formState, setFormState, readOnly = false } = props;

  const handleChange = (evt) => {
    if (readOnly) {
      return;
    }
    const { name, value } = evt.target;
    setFormState((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const defaultFieldProps = useMemo(() => {
    return {
      InputLabelProps: {
        className: classes.textField,
      },
      disabled: readOnly,
      style: readOnly ? { background: "#DCDCDC" } : {},
      variant: "outlined",
      color: "secondary",
      fullWidth: true,
      onChange: handleChange,
    };
  }, []);

  return (
    <Paper elevation={24} className={classes.paper}>
      <Typography variant="h6">Assembly Information </Typography>
      <Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <FormControl color={"secondary"} component="fieldset">
              <FormLabel component="legend">Assembly Required</FormLabel>
              <RadioGroup
                row
                name="assemblyReq"
                value={formState.assemblyReq}
                onChange={handleChange}
              >
                <FormControlLabel
                  value={"no"}
                  disabled
                  labelPlacement="end"
                  control={<Radio />}
                  label="No"
                />
                <FormControlLabel
                  value={"yes"}
                  labelPlacement="end"
                  control={<Radio />}
                  label="Yes"
                />
              </RadioGroup>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.header}
              name={"header"}
              label="Header"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.baseOutline}
              name={"baseOutline"}
              label="Base Outline"
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.wireDiameter}
              name={"wireDiameter"}
              label="Wire Diameter"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.bondPadMetalization}
              name={"bondPadMetalization"}
              label="Bond Pad Metalization"
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.mbPath}
              name={"mbPath"}
              label="M&B File Path"
            />
          </Grid>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              value={formState.mbOrArcPath}
              name={"mbOrArcPath"}
              label="Other M&B or ARC Path"
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs>
            <TextField
              {...defaultFieldProps}
              fullWidth={false}
              disabled
              style={{ width: "50%", background: "#DCDCDC" }}
              value={formState.forecastedFlag}
              name={"forecastedFlag"}
              label="Forecasted Flag"
            />
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};
export default AssemblyInfo;
