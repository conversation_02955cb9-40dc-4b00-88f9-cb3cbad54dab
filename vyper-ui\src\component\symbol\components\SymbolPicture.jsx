import React from "react";
import TextField from "@material-ui/core/TextField";

export const SymbolPicture = ({ name, picture = "" }) => {
  return (
    <TextField
      multiline
      minRows={Math.max(10, 2 + picture.split("\n").length)}
      fullWidth
      variant="outlined"
      label={name}
      name="picture"
      value={picture}
      onChange={() => {}}
      size="small"
      inputProps={{
        style: { fontFamily: "monospace", whiteSpace: "nowrap" },
      }}
      InputLabelProps={{ shrink: true }}
    />
  );
};
