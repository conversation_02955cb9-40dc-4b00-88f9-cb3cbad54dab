import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessTurnkey } from "../../../component/sameness/sameness";
import { TurnkeyCell } from "./TurnkeyCell";
import { TurnkeyDialogContext } from "./TurnkeyDialog";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
  nowrap: {
    whiteSpace: "nowrap",
  },
});

/**
 * Create the row for the turnkey
 *
 * @param vyper
 * @param builds
 * @param onChange
 * @param showSameness
 * @returns {JSX.Element}
 * @constructor
 */
export const TurnkeyRow = ({ vyper, builds, onChange, showSameness }) => {
  const { open: openDialog } = useContext(TurnkeyDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeTurnkey = (value, build) => {
    return buildDao
      .changeTurnkey(vyper.vyperNumber, build.buildNumber, value)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    openDialog({
      value: build?.turnkey?.value,
      onSave: (value) => handleChangeTurnkey(value, build),
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessTurnkey(builds),
      })}
      hover
    >
      <RowPrefix title="Turnkey" help="turnkey" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <TurnkeyCell vyper={vyper} build={build} onClick={handleClick} />
        </TableCell>
      ))}
    </TableRow>
  );
};
