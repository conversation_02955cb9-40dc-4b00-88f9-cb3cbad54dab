import { Switch } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { PimValidation } from "./PimValidation";

/**
 * @typedef UserTime
 * @property {User} user
 * @property {string} when
 */

/**
 * @typedef PimCell~PimSetupNeeded
 * @property {user} user
 * @property {string} when
 * @property {boolean} state
 */

/**
 * @typedef PimCell~PimSetupValidated
 * @property {user} user
 * @property {string} when
 * @property {boolean} state
 */

/**
 * @typedef PimCell~PimSetup
 * @property {PimCell~PimSetupNeeded} needed -
 * @property {PimCell~PimSetupValidated} validated -
 */

/**
 * @callback PimCell~onGetPimSetup
 * @param {*} item - the item
 * @return {PimCell~PimSetup} pimSetup
 */

/**
 * @callback PimCell~onChangeNeeded
 * @param {*} item - the item
 * @param {boolean} state - the new pim setup needed state.
 * @return {PimCell~PimSetup} pimSetup
 */

/**
 * @callback PimCell~onChangeValidated
 * @param {*} item - the item
 * @param {boolean} state - the new pim setup validated state.
 * @return {PimCell~PimSetup} pimSetup
 */

/**
 * Display the PIM Setup cell for a single item
 * @param {*} item - The item to display
 * @param {PimCell~onGetPimSetup} onGetPimSetup - callback to get the item's pim setup
 * @param {PimCell~onChangeNeeded} onChangeNeeded - user changed the needed state
 * @param {PimCell~onChangeValidated} onChangeValidated - user changed the validated state
 * @return {JSX.Element}
 * @constructor
 */
export function PimCell({
  item,
  onGetPimSetup,
  onChangeNeeded,
  onChangeValidated,
}) {
  const pimSetup = onGetPimSetup(item);

  function handleChangeNeeded(e) {
    onChangeNeeded(item, e.target.checked);
  }

  return (
    <DataCell source={null}>
      <div>
        No{" "}
        <Switch checked={pimSetup.needed.state} onChange={handleChangeNeeded} />{" "}
        Yes
      </div>
      {pimSetup.needed.state && (
        <PimValidation
          item={item}
          pimSetup={pimSetup}
          onChangeValidated={onChangeValidated}
        />
      )}
    </DataCell>
  );
}

PimCell.propTypes = {
  item: PropTypes.any.isRequired,
  onGetPimSetup: PropTypes.func.isRequired,
  onChangeNeeded: PropTypes.func.isRequired,
  onChangeValidated: PropTypes.func.isRequired,
};
