import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Tooltip from "@material-ui/core/Tooltip";
import withStyles from "@material-ui/core/styles/withStyles";

export const Required = ({ required = true }) => {
  const HtmlTooltip = withStyles((theme) => ({
    tooltip: {
      backgroundColor: "hsla(0, 73%, 94%, 1)",
      color: "hsla(0, 100%, 17%, 1)",
      maxWidth: 220,
      fontSize: theme.typography.pxToRem(12),
      border: "1px solid black",
      borderRadius: 12,
    },
  }))(Tooltip);

  const styles = makeStyles(() => ({
    required: {
      color: "red",
      fontSize: "1.5rem",
      verticalAlign: "bottom",
      cursor: "default",
    },
  }));

  const classes = styles();

  return required ? (
    <HtmlTooltip title="Required" placement="top" arrow>
      <span className={classes.required}>*</span>
    </HtmlTooltip>
  ) : null;
};
