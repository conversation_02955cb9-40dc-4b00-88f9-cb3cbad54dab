import { Chip } from "@material-ui/core";
import PersonAddIcon from "@material-ui/icons/PersonAdd";
import PropTypes from "prop-types";
import React from "react";

/**
 * Show the add owner chip
 *
 * @param canEdit
 * @param onAdd
 * @returns {JSX.Element}
 * @function
 */
export const AddOwner = ({ canEdit, onAdd }) => {
  if (!canEdit) return null;

  return (
    <Chip
      variant="outlined"
      color="secondary"
      size="small"
      icon={<PersonAddIcon />}
      label="Add"
      onClick={onAdd}
    />
  );
};

AddOwner.propTypes = {
  canEdit: PropTypes.bool.isRequired,
  onAdd: PropTypes.func.isRequired,
};
