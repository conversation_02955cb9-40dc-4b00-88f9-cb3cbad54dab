import React, { useContext, useEffect, useRef, useState } from "react";
import { DataGrid } from "../../../component/universal";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { logError } from "../../functions/logError";
import { FlowPackConfigMapDao } from "../../../dao/FlowPackConfigMapDao";
import { DeviceFlowMapDao } from "../../../dao/DeviceFlowMapDao";
import {
  AlertDialogErrorHandler,
  NoopLoadingHandler,
} from "../../../component/fetch/DaoBase";
import { Checkbox } from "@material-ui/core";
import { FetchContext } from "../../../component/fetch/VyperFetch";

export function FlowPackConfig() {
  const deviceFlows = useRef([]);
  const defaultPackConfigs = useRef([]);
  const { vget } = useContext(FetchContext);
  const [gridData, setGridData] = useState([]);
  const [flowPackConfigData, setFlowPackConfigData] = useState([]);
  const [dynamicColDef, setDynamicColDef] = useState([]);
  const { open: openAlert } = useContext(AlertDialogContext);

  const flowPackConfigMapDao = new FlowPackConfigMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new NoopLoadingHandler(),
  });

  const deviveFlowMapDao = new DeviceFlowMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new NoopLoadingHandler(),
  });

  useEffect(() => {
    getDeviceFlowData().catch(logError);
  }, []);

  useEffect(() => {
    getDefaultPackConfigs().catch(logError);
  }, [deviceFlows]);

  useEffect(() => {
    return flowPackConfigMapDao
      .list(0, 1000)
      .then((json) => {
        const finalFlowRows = json.map((flow) => flow.id); // flow id combination
        // Prepare mapping
        const tempGridData = defaultPackConfigs.current.map((rowData) => {
          const currRowData = {
            id: rowData.id,
            flowRow: rowData.value,
          };
          deviceFlows.current.map((flow) => {
            currRowData['"flow' + flow.id + '"'] =
              finalFlowRows.filter(
                (flowConfigId) =>
                  flowConfigId.flowId === flow.id &&
                  flowConfigId.packConfigId === rowData.id
              ).length > 0;
          });
          return currRowData;
        });
        setFlowPackConfigData(finalFlowRows);
        console.log("Setting grid data from > to ", gridData, tempGridData);
        setGridData(tempGridData);
      })
      .catch(logError);
  }, [defaultPackConfigs]);

  // Get default rows
  const getDefaultPackConfigs = () => {
    return vget("/vyper/v1/packconfig/search?size=1000", (json) => {
      defaultPackConfigs.current = json.page.content;
    }).catch(logError);
  };

  /**
   *
   * @returns Get flow data
   */
  const getDeviceFlowData = () => {
    return deviveFlowMapDao
      .list(0, 1000)
      .then((json) => {
        deviceFlows.current = json;
        const flowColumns = json.map((eachFlow) => {
          const currentFlowId = '"flow' + eachFlow.id + '"';
          return {
            field: currentFlowId,
            title: eachFlow.flowName,
            render: (rowData) => {
              return (
                <div>
                  <Checkbox
                    color="secondary"
                    checked={rowData[currentFlowId] === true}
                    onChange={(event) => saveSelection(rowData, eachFlow.id)}
                  />
                </div>
              );
            },
            cellStyle: {
              maxWidth: 5,
            },
          };
        });

        setDynamicColDef([
          {
            field: "id",
            title: "id",
            hidden: true,
          },
          {
            field: "flowRow",
            title: "Pack Config",
            cellStyle: {
              minWidth: 20,
            },
          },
          ...flowColumns,
        ]);
      })
      .catch(logError);
  };

  const refreshGrid = (newRowData, flowId) => {
    // Prepare mapping
    const tempGridData = defaultPackConfigs.current.map((rowData) => {
      const currRowData = {
        id: rowData.id,
        flowRow: rowData.value,
      };
      deviceFlows.current.map((flow) => {
        currRowData['"flow' + flow.id + '"'] =
          flowPackConfigData.filter(
            (flowConfigId) =>
              flowConfigId.flowId === flow.id &&
              flowConfigId.opnId === rowData.id
          ).length > 0;
      });
      return currRowData;
    });

    console.log({ tempGridData });
    setGridData(tempGridData);

    //setGridData( gridData.map( gridRow => gridRow.id === rowData.id ? newRowData: gridRow ));
  };

  const saveSelection = (rowData, flowId) => {
    return new Promise((resolve, reject) => {
      // true => create
      if (!rowData['"flow' + flowId + '"']) {
        flowPackConfigMapDao
          .create({ flowId: flowId, packConfigId: rowData.id })
          .then((flowPackConfigMap) => {
            setFlowPackConfigData([
              flowPackConfigMap.id,
              ...flowPackConfigData,
            ]);
            resolve();
          })
          .catch(() => reject());
      } else {
        //false ==> delete
        flowPackConfigMapDao
          .delete(flowId, rowData.id)
          .then(() => {
            setFlowPackConfigData(
              flowPackConfigData.filter(
                (flowPackConfig) =>
                  flowPackConfig.id.flowId != flowId &&
                  flowPackConfig.id.packConfigId != rowData.id
              )
            );
            resolve();
          })
          .catch(() => reject());
      }
      rowData['"flow' + flowId + '"'] = !rowData['"flow' + flowId + '"'];
      //refreshGrid(rowData, flowId);
    });
  };

  return (
    <div>
      <DataGrid
        title="Flow PackConfig Map"
        columns={dynamicColDef}
        data={gridData}
        editable={true}
        options={{
          search: false,
          pageSize: 20,
          pageSizeOptions: [5, 10, 20, 50, 100, 200],
          cellStyle: {
            minWidth: 20,
            fontSize: "14px",
            height: 10,
          },
          maxBodyHeight: "60%",
        }}
      />
    </div>
  );
}
