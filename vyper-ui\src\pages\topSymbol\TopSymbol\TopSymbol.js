import React from "react";
import { DataGrid } from "../../../component/universal";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  root: {},
});

export const TopSymbol = () => {
  const columns = [
    { field: "facilityAt", title: "Facility A/T", editable: "never" },
    { field: "pkgGroup", title: "Package Group", editable: "never" },
    { field: "pkg", title: "Package", editable: "never" },
    { field: "pin", title: "Pins", editable: "never" },
    { field: "componentValue", title: "Symbol Name" },
  ];

  // post the new symbol name to the server
  const handleUpdate = (newValue, oldValue, rowData, columnDef) => {
    let data = {
      method: "POST",
      credentials: "include",
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Pragma: "no-cache",
        "Cache-Control": "no-cache",
      },
      body: JSON.stringify({
        ...rowData,
        [columnDef.field]: newValue,
      }),
    };

    return new Promise((resolve, reject) => {
      return fetch(`/vyper/v1/topsymbol/${rowData.id}`, data).then(
        (response) => {
          if (response.ok) {
            rowData[columnDef.field] = newValue;
            return resolve();
          } else {
            return reject();
          }
        }
      );
    });
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Topside Symbols"
        url={`/vyper/v1/topsymbol/search`}
        actions={[]}
        columns={columns}
        pageable
        pageSize={20}
        editable={true}
        cellEditable={{
          onCellEditApproved: handleUpdate,
        }}
      />
    </div>
  );
};
