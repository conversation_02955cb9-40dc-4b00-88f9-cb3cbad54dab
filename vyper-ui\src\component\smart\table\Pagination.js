import React from "react";
import { Button, TablePagination } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  pagination: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
  },
});

/**
 *
 * @param result
 * @param onChangePage
 * @param onChangeRowsPerPage
 * @returns {*}
 * @constructor
 */
export const Pagination = ({
  result,
  onChangePage,
  onChangeRowsPerPage,
  onClearFilters,
}) => {
  const classes = useStyles();

  return (
    <div className={classes.pagination}>
      <div>
        <Button
          type="button"
          color="secondary"
          variant="outlined"
          size="small"
          onClick={onClearFilters}
        >
          Clear Filters
        </Button>
      </div>
      <TablePagination
        rowsPerPageOptions={[5, 10, 20, 50, 100]}
        component="div"
        count={result.totalElements || 0}
        rowsPerPage={result.size || 10}
        page={result.number || 0}
        onChangePage={onChangePage}
        onChangeRowsPerPage={onChangeRowsPerPage}
      />
    </div>
  );
};
