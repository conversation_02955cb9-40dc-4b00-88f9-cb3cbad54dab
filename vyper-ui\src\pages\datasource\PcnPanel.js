import { makeStyles } from "@material-ui/core/styles";
import React from "react";
import { DataGrid } from "../../component/universal";

const useMyStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(3),
  },
}));

export const PcnPanel = () => {
  const columns = [
    { title: "PCN ID", field: "pcnId", hidden: true },
    { title: "PCN Number", field: "pcnNumber" },
    { title: "Project Name", field: "projectName" },
    { title: "PCN Title", field: "pcnTitle" },
    { title: "Group Name", field: "groupName" },
  ];

  const classes = useMyStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Changelink PCNs"
        url={`/vyper/v1/changelink/pcn/search`}
        actions={[]}
        columns={columns}
        pageable
        pageSize={20}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
      />
    </div>
  );
};
