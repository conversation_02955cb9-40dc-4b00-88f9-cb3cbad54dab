import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../../mockup/RowPrefix";
import { PimCell } from "./PimCell";

/**
 * Display a row of PIM Setup Required cells.
 * @param {*[]} items - The items to display
 * @param {PimCell~onGetPimSetup} onGetPimSetup - callback to get the item's pim setup
 * @param {PimCell~onChangeNeeded} onChangeNeeded - user changed the needed state
 * @param {PimCell~onChangeValidated} onChangeValidated - user changed the validated state
 * @return {JSX.Element}
 * @constructor
 */
export function PimRow({
  items,
  onGetPimSetup,
  onChangeNeeded,
  onChangeValidated,
}) {
  return (
    <TableRow hover>
      <RowPrefix help="pimsetup" title="Is PIM setup Required" required />
      {items.map((item, n) => (
        <TableCell key={n}>
          <PimCell
            item={item}
            onGetPimSetup={onGetPimSetup}
            onChangeNeeded={onChangeNeeded}
            onChangeValidated={onChangeValidated}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

PimRow.propTypes = {
  items: PropTypes.array.isRequired,
  onGetPimSetup: PropTypes.func.isRequired,
  onChangeNeeded: PropTypes.func.isRequired,
  onChangeValidated: PropTypes.func.isRequired,
};
