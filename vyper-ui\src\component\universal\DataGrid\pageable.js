import { fetchGet, uriEncode } from "../../utils";

export const generateUrl = (url, params) => {
  let base = url;

  // If params, we must preserve.
  let at = url.indexOf("?");
  if (at > 0) {
    base = url.substring(0, at);
  }

  return `${base}?${generateUrlQuery(url, params)}`;
};

export const generateUrlQuery = (url, params) => {
  // console.debug("query params => " + JSON.stringify(params));
  /**
   * === Query params: ===
   *
   * filters:        [
   *                   {
   *                     "column":{ "title": $columnTitle, "field": $columnField, ... }
   *                     "operator": $filterOperator,
   *                     "value": $filterValue, ...
   *                   }, ...
   *                 ]
   *
   *                 Filters the column with title $columnTitle and field id $columnField
   *                 using operator $filterOperator and value $filterValue.
   *                 NOTES: $filterOperator currently only supports `like`
   *                 $columnTitle: [.]*
   *                 $columnField: [.]*
   *                 $filterOperator: (=|~|...) defaults to `=` #TODO: discover more options
   *                 - translates to $filterOperatorWord:
   *                   `=` -> `equals`
   *                   `~` -> `like`
   *                   ... #TODO: discover more options
   *                 - defaults to `like`
   *                 $filterValue: [.]*
   *                 - query as `&filter=$filterOperatorWord,$columnField,$filterValue`
   *
   * orderBy:        {
   *                   "title": $columnTitle,
   *                   "field": $columnField,
   *                   "tableData": {"groupSort": $sortDirection, ...}
   *                 }
   *
   *                 Sorts the column with title $columnTitle and field id $columnField
   *                 with sort direction $sortDirection.
   *                 $columnTitle: [.]*
   *                 $columnField: [.]*
   *                 $sortDirection: ('asc'|'desc')
   *                 - query as `&sort=$columnField,$sortDirection`
   *
   * orderDirection: (null|'asc'|'desc') | Direction of the sort (for one sorted column only).
   *                 - not used (instead we're using the value in $orderBy)
   *
   * page:           [0-9]+ | The nth page to fetch (0-index)
   *                 - query as `&page=$page`
   *
   * pageSize:       [0-9]+ | Number of records per page.
   *                 - query as `&size=$pageSize`
   *
   * search:         [.]* | General filter from the search box.
   *                 - currently not supported
   *
   * totalCount:     [0-9]+ | Total number of records.
   *                 - not used
   *
   */

  // Preserve the original query parameters from base url
  let oldQuery = null;
  const at = url.indexOf("?");
  if (at > 0) {
    oldQuery = url.substring(at + 1);
  }

  let query = {};
  query.page = params.page;
  query.size = params.pageSize;

  query = Object.keys(query)
    .map((key) => uriEncode(key) + "=" + uriEncode(query[key]))
    .join("&")
    .replace(/%20/g, "+");

  // Add sorts
  query = [query];
  if (params.orderBy) {
    query.push(
      `sort=${uriEncode(params.orderBy.field)},${uriEncode(
        params.orderDirection
      )}`
    );
  }
  query = query.join("&");

  // Add filters
  query = [query];
  if (params.filters && params.filters.length > 0) {
    query.push(
      params.filters
        .reduce((sum, e, i) => {
          sum.push(
            // `filter=like,${uriEncode(e.column.field)},${uriEncode(e.value)}` //  for rex api
            `filter=${uriEncode(e.column.field + "|contains|" + e.value)}` // for vyper api
          );
          return sum;
        }, [])
        .join("&")
    );
  }

  // IE has issues matching response to the exact same request query
  // To fix that, we are adding a random number to the query as differentiator
  query.push(`rn=${Math.floor(Math.random() * 1000)}`);

  // Add in the original query params
  query = query.join("&");
  if (oldQuery !== null) {
    query = [oldQuery, query].join("&");
  }

  // console.debug('query => '+query)
  return query;
};

export const refreshData = (url, query, resolve, reject, pageable = false) => {
  const fetchUrl = pageable ? generateUrl(url, query) : url;
  fetchGet(
    fetchUrl,
    (httpStatus, result) => {
      if (!!result && result !== []) {
        if (pageable) {
          // resolve assuming pageable format response
          resolve({
            data: result.content,
            page: result.number,
            totalCount: result.totalElements,
          });
        } else {
          // resolve assuming a plain list response
          resolve({
            data: result,
            page: 0,
            totalCount: result.length,
          });
        }
      } else {
        // resolve to an empty data list
        resolve({
          data: [],
          page: 0,
          totalCount: 0,
        });
      }
    },
    (error) => reject(error)
  );
};
