import PropTypes from "prop-types";
import React from "react";
import { TravelerBody } from "./TravelerBody";
import { TravelerHeaders } from "./TravelerHeaders";
import { TravelerTitle } from "./TravelerTitle";

/**
 * Returns the traveler.
 * @param {Traveler | null} traveler - The traveler to display
 * @param {string} number - the traveler's number. Ex: the buildNumber, praNumber or vscnNumber.
 * @returns {JSX.Element|null}
 * @constructor
 */
export function Traveler({ traveler, number }) {
  if (traveler == null || Object.keys(traveler.header || {}).length === 0) {
    return null;
  }
  return (
    <pre>
      <TravelerTitle number={number} />
      <br />
      <TravelerHeaders header={traveler.header} />
      <br />
      <TravelerBody traveler={traveler} />
    </pre>
  );
}

Traveler.propTypes = {
  traveler: PropTypes.object,
  number: PropTypes.string.isRequired,
};
