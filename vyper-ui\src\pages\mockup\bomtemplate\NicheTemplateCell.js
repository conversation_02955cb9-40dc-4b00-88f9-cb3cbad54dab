import React from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import config from "src/buildEnvironment";
import makeStyles from "@material-ui/core/styles/makeStyles";
import {
  hasFacility,
  hasMaterial,
  hasPackageNiche,
} from "src/pages/vyper/FormStatus";
import { convertBuildNumbertoVyperNumber } from "src/component/helper/convertBuildNumbertoVyperNumber";
import { DataCell } from "src/component/datacell/DataCell";
import { HasBomTemplate } from "src/pages/mockup/bomtemplate/HasBomTemplate";
import { Development } from "src/pages/mockup/bomtemplate/Development";
import { BomTemplateEmailLink } from "src/pages/mockup/bomtemplate/BomTemplateEmailLink";

const { externalUse } = config;

const useStyles = makeStyles(() => ({
  link: {
    textDecoration: "none",
    color: "rgb(85, 26, 139)",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
      color: "red",
    },
  },
}));

/**
 * description
 * @param {object} vyper
 * @param {object} build
 * @return {JSX.Element}
 * @constructor
 */
export const NicheTemplateCell = ({ vyper, build }) => {
  if (!hasMaterial(build) || !hasFacility(build) || !hasPackageNiche(build))
    return null;

  const bomTemplate = build.bomTemplate;

  const classes = useStyles();

  const getBomTemplate = function () {
    if (bomTemplate.object.name == null) {
      return <div>no Bill of Process Template available</div>;
    } else if (externalUse) {
      return (
        <div>
          {bomTemplate.object.name.split(",").map((name) => (
            <div key={name}>{name && name.replace("BOM", "BOP")}</div>
          ))}
        </div>
      );
    }

    return (
      <Link
        className={classes.link}
        to={`/projects/${vyper.vyperNumber}/builds/${build.buildNumber}/bomtemplate`}
      >
        {bomTemplate.object.name.split(",").map((name) => (
          <div key={name}>{name && name.replace("BOM", "BOP")}</div>
        ))}
      </Link>
    );
  };

  const buildNumber = build.buildNumber;
  const vyperNumber = convertBuildNumbertoVyperNumber(buildNumber);

  return (
    <DataCell source={bomTemplate.source}>
      <div>Bill of Process Template</div>
      <HasBomTemplate bomTemplate={bomTemplate} build={build} />
      <Development bomTemplate={bomTemplate} />
      <div>{getBomTemplate()}</div>
      <BomTemplateEmailLink
        vyperNumber={vyperNumber}
        buildNumber={buildNumber}
        bomTemplate={bomTemplate}
      />
    </DataCell>
  );
};

NicheTemplateCell.propTypes = {
  vyper: PropTypes.object.isRequired,
  build: PropTypes.object.isRequired,
};
