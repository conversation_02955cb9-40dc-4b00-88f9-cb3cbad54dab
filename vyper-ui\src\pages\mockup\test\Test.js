import React, { useContext } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import PublishIcon from "@material-ui/icons/Publish";
import GetAppIcon from "@material-ui/icons/GetApp";
import RemoveIcon from "@material-ui/icons/Remove";
import VisibilityIcon from "@material-ui/icons/Visibility";
import { makeStyles } from "@material-ui/core/styles";
import Tooltip from "@material-ui/core/Tooltip";
import { Link } from "react-router-dom";
import envGlobal from "../../../buildEnvironment";
import {
  hasBomTemplate,
  hasFacility,
  hasMaterial,
  hasPackageNiche,
  existsInFlow,
} from "../../vyper/FormStatus";
import { TestButton } from "./TestButton";
import IconButton from "@material-ui/core/IconButton";
import { HelperContext } from "src/component/helper/Helpers";

const useStyles = makeStyles(() => ({
  icon: {
    marginRight: ".5rem",
    cursor: "pointer",
  },
}));

export const Test = ({ vyper, build, onUpload, onView, onDelete }) => {
  const classes = useStyles();
  const { canEditTest } = useContext(HelperContext);
  const canEdit = canEditTest(vyper, build);

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !hasPackageNiche(build) ||
    !hasBomTemplate(build) ||
    !existsInFlow(build, "Test")
  )
    return null;

  const hasTest = build.test?.content !== null;

  let link;
  if (envGlobal.basePath === "/") {
    link = `/vyper/v1/vyper/test/download/${vyper.vyperNumber}/${build.buildNumber}`;
  } else {
    // basePath === "/vyper" like on simba-dev and -production
    link = `/v1/vyper/test/download/${vyper.vyperNumber}/${build.buildNumber}`;
  }

  if (build.turnkey.value === "TKY") {
    return (
      <DataCell source={build.test.source}>
        <TestButton
          tooltipTitle="Upload"
          enabled={canEdit}
          onClick={onUpload}
          icon={PublishIcon}
        />

        <TestButton
          tooltipTitle="View"
          enabled={hasTest}
          onClick={onView}
          icon={VisibilityIcon}
        />

        <Tooltip title="Download" placement="top" arrow>
          <span>
            <IconButton
              className={classes.icon}
              fontSize="small"
              color="primary"
              disabled={!hasTest}
            >
              <Link to={link} target="_blank">
                <GetAppIcon color={hasTest ? "primary" : "disabled"} />
              </Link>
            </IconButton>
          </span>
        </Tooltip>

        <TestButton
          tooltipTitle="Remove"
          enabled={hasTest && canEdit}
          onClick={onDelete}
          icon={RemoveIcon}
        />
      </DataCell>
    );
  } else {
    return <DataCell source={null}>N/A</DataCell>;
  }
};
