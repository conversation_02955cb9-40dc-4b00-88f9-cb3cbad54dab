import React from "react";
import Dialog from "@material-ui/core/Dialog";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { DataGrid } from "../universal";
import { gridActionSelectRecord } from "../universal/DataGrid/gridActions";
import { Button, DialogActions } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  paper: {
    minWidth: "1000px",
  },
  root: {
    marginBottom: theme.spacing(3),
  },
}));

export const TableDialog = ({
  open,
  handleClose,
  handleSelect,
  data,
  pkgGroup,
}) => {
  const classes = useStyles();

  const columns = [
    {
      title: "Package Niche",
      field: "pkgNiche",
      defaultFilter: `${pkgGroup}`,
      defaultSort: "asc",
    },
    {
      title: "Flow Template Name",
      field: "flowTemplateName",
    },
    {
      title: "Flow Requester",
      field: "flowRequester",
    },
    {
      title: "Flow Created Date",
      field: "flowCreatedDate",
    },
    {
      title: "Revision",
      field: "revision",
    },
  ];

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        classes={{ paper: classes.paper }}
        maxWidth="xl"
      >
        <DataGrid
          title="Select Supported Package Niches"
          data={data}
          columns={columns}
          actions={[gridActionSelectRecord(handleSelect)]}
          options={{
            search: false,
            pageSize: 10,
            pageSizeOptions: [5, 10, 20, 50],
          }}
        />
        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
