import React, { useRef, useState } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormLabel,
  RadioGroup,
  Radio,
} from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { AgGridReact } from "ag-grid-react"; // the AG Grid React Component
import "ag-grid-community/dist/styles/ag-grid.css"; // Core grid CSS, always needed
import "ag-grid-community/dist/styles/ag-theme-alpine.css"; // Optional theme CSS
import { exportGridAsExcel } from "/src/lib/TiServerAgGrid";
import useSnackbar from "/src/hooks/Snackbar";

const useStyles = makeStyles((theme) => ({
  deviceGridStyle: {
    "& .ag-header-row": {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
    },
  },
}));

export const SpecTemplatePopup = ({
  vbuild,
  specConfig,
  open,
  handleCloseDialog,
  handleAddRows,
}) => {
  const classes = useStyles();
  const gridRef = useRef();
  const { enqueueErrorSnackbar } = useSnackbar();
  const [flagOperationMode, setFlagOperationMode] = useState(false);
  const defaultGridColDef = { resizable: true, enableCellTextSelection: true };


  const templateColumns = [
    { headerName: "Material", field: "material", hide: true,  },
    { headerName: "Old Material", field: "oldMaterial", hide: true },
    { headerName: "Spec device", field: "specDevice", hide: true },
    { headerName: "Sub flow *", field: "subFlowType", width: 120 },
    { headerName: "Operation", field: "name", width: 120 },
    { headerName: "Comp Name *", field: "componentName", width: 200 },
    { headerName: "Comp Value *", field: "componentValue" },
    { headerName: "Occurrence", field: "priority", width: 100 },
    { headerName: "Attr name", field: "attributeName" },
    { headerName: "Attr value", field: "attributeValue" },
  ];

  /**
   * Toggle full operation mode vs all no operation mode ( leave operation as empty)
   */
  const toggleMode = () => {
    setFlagOperationMode(!flagOperationMode);
  };

  /**
   * Downloads the excel format of allowed changes 
   * with the reference traveler data as sample 
   * @returns Excel file
   */
  const downloadReferenceSample = () => {
    const api = gridRef.current.api;
    const columnApi = gridRef.current.columnApi;

    if (!api && !columnApi) {
      return;
    }

    exportGridAsExcel(api, columnApi, {
      fileName: "Sample_ATSS_Mass_Upload.xlsx",
      columnFilter: (col) => {return true}
    });
  };

  /**
   * Array to appended to spec data sheet
   * @param {*} rows 
   * @returns 
   */
  const getRowsToAdd = (rows) =>{
        return rows.map( (row,index)  => {
            return {
                id: "",
                material: index == 0? "Enter SAP Material" : "",
                oldMaterial: "",
                specDevice: "",
                flowType: row.subFlowType,
                operationName: row.name || null,
                componentName: row.componentName,
                componentValue: row.componentValue,
                componentOccurrance: row.priority || null,
                attributeName: row.attributeName || null,
                attributeValue: row.attributeValue || null
            }
        })
  }

  const addSelectedToSheet = () =>{
    const selectedRecords = gridRef.current.api.getSelectedRows();
    if (selectedRecords.length === 0) {
      enqueueErrorSnackbar("Please select at least one line to add or Add All !");
      return;
    }

    // If handle rows defined
    if ( handleAddRows ){
        handleAddRows(getRowsToAdd(selectedRecords));
        handleCloseDialog();
    }    

  }

  const addToSheet = () =>{
    let newRecords = [];
    gridRef.current.api.forEachNode( node => newRecords = newRecords.concat(node.data));
    if ( handleAddRows ){
        handleAddRows( getRowsToAdd(newRecords));
        handleCloseDialog();
    }
  }

  /**
   *
   * @param {*} comp
   */
  const getConfigComponentAttributes = (comp) => {
    return {
      name: comp.name,
      value: comp.value,
      priority: comp.priority,
      attributes: comp.attributes.filter((attr) =>
        specConfig.some(
          (config) =>
            config.componentName === comp.name &&
            attr.name === config.attributeName
        )
      ),
    };
  };

  const filterOperationComponents = (operation) => {
    const matchingComponents = operation.components
      .filter((comp) =>
        specConfig.some(
          (config) =>
            comp.name === config.componentName ||
            comp.name === config.attributeName
        )
      )
      .map((comp) => getConfigComponentAttributes(comp));

    return {
      subflowType: operation.subflowType,
      name: "",
      components: matchingComponents,
    };
  };

  /**
   *
   * @param {*} operation
   */
  const mapComponentValues = (operation) => {
    const matchingComponents = operation.components
      .filter((comp) =>
        specConfig.some(
          (config) =>
            comp.name === config.componentName ||
            comp.name === config.attributeName
        )
      )
      .map((comp) => getConfigComponentAttributes(comp));

    return {
      subflowType: operation.subflowType,
      name: operation.name,
      components: matchingComponents,
    };
  };

  /**
   * Return an array of components in below format
   * Sub Flow -- Comp name -- Comp Value -- Attribute name -- Attribute Value
   * @param {*} opr
   * @returns
   */
  const getComponentArray = (opr) => {
    let oprComponents = opr.components.flatMap((comp) => {
      return {
        subFlowType: opr.subflowType,
        name: opr.name,
        componentName: comp.name,
        componentValue: comp.value,
        priority: comp.priority,
        attributes: comp.attributes,
      };
    });

    // Add cust , ecat as attributes to Topside Symbol
    if (opr.name.includes("Symbol")) {
      const symbolComponent = oprComponents.filter((oprComponent) =>
        oprComponent.componentName.includes("Symbol")
      );
      const embedComponents = oprComponents.filter(
        (oprComponent) =>
          oprComponent.componentName.includes("CUST") ||
          oprComponent.componentName.includes("ECAT")
      );
      if (
        symbolComponent != undefined &&
        symbolComponent.length > 0 &&
        embedComponents != undefined
      ) {
        embedComponents.forEach((embComponent) => {
          symbolComponent[0].attributes.push({
            name: embComponent.componentName,
            value: embComponent.componentValue,
          });
        });
        // Add only Topside Symbol
        oprComponents = [];
        oprComponents.push(symbolComponent[0]);
      }
    }

    return oprComponents.flatMap((oprComponent) => {
      if (oprComponent.attributes.length > 0) {
        return oprComponent.attributes.flatMap((compAttr) => {
          return {
            subFlowType: oprComponent.subFlowType,
            name: oprComponent.name,
            componentName: oprComponent.componentName,
            componentValue: oprComponent.componentValue,
            priority: oprComponent.priority,
            attributeName: compAttr.name,
            attributeValue: compAttr.value,
          };
        });
      } else {
        return oprComponent;
      }
    });
  };

  // Find matching operations which has components from spec rule config
  const referenceComponents = vbuild.traveler.operations
    .map((opr) => mapComponentValues(opr))
    .flatMap((opr) => getComponentArray(opr))
    .sort((opr1, opr2) => {
      if (opr1.subFlowType < opr2.subFlowType) return -1;
      else if (opr1.subFlowType > opr2.subFlowType) return 1;
      else if (opr1.componentName < opr2.componentName) return -1;
      else if (opr1.componentName > opr2.componentName) return 1;
      else if (opr1.name < opr2.name) return -1;
      else if (opr1.name > opr2.name) return 1;
      else return 0;
    });
  // Find only components
  const referenceComponentAllOperations = referenceComponents
    .map((opr) => {
      return {
        subFlowType: opr.subFlowType,
        name: "",
        componentName: opr.componentName,
        componentValue: opr.componentValue,
        priority: null,
        attributeName: opr.attributeName,
        attributeValue: opr.attributeValue,
      };
    })
    .filter(
      (comp, index, allComp) =>
        allComp.findIndex((test) => {
          return (
            test.subFlowType === comp.subFlowType &&
            test.componentName === comp.componentName &&
            test.componentValue === comp.componentValue &&
            test.attributeName === comp.attributeName
          );
        }) === index
    );

  const modeData = flagOperationMode
    ? referenceComponents
    : referenceComponentAllOperations;

  return (
    <Dialog open={open} fullWidth maxWidth="lg" onClose={handleCloseDialog}>
      <DialogTitle>
        Allowed changes with Reference device sample data
      </DialogTitle>

      <DialogContent>
        <div
          style={{ height: "600px" }}
          className={`ag-theme-alpine ${classes.deviceGridStyle}`}
        >
          <FormControl component="fieldset">
            <RadioGroup
              row
              aria-label="mode"
              name="modeOperation"
              value={flagOperationMode}
              onChange={toggleMode}
            >
              <FormControlLabel
                value={false}
                control={<Radio />}
                label="All ( W/O) operation"
              />
              <FormControlLabel
                value={true}
                control={<Radio />}
                label="With Specific operation"
              />
            </RadioGroup>
          </FormControl>
          <AgGridReact
            ref={gridRef}
            defaultColDef={defaultGridColDef}
            columnDefs={templateColumns}
            rowData={modeData}
            enableCellTextSelection={true}
            enableRangeSelection={true}
            rowSelection={"multiple"}
          />
        </div>
      </DialogContent>

      <DialogActions>        
        <Button
          variant="text"
          color="primary"
          onClick={downloadReferenceSample}
        >
          Download
        </Button>
        <Button
          variant="text"
          color="primary"
          onClick={addSelectedToSheet}
        >
          Add selected
        </Button>
        <Button
          variant="text"
          color="primary"
          onClick={addToSheet}
        >
          Add All 
        </Button>
        <Button variant="outlined" color="primary" onClick={handleCloseDialog}>
          Close
        </Button>        
      </DialogActions>
    </Dialog>
  );
};
