import React, { useContext, useMemo } from "react";
import {
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@material-ui/core";
import { RejectionDialogContext } from "../../RejectionDialog";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";
import { ApprovalOperationContext } from "src/component/approvaloperation/ApprovalOperation";

const generateMessage = (missingCheck, messageType) => {
  return (
    <div>
      <div>You are unable to perform that action</div>
      <br />
      <List>
        {missingCheck.map((operation) => (
          <ListItem key={operation}>
            <ListItemIcon>
              <ArrowRightAltIcon color="primary" />
            </ListItemIcon>
            <ListItemText>
              The operation is not {messageType} - {operation}
            </ListItemText>
          </ListItem>
        ))}
      </List>
    </div>
  );
};

/**
 * return a array of operations that are required and not checked for the required groups
 * @param {*} validatedComponents
 * @param {String} group - Approval Group  Ex: VYPER_FMX_ASSY_BOND
 * @returns
 */
const determineUncheckedOperations = function (
  validatedComponents,
  group,
  findApprovalOperationByOperation
) {
  // loop through the components
  // filter out unchecked components
  // filter out components not approved by the group

  const operations = Object.entries(validatedComponents)
    .filter(([key, component]) => !component.checked)
    .filter(([key, component]) => {
      const ao = findApprovalOperationByOperation(component.parentOperation);

      if (ao == null) return false;
      return group.toLowerCase().includes(ao?.groupText?.toLowerCase());
    })
    .map(([key, component]) => component.parentOperation);

  return operations;
};

export const PraTaskSvcActions = (props) => {
  const {
    validatedComponents,
    userAssignments,
    buttonLabel,
    handleTaskButtonClick,
    pra,
  } = props;
  const { open: openAlert } = useContext(AlertDialogContext);

  const { showRejectionDialog } = useContext(RejectionDialogContext);
  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );

  const isStateBuReview = pra.state === "PRA_BU_REVIEW";
  const isCommentRequired = isStateBuReview ? "Required" : "Optional";

  /**
   * Returns true if the verifier passed
   * @param verifier
   * @returns {boolean}
   */
  const verifierPassed = (verifier) => {
    const allowsPartials = ["SOURCE_PGS", "SOURCE_PAVV_COMPONENT"];
    return (
      verifier.status === "FULLY_VERIFIED" ||
      (allowsPartials.includes(verifier.source) &&
        verifier.status === "PARTIALLY_VERIFIED")
    );
  };

  // get the list of unverified verifiers

  const unverified = useMemo(() => {
    return [
      ...new Set(
        pra.verifiers
          .filter((verifier) => !verifierPassed(verifier))
          .map((verifier) => verifier.name)
      ),
    ];
  }, [pra.verifiers]);

  const handleDialogSave = ([reason, comment, selectedGroup]) => {
    const missingChecked = determineUncheckedOperations(
      validatedComponents,
      selectedGroup,
      findApprovalOperationByOperation
    );

    if (buttonLabel === "Approve" && missingChecked.length > 0) {
      const message = generateMessage(missingChecked, "approved");
      openAlert({
        title: `Can't Perform Action: ${buttonLabel}`,
        message: message,
      });

      return;
    }
    handleTaskButtonClick(buttonLabel, comment, reason || "", selectedGroup);
  };

  const showApprovePrompt = () => {
    // Directly moving to PRA_APPROVED when state is in PRA_BU_REVIEW and owner approves.
    if (isStateBuReview && buttonLabel === "Approve") {
      if (unverified?.length > 0) {
        const message = generateMessage(unverified, "verified");
        openAlert({
          title: `Can't Perform Action: ${buttonLabel}`,
          message: message,
        });
        return;
      }
      handleTaskButtonClick(buttonLabel, "", "", "BU_APPROVERS");
      return;
    }

    showRejectionDialog({
      type: buttonLabel === "Rework" ? "select" : undefined,
      type2: "select",
      title: `PRA: ${buttonLabel}`,
      description: `(${isCommentRequired}) Type in reason for ${buttonLabel}:`,
      multiline: false,
      rows: 50,
      maxWidth: "sm",
      fullWidth: true,
      // options: values,     // reject reasons
      options2: userAssignments, // groups
      isStateBuReview,
      onSave: handleDialogSave,
    });
  };

  return (
    <Button
      variant="contained"
      color="primary"
      size="small"
      type="button"
      name="action"
      onClick={showApprovePrompt}
    >
      {buttonLabel}
    </Button>
  );
};

export default PraTaskSvcActions;
