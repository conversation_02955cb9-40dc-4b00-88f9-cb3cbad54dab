import React from "react";
import { IconButton, makeStyles } from "@material-ui/core";
import HelpIcon from "@material-ui/icons/Help";

const useStyles = makeStyles({
  bar: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
});

export const VscnPageTitle = ({ vscn, title }) => {
  const classes = useStyles();

  return (
    <h2 className={classes.bar}>
      <div>{`${vscn?.vscnNumber} [${vscn?.state}] - ${title}`}</div>
    </h2>
  );
};
