import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React from "react";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessPackConfig } from "../../../component/sameness/sameness";
import { PackConfigCell } from "./PackConfigCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const PackConfigRow = ({ vyper, builds, onChange, showSameness }) => {
  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessPackConfig(builds),
      })}
      hover
    >
      <RowPrefix help="packConfig" title="Pack Config" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <PackConfigCell vyper={vyper} build={build} onChange={onChange} />
        </TableCell>
      ))}
    </TableRow>
  );
};
