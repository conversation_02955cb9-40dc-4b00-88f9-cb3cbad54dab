import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  DialogActions,
  DialogContent,
  DialogTitle,
  makeStyles,
} from "@material-ui/core";
import { SymbolLocation } from "src/component/symbol/components/SymbolLocation";
import { SymbolFilter } from "src/component/symbol/components/SymbolFilter";
import { SymbolName } from "src/component/symbol/components/SymbolName";
import { SymbolEcat } from "src/component/symbol/components/SymbolEcat";
import { SymbolCusts } from "src/component/symbol/components/SymbolCusts";
import { useFilteredSymbols } from "src/component/symbol/hooks/useFilteredSymbols";
import { SymbolPicture } from "src/component/symbol/components/SymbolPicture";
import { findSymbolByValue } from "src/component/symbol/api/symbolsApi";
import { logError } from "src/pages/functions/logError";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Grid from "@material-ui/core/Grid";
import { custx } from "src/component/symbol/custx";
import { useCustLength } from "src/component/symbol/hooks/useCustLength";
import Instructions from "src/component/instructions/Instructions";

const useStyles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    padding: theme.spacing(1),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "#ffffff",
  },
  autocomplete: {
    minWidth: "13rem",
  },
  dialog: {
    padding: "3rem",
    margin: "3rem",
  },
  formControl: {
    paddingRight: "1rem",
  },
  bottom: {
    marginBottom: "1rem",
  },
}));

export const SymbolDialog = ({
  open,
  onClose,
  facilityAt,
  pkg,
  pin,
  buildType,
  defaultName,
  defaultEcat,
  defaultCusts,
  onSave,
}) => {
  const [location, setLocation] = useState("TOP");
  const [filter, setFilter] = useState("FACILITY_PKG");
  const [name, setName] = useState("");
  const [picture, setPicture] = useState("");
  const [ecat, setEcat] = useState("");
  const { filteredSymbols } = useFilteredSymbols(filter, pkg, facilityAt);
  const [custs, setCusts] = useState([]);
  // { name: "CUST1", value: "ALPHA", ignoreBlank: "Y"" },
  const { lenAndCustNames } = useCustLength(pkg, facilityAt, name, pin);
  const classes = useStyles();

  // on show, set the state
  useEffect(() => {
    if (!open) {
      return;
    }

    setFilter("FACILITY_PKG");
    setName(defaultName);
    setPicture("");
    setEcat(defaultEcat);
    setCusts(defaultCusts);
  }, [open]);

  // when the name changes, download the picture
  useEffect(() => {
    if (!name) {
      return;
    }

    findSymbolByValue(name)
      .then((s) => setPicture(s.text))
      .catch((e) => {
        setPicture(`--- picture not found - ${name} ---`);
        logError(e);
      });
  }, [name, picture]);

  // when the picture changes, update the custs
  useEffect(() => {
    if (!picture) {
      return;
    }

    // determine which custX are on the picture
    const required = custx.filter((cust) =>
      picture.toLowerCase().includes(`{${cust}}`.toLowerCase())
    );

    // determine which custX we don't have in the array
    const needed = required.filter(
      (cust) => null == custs.find((c) => c.name === cust)
    );

    // add them
    let newCusts = [...custs];
    needed.forEach((cust) =>
      newCusts.push({ name: cust, value: "", ignoreBlank: "N" })
    );

    // remove ones not needed
    newCusts = newCusts.filter((c) => required.includes(c.name));

    setCusts(
      newCusts.sort((a, b) => {
        if (a.name < b.name) return -1;
        if (a.name > b.name) return 1;
        return 0;
      })
    );
  }, [picture]);

  const symbolNames = filteredSymbols
    .map((symbol) => symbol.object)
    .map((object) => object.name);

  const lengths = lenAndCustNames.reduce(
    (acc, cur) => ({ ...acc, [cur.custFieldName]: cur.maxLen }),
    {}
  );

  const canSave =
    !!name &&
    !!location &&
    !!ecat &&
    custs.every((c) => c.ignoreBlank === "Y" || !!c.value) &&
    custs.every((c) => !lengths[c.name] || c.value?.length <= lengths[c.name] || c.ignoreBlank === "Y");

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl">
      <DialogTitle className={classes.title}>
        Choose the Symbolization
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          <Grid item md={4} xs={12}>
            <SymbolLocation
              location={location}
              onChange={(e) => setLocation(e.target.value)}
              buildType={buildType}
            />
          </Grid>

          <Grid item md={4} xs={12}>
            <SymbolFilter
              filter={filter}
              onChange={(e) => setFilter(e.target.value)}
              buildType={buildType}
            />
          </Grid>

          <Grid item md={4} xs={12}>
            <SymbolName
              name={name}
              onChange={(v) => setName(v)}
              options={symbolNames}
              buildType={buildType} // Disable if buildType is "Minor Change"
            />
          </Grid>

          <Grid item md={4} xs={12}>
            <SymbolEcat ecat={ecat} onChange={(e) => setEcat(e.target.value)} />

            <br />

            <SymbolCusts custs={custs} lengths={lengths} onChange={setCusts} />
          </Grid>

          <Grid item md={8} xs={12}>
            <SymbolPicture name={name} picture={picture} />
          </Grid>
        </Grid>

        <Instructions
          title="Instructions For Symbol Maintenance"
          show={false}
          instructions={[
            <span>
              Click here (
              <a
                href="http://workqueue.sc.ti.com/atss/AppServlet?action=getTabs&forward=tabFrames.jsp&screen=Work Queue"
                target="_blank"
              >
                Work Queue link
              </a>
              ) to add new symbol."
            </span>,
            'Choose group = "TSYMBOL SETUPS" and type = "Non-ATSS" and fill all the required fields and hit "SAVE" to submit the request to Spec Team.',
            <span>
              Click{" "}
              <a href="http://qs.sc.ti.com/devicemarking/" target="_blank">
                Device Marking Requests (1 - 4 Chars)
              </a>{" "}
              to request a new marking.
            </span>,
          ]}
        />
      </DialogContent>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogActions>
        <Button onClick={onClose} variant="outlined" color="primary">
          Cancel
        </Button>
        <Button
          onClick={() => onSave(location, name, picture, ecat, custs)}
          variant="contained"
          color="primary"
          disabled={!canSave}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};
