import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React from "react";
import { samenessFlow } from "src/component/sameness/sameness";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { BuildFlowCell } from "src/pages/mockup/flow/BuildFlowCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const BuildFlowRow = ({ vyper, builds, showSameness }) => {
  const classes = useStyles();
  const defaultFlow = { flowName: "TKY", system: { name: "VYPER" } };

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessFlow(builds),
      })}
      hover
    >
      <RowPrefix
        help="Build Flow name. TKY or MFF Specific flow name"
        title="Build Flow"
      />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <BuildFlowCell
            vyper={vyper}
            build={build}
            buildFlow={build.buildFlow ? build.buildFlow : defaultFlow}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
