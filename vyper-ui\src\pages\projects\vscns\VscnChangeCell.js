import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../../mockup/VyperLink";
import { PraIcons } from "src/pages/vyper/pras/PraIcons";
import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
  center: {
    display: "flex",
    alignItems: "center",
    flexDirection: "column",
  },
  icons: {
    minWidth: 150,
  },
}));

export const VscnChangeCell = ({ vscn, onClick }) => {
  const classes = useStyles();

  const verifiers = vscn.verifiers.filter(
    (verifier) => verifier.name === "ChangeNumber"
  );

  const canEdit = vscn.state === "VSCN_DRAFT";
  return (
    <DataCell source={vscn.changeNumber?.source}>
      <div className={classes.root}>
        <div className={classes.icons}>
          <PraIcons verifiers={verifiers} />
        </div>
        <div className={classes.root}>
          <VyperLink onClick={onClick} canEdit={canEdit}>
            {vscn.changeNumber?.changeNumber == null
              ? "click to select"
              : vscn.changeNumber.changeNumber}
          </VyperLink>
        </div>
      </div>
    </DataCell>
  );
};
