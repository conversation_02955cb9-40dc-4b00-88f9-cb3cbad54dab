import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  withStyles,
} from "@material-ui/core";
import clsx from "clsx";

const useStyles = makeStyles({
  root: {
    whiteSpace: "nowrap",
  },
  table: {
    // fontSize: "0.75rem",
    "& thead": {
      backgroundColor: "hsla(60, 33%, 90%, 1)",
    },
    "& pre": {
      margin: 0,
      padding: "2px",
    },
    tableLayout: "fixed"
  },
  remove: {
    backgroundColor: "hsla(0, 100%, 80%, 1)",
  },
  add: {
    backgroundColor: "hsla(56, 100%, 50%, 1);",
  },
  modified: {
    backgroundColor: "hsla(128, 100%, 80%, 1)",
  },
  lines: {
    backgroundColor: "hsla(60, 33%, 90%, 1)",
    textAlign: "center",
    width: "0",
  },
  text: {
    width: "100%",
    fontSize: "0.9em",
  },
});

const StyledTableCell = withStyles({
  root: {
    borderBottom: "none",
    height: "10px",
    padding: "0px 0px",
  },
})(TableCell);

/**
 *
 * @param differences
 * @param label1
 * @param label2
 * @param showChange
 * @returns {JSX.Element}
 * @constructor
 */
export const Difference = ({
  differences,
  label1 = "Left",
  label2 = "Right",
  showChange = false,
}) => {
  const classes = useStyles();

  if (differences == null) return null;

  return (
    <div className={classes.root}>
      <Table
        size="small"
        className={classes.table}
      >
        <TableHead>
          <TableRow>
            <StyledTableCell align="center">Text 1</StyledTableCell>
            <StyledTableCell align="center">Text 2</StyledTableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {differences?.map((diff, ind) => (
            <TableRow key={ind}>
              <StyledTableCell
                className={clsx({
                  [classes.text]: true,
                  [classes.remove]: diff.differenceType === "REMOVE",
                  [classes.modified]: diff.differenceType === "MODIFIED",
                })}
              >
                <pre>{diff.text1}</pre>
              </StyledTableCell>
              <StyledTableCell
                className={clsx({
                  [classes.text]: true,
                  [classes.add]: diff.differenceType === "ADD",
                  [classes.modified]: diff.differenceType === "MODIFIED",
                })}
              >
                <pre>{diff.text2}</pre>
              </StyledTableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
