export const filterPras = (pras, currentFilters) => {
  // if pras is invalid or empty, return empty list
  if (pras == null || currentFilters == null || pras.length === 0) {
    return [];
  }
  let newPras = [...pras];
  // update the order

  newPras = [...newPras].sort((a, b) => {
    if (a.praNumber < b.praNumber) return 1;
    else if (a.praNumber > b.praNumber) return -1;
    else return 0;
  });
  return newPras;
};
