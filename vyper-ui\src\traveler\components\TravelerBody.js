import PropTypes from "prop-types";
import React from "react";
import { TravelerSubflow } from "./TravelerSubflow";

/**
 * Return the body rows for the traveler.
 * @param {Traveler} traveler - The traveler
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerBody({ traveler }) {
  return (
    <>
      {traveler.subFlows.map((subFlow, n) => (
        <TravelerSubflow key={n} subFlow={subFlow} />
      ))}
    </>
  );
}

TravelerBody.propTypes = {
  traveler: PropTypes.object.isRequired,
};
