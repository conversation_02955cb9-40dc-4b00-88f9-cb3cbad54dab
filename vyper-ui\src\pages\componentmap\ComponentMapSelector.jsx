import { ListItem, ListItemText, TextField } from "@material-ui/core";
import React, { useMemo, useState } from "react";
import List from "@material-ui/core/List";
import { makeStyles } from "@material-ui/styles";
import PropTypes from "prop-types";

const useStyles = makeStyles(() => ({
  list: {
    maxHeight: "60vh",
    overflowY: "scroll",
  },
}));

/**
 * Shows a list of component names in a column. a text field is above the list, which
 * filters the list of names.
 * @param {string} name
 * @param {function} onChange
 * @param {string[]} names
 * @return {JSX.Element}
 * @constructor
 */
export const ComponentMapSelector = ({ name, onChange, names }) => {
  const classes = useStyles();
  const [filter, setFilter] = useState("");

  const filteredName = useMemo(() => {
    return filter === ""
      ? names
      : names.filter((n) => n.toLowerCase().includes(filter.toLowerCase()));
  }, [names, filter]);

  return (
    <div>
      <TextField
        variant="outlined"
        label="Filter"
        name="Filter"
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
      />
      <List className={classes.list}>
        {filteredName.map((n) => (
          <ListItem key={n} selected={name === n} onClick={() => onChange(n)}>
            <ListItemText primary={n} />
          </ListItem>
        ))}
      </List>
    </div>
  );
};

ComponentMapSelector.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  names: PropTypes.array.isRequired,
};
