import React from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { VyperLink } from "src/pages/mockup/VyperLink.js";

export const Pcns = ({ pcns, onClick, canEdit }) => {
  return (
    <>
      {pcns.map((pcn, n) => (
        <DataCell key={n} source={pcn?.source}>
          <VyperLink onClick={onClick} canEdit={canEdit}>
            {pcn.object?.pcnNumber || "click to select"}
          </VyperLink>
        </DataCell>
      ))}
    </>
  );
};
