import { Table, TableCell, TableRow } from "@material-ui/core";
import TableBody from "@material-ui/core/TableBody";
import { Row } from "./Row";
import React from "react";

export const CompareBuild = ({ build1, build2 }) => {
  if (build1 == null || build2 == null) return null;

  const builds = [build1, build2];

  const components = builds
    .map((build) => build.components)
    .flat()
    .map((component) => component.name)
    .sort()
    .filter((value, index, array) => array.indexOf(value) === index);

  return (
    <div>
      <Table>
        <TableBody>
          <TableRow>
            <TableCell>
              <h3>Build Number</h3>
            </TableCell>
            <TableCell>
              <h3>{build1.buildNumber}</h3>
            </TableCell>
            <TableCell>
              <h3>{build2.buildNumber}</h3>
            </TableCell>
          </TableRow>
          <Row name="State" builds={builds} value={(build) => build.state} />
          <Row
            name="Device"
            builds={builds}
            value={(build) => build.material.object.Material}
          />
          <Row
            name="Facility"
            builds={builds}
            value={(build) => build.facility.object.PDBFacility}
          />
          <Row
            name="Package Niche"
            builds={builds}
            value={(build) => build.packageNiche.name}
          />
          <Row
            name="Bill of Process Templates"
            builds={builds}
            value={(build) => build.bomTemplate.object.name}
          />
          <Row
            name="Dies"
            builds={builds}
            value={(build) =>
              build.dies.dieInstances
                .flatMap((dieInstances) => dieInstances)
                .flatMap((dieInstance) => dieInstance.dies)
                .map((die) => die.object.name)
                .join(",")
            }
          />

          {components.map((name, n) => (
            <Row
              key={n}
              name={name}
              builds={builds}
              value={(build) =>
                build.components
                  .filter((component) => component.name === name)
                  .flatMap((component) => component?.instances ?? [])
                  .flatMap((instance) => instance?.priorities ?? [])
                  .flatMap((priority) => priority?.object.name ?? [])
                  .join(",")
              }
            />
          ))}

          <Row
            name="Symbolization"
            builds={builds}
            value={(build) =>
              build.symbolization.symbols
                .map((symbol) => symbol.object.name)
                .join(",")
            }
          />
          <Row
            name="ESL"
            builds={builds}
            value={(build) => build.esl.object.value}
          />
          <Row
            name="Turnkey"
            builds={builds}
            value={(build) => build.turnkey.value}
          />
          <Row
            name="Pack Config"
            builds={builds}
            value={(build) => build.packConfig.object.value}
          />
        </TableBody>
      </Table>
    </div>
  );
};
