import React, { useState, useContext, useEffect, useRef } from "react";
import { DataGrid } from "../../../component/universal";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { logError } from "../../functions/logError";
import { FlowOperationsMapDao } from "../../../dao/FlowOperationsMapDao";
import { DeviceFlowMapDao } from "../../../dao/DeviceFlowMapDao";
import { AlertDialogErrorHandler } from "../../../component/fetch/DaoBase";
import { Checkbox } from "@material-ui/core";
import {
  gridActionEditRecord,
  gridActionDeleteRecord,
} from "../../../component/universal";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import { FlowRowMapDialog } from "./FlowRowMapDialog";
import AddBoxIcon from "@material-ui/icons/AddBox";

export function FlowOperationsConfig() {
  const deviceFlows = useRef([]);
  const defaultRows = useRef([]);
  //const [defaultRows, setDefaultRows] = useState([]);
  //const [deviceFlows, setDeviceFlows] = useState([]);
  const [gridData, setGridData] = useState([]);
  const [flowOpnData, setFlowOpnData] = useState([]);
  const [dynamicColDef, setDynamicColDef] = useState([]);
  const { open: openAlert } = useContext(AlertDialogContext);

  const [mffFlows, setMffFlows] = useState();
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  // const { showOperationsDialog } = useContext(FlowOperationsDialogContext);

  const flowOperationsMapDao = new FlowOperationsMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
  });

  const deviveFlowMapDao = new DeviceFlowMapDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
  });

  useEffect(() => {
    getDeviceFlowData().catch(logError);
  }, []);

  useEffect(() => {
    getDefaultRows().catch(logError);
  }, [deviceFlows]);

  useEffect(() => {
    return flowOperationsMapDao
      .list(0, 1000)
      .then((json) => {
        //console.log({ json });
        const finalFlowRows = json.map((flow) => flow.id); // flow id combination

        // Prepare mapping
        const tempGridData = defaultRows.current.map((rowData) => {
          const currRowData = {
            id: rowData.id,
            flowRow: rowData.opnName,
          };
          deviceFlows.current.map((flow) => {
            currRowData['"flow' + flow.id + '"'] =
              finalFlowRows.filter(
                (flowopnid) =>
                  flowopnid.flowId === flow.id && flowopnid.opnId === rowData.id
              ).length > 0;
          });
          return currRowData;
        });
        setFlowOpnData(finalFlowRows);
        console.log("Setting grid data from > to ", gridData, tempGridData);
        setGridData(tempGridData);
      })
      .catch(logError);
  }, [defaultRows]);

  // Get default rows
  const getDefaultRows = () => {
    return flowOperationsMapDao
      .defaultRows(0, 1000)
      .then((json) => {
        defaultRows.current = json;
        //setDefaultRows(json);
      })
      .catch(logError);
  };

  /**
   *
   * @returns Get flow data
   */
  const getDeviceFlowData = () => {
    return deviveFlowMapDao
      .list(0, 1000)
      .then((json) => {
        deviceFlows.current = json;
        //setDeviceFlows(json);
        const flowColumns = json.map((eachFlow) => {
          const currentFlowId = '"flow' + eachFlow.id + '"';
          return {
            field: currentFlowId,
            title: eachFlow.flowName,
            render: (rowData) => {
              return (
                <div>
                  <Checkbox
                    color="secondary"
                    checked={rowData[currentFlowId] === true}
                    onChange={(event) => saveSelection(rowData, eachFlow.id)}
                  />
                </div>
              );
            },
            cellStyle: {
              maxWidth: 5,
            },
          };
        });

        setDynamicColDef([
          {
            field: "id",
            title: "id",
            hidden: true,
          },
          {
            field: "flowRow",
            title: "Row",
            cellStyle: {
              minWidth: 20,
            },
          },
          ...flowColumns,
        ]);
      })
      .catch(logError);
  };

  const refreshGrid = (newRowData, flowId) => {
    // Prepare mapping
    const tempGridData = defaultRows.current.map((rowData) => {
      const currRowData = {
        id: rowData.id,
        flowRow: rowData.opnName,
      };
      deviceFlows.current.map((flow) => {
        currRowData['"flow' + flow.id + '"'] =
          flowOpnData.filter(
            (flowopnid) =>
              flowopnid.flowId === flow.id && flowopnid.opnId === rowData.id
          ).length > 0;
      });
      return currRowData;
    });
    setGridData(tempGridData);

    //setGridData( gridData.map( gridRow => gridRow.id === rowData.id ? newRowData: gridRow ));
  };

  const saveSelection = (rowData, flowId) => {
    console.log("Changing ", rowData, flowId, flowOpnData);

    return new Promise((resolve, reject) => {
      // true => create
      if (!rowData['"flow' + flowId + '"']) {
        flowOperationsMapDao
          .create({ flowId: flowId, opnId: rowData.id })
          .then((flowRowMap) => {
            setFlowOpnData([flowRowMap.id, ...flowOpnData]);
            resolve();
          })
          .catch(() => reject());
      } else {
        //false ==> delete
        flowOperationsMapDao
          .delete(flowId, rowData.id)
          .then(() => {
            setFlowOpnData(
              flowOpnData.filter(
                (flowOpn) =>
                  flowOpn.id.flowId != flowId && flowOpn.id.opnId != rowData.id
              )
            );
            resolve();
          })
          .catch(() => reject());
      }
      rowData['"flow' + flowId + '"'] = !rowData['"flow' + flowId + '"'];
      //refreshGrid(rowData, flowId);
    });
  };

  console.log("Data > ", gridData);

  return (
    <div>
      <DataGrid
        title="Flow Rows Map"
        columns={dynamicColDef}
        data={gridData}
        editable={true}
        options={{
          search: false,
          pageSize: 20,
          pageSizeOptions: [5, 10, 20, 50, 100, 200],
          cellStyle: {
            minWidth: 20,
            fontSize: "14px",
            height: 10,
          },
          maxBodyHeight: "60%",
        }}
      />
    </div>
  );
}
