import React from "react";
import { Typography } from "@material-ui/core";

const HttpErrorPage = (props) => {
  let { code, message } = props;
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Typography variant="h1" style={{ fontWeight: 900 }}>
        {code}
      </Typography>
      {message && <Typography>{message}</Typography>}
    </div>
  );
};

export default HttpErrorPage;
