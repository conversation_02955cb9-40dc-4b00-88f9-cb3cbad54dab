import config from "../../buildEnvironment";

/**
 * TaskService namespace
 * @namespace TaskService
 */

const {
  baseUrl: BASE_URL,
  appUuid: APP_UUID,
  buildTemplateUuid: buildTemplateUuid,
  praTemplateUuid: praTemplateUuid,
  vscnTemplateUuid: vscnTemplateUuid,
} = config.taskSvcEnv;

const createPostBody = (payload) => {
  return {
    method: "post",
    credentials: "include",
    cache: "no-cache",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Pragma: "no-cache",
      "Cache-Control": "no-cache",
    },
    body: JSON.stringify(payload),
  };
};

const getDistinctUserIds = (users) => {
  let distinctUserList = new Set();
  users.forEach((user) => {
    distinctUserList.add(user.uid);
  });
  return [...distinctUserList];
};

/**
 * Create a task service task
 *
 * @param metaData
 * @param taskContext
 * @param atApproverGroups
 * @param buApproverAids
 * @return {Promise<Response | void>}
 */
export const createTask = (
  metaData,
  taskContext,
  atApproverGroups,
  buApproverAids,
  atBranches,
  buBranches
) => {
  const atFunctionAttr = {
    fnctName: "",
    branchList: [],
  };

  const taskSvcPayload = {
    tmplUuid:
      taskContext.ctxIdLabel === "praNumber"
        ? praTemplateUuid
        : buildTemplateUuid,
    ...taskContext,
    metaList: metaData,
    fnctList: [],
  };

  //create AT functions
  addAtApprovalFunction(
    taskSvcPayload,
    atApproverGroups,
    atBranches,
    atFunctionAttr
  );

  //create BU function
  addBuApprovalFunction(
    taskSvcPayload,
    atFunctionAttr,
    buBranches,
    buApproverAids
  );

  return fetch(
    `${BASE_URL}/Tasks?allowFnctWithoutUsers=false`,
    createPostBody(taskSvcPayload)
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

/**
 * Update an existing task
 * @param contextKey
 * @param taskChangesPayload
 * @return {Promise<Response>}
 */
export const updateTask = (
  contextKey,
  taskChangesPayload,
  atApproverGroups,
  buApproverAids,
  atBranches,
  buBranches
) => {
  const atFunctionAttr = {
    fnctName: "",
    branchList: [],
  };

  // flag to delete non existing functions
  taskChangesPayload.deleteNonExisitingFncts = true;

  //create AT functions
  addAtApprovalFunction(
    taskChangesPayload,
    atApproverGroups,
    atBranches,
    atFunctionAttr
  );

  //create BU function
  addBuApprovalFunction(
    taskChangesPayload,
    atFunctionAttr,
    buBranches,
    buApproverAids
  );

  return fetch(
    `${BASE_URL}/Apps/${APP_UUID}/TemplateContextTasks/${contextKey}?allowFnctWithoutUsers=false`,
    createPostBody(taskChangesPayload)
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

export const updateTaskAssignment = (
  taskUuid,
  asmtUuid,
  taskChangesPayload
) => {
  return fetch(
    `${BASE_URL}/Tasks/${taskUuid}/Assignments/${asmtUuid}`,
    createPostBody(taskChangesPayload)
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

export const fetchUserAssignments = (params = "") => {
  return fetch(`${BASE_URL}/Assignments/Inbox${params}`)
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json())
    .then((assignments) => assignments.value);
};

export const fetchGroups = () => {
  return fetch(`${BASE_URL}/RoleSvc/Apps/${APP_UUID}/Groups`)
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json())
    .then((groupsInfo) => groupsInfo.value.map((group) => group.name));
};

export const refreshTaskAssignments = (taskList) => {
  return fetch(
    `${BASE_URL}/Tasks/BulkSyncFunction?allowFnctWithoutUsers=false`,
    createPostBody(taskList)
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

export const fetchAssignments = (params) => {
  return fetch(`${BASE_URL}/Assignments?appUuid=${APP_UUID}${params}`)
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

/**
 * @param {Array<string>} taskList
 * @param {string} fnctName
 * @param {Array<string>} userIdList
 * @returns Promise of bulk update response
 */
export const bulkUpdateTaskFunction = (taskList, fnctName, userIdList) => {
  const reqBody = {
    taskUuidList: taskList,
    fnctList: [{ fnctName, userIdList }],
  };
  return fetch(
    `${BASE_URL}/Tasks/BulkUpdate?allowFnctWithoutUsers=false`,
    createPostBody(reqBody)
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

/**
 * The response of the Assignments endpoint.
 * @typedef {object} TaskService~TaskAssignment
 * @property {string} asmtUuid - UUID
 * @property {string} taskUuid - UUID
 * @property {string} taskName - The name of the task  - the build number
 * @property {string} ctxIdLabel - The context label. always "buildNumber"
 * @property {string} ctxIdValue - The context value. the build number
 * @property {number} iteration - the iteration number
 * @property {string} stateName - the current state
 * @property {string} fnctName - the user's group
 * @property {string} fnctSatisfied - false
 * @property {string} creatorId - the aid/xid of the user who created the record
 * @property {string} creatorName - the name of the user who created the record
 * @property {string} ownerId - the aid/xid of the user who owns the record
 * @property {string} ownerName - the name of the user who owns the record
 * @property {string} userId - "a0866112"
 * @property {string} userName - "Chih-Sheng Chen"
 * @property {string} tmplName - the name of the task's template
 * @property {string} appUuid - the application's (Vyper) uuid
 * @property {string} appName - the name of the application
 * @property {string} createdDttm - time the record was created
 * @property {string} modifiedDttm - time the record was last modified
 * @property {string} current - true
 * @property {string} complete - false
 */

/**
 * The response of the Assignments endpoint.
 * @typedef {object} TaskService~TaskAssignmentResponse
 * @property {number} @count - The number of records in the value array
 * @property {TaskAssignment[]} value - A array of task items
 */

/**
 * Call the task service /assignments endpoints. returns a record for each person who could approve the task, with task details
 * @param contextKey
 * @returns {Promise<TaskService~TaskAssignmentResponse>}
 */
export const fetchTaskAssignments = (contextKey) => {
  return fetch(
    `${BASE_URL}/Apps/${APP_UUID}/TemplateContextTasks/${contextKey}/Assignments`
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

export const fetchTaskGroups = (contextKey) => {
  return fetch(
    `${BASE_URL}/Apps/${APP_UUID}/TemplateContextTasks/${contextKey}/Functions`
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json())
    .then((json) => json.value.map((group) => group.fnctName));
};

export const fetchUserTaskInfo = (taskList) => {
  const taskSvcPayload = {
    taskUuidList: taskList,
  };
  return fetch(`${BASE_URL}/Tasks/Query`, createPostBody(taskSvcPayload))
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json())
    .then((tasks) => tasks.value);
};

/**
    All functions for VSCN task creations
*/
export const createVscnTask = (
  metaData,
  taskContext,
  atApproverGroups,
  buApproverAids,
  atBranches
) => {
  // build the task service data structures

  const atFunctionAttr = {
    fnctName: "",
    branchList: [],
  };

  const taskSvcPayload = {
    tmplUuid: vscnTemplateUuid,
    ...taskContext,
    metaList: metaData,
    fnctList: [],
  };

  //create AT functions
  addAtApprovalFunction(
    taskSvcPayload,
    atApproverGroups,
    atBranches,
    atFunctionAttr
  );

  return fetch(
    `${BASE_URL}/Tasks?allowFnctWithoutUsers=false`,
    createPostBody(taskSvcPayload)
  )
    .then((response) => {
      if (response.ok) return response;
      else throw response;
    })
    .then((response) => response.json());
};

const addAtApprovalFunction = (
  taskSvcPayload,
  atApproverGroups,
  atBranches,
  atFunctionAttr
) => {
  atApproverGroups
    .map((item) => {
      if (typeof item === "string") {
        // Some of the calls to createTask pass group names instead of group objects
        // Remap group name to object containing the group name
        return { name: item };
      } else {
        return item;
      }
    })
    .forEach((group) => {
      //deep copy object
      const atGroupFunction = JSON.parse(JSON.stringify(atFunctionAttr));

      /* If group is from EM */
      if (group?.users?.length > 0) {
        atGroupFunction.branchList = [...atBranches];
        atGroupFunction["fnctName"] = group.name;

        atGroupFunction["minAprvType"] = "Absolute_Count";
        atGroupFunction["minAprvCnt"] = 1;

        atGroupFunction["userIdList"] = getDistinctUserIds(group.users);
      } else {
        /* Role service group based task creation */
        atGroupFunction.branchList = [...atBranches];
        atGroupFunction["fnctName"] = group.name;
        atGroupFunction["dataSrc"] = "ROLESVC";

        atGroupFunction["minAprvType"] = "Absolute_Count";
        atGroupFunction["minAprvCnt"] = 1;

        atGroupFunction["dataSrcCfg"] = {
          dataSrc: "ROLESVC",
          appUuid: APP_UUID,
          roleName: "USER",
          groupName: group.name,
        };
      }

      taskSvcPayload.fnctList.push(atGroupFunction);
    });
};

const addBuApprovalFunction = (
  taskSvcPayload,
  atFunctionAttr,
  buBranches,
  buApproverAids
) => {
  //deep copy object
  const buGroupFunction = JSON.parse(JSON.stringify(atFunctionAttr));

  buGroupFunction.branchList = [...buBranches];
  buGroupFunction["fnctName"] = "BU_APPROVERS";
  buGroupFunction["userIdList"] = [...buApproverAids];

  buGroupFunction["minAprvType"] = "Absolute_Count";
  buGroupFunction["minAprvCnt"] = 1;

  taskSvcPayload.fnctList.push(buGroupFunction);
};
