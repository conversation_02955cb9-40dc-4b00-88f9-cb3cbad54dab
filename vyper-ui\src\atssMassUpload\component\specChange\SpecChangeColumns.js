import { textColumn, keyColumn, intColumn } from "react-datasheet-grid";
import "./SpecChange.css";

export const SpecChangeColumns = [
  { ...keyColumn("material", textColumn), width: 18, title: "Material", disabled: ({ rowData }) => rowData?.scnId != undefined && rowData?.scnId !== "" },
  { ...keyColumn("oldMaterial", textColumn), width: 16, title: "Old Material", disabled: ({ rowData }) => rowData?.scnId != undefined && rowData?.scnId !== "" },
  { ...keyColumn("specDevice", textColumn), width: 18, title: "Spec Device", disabled: ({ rowData }) => rowData?.scnId != undefined && rowData?.scnId !== "" },
  { ...keyColumn("flowType", textColumn), width: 10, title: "Flow Type" },
  {
    ...keyColumn("operationName", textColumn),
    width: 15,
    title: "Operation Name",
  },
  {
    ...keyColumn("componentName", textColumn),
    width: 18,
    title: "Component Name",
  },
  {
    ...keyColumn("componentValue", textColumn),
    width: 20,
    title: "Component Value",
  },
  { ...keyColumn("componentOccurrance", intColumn), title: "Occurence" },
  {
    ...keyColumn("attributeName", textColumn),
    width: 15,
    title: "Attribute Name",
  },
  {
    ...keyColumn("attributeValue", textColumn),
    width: 20,
    title: "Attribute Value",
  },
  {
    ...keyColumn("validationStatus", textColumn),
    title: "Validation Status",
    minWidth: 250,
  },
  {
    ...keyColumn("deviceId", textColumn),
    cellClassName: "custom-dsg-hide-column",
    headerClassName: "custom-dsg-hide-header",
    width: 0,
  },

  {
    ...keyColumn("changeId", textColumn),
    cellClassName: "custom-dsg-hide-column",
    headerClassName: "custom-dsg-hide-header",
    width: 0,
  },
  {
    ...keyColumn("scnId", textColumn),
    cellClassName: "custom-dsg-hide-column",
    headerClassName: "custom-dsg-hide-header",
    width: 0,
  },
];
