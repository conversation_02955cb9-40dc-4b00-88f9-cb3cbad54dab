import { Chip } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import PropTypes from "prop-types";
import React from "react";

const useStyles = makeStyles((theme) => ({
  root: {
    marginRight: theme.spacing(1),
    marginBottom: "5px",
    fontWeight: "550",
  },
}));

export const PersistentFilters = ({ filter, value, columns }) => {
  const classes = useStyles();
  filter = columns.find((column) => column.field === filter).headerName;
  return (
    <div className={classes.root}>
      <Chip variant="outlined" size="medium" label={`${filter} / ${value}`} />
    </div>
  );
};

PersistentFilters.propTypes = {
  filter: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  columns: PropTypes.array.isRequired,
};
