import React from "react";
import { But<PERSON>, Grid, Select, TextField } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import MenuItem from "@material-ui/core/MenuItem";
import produce from "immer";
import FormHelperText from "@material-ui/core/FormHelperText";
import { Autocomplete } from "@material-ui/lab";

const useStyles = makeStyles({
  root: {
    fontSize: "10px",
    padding: "10px",
    paddingLeft: "1rem",
    borderLeft: "none",
    borderRight: "none",
    borderRadius: "7%",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
    top: 60,
  },
  header: {
    fontWeight: "bold",
  },
});

const VyperFilter = ({
  onChange,
  currentFilters,
  getFilterVyperData,
  totalFilteredBuilds,
  totalBuilds,
}) => {
  const VyperFilterData = getFilterVyperData();

  const handleReverseBuildFilter = (event) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.order = event.target.value;
        return draft;
      })
    );
  };
  const handleBuildtypeFilter = (event) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.buildtype = event.target.value;
        return draft;
      })
    );
  };
  const handleBuildStateFilter = (event) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.buildState = event.target.value;
        return draft;
      })
    );
  };
  const handleFacilityFilter = (event) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.facility = event.target.value;
        return draft;
      })
    );
  };
  const handleMaterialChange = (event) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.material = event.target.value;
        return draft;
      })
    );
  };
  const handleClearFilters = () => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.buildState = VyperFilterData.buildState[0];
        draft.order = VyperFilterData.order[0];
        draft.buildtype = VyperFilterData.buildtype[0];
        draft.facility = VyperFilterData.facilities[0];
        draft.buildNumbers = [];
        draft.material = VyperFilterData.materials[0];
        return draft;
      })
    );
  };

  const handleBuildSelect = (e, val) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.buildNumbers = val;
        return draft;
      })
    );
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <form>
        <Grid container spacing={3}>
          <Grid item sm={4}>
            <Autocomplete
              multiple
              options={VyperFilterData.buildNumbers}
              onChange={handleBuildSelect}
              getOptionLabel={(filterOptions) => filterOptions.buildNumber}
              defaultValue={currentFilters.buildNumbers}
              value={currentFilters.buildNumbers}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="standard"
                  placeholder="Build Number(s)"
                />
              )}
            />
            <FormHelperText>Build Number(s)</FormHelperText>
          </Grid>
          <Grid item>
            <Select
              onChange={handleReverseBuildFilter}
              value={currentFilters.order}
            >
              {VyperFilterData.order.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Build Order</FormHelperText>
          </Grid>

          <Grid item>
            <Select
              onChange={handleBuildtypeFilter}
              value={currentFilters.buildtype}
            >
              {VyperFilterData.buildtype.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Buildtype</FormHelperText>
          </Grid>

          <Grid item>
            <Select
              onChange={handleBuildStateFilter}
              value={currentFilters.buildState}
            >
              {VyperFilterData.buildState.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Build State</FormHelperText>
          </Grid>
          <Grid item>
            <Select
              onChange={handleFacilityFilter}
              value={currentFilters.facility}
            >
              {VyperFilterData.facilities.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Facility</FormHelperText>
          </Grid>
          <Grid item>
            <Select
              onChange={handleMaterialChange}
              value={currentFilters.material}
            >
              {VyperFilterData.materials.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Material</FormHelperText>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              color="primary"
              onClick={handleClearFilters}
            >
              Clear Filters
            </Button>
          </Grid>
          <Grid item>
            <FormHelperText>
              Showing {totalFilteredBuilds} of {totalBuilds} builds
            </FormHelperText>
          </Grid>
        </Grid>
      </form>
    </div>
  );
};

export default VyperFilter;
