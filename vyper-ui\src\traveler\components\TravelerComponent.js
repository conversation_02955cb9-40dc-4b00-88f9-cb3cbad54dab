import PropTypes from "prop-types";
import React from "react";
import { TravelerAttribute } from "./TravelerAttribute";
import { TravelerParagraph } from "./TravelerParagraph";
import { useTravelerStyles } from "./travelerStyles";

/**
 * return the component row for the traveler
 * @param {TravelerComponent} component - The component
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerComponent({ component }) {
  const classes = useTravelerStyles();
  const name = classes[component.validStatus];
  return (
    <>
      <span className={name}>
        {("".padStart(3, " ") + component.name).padEnd(32, " ")}
      </span>
      :{" "}
      <span>
        {(component.value || "").padEnd(52, " ")}
        {component.priority.toString().padStart(2, "0")}
      </span>
      <br />
      {component.attributes.map((attribute, n) => (
        <TravelerAttribute key={n} attribute={attribute} />
      ))}
      {component.paragraph && (
        <TravelerParagraph paragraph={component.paragraph} />
      )}
    </>
  );
}

TravelerComponent.propTypes = {
  component: PropTypes.object.isRequired,
};
