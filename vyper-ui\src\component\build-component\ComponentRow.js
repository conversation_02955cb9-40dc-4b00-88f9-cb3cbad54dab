import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { requiredComponentsByVyperBuilds } from "src/component/required/requiredComponents";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessComponents } from "../sameness/sameness";
import { ComponentCell } from "./ComponentCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const ComponentRow = ({
  vyper,
  builds,
  title,
  onSave,
  showSameness,
}) => {
  const { buildDao, praDao } = useContext(DataModelsContext);

  // determine if any of the component values are different across the builds

  // This needs to be pulled out
  const handleChangeBuildComponents = (component, build, revertPgs) => {
    return buildDao
      .changeComponents(
        vyper.vyperNumber,
        build.buildNumber,
        component,
        revertPgs
      )
      .then((json) => onSave(json))
      .catch(noop);
  };
  const handleChangePraComponents = (component, pra, revertPgs) => {
    return praDao
      .changeComponents(vyper.vyperNumber, pra.praNumber, component, revertPgs)
      .then((json) => onSave(json))
      .catch(noop);
  };

  const getTooltip = (title) => {
    switch (title) {
      case "Leadframe":
        return "leadframe";

      case "Mount Compound":
        return "mountcompound";

      case "MB Diagram":
        return "mbdiagram";

      case "Wire":
        return "wire";

      case "Mold Compound":
        return "moldcompound";

      case "MSL":
        return "msl";

      default:
        return "component";
    }
  };

  const classes = useStyles();

  const required = requiredComponentsByVyperBuilds(builds).includes(title);

  return (
    <TableRow
      className={clsx({
        [classes.difference]:
          showSameness && !samenessComponents(builds, title),
      })}
      hover
    >
      <RowPrefix help={getTooltip(title)} title={title} required={required} />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <ComponentCell
            vyper={vyper}
            build={build}
            title={title}
            onSaveBuild={handleChangeBuildComponents}
            onSavePra={handleChangePraComponents}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
