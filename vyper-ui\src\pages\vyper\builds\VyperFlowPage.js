import clsx from "clsx";
import React, { useContext } from "react";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { BackToBuildFormLink } from "../../../component/backbutton/BackToBuildFormLink";
import TableHead from "@material-ui/core/TableHead";
import { SourceIcon } from "src/component/sourceicon/SourceIcon";
import { BuildPageTitle } from "src/pages/vyper/BuildPageTitle";
import { DataModelsContext } from "src/DataModel";
import { EngineeringIcon } from "src/component/icons/EngineeringIcon";

const useStyles = makeStyles({
  root: {},
  centered: {
    display: "flex",
    whiteSpace: "nowrap",
  },
  deleted: {
    textDecoration: "line-through",
  },
});

export const VyperFlowPage = ({ vyperNumber }) => {
  const { build } = useContext(DataModelsContext);

  // flatten the flow structure

  const items = [];
  build?.flow?.object?.operations?.forEach((operation) => {
    items.push({
      oSubFlow: operation.subFlow,
      oName: operation.name,
      oRequired: operation.required,
      oSource: operation.source,
      engineering: operation.engineering,
      deleted: operation.engineeringDeleted,
    });

    operation.components.forEach((component) => {
      items.push({
        cName: component.name,
        cRequired: component.required,
        cSource: component.source,
        deleted: operation.engineeringDeleted,
      });
    });
  });

  const classes = useStyles();

  //

  return (
    <div className={classes.root}>
      <BackToBuildFormLink vyperNumber={vyperNumber} build={build} />

      <BuildPageTitle build={build} title="Flow View" />

      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow hover>
              <TableCell>Sub Flow</TableCell>
              <TableCell>Operation</TableCell>
              <TableCell>Operation Required</TableCell>
              <TableCell>Component</TableCell>
              <TableCell>Component Required</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item, n) => (
              <TableRow
                key={n}
                hover
                className={clsx({ [classes.deleted]: item.deleted })}
              >
                <TableCell>{item.oSubFlow}</TableCell>
                <TableCell className={classes.centered}>
                  <SourceIcon source={item.oSource} />
                  {item.oName}
                  {item.engineering === "Y" ? <EngineeringIcon /> : null}
                </TableCell>
                <TableCell>{item.oRequired}</TableCell>
                <TableCell className={classes.centered}>
                  <SourceIcon source={item.cSource} />
                  {item.cName || (
                    <>
                      <br />
                    </>
                  )}
                </TableCell>
                <TableCell>{item.cRequired}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};
