import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import React, { useContext, useEffect, useState } from "react";
import { FetchContext } from "../fetch/VyperFetch";
import { Attributes } from "./Attributes";
import { BuildType } from "./BuildType";
import { Description } from "./Description";
import { SelectCopyBuild } from "./SelectCopyBuild";
import { Title } from "./Title";
import { Flow } from "./Flow";
import PropTypes from "prop-types";
import { DeviceFlowMapDao } from "../../dao/DeviceFlowMapDao";
import { logError } from "../../pages/functions/logError";
import { SymbolChoice } from "./SymbolChoice";
import { DataModelsContext } from "../../../src/DataModel";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
});

///////////////////////////////////////////////////////////////////////////////////////////////////

const pgsOperations = ["Leadframe", "Mold Compound", "Mount Compound", "Wire"];

///////////////////////////////////////////////////////////////////////////////////////////////////

const NEW_MULTI_BUILD = "New Multi-Build";

const symbolOptions = [
  { id: "choice 1", description: "Symbol at end of Assembly" },
  {
    id: "choice 2",
    description:
      "Symbol at end of Assembly with symbol verification at end of Test",
  },
];

export const NewBuildDialog = ({
  children /*open, onClose, onAddNewBuild, onAddCopiedBuild*/,
}) => {
  const { vget } = useContext(FetchContext);
  const { build } = useContext(DataModelsContext);

  // the form
  const [vyperNumber, setVyperNumber] = useState();
  const [buildNumber, setBuildNumber] = useState();
  const [buildType, setBuildType] = useState();
  const [description, setDescription] = useState();
  const [copyBuild, setCopyBuild] = useState();
  const [defaultFlow, setDefaultFlow] = useState([]);
  const [symbolChoice, setSymbolChoice] = useState(
    build?.buildFlow?.symbolChoice
  );
  const [flowData, setFlowData] = useState([
    {
      flowId: "",
      flowName: "",
      device: {},
      facility: "",
      isMultiBuild: false,
      specDevice: "",
      facilities: [],
      specDevices: [],
      allowedFlows: [],
      templateSource: { templateType: "DEVICE_PKGNICHE" },
    },
  ]);
  const [mffFlows, setMffFlows] = useState([]);
  // non-form state
  const [onSave, setOnSave] = useState();
  const [open, setOpen] = useState(false);
  const [devicePkgNiche, setDevicePkgNiche] = useState();
  const [pgsData, setPgsData] = useState();
  const [materialData, setMaterialData] = useState();

  // Controls the visibility of input structures on the form
  const [showCurrentSelectedBuild, setShowCurrentSelectedBuild] =
    useState(false);
  const [minorChangeTableData, setMinorChangeTableData] = useState([]);

  // Resets all the values in the dialog
  const resetValues = () => {
    setBuildType(undefined);
    setDescription(undefined);
    setDevicePkgNiche(undefined);
    setCopyBuild(undefined);
    setPgsData(undefined);
    setMaterialData(undefined)
    setFlowData([
      {
        flowId: defaultFlow.id,
        flowName: defaultFlow.flowName,
        device: {},
        facility: "",
        isMultiBuild: false,
        specDevice: "",
        facilities: [],
        specDevices: [],
        allowedFlows: getAllowedFlows(0),
        templateSource: { templateType: "DEVICE_PKGNICHE" },
      },
    ]);
    setSymbolChoice(undefined);
  };

  // open the dialog
  // device is the device attributes object
  const handleOpen = ({
    vyperNumber,
    buildNumber,
    buildType,
    description,
    symbolChoice,
    onSave,
  }) => {
    setVyperNumber(vyperNumber);
    setBuildNumber(buildNumber);
    setBuildType(buildType);
    setDescription(description);
    setOnSave(() => onSave);
    setSymbolChoice(symbolChoice);
    setOpen(true);
  };

  // When the Build is selected under minor change
  const handleBuildSelect = (e, row) => {
    // Save the row data
    setCopyBuild(row);
    // Show the row data
    setShowCurrentSelectedBuild(true);
  };

  // close the dialog when cancel or escape is clicked/pressed
  const handleClickCancel = () => setOpen(false);

  const deviceFlowMapDao = new DeviceFlowMapDao();

  useEffect(() => {
    refresh().catch(logError);
  }, []);

  const refresh = () => {
    return deviceFlowMapDao
      .list(0, 1000)
      .then((json) => {
        setDefaultFlow(
          json.filter((flowData) => {
            return flowData.default;
          })[0]
        );
        setMffFlows(json);
      })
      .catch(logError);
  };

  // get the multiBuild flag from the api

  // useEffect(() => {
  //   if (device == null || facility == null) {
  //     return;
  //   }

  //   const url = `/vyper/v1/vyper/atss/multibuild?device=${device.Material}&facilityAt=${facility.PDBFacility}`;
  //   vget(url, (json) => setMultiBuildAtss(json));
  // }, [device, facility]);

  // when the facility changes, get the package niche
  useEffect(() => {
    if (
      Object.keys(flowData[0].device).length === 0 ||
      (flowData[0].device != null &&
        flowData[0].facility != null &&
        flowData[0].facility !== "")
    ) {
      setDevicePkgNiche(flowData[0].device.PackageNiche);
    }
  }, [flowData[0].device, flowData[0]?.facility]);

  const getAllowedFlows = (index) => {
    const previousFlow = index > 0 ? flowData.slice(index - 1, index)[0] : {};

    return mffFlows.filter((flow) => {
      return (
        (index <= 0 && flow.predecessorFlows.length == 0) ||
        (index > 0 &&
          flow.flowName != defaultFlow.flowName &&
          previousFlow != undefined &&
          flow.predecessorFlows.indexOf(previousFlow.flowId) >= 0)
      );
    });
  };

  const handleChangeDescription = (e) => {
    if (description == "" || description === undefined) {
      setFlowData([
        {
          flowId: defaultFlow.id,
          flowName: defaultFlow.flowName,
          device: {},
          facility: "",
          isMultiBuild: false,
          specDevice: "",
          facilities: [],
          specDevices: [],
          allowedFlows: getAllowedFlows(0),
          templateSource: { templateType: "DEVICE_PKGNICHE" },
        },
      ]);
    }
    setDescription(e.target.value);
  };

  // handle changing of the build type
  const handleChangeBuildType = (buildType) => {
    setBuildType(buildType);
  };

  // handle the changing/ modifying/ deleting of flow Data
  const handleChangeFlowData = (e, index, field) => {
    const newFlowData = [...flowData];
    if (field === "device") {
      newFlowData[index][field] = e.materialAttributes;
      newFlowData[index]["facilities"] = e.facilities;
      newFlowData[index]["hasBomTemplateForPkgNiche"] = undefined;
      newFlowData[index]["differentPackageNicheExists"] = undefined;
      newFlowData[index]["pgsData"] = e.pgsData;

      // by default, set the spec devices to the old material
      flowData[index]["specDevices"] = [flowData[index]["device"].OldMaterial];
      newFlowData[index]["specDevice"] = flowData[index]["device"].OldMaterial;

      if (buildType === "Minor Change") {
        updatePgsData();
      }
    } else if (field === "isMultiBuild") {
      // if multi-build is set to true, set specDevice to the list of spec devices for the material, and "New Multi-Build"
      if (e.target.value === "True") {
        // get the list of spec devices
        const url = `/vyper/v1/vyper/atss/multibuildspecs?device=${flowData[index].device.Material}&facilityAt=${flowData[index].facility.PDBFacility}`;
        vget(url, (json) => {
          // if there is only 1 return, then it's the old material. don't include it in the selections
          // otherwise, include all returned specDevices
          if (json.length === 1) {
            flowData[index]["specDevices"] = [NEW_MULTI_BUILD];
            newFlowData[index]["specDevice"] = NEW_MULTI_BUILD;
          } else {
            flowData[index]["specDevices"] = [NEW_MULTI_BUILD, ...json];
          }
        });
      } else {
        flowData[index]["specDevices"] = [
          flowData[index]["device"].OldMaterial,
        ];
        newFlowData[index]["specDevice"] =
          flowData[index]["device"].OldMaterial;
      }
      newFlowData[index][field] = e.target.value === "True" ? true : false;
    } else if (field === "flowName") {
      newFlowData[index]["flowName"] = e.target.value;
      newFlowData[index]["flowId"] = mffFlows.filter(
        (flow) => flow.flowName === e.target.value
      )[0].id;

      if (!showSymbolChoice) {
        setSymbolChoice(undefined);
      }
    } else if (field === "copyFromAtss") {
      const { facility, material, status } = e;
      newFlowData[index]["templateSource"] = {}; // reset templateSource before modifying/adding data
      newFlowData[index]["templateSource"]["templateType"] = "ATSS";
      newFlowData[index]["templateSource"]["atssFacility"] = facility;
      newFlowData[index]["templateSource"]["atssMaterial"] = material;
      newFlowData[index]["templateSource"]["atssStatus"] = status;
    } else if (field === "copyFromVyper") {
      const buildNumber = e;
      newFlowData[index]["templateSource"] = {};
      newFlowData[index]["templateSource"]["templateType"] = "VYPER";
      newFlowData[index]["templateSource"]["vyperBuildNumber"] = buildNumber;
    } else if (field === "copyFromSimilarPkgNiche") {
      const similarPkgNiche = e;
      newFlowData[index]["templateSource"] = {};
      newFlowData[index]["templateSource"]["templateType"] = "SIMILAR_PKGNICHE";
      newFlowData[index]["templateSource"]["similarPkgNiche"] = similarPkgNiche;
    } else if (field === "NO") {
      if (
        index === 0 &&
        newFlowData[index]["templateSource"]["templateType"] ===
          "SIMILAR_PKGNICHE"
      ) {
        for (let i = 0; i < newFlowData.length; i++) {
          newFlowData[i]["templateSource"] = {};
        }
      } else {
        newFlowData[index]["templateSource"] = {};
      }
    } else if (field === "hasBomTemplateForPkgNiche") {
      newFlowData[index]["hasBomTemplateForPkgNiche"] = e;
    } else if (field === "facility") {
      newFlowData[index]["differentPackageNicheExists"] = undefined;
      const facilityInfo = e.target.value;
      newFlowData[index]["facility"] = facilityInfo;
      const packageNicheMapping =
        newFlowData[index]["device"].PackageNicheMapping;
      const packageNiche = packageNicheMapping.find(
        (map) => map.PlantCode === facilityInfo.PlantCode
      )?.PackageNiche;

      if (packageNiche != undefined) {
        newFlowData[index]["device"].PackageNiche = packageNiche;
      } else {
        let packageNicheForOtherFacility;
        // logic to check if package niche is same across other facilities
        const matchingPackageNiche = packageNicheMapping
          .filter((map) => map.PackageNiche != undefined)
          .every((map, index, arr) => {
            if (index === 0) {
              packageNicheForOtherFacility = map.PackageNiche;
              return true;
            }
            return arr[0].PackageNiche === map.PackageNiche;
          });

        if (matchingPackageNiche && packageNicheForOtherFacility) {
          newFlowData[index]["device"].PackageNiche =
            packageNicheForOtherFacility;
        } else {
          newFlowData[index]["differentPackageNicheExists"] = true;
          newFlowData[index]["hasBomTemplateForPkgNiche"] = false;
        }
      }
    } else {
      newFlowData[index][field] = e.target.value;
    }
    newFlowData[index]["allowedFlows"] = getAllowedFlows(index);

    if (field === "flowName") {
      setFlowData(newFlowData.filter((f, i) => i <= index));
    } else {
      setFlowData(newFlowData);
    }
  };

  const handleAddFlowData = () =>
    setFlowData([
      ...flowData,
      {
        flowId: "",
        flowName: "",
        device: flowData[0]?.device || {},
        facility: "",
        isMultiBuild: false,
        specDevice: flowData[0]?.specDevice || {},
        facilities: flowData[0]?.facilities || {},
        specDevices: flowData[0]?.specDevices || {},
        allowedFlows: getAllowedFlows(flowData.length),
        hasBomTemplateForPkgNiche: flowData[0]?.hasBomTemplateForPkgNiche,
        templateSource:
          flowData[0]?.templateSource?.templateType === "SIMILAR_PKGNICHE"
            ? flowData[0].templateSource
            : { templateType: "DEVICE_PKGNICHE" },
        pgsData: flowData[0]?.pgsData,
      },
    ]);

  const handleDeleteFlowData = (index) => {
    setFlowData((prevFlowData) => prevFlowData.filter((_, i) => i !== index));
  };

  const handleChangeSymbolChoice = (e) => {
    setSymbolChoice(e.target.value);
  };

  // if buildtype is change to minor change, or the device doesn't have a
  // package niche, then update the pgs data
  useEffect(() => {
    if (
      Object.keys(flowData[0].device).length === 0 ||
      flowData[0].device == null ||
      flowData[0].facility == null ||
      flowData[0].facility === "" ||
      buildType == null ||
      buildType === ""
    ) {
      return;
    }

    if (buildType === "Minor Change" || !checkPackageNiche(devicePkgNiche)) {
      updatePgsData();
    }
  }, [flowData[0].device, flowData[0].facility, buildType]);

  const updatePgsData = () => {
    if (
      Object.keys(flowData[0].device).length === 0 ||
      flowData[0].device == null ||
      flowData[0].facility == null ||
      flowData[0].facility == ""
    ) {
      return;
    }

    const plantCode = flowData[0].facility.PlantCode;

    // This will store the PGS components that we will get
    let PGSops = {};

    // URL for getting PGS components
    let url = `/vyper/v1/vyper/pgscomponents`;
    url += `?plantCode=${encodeURI(plantCode)}`;
    url += `&material=${encodeURI(flowData[0].device.Material)}`;

    // Add all the operations wanted to the URL
    pgsOperations.forEach(
      (operation) => (url += `&operations=${encodeURI(operation)}`)
    );

    // Run the Query
    vget(url, (json) => {
      // Clean the API return just to grab the components partnumber
      pgsOperations.forEach((operation) => {
        PGSops[operation] = json.build?.components?.find(
          (c) => c.name === operation
        );

        if (PGSops[operation].instances.length !== 0) {
          PGSops[operation] =
            PGSops[operation].instances[0].priorities[0].object.name;
        } else {
          PGSops[operation] = null;
        }
      });

      let MaterialInfo = {};

      let findBuildsURL = '/vyper/v1/vyper/findAllVyperBuildsByMaterial?';

      findBuildsURL += `facility=${encodeURIComponent(
        flowData[0].facility.PDBFacility
      )}`;

      findBuildsURL += `&pins=${encodeURIComponent(
        flowData[0].device.PackagePin
      )}`;

      findBuildsURL += `&pkg=${encodeURIComponent(
        flowData[0].device.PackageDesignator
      )}`;

      findBuildsURL += `&sbe=${encodeURIComponent(
        flowData[0].device.SBE
      )}`;

      findBuildsURL += `&build_flow=${encodeURIComponent(
        flowData[0].flowName
      )}`;

      vget(findBuildsURL, (result) => {
        // Set the data table
        setMinorChangeTableData(result);
      });

      MaterialInfo['material'] = flowData[0].device.Material;
      MaterialInfo['facility'] = flowData[0].facility.PDBFacility;
      MaterialInfo['pins'] = flowData[0].device.PackagePin;
      MaterialInfo['pkg'] = flowData[0].device.PackageDesignator;
      MaterialInfo['sbe'] = flowData[0].device.SBE;
      MaterialInfo['sbe1'] = flowData[0].device.SBE1
      MaterialInfo['build_flow'] = flowData[0].flowName;

      setMaterialData(MaterialInfo);

      PGSops["Material"] = flowData[0].device.Material;
      PGSops["Facility"] = flowData[0].facility;
      PGSops["Package Niche"] = flowData[0].device.PackageGroup;
      PGSops["Plant Code"] = plantCode;

      //
      setPgsData(PGSops);
    });
  };

  const MODE_MINOR = "BUILD_MINOR";
  const MODE_NEW = "BUILD_NEW";
  const MODE_UPDATE = "BUILD_UPDATE";

  // This is the onsubmit function
  const handleClickSubmit = () => {
    let mode;
    if (buildType === "Minor Change") {
      mode = MODE_MINOR;
    } else if (buildNumber == null) {
      mode = MODE_NEW;
    } else {
      mode = MODE_UPDATE;
    }

    onSave(
      mode,
      vyperNumber,
      buildNumber,
      flowData,
      description,
      buildType,
      copyBuild?.BUILD_NUMBER,
      symbolOptions.filter((o) => o.description === symbolChoice)?.[0]?.id
    );

    resetValues();

    setOpen(false);
  };

  let showSymbolChoice =
    flowData.length >= 2 &&
    flowData[flowData.length - 1].flowName.toLowerCase() === "test";

  // Error Checking the description input
  const checkDescriptionError = () =>
    description === "" || !description?.trim().length;

  const checkPackageNiche = (devicePkgNiche) =>
    !(devicePkgNiche == null || devicePkgNiche.trim() === "");

  const showDescription =
    buildType !== null && buildType !== "" && buildType !== undefined;
  const showMinorChange = buildType === "Minor Change";

  const showFlowData =
    buildType !== "" &&
    buildType !== undefined &&
    description !== "" &&
    description != null;

  let showSubmit =
    description != null &&
    description !== "" &&
    buildType != null &&
    buildType !== "" &&
    flowData.length > 0 &&
    // (showSymbolChoice ? symbolChoice != undefined : true) &&
    flowData.every((obj) => {
      if (
        obj?.facility?.PDBFacility !== undefined &&
        obj.flowName !== "" &&
        obj?.device?.Material !== undefined
      ) {
        const hasCopyTemplateType =
          obj?.templateSource?.templateType !== undefined &&
          obj?.templateSource?.templateType !== "" &&
          obj?.templateSource?.templateType !== "DEVICE_PKGNICHE";

        if (obj?.hasBomTemplateForPkgNiche === false && !hasCopyTemplateType) {
          if (buildType === "Minor Change") {
            return true;
          }
          return false;
        }

        if (hasCopyTemplateType) {
          const templateType = obj?.templateSource?.templateType;
          if (templateType === "SIMILAR_PKGNICHE") {
            const hasSimilarPkgNiche =
              obj?.templateSource?.similarPkgNiche !== undefined &&
              obj?.templateSource?.similarPkgNiche !== "";
            return hasSimilarPkgNiche;
          } else if (templateType === "VYPER") {
            const hasCopyFromVyperVBuild =
              obj?.templateSource?.vyperBuildNumber !== undefined &&
              obj?.templateSource?.vyperBuildNumber !== "";
            return hasCopyFromVyperVBuild;
          } else if (templateType === "ATSS") {
            const hasCopyFromAtssData =
              obj?.templateSource?.atssMaterial !== undefined &&
              obj?.templateSource?.atssMaterial !== "" &&
              obj?.templateSource?.atssFacility !== undefined &&
              obj?.templateSource?.atssFacility !== "" &&
              obj?.templateSource?.atssStatus !== undefined &&
              obj?.templateSource?.atssStatus !== "";
            return hasCopyFromAtssData;
          }
        }
        return true;
      } else {
        return false;
      }
    });

  if (showMinorChange) {
    showSubmit = showSubmit && copyBuild != null;
  }

  const classes = useStyles();

  return (
    <NewBuildDialogContext.Provider
      value={{
        openNewBuildDialog: handleOpen,
      }}
    >
      <Dialog
        className={classes.root}
        open={open}
        maxWidth="lg"
        fullWidth
        onClose={handleClickCancel}
      >
        <DialogTitle classes={{ root: classes.title }}>
          <Title buildNumber={buildNumber} />
        </DialogTitle>

        <DialogContent>
          <div style={{ overflow: "hidden", height: "100%", width: "100%" }}>
            <div
              style={{
                paddingRight: 17,
                height: "100%",
                width: "100%",
                boxSizing: "content-box",
                overflow: "scroll",
                overflowX: "hidden",
              }}
            >
              <h3>Choose your Build</h3>

              <BuildType
                buildType={buildType}
                onChange={handleChangeBuildType}
              />

              {showDescription && (
                <Description
                  description={description}
                  onChange={handleChangeDescription}
                  error={checkDescriptionError()}
                />
              )}

              {buildType === "Minor Change" && (
                <div style={{ margin: "0 0 30px 0", color: "black", "font-weight": "bold" }}>
                  System Created Mass Upload Builds of Reference Build Type cannot be used for Vyper Minor Change Builds.
                </div>
              )}

              {showFlowData && (
                <Flow
                  flowData={flowData}
                  flows={mffFlows}
                  onChangeFlowData={handleChangeFlowData}
                  onDeleteFlowData={handleDeleteFlowData}
                  onAddFlowData={handleAddFlowData}
                  defaultFlow={defaultFlow}
                  buildType={buildType}
                />
              )}

              {/* disable the symbol choice. VYPER-1663*/}
              {false && showSymbolChoice && (
                <SymbolChoice
                  symbolChoice={symbolChoice}
                  onChangeSymbolChoice={handleChangeSymbolChoice}
                  options={symbolOptions}
                />
              )}

              {Object.keys(flowData[0]?.device || {}).length !== 0 ? (
                <Attributes
                  device={flowData[0]?.device}
                  facility={flowData[0]?.facility}
                />
              ) : undefined}

              {showMinorChange && materialData != null && (
                <SelectCopyBuild
                  materialData={materialData}
                  copyBuild={copyBuild}
                  showCurrentSelectedBuild={showCurrentSelectedBuild}
                  onSelectBuild={handleBuildSelect}
                  minorChangeTableData={minorChangeTableData}
                  buildType={buildType}
                />
              )}
            </div>
          </div>
        </DialogContent>

        <DialogActions>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleClickCancel}
          >
            Cancel
          </Button>
          <Button
            disabled={!showSubmit}
            variant="contained"
            color="primary"
            onClick={handleClickSubmit}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </NewBuildDialogContext.Provider>
  );
};

export const NewBuildDialogContext = React.createContext(null);

NewBuildDialog.propTypes = {
  children: PropTypes.object,
};
