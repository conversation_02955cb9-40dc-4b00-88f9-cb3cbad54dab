import React, { useState } from "react";
import { makeStyles } from "@material-ui/core/styles";
import HourglassEmptyIcon from "@material-ui/icons/HourglassEmpty";

const useStyles = makeStyles({
  spinner: {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    zIndex: 10000000000,
    animation: "$spin 2s ease infinite",
  },

  "@keyframes spin": {
    "0%": {
      transform: "rotate(0deg)",
    },
    "100%": {
      transform: "rotate(360deg)",
    },
  },
});

export const Spinner = ({ children }) => {
  // any time count > 0, then show the spinner
  // when count == 0, hide it.

  const [count, setCount] = useState(0);
  const show = () => setCount((c) => c + 1);
  const hide = () => setCount((c) => c - 1);

  const classes = useStyles();

  return (
    <SpinnerContext.Provider
      value={{
        count: count,
        showSpinner: show,
        hideSpinner: hide,
      }}
    >
      <div>
        {count > 0 ? (
          <HourglassEmptyIcon
            color="secondary"
            fontSize="large"
            className={classes.spinner}
          />
        ) : null}
        {children}
      </div>
    </SpinnerContext.Provider>
  );
};

export const SpinnerContext = React.createContext(null);
