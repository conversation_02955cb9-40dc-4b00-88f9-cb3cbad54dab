import React from "react";
import WarningIcon from "@material-ui/icons/Warning";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  withStyles,
} from "@material-ui/core";

export const HtmlComponentPriorityWarningTooltip = withStyles((theme) => ({
  tooltip: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    fontSize: theme.typography.pxToRem(12),
    border: "2px solid #808080",
  },
}))(Tooltip);

export const ComponentPriorityWarning = ({ warnings = [] }) => {
  if (warnings.length === 0) {
    return null;
  }

  return (
    <HtmlComponentPriorityWarningTooltip
      arrow
      interactive
      title={
        <React.Fragment>
          <Paper style={{ overflow: "hidden" }}>
            <TableContainer style={{ maxHeight: 440 }}>
              <Table stickyHeader aria-label="sticky table">
                <TableHead>
                  <TableRow>
                    <TableCell colSpan={2} align="center">
                      Warnings
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {warnings.map((warning, n) => (
                    <TableRow key={n}>
                      <TableCell>{n + 1}</TableCell>
                      <TableCell>{warning}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </React.Fragment>
      }
    >
      <WarningIcon
        style={{
          marginLeft: "0.75em",
          color: "hsla(50, 100%, 33%, 1)",
          fontSize: "1.25em",
        }}
      />
    </HtmlComponentPriorityWarningTooltip>
  );
};
