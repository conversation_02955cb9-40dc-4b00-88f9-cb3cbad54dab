import { Paper, TableBody, TableHead } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";

import Table from "@material-ui/core/Table";
import TableCell from "@material-ui/core/TableCell";
import TableContainer from "@material-ui/core/TableContainer";
import TableRow from "@material-ui/core/TableRow";
import React from "react";

import { CompareTableCell } from "./CompareTableCell";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  table: {
    width: "100%",
  },
  //   tableContainer: {
  //     backgroundColor: "#fce09f",
  //   },
});

function CompareTable({ currentObject, copyObject, showCurrent }) {
  const items = [
    {
      title: "Device",
      currentItem: currentObject.material,
      copyItem: copyObject?.MATERIAL,
      showCurrent: showCurrent,
    },
    {
      title: "Facility",
      currentItem: currentObject.facility,
      copyItem: copyObject?.FACILITY,
      showCurrent: showCurrent,
    },
    {
      title: "Pins",
      currentItem: currentObject.pins,
      copyItem: copyObject?.PINS,
      showCurrent: showCurrent,
    },
    {
      title: "Pkg",
      currentItem: currentObject.pkg,
      copyItem: copyObject?.PKG,
      showCurrent: showCurrent,
    },
    {
      title: "SBE",
      currentItem: currentObject.sbe,
      copyItem: copyObject?.SBE,
      showCurrent: showCurrent,
    },
  ];

  const classes = useStyles();

  return (
    <div>
      <div className={classes.table}>
        <TableContainer className={classes.tableContainer} component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <h4>Fields</h4>
                </TableCell>
                <TableCell align="center">
                  <h4>Device Selected</h4>
                </TableCell>

                {showCurrent ? (
                  <TableCell align="center">
                    <h4>Selected VYPER Build: {copyObject?.BUILD_NUMBER}</h4>
                  </TableCell>
                ) : (
                  <></>
                )}
              </TableRow>
            </TableHead>

            <TableBody>
              {items.map((item) => (
                <TableRow key={item.title}>
                  <TableCell>{item.title}</TableCell>
                  <CompareTableCell
                    currentItem={item.currentItem}
                    copyItem={item.copyItem}
                    showBuild={item.showCurrent}
                  />
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </div>
  );
}

export default CompareTable;
