// noinspection RequiredAttributes

import React, { useContext, useEffect } from "react";
import { Alert } from "@material-ui/lab";
import {
  FormControlLabel,
  FormGroup,
  MenuItem,
  Paper,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tabs,
  TextField,
} from "@material-ui/core";
import Button from "@material-ui/core/Button";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TableHead from "@material-ui/core/TableHead";
import { TabPanel } from "../TabPanel";
import { useLocalStorage } from "../../../../component/hooks/useLocalStorage";
import { FlowTemplateDetailsPanel } from "src/pages/admin/sandbox/bomtemplate/FlowTemplateDetailsPanel";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";

const useStyles = makeStyles({
  form: {
    padding: "1rem",
    marginTop: "1rem",
    marginBottom: "1rem",
    border: "1px solid red",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
  },
  paper: {
    margin: "1rem",
  },
  green: {
    backgroundColor: "hsla(128, 100%, 95%, 1)",
  },
  red: {
    backgroundColor: "hsla(0, 100%, 95%, 1)",
  },
  cell: {
    color: "black",
  },
});

export const BomTemplateSandbox = () => {
  const { facilityDao, sandboxDao, plantCodes } = useContext(DataModelsContext);

  const [device, setDevice] = useLocalStorage(
    "playground.bomtemplate.device",
    null
  );
  const [plantCode, setPlantCode] = useLocalStorage(
    "playground.bomtemplate.plantCode",
    null
  );
  const [packageNiche, setPackageNiche] = useLocalStorage(
    "playground.bomtemplate.packageNiche",
    null
  );
  const [shelfLife, setShelfLife] = useLocalStorage(
    "playground.bomtemplate.shelfLife",
    "SSL (2 years)"
  );
  const [dryPack, setDryPack] = useLocalStorage(
    "playground.bomtemplate.dryPack",
    "No"
  );
  const [result, setResult] = useLocalStorage(
    "playground.bomtemplate.result",
    {}
  );
  const [tabIndex, setTabIndex] = useLocalStorage(
    "playground.bomtemplate.tabIndex",
    0
  );

  const shelfLifeValues = ["SSL (2 years)", "ESL (5 years)"];

  const handleChangeDevice = (e) => setDevice(e.target.value);
  const handleChangePlantCode = (e) => setPlantCode(e.target.value);
  const handleChangePackageNiche = (e) => setPackageNiche(e.target.value);
  const handleChangeShelfLife = (e) => setShelfLife(e.target.value);
  const handleChangeDryPack = (e) => setDryPack(e.target.value);
  const handleChange = (e, index) => setTabIndex(index);

  useEffect(() => {
    facilityDao.loadPlantCodes().catch(noop);
  }, []);

  const buttonDisabled =
    device == null || device === "" || plantCode == null || plantCode === "";

  const handleSubmit = () => {
    sandboxDao
      .bomTemplate(device, plantCode, packageNiche, shelfLife, dryPack)
      .then((json) => setResult(json))
      .catch(noop);
  };

  const classes = useStyles();

  return (
    <div>
      <br />

      <Alert severity="success">
        This form allows you to submit a device and facility, and see the
        results of the Bill of Process Template merge.
      </Alert>

      <form className={classes.form}>
        <FormGroup row>
          <FormControlLabel
            control={
              <TextField
                variant="outlined"
                label="Device"
                value={device || ""}
                onChange={handleChangeDevice}
              />
            }
          />

          <FormControlLabel
            control={
              <TextField
                label="Facility A/T"
                select
                value={plantCode || ""}
                onChange={handleChangePlantCode}
              >
                {plantCodes.map((plantCode) => (
                  <MenuItem
                    key={plantCode.facilityAt}
                    value={plantCode.plantCode}
                  >
                    {plantCode.facilityAt}
                  </MenuItem>
                ))}
              </TextField>
            }
          />

          <FormControlLabel
            control={
              <TextField
                variant="outlined"
                label="Package Niche"
                value={packageNiche || ""}
                onChange={handleChangePackageNiche}
              />
            }
          />

          <FormControlLabel
            control={
              <TextField
                select
                variant="outlined"
                label="Shelf Life"
                value={shelfLife || ""}
                onChange={handleChangeShelfLife}
              >
                {shelfLifeValues.map((v, n) => (
                  <option key={n} value={v}>
                    {v}
                  </option>
                ))}
              </TextField>
            }
          />

          <FormControlLabel
            control={
              <TextField
                select
                variant="outlined"
                label="Dry Pack"
                value={dryPack || ""}
                onChange={handleChangeDryPack}
              >
                <option value="No">No</option>
                <option value="Yes">Yes</option>
              </TextField>
            }
          />

          <FormControlLabel
            control={
              <Button
                disabled={buttonDisabled}
                color="primary"
                variant="contained"
                onClick={handleSubmit}
              >
                Go
              </Button>
            }
            label=""
          />
        </FormGroup>
      </form>

      <Paper>
        <Tabs value={tabIndex} onChange={handleChange}>
          <Tab label="Info" />
          <Tab label="Merge Context" />
          <Tab label="Templates" />
          <Tab label="Merged Template" />
        </Tabs>

        <TabPanel value={tabIndex} index={0}>
          <div>
            <h3>Info</h3>
            <pre>Device: {JSON.stringify(result?.device, null, "\t")}</pre>
            <pre>
              Plant Code {JSON.stringify(result?.plantCode, null, "\t")}
            </pre>
            <pre>
              Package Niche {JSON.stringify(result?.packageNiche, null, "\t")}
            </pre>
            <pre>When {JSON.stringify(result?.when, null, "\t")}</pre>
          </div>
        </TabPanel>

        <TabPanel value={tabIndex} index={1}>
          <h3>Merge Context</h3>
          <pre>{JSON.stringify(result?.context?.items, null, "\t")}</pre>
        </TabPanel>

        <TabPanel value={tabIndex} index={2}>
          <FlowTemplateDetailsPanel
            flowTemplateDetails={result.context?.flowTemplateDetails}
            ruleContext={result.context?.ruleContext}
          />
        </TabPanel>

        <TabPanel value={tabIndex} index={3}>
          <div>
            <h3>Merged Templates</h3>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Operation Sequence</TableCell>
                    <TableCell>Operation</TableCell>
                    <TableCell>Operation Required</TableCell>
                    <TableCell>Component Sequence</TableCell>
                    <TableCell>Component</TableCell>
                    <TableCell>Component Required</TableCell>
                    <TableCell>Global</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {result.context?.mergeTemplates.map((template, n) => (
                    <React.Fragment key={n}>
                      <TableRow>
                        <TableCell>{template.operationSequence}</TableCell>
                        <TableCell>{template.operation}</TableCell>
                        <TableCell>{template.operationRequired}</TableCell>
                        <TableCell>{template.componentSequence}</TableCell>
                        <TableCell>{template.component}</TableCell>
                        <TableCell>{template.componentRequired}</TableCell>
                        <TableCell>{template.global}</TableCell>
                      </TableRow>
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </TabPanel>
      </Paper>
    </div>
  );
};
