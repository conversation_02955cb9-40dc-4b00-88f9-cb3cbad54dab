import Button from "@material-ui/core/Button";
import React from "react";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  fillName: {
    marginTop: "0.5rem",
  },
});

export const FillComponentWidget = ({
  onClickAtss,
  onClickVyper,
  onClickClear,
  enableClear = true,
  fillComponent,
}) => {
  const classes = useStyles();

  return (
    <div
      style={{
        border: "1px solid #CC0000",
        padding: "1em",
        marginRight: "1em",
        borderRadius: "0.5em",
        paddingTop: "0.5em",
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-start",
        alignItems: "center",
      }}
    >
      <h3 style={{ margin: 0, padding: 0 }}>Fill Unselected </h3>

      <div
        style={{
          display: "flex",
          justifyContent: "flex-start",
          alignItems: "center",
          gap: "1em",
        }}
      >
        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={onClickAtss}
        >
          ATSS
        </Button>

        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={onClickVyper}
        >
          Vyper
        </Button>

        <Button
          variant="contained"
          color="primary"
          size="small"
          disabled={!enableClear}
          onClick={onClickClear}
        >
          Clear
        </Button>
      </div>

      {fillComponent?.mode !== "NONE" && (
        <div className={classes.fillName}>
          Selected:
          {fillComponent.buildNumber}&nbsp;
          {fillComponent.atssMaterial}&nbsp;
          {fillComponent.atssFacility}&nbsp;
          {fillComponent.atssStatus}
        </div>
      )}
    </div>
  );
};
