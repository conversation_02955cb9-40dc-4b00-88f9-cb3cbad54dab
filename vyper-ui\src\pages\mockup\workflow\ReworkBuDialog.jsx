import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import React, { useState } from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
} from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import TextField from "@material-ui/core/TextField";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "white",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    padding: theme.spacing(1),
  },
  required: {
    color: "red",
    fontSize: "1.5rem",
  },
}));

/**
 *
 * @param open
 * @param onClose
 * @param onRework
 * @returns {JSX.Element}
 * @constructor
 */
export const ReworkBuDialog = ({ open, onClose, onRework }) => {
  const [reason, setReason] = useState("");

  const classes = useStyles();

  const canSave = reason.length > 0;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className={classes.title}>Action: BU Rework</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>
      <DialogContent>
        <div>
          <span>Type in a reason for Rework:</span>
        </div>

        <TextField
          fullWidth
          multiline
          minRows={5}
          margin="dense"
          name="reason"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          variant="outlined"
        />
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="primary" onClick={onClose}>
          Cancel
        </Button>

        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={!canSave}
          onClick={() => onRework(reason)}
        >
          Rework
        </Button>
      </DialogActions>
    </Dialog>
  );
};
