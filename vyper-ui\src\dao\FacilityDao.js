import { DaoBase } from "src/component/fetch/DaoBase";
import { noop } from "src/component/vyper/noop";

export class FacilityDao extends DaoBase {
  constructor(params) {
    super({ name: "FacilityDao", url: "/vyper/v1/vyper", ...params });
    this.facilities = params.facilities || [];
    this.setFacilities = params.setFacilities || noop;
    this.plantCodes = params.plantCodes || [];
    this.setPlantCodes = params.setPlantCodes || noop;
  }

  loadFacilities() {
    if (this.facilities?.length > 0) {
      return Promise.resolve(this.facilities);
    } else {
      return this.handleFetch("list", "/atss/facilities", "GET").then(
        (json) => {
          this.setFacilities(json);
          return this.facilities;
        }
      );
    }
  }

  loadPlantCodes() {
    if (this.plantCodes?.length > 0) {
      return Promise.resolve(this.plantCodes);
    } else {
      return this.handleFetch("list", "/atss/plantcodes", "GET").then(
        (json) => {
          this.setPlantCodes(json);
          return this.plantCodes;
        }
      );
    }
  }
}
