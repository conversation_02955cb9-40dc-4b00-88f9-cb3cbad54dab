import React, { useContext } from "react";
import { AuthContext } from "src/component/common/auth";

// Pra Workflow States
const statePraDraft = "PRA_DRAFT";
const statePraApproved = "PRA_APPROVED";

// Workflow Actions
const approveAction = "Approve";
const deleteAction = "Delete";

export const PraWorkFlowHelpers = ({ children }) => {
  const { authUser } = useContext(AuthContext);

  const isPraState = (pra, state) => pra?.state === state || false;

  const canUpdatePra = (pra, vyper) => {
    return (
      isPraState(pra, statePraDraft) &&
      vyper?.owners?.some((owner) => {
        return authUser.uid === owner.userid;
      })
    );
  };

  return (
    <PraWorkFlowHelpersContext.Provider
      value={{
        canUpdatePra,
        approveAction,
        deleteAction,
      }}
    >
      {children}
    </PraWorkFlowHelpersContext.Provider>
  );
};

export const PraWorkFlowHelpersContext = React.createContext(null);
