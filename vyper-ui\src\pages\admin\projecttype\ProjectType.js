import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { DataGrid } from "../../../component/universal";
import { FetchContext } from "../../../component/fetch/VyperFetch";

const useStyles = makeStyles({
  root: {},
});

export const ProjectType = ({}) => {
  const { vget, vpost, vdelete } = useContext(FetchContext);

  const columns = [{ field: "projectType", title: "Project Type" }];

  // download the project types
  const [data, setData] = useState([]);
  useEffect(() => {
    vget("/vyper/v1/projecttype/search?size=1000", (json) =>
      setData(json.content)
    );
  }, []);

  const handleRowAdd = (newData) => {
    return new Promise((resolve, reject) => {
      vpost("/vyper/v1/projecttype/", newData, (projectType) => {
        setData([...data, projectType]);
        resolve();
      });
    });
  };

  const handleRowUpdate = (newData, oldData) => {
    return new Promise((resolve, reject) => {
      vpost(`/vyper/v1/projecttype/${newData.id}`, newData, () => {
        const data2 = [...data];
        data2[oldData.tableData.id] = newData;
        setData(data2);
        resolve();
      });
    });
  };

  const handleRowDelete = (oldData) => {
    return new Promise((resolve, reject) => {
      vdelete(`/vyper/v1/projecttype/${oldData.id}`, {}, () => {
        const data2 = [...data];
        data2.splice(oldData.tableData.id, 1);
        setData(data2);
        resolve();
      });
    });
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <DataGrid
        title="Project Types"
        data={data}
        columns={columns}
        editable={true}
        onRowAdd={handleRowAdd}
        onRowUpdate={handleRowUpdate}
        onRowDelete={handleRowDelete}
      />
    </div>
  );
};
