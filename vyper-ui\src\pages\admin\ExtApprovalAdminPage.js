import React, { useCallback, useContext, useEffect, useState } from "react";
import { makeStyles } from "@material-ui/core";
import { sortBy } from "lodash";

import UpdateTaskGroupsButton from "./UpdateTaskGroupsButton";
import { FetchContext } from "../../component/fetch/VyperFetch";

/**
 * @typedef {object} PlantUser
 * @property {string} name
 * @property {string} internalFlag
 * @property {string} uid
 * @property {string} eid
 */

/**
 * @typedef {object} PlantGroup
 * @property {string} name
 * @property {string} role
 * @property {boolean} roleGroup
 * @property {Array<PlantUser>} users
 */

/**
 * @typedef {object} PlantPermission
 * @property {string} plantName
 * @property {number} plantId
 * @property {Array<PlantGroup>} groups
 */

/** @type {Array<PlantPermission>} */
const EMPTY_PLANT_PERMS = [];

/** @type {Array<PlantGroup>} */
const EMPTY_PLANT_GROUPS = [];

/** @type {Array<string>} */
const EMPTY_GROUP_NAMES = [];

/** @type {CSSStyleDeclaration} */
const buttonStyle = {};

const useStyles = makeStyles(() => ({
  root: {},
  widget: {
    width: "16rem",
  },
  valid: {
    backgroundColor: "lightgreen",
  },
  invalid: {
    backgroundColor: "pink",
  },
}));

export const ExtApprovalAdminPage = () => {
  const classes = useStyles();
  const { vget } = useContext(FetchContext);
  const [allPlantPerms, setAllPlantPerms] = useState(EMPTY_PLANT_PERMS);
  const [allPlantGroups, setAllPlantGroups] = useState(EMPTY_PLANT_GROUPS);
  const [allGroupNames, setAllGroupNames] = useState(EMPTY_GROUP_NAMES);

  useEffect(() => {
    vget("/vyper/v1/emas/plant/roles", setAllPlantPerms);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const groups = sortBy(
      allPlantPerms.flatMap((item) => item.groups),
      "name"
    );
    setAllPlantGroups(groups);
    setAllGroupNames(groups.map((item) => item.name));
  }, [allPlantPerms]);

  const getGroupUserIds = useCallback(
    /**
     * @param {string} groupName
     * @returns {Array<string>}
     */
    (groupName) => {
      const group = allPlantGroups.find((item) => item.name === groupName);
      if (group) {
        return group.users.map((item) => item.uid);
      }
      return [];
    },
    [allPlantGroups]
  );

  return (
    <div className={classes.root}>
      <h3>External Approval Admin</h3>

      {allGroupNames.length > 0 && (
        <UpdateTaskGroupsButton
          extGroup={true}
          getGroupUserIds={getGroupUserIds}
          groupNames={allGroupNames}
          style={buttonStyle}
          title="Update External Approval Tasks"
        />
      )}

      <hr />
      {allPlantGroups.map((group) => (
        <div key={group.name}>
          {group.name}: {group.users.map((item) => item.uid).join("; ")}
        </div>
      ))}
    </div>
  );
};
