import { But<PERSON> } from "@material-ui/core";
import PropTypes from "prop-types";
import React, {useState, useEffect, useContext} from "react";
import { AuthContext } from "src/component/common/auth";
import { ApprovalDialog } from "../../mockup/workflow/ApprovalDialog";
import { ReworkDialog } from "../../mockup/workflow/ReworkDialog";
import TaskStatusDialog from "../../mockup/workflow/TaskStatusDialog";
import useSnackbar from "../../../hooks/Snackbar";
import { fetchAllVscnTasks, submitVscnToAtGroup, processTasks, approveOrRejectTheTask } from "./VscnTasksUtility";

/**
 * Display the Work Flow cell for a VSCN
 *
 * @param {Vscn} item - The VSCN object
 * @param {WorkFlowCell2~onClick} onClick - Called when the user clicks a button
 * @return {JSX.Element}
 * @constructor
 */
export function VscnWorkflowCell({ item: vscn, onClick }) {

  // Get the task assignments and verify if user has an approved function
  const { authUser } = useContext(AuthContext);
  const [tasks, setTasks] = useState([]);
  const { enqueueErrorSnackbar } = useSnackbar();
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showReworkDialog, setShowReworkDialog] = useState(false);
  const [taskStatusDialog, setTaskStatusDialog] = useState(false);
  
  const {
    unApprovedGroups,
    approvedGroups,
    isFrozen,
    unApprovedGroupsAsObjects,
    taskUser,
    isApprover,
  } = processTasks(tasks, authUser);

  const enableAtssSubmit = vscn.state == "VSCN_APPROVED";
  const enableSubmit = vscn.state == "VSCN_DRAFT";
  const enableApprove = isApprover && vscn.state == "VSCN_AT_REVIEW";
  const enableDelete = vscn.state == "VSCN_DRAFT";
  const enableReject = isApprover && vscn.state == "VSCN_AT_REVIEW";
  



  useEffect(() => {
    if (
      authUser.uid === "" ||
      vscn?.vscnNumber === "" ||
      vscn?.vscnNumber === undefined
    ) {
      return;
    }

    fetchAllVscnTasks(vscn, authUser, setTasks).catch((err) =>
      console.log(err)
    );
  }, [authUser, vscn?.vscnNumber]);

  const handleWorkflowChange = (status) => {
    onClick(vscn, status);
  };

  const approveTask = (group, comment) => {
    approveOrRejectTheTask(
      tasks,
      authUser,
      "Approve",
      comment,
      null,
      group
    ).then((data) => {
      setShowApprovalDialog(false);
    });
  };

  const rejectTask = (group, reason, comment) => {
    approveOrRejectTheTask(
      tasks,
      authUser,
      "Reject",
      comment,
      reason,
      group
    ).then((data) => {
      setShowReworkDialog(false);
      handleWorkflowChange("REJECT");
    });
  };


  const submitToAtReview = () => {
    submitVscnToAtGroup(vscn, authUser, "submit")
    .then(() => {
      handleWorkflowChange("SUBMIT");
    })
    .catch((err) => {
      enqueueErrorSnackbar(err.toString());
    });
  }


  return (
    <>
      <ApprovalDialog
        open={showApprovalDialog}
        approvalGroups={unApprovedGroups}
        onApprove={approveTask}
        onClose={() => {
          setShowApprovalDialog(false);
        }}
      />
      <ReworkDialog
        open={showReworkDialog}
        reworkGroups={unApprovedGroups}
        onRework={rejectTask}
        onClose={() => setShowReworkDialog(false)}
      />
      <TaskStatusDialog
        open={taskStatusDialog}
        contextKey={`VSCN_Approval~VscnNumber~${vscn.vscnNumber}`}
        handleClose={() => setTaskStatusDialog(false)}
      />
      {enableSubmit && (
        <Button
          variant="contained"
          color="primary"
          onClick={submitToAtReview}
        >
          Submit
        </Button>
      )}
      {enableApprove && (
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowApprovalDialog(true)}
        >
          Approve
        </Button>
      )}
      {enableReject && (
          <Button
            variant="contained"
            color="primary"
            onClick={() => setShowReworkDialog(true)}
          >
            Reject
          </Button>
        )}
      {enableDelete && (
        <Button
          variant="contained"
          color="primary"
          onClick={() => onClick(vscn, "DELETE")}
        >
          Delete
        </Button>
      )}
      {enableAtssSubmit && (
        <Button
          variant="contained"
          color="primary"
          onClick={() => onClick(vscn, "SUBMIT_TO_ATSS")}
        >
          Submit to ATSS
        </Button>
      )}
    </>
  );
}

VscnWorkflowCell.propTypes = {
  item: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
};
