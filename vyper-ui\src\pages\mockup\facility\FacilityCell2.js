import PropTypes from "prop-types";
import React from "react";
import { DataCell } from "../../../component/datacell/DataCell";

/**
 * @callback FacilityCell2~onGetFacilityObject
 * @param {*} item - the item
 * @return {FacilityObject} facilityObject - the facility object
 */

/**
 * Display the facility cell
 *
 * @param {*} item - The item
 * @param {FacilityCell2~onGetFacilityObject} onGetFacilityObject - callback to retrieve the item's facility object
 * @return {JSX.Element}
 * @constructor
 */
export function FacilityCell2({ item, onGetFacilityObject }) {
  const facilityObject = onGetFacilityObject(item);
  return <DataCell source={null}>{facilityObject.PDBFacility}</DataCell>;
}

FacilityCell2.propTypes = {
  item: PropTypes.any.isRequired,
  onGetFacilityObject: PropTypes.func.isRequired,
};
