import React, { useContext } from "react";
import { Button } from "@material-ui/core";
import { AddPraDialogContext } from "src/pages/home/<USER>/AddPraDialog";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  buttonColor: {
    backgroundColor: "hsla(200, 100%, 35%, 1)",
    color: "white",
    "&:hover": {
      backgroundColor: "hsla(200, 100%, 25%, 1)",
    },
  },
});
export const AddPraButton = ({ handleAddNewPra }) => {
  const classes = useStyles();

  const { openPraDialog: openDialog } = useContext(AddPraDialogContext);
  const handleOpenDialog = () => {
    openDialog({
      onAddPra: (buildNumber) => {
        handleAddNewPra(buildNumber);
      },
    });
  };

  return (
    <Button
      variant="contained"
      className={classes.buttonColor}
      size="small"
      onClick={handleOpenDialog}
    >
      Add PRA
    </Button>
  );
};

export default AddPraButton;
