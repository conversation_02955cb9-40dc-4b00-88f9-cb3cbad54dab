import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import Button from "@material-ui/core/Button";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import CloseIcon from "@material-ui/icons/Close";
import React, { useContext, useState } from "react";
import { HelperContext } from "src/component/helper/Helpers";

const MODE_NORMAL = "normal";
const MODE_ENGINEERING = "engineering";

const useStyles = makeStyles({
  root: {},
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  closeButton: {
    color: "white",
  },
  layout: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
});

export const RemoveOperationDialog = ({ children }) => {
  const { currentUserIsSCP } = useContext(HelperContext);

  const [open, setOpen] = useState(false);
  const [build, setBuild] = useState();
  const [operation, setOperation] = useState([]);
  const [required, setRequired] = useState();
  const [mode, setMode] = useState(MODE_NORMAL);
  const [onRemove, setOnRemove] = useState();

  /**
   * Open the dialog
   * @param build The build object
   * @param operations The list of operations
   * @param onRemove callback {name:'operation name', engineering:'Y' or 'N'}
   */
  const handleOpen = ({ build, operation, required, onRemove }) => {
    setOpen(true);
    setBuild(build);
    setOperation(operation);
    setRequired(required);
    setOnRemove(() => onRemove);
    setMode(MODE_NORMAL);
  };

  // user clicked the close button
  const handleClickClose = () => {
    setOpen(false);
  };

  // user clicked the remove button
  const handleClickRemove = () => {
    onRemove({
      name: operation,
      engineering: mode === MODE_NORMAL ? "N" : "Y",
    });
    handleClickClose();
  };

  // toggle the mode between normal and engineering
  const handleClickMode = () => {
    setMode(mode === MODE_NORMAL ? MODE_ENGINEERING : MODE_NORMAL);
  };

  // determine if the current user SCP for the build's site
  const isSCP = currentUserIsSCP(null, build);

  // determine if the current build is experimental
  const isExperimental = build?.buildtype
    ?.toUpperCase()
    ?.includes("EXPERIMENTAL");

  // determine if the experimental operation is allowed
  const canBeExperimental = isSCP && isExperimental;

  const canRemove =
    (mode === MODE_NORMAL && required !== "REQUIRED") ||
    mode === MODE_ENGINEERING;

  const classes = useStyles();

  return (
    <RemoveOperationDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog className={classes.root} open={open} onClose={handleClickClose}>
        <DialogTitle classes={{ root: classes.title }}>
          <div className={classes.layout}>
            <div>Remove Operation</div>
            <IconButton
              className={classes.closeButton}
              onClick={handleClickClose}
            >
              <CloseIcon />
            </IconButton>
          </div>
        </DialogTitle>

        <DialogContent>
          <p>
            To delete a required operation click the engineering button to
            switch to Engineering Operation mode.
          </p>
          <p>Are you sure you want to remove the operation {operation}?</p>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClickMode}
            variant="contained"
            color="secondary"
            disabled={!canBeExperimental}
          >
            {mode === MODE_NORMAL ? MODE_ENGINEERING : MODE_NORMAL}
          </Button>
          <Button onClick={handleClickClose} variant="outlined" color="primary">
            Close
          </Button>
          <Button
            onClick={handleClickRemove}
            variant="contained"
            color="primary"
            disabled={!canRemove}
          >
            Remove
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </RemoveOperationDialogContext.Provider>
  );
};

export const RemoveOperationDialogContext = React.createContext(null);
