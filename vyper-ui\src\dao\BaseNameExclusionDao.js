import { DaoBase } from "src/component/fetch/DaoBase";

export class BaseNameExclusionDao extends DaoBase {
  constructor(params) {
    super({
      name: "BaseNameExclusionDao",
      url: "/vyper/v1/basename/exclusions",
      ...params,
    });
  }

  list() {
    return this.handleFetch("list", `/`, "GET");
  }

  create(baseName) {
    return this.handleFetch("create", ``, "POST", { baseName });
  }

  update(oldBaseName, newBaseName) {
    return this.handleFetch("update", `/${oldBaseName}`, "POST", {
      baseName: newBaseName,
    });
  }

  deleteByBaseName(baseName) {
    return this.handleFetch("deleteByBaseName", `/${baseName}`, "DELETE");
  }
}
