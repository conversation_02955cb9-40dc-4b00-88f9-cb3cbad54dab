import axios from "axios";
import { useQuery } from "react-query";
import { BASE_URL } from "../common/scswrAPI";
import useSnackbar from "../../../../hooks/Snackbar";

export let useUpdatableSwrsGetter = (swrTab) => {
  let { enqueueErrorSnackbar } = useSnackbar();
  let updatableSwrsGetter = useQuery(
    ["getupdateswrs", "tableName", "REQUESTS_REDBULL", "swrTab", swrTab],
    () => {
      return axios
        .get(
          `${BASE_URL}/getupdateswrs?tableName=REQUESTS_REDBULL&swrTab=${swrTab}`
        )
        .then((res) => res.data);
    },
    {
      onError: () => {
        enqueueErrorSnackbar("An error occured while trying to load data.");
      },
    }
  );

  return updatableSwrsGetter;
};
