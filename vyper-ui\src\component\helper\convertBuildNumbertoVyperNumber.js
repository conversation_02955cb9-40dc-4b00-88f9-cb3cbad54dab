export const convertBuildNumbertoVyperNumber = (buildNumber) => {
  if (!buildNumber) {
    return buildNumber;
  }
  return "VYPER" + buildNumber.substring(6, buildNumber.indexOf("-"));
};

export const convertPraNumbertoVyperNumber = (praNumber) => {
  if (!praNumber) {
    return praNumber;
  }
  return "VYPER" + praNumber.substring(3, praNumber.indexOf("-"));
};

export const convertVscnNumbertoVyperNumber = (vscnNumber) => {
  if(!vscnNumber) {
    return vscnNumber;
  }
  return "VYPER" + vscnNumber.substring(4, vscnNumber.indexOf("-"));
}