import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { formatDate } from "../../../../component/dateFormat";
import {
  convertBuildNumbertoVyperNumber,
  convertPraNumbertoVyperNumber,
  convertVscnNumbertoVyperNumber
} from "../../../../component/helper/convertBuildNumbertoVyperNumber";
import Button from "@material-ui/core/Button";
import TiServerAgGrid, {
  exportGridAsExcel,
} from "../../../../../src/lib/TiServerAgGrid";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import { Grid, Switch } from "@material-ui/core";
import config from "../../../../../src/buildEnvironment";
import {
  fetchUserAssignments,
  fetchUserTaskInfo,
} from "/src/component/api/taskService2";

const { taskSvcEnv, externalUse } = config;

let body = document.body,
  html = document.documentElement;
let height =
  Math.max(
    body.scrollHeight,
    body.offsetHeight,
    html.clientHeight,
    html.scrollHeight,
    html.offsetHeight
  ) - 200;

const selectCompRenderer = (node) => {
  const rowData = node.data;
  if (rowData == null) {
    return <></>;
  }

  if (rowData.buildNumber.includes("PRA")) {
    return (
      <Link to={`/projects/${rowData.vyperNumber}/pras/${rowData.buildNumber}`}>
        {rowData.buildNumber}
      </Link>
    );
  }

  if (rowData.buildNumber.includes("VBUILD")){
    return (
      <Link to={`/projects/${rowData.vyperNumber}/builds/${rowData.buildNumber}/selection`}>
        {rowData.buildNumber}
      </Link>
    );
  }
  if (rowData.buildNumber.includes("VSCN")){
    return (
      <Link to={`/projects/${rowData.vyperNumber}/vscns/${rowData.buildNumber}`}>
        {rowData.buildNumber}
      </Link>
    );
  }

  return (
    <Link to={`/atssmassupload/projects/actions/${rowData.muProjectId}`}>
      {rowData.buildNumber}
    </Link>
  );
};

const hideUserId = (node) => {
  const rowData = node.data;
  if (rowData !== undefined) {
    if (externalUse) {
      const regex = new RegExp("^([a|x][0-9]{7},?s*)+$");
      if (regex.test(rowData.owners)) {
        return <></>;
      }
    }

    return <span>{rowData.owners}</span>;
  }
  return <></>;
};

const columns = [
  {
    headerName: "Vyper Number",
    field: "vyperNumber",
    cellRenderer: (node) => {
      const rowData = node.data;
      if (rowData != null) {
        return (
          <Link to={`/projects/${rowData.vyperNumber}`}>
            {rowData.vyperNumber}
          </Link>
        );
      }
      return <></>;
    },
    linkCellRendererParams: {
      links: (params) => {
        /** @type {ResultRowData} */
        const data = params.data;
        const label = `${data.vyperNumber}`;
        const url = location.origin + `/vyper/projects/${data.vyperNumber}`;
        return { label, url };
      },
    },
  },
  {
    headerName: "Build Number",
    field: "buildNumber",
    cellRenderer: selectCompRenderer,
    linkCellRendererParams: {
      links: (params) => {
        const rowData = params.data;
        let label;
        let url;

        if (rowData == null) {
          url = null;
          label = null;
        } else if (rowData.buildNumber.includes("PRA")) {
          url =
            location.origin +
            `/vyper/projects/${rowData.vyperNumber}/pras/${rowData.buildNumber}`;
        } else if (rowData.buildNumber.includes("VBUILD")) {
          url =
            location.origin +
            `/vyper/projects/${rowData.vyperNumber}/builds/${rowData.buildNumber}/selection`;
        }
        else {
          url =
            location.origin +
            `/vyper/atssmassupload/projects/actions/${rowData.muProjectId}`;
        }
        label = rowData.buildNumber;

        return { label, url };
      },
    },
  },
  { headerName: "Device", field: "device" },
  { headerName: "Description", field: "description" },
  { headerName: "Title", field: "title" },
  { headerName: "VYPER Submitter", field: "creator" },
  { headerName: "Status", field: "status" },
  {
    headerName: "VYPER Owners",
    field: "owners",
    cellRenderer: hideUserId,
  },
  {
    headerName: "Group",
    field: "group",
    cellStyle: { "white-space": "normal" },
    width: 220,
  },
  { headerName: "Created Date", field: "dateCreated" },
  { headerName: "Changed Date", field: "dateUpdated" },
];

const defaultColDef = {
  autoHeight: true,
};

function groupByFncUuid(array) {
  return array.reduce((objectsByKeyValue, obj) => {
    const value = `${obj["taskUuid"]}~${obj["fnctName"]}`;
    objectsByKeyValue[value] = (objectsByKeyValue[value] || []).concat(obj);
    return objectsByKeyValue;
  }, {});
}

function generateRowData(assignment, tasks) {
  const { taskUuid, fnctName } = assignment;
  const {
    taskName,
    creatorName,
    metaList = [],
    stateName,
    createdDttm,
    modifiedDttm,
  } = tasks[taskUuid];
  const metadata = new Map();
  metaList.reduce((map,obj) => {
    map.set(obj.attrName, obj.attrValue);
    return map;
  }, metadata);
  const vyperNumber = taskName.includes("PRA")
    ? convertPraNumbertoVyperNumber(taskName)
    : taskName.includes("VBUILD") ? convertBuildNumbertoVyperNumber(taskName) : taskName.includes("VSCN") ? convertVscnNumbertoVyperNumber(taskName) : metadata.get("ref vyper number");
  return {
    vyperNumber: vyperNumber,
    buildNumber: taskName,
    muProjectId: metadata.get("project id"),
    creator: creatorName,
    description: metadata.get("description"),
    device: metadata.get("device id"),
    group: fnctName,
    owners: metadata.get("owners") == undefined ? metadata.get("owner") : metadata.get("owners"),
    title: metadata.get("title"),
    status: stateName?.toLowerCase()?.includes("approved")
      ? "Approved"
      : "Pending",
    dateCreated: formatDate(createdDttm),
    dateUpdated: formatDate(modifiedDttm),
  };
}

function getUniqueTasks(assignments) {
  let uniqueTasks = new Set();
  assignments.forEach((assignment) => {
    uniqueTasks.add(assignment.taskUuid);
  });
  return [...uniqueTasks];
}

export const MyApprovalsPage = () => {
  const [filterState, setFilterState] = useLocalStorage(
    "tableFilterStates.myApprovalsState",
    {}
  );
  const [hasTableLoaded, setHasTableLoaded] = useState(false);
  const [gridRef, setGridRef] = useState(null);

  const [checkedValue, setCheckedValue] = useState(false);

  const [pending, setPending] = useState([]);
  const [prevApproved, setPrevApproved] = useState([]);

  const onFirstDataRendered = (api) => {
    setGridRef(api);
    setHasTableLoaded(true);
  };

  const handleSwitch = () => {
    setCheckedValue((checkedValue) => !checkedValue);
  };

  useEffect(() => {
    let assignments = fetchUserAssignments(`?current=true&appUuid=${taskSvcEnv.appUuid}`).then(
      (assignments) =>
        assignments.filter((assignment) => !assignment.fnctSatisfied)
    );

    let tasks = assignments.then(getUniqueTasks).then((uniqueTasks) => {
      return fetchUserTaskInfo(uniqueTasks).then((tasks) => {
        return tasks.reduce((acc, cur) => {
          acc[cur.taskUuid] = cur;
          return acc;
        }, {});
      });
    });

    Promise.all([assignments, tasks])
      .then(([assignments, tasks]) => {
        return assignments.map((assignment) =>
          generateRowData(assignment, tasks)
        );
      })
      .then(setPending);
  }, []);

  useEffect(() => {
    let assignments = fetchUserAssignments(`?appUuid=${taskSvcEnv.appUuid}`)
      .then((userAssignments) => groupByFncUuid(userAssignments)) // filter by group
      .then((groupedAssignments) => {
        // sort by latest
        return Object.entries(groupedAssignments)
          .map(([key, value]) => {
            return value.sort((a, b) =>
              a.modifiedDttm > b.modifiedDttm
                ? -1
                : b.modifiedDttm > a.modifiedDttm
                ? 1
                : 0
            )[0];
          })
          .filter(
            (groupedSorted) =>
              groupedSorted.complete &&
              !groupedSorted?.branchName?.includes("rework")
          );
      });

    let tasks = assignments.then(getUniqueTasks).then((uniqueTasks) => {
      return fetchUserTaskInfo(uniqueTasks).then((tasks) => {
        return tasks.reduce((acc, cur) => {
          acc[cur.taskUuid] = cur;
          return acc;
        }, {});
      });
    });

    Promise.all([assignments, tasks])
      .then(([assignments, tasks]) => {
        return assignments.map((assignment) =>
          generateRowData(assignment, tasks)
        );
      })
      .then(setPrevApproved);
  }, []);

  const exportAsExcel = () => {
    const api = gridRef.api;
    const columnApi = gridRef.columnApi;
    exportGridAsExcel(api, columnApi, {
      fileName: `My_${!checkedValue ? "Pending" : "Approved"}_Vyper.xlsx`,
    });
  };

  return (
    <div>
      <Grid component="label" container item alignItems="center" spacing={0}>
        <Grid item>My Pending</Grid>
        <Grid item>
          <Switch
            checked={checkedValue}
            onChange={handleSwitch}
            color="secondary"
          />
        </Grid>
        <Grid item>My Approved</Grid>
      </Grid>
      <div
        style={{
          fontSize: "1.25rem",
          fontWeight: "bold",
          marginBottom: "5px",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        My {!checkedValue ? "Pending" : "Approved"}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Button
            variant="contained"
            onClick={() => {
              gridRef.api.setFilterModel({});
            }}
            color="secondary"
            style={{ marginRight: "10px" }}
          >
            Clear filters
          </Button>
          <Button variant="contained" onClick={exportAsExcel} color="secondary">
            Export as Excel
          </Button>
        </div>
      </div>
      <TiServerAgGrid
        columnDefs={columns}
        defaultColDef={defaultColDef}
        style={{ height }}
        paginationSelectOptions={[5, 10, 20]}
        defaultPageSize={20}
        getFilterModel={hasTableLoaded && setFilterState}
        onFirstDataRendered={onFirstDataRendered}
        initialFilterModel={filterState}
        rowModelType={"clientSide"}
        rowData={!checkedValue ? pending : prevApproved}
      />
    </div>
  );
};
