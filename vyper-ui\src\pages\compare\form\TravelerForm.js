import React, { useContext, useState } from "react";
import {
  <PERSON>ton,
  FormControlLabel,
  FormGroup,
  makeStyles,
} from "@material-ui/core";
import { useLocalStorage } from "../../../component/hooks/useLocalStorage";
import { BuildNumberAutocomplete } from "../../../autocomplete/components/BuildNumberAutocomplete";
import { DifferenceHeader } from "../difference/DifferenceHeader";
import { Difference } from "../difference/Difference";
import { CompareDao } from "src/dao/CompareDao";
import {
  AlertDialogErrorHandler,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { SpinnerContext } from "src/component/Spinner";
import { noop } from "src/component/vyper/noop";

const useStyles = makeStyles((theme) => ({
  root: {},
  form: {},
  widget: {
    marginRight: "3rem",
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
  },
}));

export const TravelerForm = () => {
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);

  const [buildNumber1, setBuildNumber1] = useLocalStorage(
    "compare.traveler.build1"
  );
  const [buildNumber2, setBuildNumber2] = useLocalStorage(
    "compare.traveler.build2"
  );

  const [header, setHeader] = useState([]);
  const [differences, setDifferences] = useState();
  const [labels, setLabels] = useState({ left: "Left", right: "Right" });

  const compareDao = new CompareDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    setHeader([
      { label: "Vyper Build Number 1", value: buildNumber1 },
      { label: "Vyper Build Number 2", value: buildNumber2 },
    ]);

    setLabels({ left: "Build 1", right: "Build2" });

    compareDao
      .traveler(buildNumber1, buildNumber2, null, null)
      .then((json) => setDifferences(json.differences))
      .catch(noop);
  };

  const enableButton = buildNumber1 != null && buildNumber2 != null;

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <form className={classes.form} onSubmit={handleSubmit}>
        <FormGroup row>
          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                id="build-traveler-build1"
                label="Build Number 1"
                variant="outlined"
                aria-describedby="build1-text"
                defaultNumber={buildNumber1}
                onSelect={(number) => setBuildNumber1(number)}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <BuildNumberAutocomplete
                id="build-traveler-build2"
                label="Build Number 2"
                variant="outlined"
                aria-describedby="build2-text"
                defaultNumber={buildNumber2}
                onSelect={(number) => setBuildNumber2(number)}
              />
            }
          />

          <FormControlLabel
            className={classes.widget}
            label=""
            control={
              <Button
                type="submit"
                color="primary"
                variant="contained"
                disabled={!enableButton}
              >
                Compare
              </Button>
            }
          />
        </FormGroup>
      </form>

      <DifferenceHeader items={header} />

      <Difference
        label1={labels.left}
        label2={labels.right}
        differences={differences}
      />
    </div>
  );
};
