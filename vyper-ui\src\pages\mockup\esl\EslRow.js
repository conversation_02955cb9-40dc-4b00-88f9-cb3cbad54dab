import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext, useEffect } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { QuestionDialogContext } from "../../../component/question/QuestionDialog";
import { samenessEsl } from "../../../component/sameness/sameness";
import { EslCell } from "./EslCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

/**
 * Create the row to display the esl information
 *
 * @param vyper
 * @param builds
 * @param onChange
 * @param showSameness
 * @returns {JSX.Element}
 * @constructor
 */
export const EslRow = ({ vyper, builds, onChange, showSameness }) => {
  const { buildDao, eslDao, esls } = useContext(DataModelsContext);
  const { showQuestionDialog } = useContext(QuestionDialogContext);

  useEffect(() => {
    eslDao.list().catch(noop);
  }, []);

  const handleChangeEsl = (value, build) => {
    return buildDao
      .changeEsl(vyper.vyperNumber, build.buildNumber, value)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    showQuestionDialog({
      type: "select",
      title: "Select Extended Shelf Life",
      description: `Select the ESL value for this build.`,
      value: build.esl?.object?.value,
      multiline: false,
      rows: 10,
      options: esls.map((e) => e.value),
      onSave: (value) => handleChangeEsl(value, build),
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessEsl(builds),
      })}
      hover
    >
      <RowPrefix help="esl" title="ESL" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <EslCell vyper={vyper} build={build} onClick={handleClick} />
        </TableCell>
      ))}
    </TableRow>
  );
};
