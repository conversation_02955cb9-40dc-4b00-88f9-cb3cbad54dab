import React, { useEffect, useState } from "react";

import { Switch, FormGroup, FormControlLabel, Paper, Button } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import ColumnMassReviewScreen from "./ColumnMassReviewScreen";
import RowMassReviewScreen from "./RowMassReviewScreen";
import {
  Link,
  useParams,
  useHistory,
} from "react-router-dom/cjs/react-router-dom";

const useStyles = makeStyles((theme) => ({
  flexRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent:"space-between",
    alignItems: "center",
    gap: "0.8rem",
    flexWrap: "wrap",
    margin: "0.1rem 0"    
  },
  paper: {
    padding: "0.2rem",
  },
}));

const MassReviewScreen = (props) => {
  const history = useHistory();
  const { projectId } = useParams();
  const [travelerData, setTravelerData] = useState();
  const [unifiedView, setUnifiedView] = useState(false);
  const [travelerMode, setTravelerMode] = useState("VYPER");
  const [comparisonType, setComparisonType] = useState("COLUMN");
  const [isLoading, setIsLoading] = useState(false);
  const [comparisionConfig, setComparisionConfig] = useState({
    flows: ["HEADER", "ASSEMBLY", "TEST", "PACK"],
    operations: [],
    components: [],
  });
  const classes = useStyles();

  const handleChange = (event) => {
    setUnifiedView(event.target.checked);
  };
  const handleTravelerModeChange = (event) => {
    setTravelerMode(event.target.checked ? "VYPER" : "ATSS");
  };
  const handleComparisonTypeChange = (event) => {
    setComparisonType(event.target.checked ? "COLUMN" : "ROW");
  };

  useEffect(() => {
    setIsLoading(true);
    fetch(
      `/vyper/v1/atssmassupload/traveler/${projectId}?travelerMode=${travelerMode}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      }
    )
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        setTravelerData(data);
        setIsLoading(false);
      });
    return () => {
      setIsLoading(false);
    };
  }, [travelerMode]);
  if (isLoading) return <div>Loading...</div>;
  if (!travelerData) return <div>No data...</div>;
  return (
    <div>
      <div className={classes.flexRow}>
        <h4>
          Project Travelers comparision for Project ID :{" "}
          <span>
            <Link
            onClick={(e) => {
            e.preventDefault();
            history.goBack();
            }}
            >
            {travelerData.projectHeader?.projNumber}
            </Link>
          </span>
        </h4>
        <Button
          variant="contained"
          color="secondary"
          onClick={() => history.push(`/atssmassupload/project/${travelerData.projectHeader?.projNumber}/specchanges`)}
        >
          Back to Upload
        </Button>
      </div>
      <div className={classes.flexRow}>
        <Paper elevation={0} className={classes.paper}>
          <FormGroup row>
            <FormControlLabel
              style={{
                width: "fit-content",
              }}
              control={
                <Switch
                  checked={travelerMode == "VYPER"}
                  onChange={handleTravelerModeChange}
                  inputProps={{ "aria-label": "controlled" }}
                />
              }
              label={`Switch to ${travelerMode === "VYPER" ? "ATSS" : "VYPER"}`}
            />
            <FormControlLabel
              style={{
                width: "fit-content",
              }}
              control={
                <Switch
                  checked={unifiedView}
                  onChange={handleChange}
                  inputProps={{ "aria-label": "controlled" }}
                />
              }
              label="Toggle Unified View"
            />
            <FormControlLabel
              style={{
                width: "fit-content",
              }}
              control={
                <Switch
                  checked={comparisonType === "COLUMN"}
                  onChange={handleComparisonTypeChange}
                  inputProps={{ "aria-label": "controlled" }}
                />
              }
              label={`Switch to ${
                comparisonType === "COLUMN" ? "ROW" : "COLUMN"
              } View`}
            />
          </FormGroup>
        </Paper>
        <Paper elevation={0} className={classes.paper}>
          <b> Legends: </b>
          <div className="legend" style={{ backgroundColor: "yellow" }}>
            {" "}
          </div>{" "}
          Add{" "}
          <div className="legend" style={{ backgroundColor: "#fcc" }}>
            {" "}
          </div>{" "}
          Obsolete{" "}
          <div className="legend" style={{ backgroundColor: "#cfc" }}>
            {" "}
          </div>{" "}
          Change
        </Paper>
      </div>
      
      
      <div className="comparision-view">
        {comparisonType === "ROW" ? (
          <RowMassReviewScreen
            unifiedView={unifiedView}
            travelerData={travelerData}
            comparisionConfig={comparisionConfig}
          />
        ) : (
          <ColumnMassReviewScreen
            unifiedView={unifiedView}
            travelerData={travelerData}
            comparisionConfig={comparisionConfig}
          />
        )}
      </div>
    </div>
  );
};
export default MassReviewScreen;
