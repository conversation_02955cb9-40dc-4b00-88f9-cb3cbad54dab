import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import React, { useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import PriorityHighIcon from "@material-ui/icons/PriorityHigh";
import { getMessages } from "src/pages/mockup/armarc_checker/armarcChecker";

const useStyles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    padding: theme.spacing(1),
  },
  message: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "flex-end",
    gap: 3,
    marginBottom: "12px",
  },
}));

export const ArmArcCheckerDialog = ({
  open,
  onClose,
  build,
  button1Text,
  onButton1,
  button2Text,
  onButton2,
}) => {
  const classes = useStyles();
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    if (open && build != null) {
      setMessages(getMessages(build));
    }
  }, [open, build]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className={classes.title}>
        ArmArc Checker Results
      </DialogTitle>

      <DialogContent>
        {messages.length === 0 ? (
          <h3>There are no mismatches for ARM/ARC values.</h3>
        ) : (
          <h3>These build values do not match the ARM/ARC values:</h3>
        )}

        {messages.map((m) => (
          <div key={m} className={classes.message}>
            <PriorityHighIcon />
            <div>{m}</div>
          </div>
        ))}
      </DialogContent>

      <DialogActions>
        {button1Text && (
          <Button variant="contained" color="primary" onClick={onButton1}>
            {button1Text}
          </Button>
        )}
        {button2Text && (
          <Button variant="contained" color="primary" onClick={onButton2}>
            {button2Text}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};
