/**
 * Filter and sort the builds
 * @param builds Array of builds
 * @param currentFilters The filters
 * @returns {*[]} The processed array of builds
 */
export const filterComments = (comments, currentFilters) => {
  // if comments is invalid or empty, return empty list
  if (comments == null || currentFilters == null || comments.length === 0) {
    return [];
  }

  // make a copy of the builds
  let newComments = [...comments].reverse();

  // filter the facilities
  if (currentFilters.operation !== "All Comments") {
    newComments = newComments.filter(
      (c) => c.operation === currentFilters.operation
    );
  }

  return newComments;
};
