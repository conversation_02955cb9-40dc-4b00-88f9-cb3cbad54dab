/**
 *
 * @typedef {Object} Vyper
 * @property {string} vyperNumber
 *
 */

/**
 *
 * @typedef {Object} Build
 * @propety {string} buildNumber
 *
 */

/**
 * A list of possible symbolization texts for a package and facility
 * @typedef {Object} Symbol
 * @property {SymbolObject} object
 * @property {Source} source
 */

/**
 * A symbolization object - the location, name and text.
 *
 * @typedef {Object} SymbolObject
 * @property {SymbolLocation} location - The location of the symbol (TOP or BOTTOM)
 * @property {string} name - the component value of the symbol
 * @property {string} picture - shows the original template paragraph text
 * @property {string} display - paragraph text with ecat and custx values filled in
 */

/**
 * A enum showing where on the device the symbol is printed (TOP or BOTTOM)
 * @typedef SymbolLocation
 * @readonly
 * @enum {string}
 */

/**
 * The source of the information. It can be a system (like ATSS) or a specific user.
 * @typedef {Object} Source
 * @property {System} system
 * @property {User} user
 */

/**
 * An enumeration of the system that originated the data
 * @typedef System
 * @readonly
 * @enum {string}
 */

/**
 * The user who set the data
 * @typedef {Object} User
 * @property {string} userid
 * @property {string} username
 */

/**
 * @typedef LenAndCustName
 * @property {number} maxLen
 * @property {string} custFieldName
 */
