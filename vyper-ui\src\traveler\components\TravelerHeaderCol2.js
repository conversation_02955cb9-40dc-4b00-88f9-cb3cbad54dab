import PropTypes from "prop-types";
import React from "react";

/**
 * Returns the formatted value for the 2nd column.
 * @param {* | null} value - the value to display. nulls are converted to empty strings.
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerHeaderCol2({ value }) {
  return <>{(value == null ? "" : value.toString()).padEnd(31, " ")}</>;
}

TravelerHeaderCol2.propTypes = {
  value: PropTypes.any,
};
