import React from "react";
import CommentIcon from "@material-ui/icons/Comment";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { IconButton, Tooltip, Zoom } from "@material-ui/core";

const useStyles = makeStyles({
  root: {
    display: "inline",
  },
});

export const OperationCommentButton = ({
  title,
  disabled,
  required,
  onClick,
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Tooltip
        title={title}
        placement="top"
        TransitionComponent={Zoom}
        arrow
        interactive
      >
        <span>
          {required ? (
            <IconButton disabled size="small">
              <CommentIcon fontSize="small" />
            </IconButton>
          ) : (
            <IconButton
              disabled={disabled}
              color="primary"
              size="small"
              onClick={onClick}
            >
              <CommentIcon fontSize="small" />
            </IconButton>
          )}
        </span>
      </Tooltip>
    </div>
  );
};
