import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  IconButton,
} from "@material-ui/core";
import { DataGrid } from "src/component/universal";
import ArrowRightAlt from "@material-ui/icons/ArrowRightAlt";

export const AddPraDialog = ({ children }) => {
  const [selectedBuild, setSelectedBuild] = useState(undefined);
  const [open, setOpen] = useState(false);
  const [addNewPra, setAddNewPra] = useState(undefined);

  const onBuildSelect = (e, row) => {
    setSelectedBuild(row.buildNumber);
  };
  const handleOpen = ({ onAddPra }) => {
    setAddNewPra(() => onAddPra);
    setOpen(true);
  };
  const handleClickCancel = () => {
    setOpen(false);
    resetValues();
  };
  const resetValues = () => {
    setSelectedBuild(undefined);
  };
  // This is the onsubmit function
  const handleClickSave = () => {
    resetValues();
    setOpen(false);
    addNewPra(selectedBuild);
  };
  return (
    <AddPraDialogContext.Provider
      value={{
        openPraDialog: handleOpen,
      }}
    >
      <Dialog open={open} maxWidth="lg">
        <DialogTitle>Add PRA</DialogTitle>
        <DialogContent>
          <SelectBuild onBuildSelect={onBuildSelect} />
          {selectedBuild ? (
            <>
              <p>Build Selected: </p>
              <p>{selectedBuild}</p>
            </>
          ) : null}
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleClickCancel}
          >
            Cancel
          </Button>
          <Button
            disabled={selectedBuild == null}
            variant="contained"
            color="primary"
            onClick={handleClickSave}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </AddPraDialogContext.Provider>
  );
};

export const AddPraDialogContext = React.createContext(null);

const SelectBuild = ({ onBuildSelect }) => {
  const columns = [
    { title: "Build Number", field: "buildNumber" },
    { title: "Build Type", field: "buildtype" },
    { title: "Device", field: "material" },
    { title: "Facility", field: "facilityAt" },
    { title: "Title", field: "title" },
    { title: "State", field: "state" },
  ];

  return (
    <div>
      <DataGrid
        title={<div>Build</div>}
        columns={columns}
        url={`/vyper/v1/vyper/project/device/?filter=state|contains|FINAL_APPROVED,buildtype|<>|Experimental`}
        pageable
        pageSize={5}
        pageSizeOptions={[5, 10, 20, 50, 100, 200]}
        options={{
          search: false,
        }}
        actions={[
          {
            icon: () => (
              <IconButton color="primary" size="small">
                <ArrowRightAlt />
              </IconButton>
            ),
            tooltip: "Select",
            onClick: onBuildSelect,
          },
        ]}
      />
    </div>
  );
};
