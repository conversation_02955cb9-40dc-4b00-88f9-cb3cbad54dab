import React from "react";
import { IconButton, Tooltip } from "@material-ui/core";
import VisibilityIcon from "@material-ui/icons/Visibility";

export const tableOptions = [
  { value: "atApprovalSwr", label: "Save for AT Approval" },
  { value: "submitSwr", label: "Submit" },
  { value: "saveSwr", label: "Save" },
  { value: "voidFcstSwr", label: "Void Forecast" },
  { value: "voidSwr", label: "Void" },
];

export const columnDefs = [
  {
    field: "Actions",
    checkboxSelection: true,
    headerName: "Action(s)",
    flex: 0,
    headerCheckboxSelection: true,
    cellRenderer: (params) => {
      let scswrID = params.data.SWR_ID;
      return (
        <Tooltip title={"View SWR Changes"} placement={"top"} arrow>
          <IconButton
            color="secondary"
            size={"large"}
            href={`/vyper/batch/forecastsbeview/${scswrID}`}
            target="_blank"
          >
            <VisibilityIcon />
          </IconButton>
        </Tooltip>
      );
    },
    filter: false,
  },
  { field: "SWR_ID", headerName: "SWR ID", flex: 1 },
  { field: "CURRENT_STATUS", headerName: "Status", flex: 1 },
  { field: "TITLE", headerName: "SWR Title", flex: 1 },
  { field: "DEVICE_NAME", headerName: "Matl Master Device Name", flex: 1 },
];
