import React from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/styles";
import { DataCell } from "src/component/datacell/DataCell";

const useStyles = makeStyles(() => ({
  highlight: {
    backgroundColor: "hsla(60, 100%, 80%, 1)",
  },
}));

/**
 * description
 * @param {object} vyper
 * @param {object} build
 * @return {JSX.Element}
 * @constructor
 */
export const VyperTemplateCell = ({ vyper, build }) => {
  const classes = useStyles();

  const bomTemplate = build.bomTemplate;
  const copy = build.templateSource.vyperBuildNumber;

  return (
    <DataCell source={bomTemplate.source}>
      <div className={classes.highlight}>Vyper Copy Template</div>
      <div>{copy}</div>
    </DataCell>
  );
};

VyperTemplateCell.propTypes = {
  vyper: PropTypes.object.isRequired,
  build: PropTypes.object.isRequired,
};
