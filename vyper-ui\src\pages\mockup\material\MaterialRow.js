import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { NewBuildDialogContext } from "src/component/newBuild/NewBuildDialog";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessDevice } from "../../../component/sameness/sameness";
import { MaterialCell } from "./MaterialCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const MaterialRow = ({ vyper, builds, onChange, showSameness }) => {
  const { buildDao } = useContext(DataModelsContext);
  const { openNewBuildDialog } = useContext(NewBuildDialogContext);

  const handleClick = (build) => {
    openNewBuildDialog({
      vyperNumber: vyper.vyperNumber,
      buildNumber: build.buildNumber,
      device: build.material.object,
      facility: build.facility.object.PDBFacility,
      multiBuild: build.multiBuild?.multiBuild,
      specDevice: build.multiBuild?.specDevice,
      description: build.description,
      buildType: build.buildtype,
      onSave: (
        mode,
        vyperNumber,
        buildNumber,
        material,
        facility,
        multiBuild,
        specDevice,
        description,
        buildType,
        copyBuildNumber
      ) => {
        return buildDao
          .updateBuild(
            mode,
            vyperNumber,
            buildNumber,
            material,
            facility,
            multiBuild,
            specDevice,
            description,
            buildType,
            copyBuildNumber
          )
          .then((json) => onChange(json))
          .catch(noop);
      },
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessDevice(builds),
      })}
      hover
    >
      <RowPrefix help="device" title="Device" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <MaterialCell vyper={vyper} build={build} onClick={handleClick} />
        </TableCell>
      ))}
    </TableRow>
  );
};
