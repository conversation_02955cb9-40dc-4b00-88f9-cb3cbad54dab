import React, { useContext, useEffect } from "react";
import { RouteManager, RouteNotFound } from "src/component/common";
import { VyperSelectPage } from "src/pages/select/VyperSelectPage";
import { VyperSummaryPage } from "src/pages/vyper/builds/VyperSummaryPage";
import { VyperTravelerPage } from "src/pages/mockup/traveler/VyperTravelerPage";
import { VyperTravelerPlainPage } from "src/pages/mockup/traveler/VyperTravelerPlainPage";
import { ApprovalsHistory } from "src/pages/vyper/builds/ApprovalsHistory";
import { VyperBomTemplatePage } from "src/pages/vyper/builds/VyperBomTemplatePage";
import { VyperFlowPage } from "src/pages/vyper/builds/VyperFlowPage";
import { Route, Switch, useParams } from "react-router-dom";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { VyperBuildFormPage } from "src/pages/vyper/builds/VyperBuildFormPage";

export const VyperBuildPage = ({ vyperNumber }) => {
  const { buildDao } = useContext(DataModelsContext);

  const { buildNumber } = useParams();

  // retrieve the build and store in the cache
  useEffect(() => {
    buildDao.findByBuildNumber(buildNumber).catch(noop);
  }, [buildNumber]);

  const params = { vyperNumber, buildNumber };

  return (
    <Switch>
      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/selection"
        render={() => {
          return <VyperSelectPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/summary"
        render={() => {
          return <VyperSummaryPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/traveler"
        render={() => {
          return <VyperTravelerPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/plain"
        render={() => {
          return <VyperTravelerPlainPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/approvalsHistory"
        render={() => {
          return (
            <ApprovalsHistory
              {...params}
              contextKey={`Vyper Approval Flow~buildNumber~${buildNumber}`}
            />
          );
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/bomtemplate"
        render={() => {
          return <VyperBomTemplatePage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber/flow"
        render={() => {
          return <VyperFlowPage {...params} />;
        }}
      />

      <Route
        exact
        path="/projects/:vyperNumber/builds/:buildNumber"
        render={() => {
          return <VyperBuildFormPage {...params} />;
        }}
      />

      <RouteManager component={RouteNotFound} />
    </Switch>
  );
};
