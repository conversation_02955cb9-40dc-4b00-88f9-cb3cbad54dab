import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { SingleSelectionDialogContext } from "../../../component/component/SingleSelectionDialog";
import { VyperLink } from "../../../pages/mockup/VyperLink";
import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles(() => ({
  root: {
    display: "flex",
    justifyItems: "flex-start",
    alignItems: "center",
  },
  center: {
    display: "flex",
    alignItems: "center",
    flexDirection: "column",
  },
  icons: {
    minWidth: 150,
  },
}));

function ChangeSelectCell(props) {
  const classes = useStyles();

  const { selectedChange, changeData, onClick } = props;
  const { openSingleSelectionDialog } = useContext(
    SingleSelectionDialogContext
  );

  const columns = [
    { field: "changeNumber", title: "Change Number" },
    { field: "changeType", title: "Change Type" },
    { field: "changeOwner", title: "Change Owner" },
    { field: "projectName", title: "Project Name" },
    { field: "changeTitle", title: "Change Title" },
  ];

  const handleChangeLinkChange = ( changeNumber ) => {
    onClick(changeNumber);
  };

  const handleOpen = (data) => {
    openSingleSelectionDialog({
      columns,
      data: data,
      title: "Select ChangeLink Number",
      handleSelect: (event, data) =>
        handleChangeLinkChange(data?.changeNumber),
    });
  };
  const canEdit = true;
  return (
    <div className={classes.root}>
      <VyperLink
        onClick={() => {
          handleOpen(changeData);
        }}
        canEdit={canEdit}
      >
        {!selectedChange ? "click to select" : selectedChange}
      </VyperLink>
    </div>
  );
}

export default ChangeSelectCell;
