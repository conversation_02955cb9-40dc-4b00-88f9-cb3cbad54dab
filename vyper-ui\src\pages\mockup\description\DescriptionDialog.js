import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import CloseIcon from "@material-ui/icons/Close";
import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";

const styles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    paddingTop: 4,
    paddingBottom: 4,
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "#ffffff",
  },
}));

export function DescriptionDialog({
  open,
  initialDescription,
  onSave,
  onClose,
}) {
  const [description, setDescription] = useState("");

  const classes = styles();

  // when opened, set the description to the initial description
  useEffect(() => {
    if (open) {
      setDescription(initialDescription || "");
    }
  }, [open]);

  function handleChange(e) {
    setDescription(e.target.value);
  }

  function handleSave() {
    onSave(description);
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl">
      <DialogTitle className={classes.title}>Edit The Description</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent>
        <TextField
          autoFocus
          fullWidth
          multiline
          minRows={10}
          margin="dense"
          name="value"
          value={description}
          onChange={handleChange}
          variant="outlined"
        />
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="secondary" onClick={onClose}>
          Cancel
        </Button>
        <Button variant="contained" color="primary" onClick={handleSave}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}

DescriptionDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  initialDescription: PropTypes.string,
  onSave: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};
