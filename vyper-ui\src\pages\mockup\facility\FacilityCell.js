import React, { useContext } from "react";
import { hasMaterial } from "../../vyper/FormStatus";
import { DataCell } from "../../../component/datacell/DataCell";
import { VyperLink } from "../VyperLink";
import { Highlight } from "../../../component/highlight/Highlight";
import { HelperContext } from "src/component/helper/Helpers";

export const FacilityCell = ({ vyper, build, onClick }) => {
  const { canEditFacility } = useContext(HelperContext);
  const canEdit = canEditFacility(vyper, build);

  if (!hasMaterial(build)) return null;

  const text =
    build.facility?.object?.PDBFacility == null
      ? "click to select"
      : build.facility?.object?.PDBFacility;

  return (
    <DataCell source={build.facility?.source}>
      <VyperLink onClick={() => onClick(build)} canEdit={canEdit}>
        {text}
      </VyperLink>
    </DataCell>
  );
};
