import React, { useEffect } from "react";
import PropTypes from "prop-types";
import MaterialTable from "material-table";
import Refresh from "@material-ui/icons/Refresh";
import { default as tableIcons } from "./icons";
//import { fetchGet } from "../../utils";
import { tableConfig, editableStyleOptions } from "./defaults";
import { refreshData } from "./pageable";

const ClientEditableDataGrid = ({
  actions = [],
  columns = [],
  data = [],
  detailPanel = [],
  handleSelect,
  selectable = false,
  title = "",
  setData,
  forUpdate,

  onRowAdd,
  onRowUpdate,
  onRowDelete,
  options,
  editable,
  ...rest
}) => {
  return (
    <MaterialTable
      minRows={tableConfig.minRows}
      actions={actions}
      columns={columns}
      data={data}
      detailPanel={detailPanel}
      icons={tableIcons}
      onSelectionChange={selectable ? (rows) => handleSelect(rows) : null}
      options={{
        ...editableStyleOptions,
        ...{ selection: selectable },
        ...{ filtering: tableConfig.filtering },
        ...options,
      }}
      title={title}
      editable={{
        onRowAdd: onRowAdd,
        onRowUpdate: onRowUpdate,
        onRowDelete: onRowDelete,

        // onRowUpdate: (newData, oldData) =>
        //   new Promise((resolve, reject) => {
        //     setTimeout(() => {
        //       const dataForUpdate = newData;
        //       const dataUpdate = [...data];
        //       const index = oldData.tableData.id;
        //       dataUpdate[index] = dataForUpdate;
        //       setData([...dataUpdate]);
        //       forUpdate(dataForUpdate);
        //       resolve();
        //     }, 1000);
        //   }),
      }}
      {...rest}
    />
  );
};

const ServerEditableDataGrid = ({
  actions = [],
  columns = [],
  detailPanel = [],
  handleSelect,
  handleInsert,
  handleUpdate,
  pageable = false,
  selectable = false,
  timestamp = null,
  title = "",
  url,
  pageSizeOptions,
  ...rest
}) => {
  const tableRef = React.createRef();
  const refreshAction = [
    {
      icon: () => <Refresh />,
      tooltip: "Refresh Data",
      isFreeAction: true,
      onClick: () => tableRef.current && tableRef.current.onQueryChange(),
    },
  ];

  useEffect(() => {
    tableRef.current && tableRef.current.onQueryChange();
  }, [url, timestamp]);

  return (
    <MaterialTable
      minRows={tableConfig.minRows}
      actions={[...refreshAction, ...actions]}
      columns={columns}
      data={(query) =>
        new Promise((resolve, reject) => {
          refreshData(url, query, resolve, reject, pageable);
        })
      }
      detailPanel={detailPanel}
      icons={tableIcons}
      onSelectionChange={selectable ? (rows) => handleSelect(rows) : null}
      options={{
        ...editableStyleOptions,
        ...{ selection: selectable },
        ...{ filtering: tableConfig.filtering },
        ...{ pageSize: tableConfig.defaultPageSize },
        ...{
          pageSizeOptions: pageSizeOptions
            ? pageSizeOptions
            : tableConfig.defaultPageSizeOptions,
        },
        ...{ defaultExpanded: true },
      }}
      title={title}
      editable={{
        onRowAdd: (newData) =>
          new Promise((resolve, reject) => {
            if (typeof handleInsert === "function") {
              handleInsert(newData, undefined, resolve());
            } else {
              reject();
            }
          }),
        onRowUpdate: (newData, oldData) =>
          new Promise((resolve, reject) => {
            if (typeof handleUpdate === "function") {
              handleUpdate(newData, oldData, resolve());
            } else {
              reject();
            }
          }),
      }}
      {...rest}
    />
  );
};

ClientEditableDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  data: PropTypes.array.isRequired,
  detailPanel: PropTypes.any,
  forUpdate: PropTypes.func,
  handleSelect: PropTypes.func,
  selectable: PropTypes.bool,
  setData: PropTypes.func,
  title: PropTypes.string,
};

ServerEditableDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  detailPanel: PropTypes.any,
  forUpdate: PropTypes.func,
  handleSelect: PropTypes.func,
  pageable: PropTypes.bool,
  selectable: PropTypes.bool,
  setData: PropTypes.func,
  title: PropTypes.string,
  url: PropTypes.string,
};

export { ClientEditableDataGrid, ServerEditableDataGrid };
