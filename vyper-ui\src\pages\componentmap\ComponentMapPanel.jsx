import { makeStyles } from "@material-ui/core/styles";
import React, { useContext, useEffect, useMemo, useState } from "react";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import { ComponentMapSelector } from "src/pages/componentmap/ComponentMapSelector";
import { ComponentMapForm } from "src/pages/componentmap/ComponentMapForm";
import { useComponentMap } from "src/api/useComponentMap";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";

const useStyles = makeStyles({
  alignRight: {
    textAlign: "right",
  },
  field: {
    marginBottom: "1em",
  },
  header4: {
    borderBottom: "1px solid #CC0000",
  },
  split: {
    display: "flex",
    justifyContent: "flex-start",
    alignContent: "flex-start",
    gap: "2em",
  },
});

/////////////////////////////////////////////////////////////////////////////////////////////////////////////

export const ComponentMapPanel = () => {
  const { list, create, update } = useComponentMap();
  const { open: openError } = useContext(ErrorDialogContext);

  // the selected component
  const [name, setName] = useLocalStorage(
    "admin.index.componentmap.name",
    "Leadframe"
  );
  const [componentMaps, setComponentMaps] = useState([]);
  const [componentMap, setComponentMap] = useState();
  const classes = useStyles();

  // load the component maps
  useEffect(() => {
    list()
      .then(setComponentMaps)
      .catch((err) => openError({ title: "Fetch Component Maps", error: err }));
  }, [list]);

  // store the selected component map
  useEffect(() => {
    setComponentMap(componentMaps.find((cm) => cm.name === name));
  }, [name, componentMaps]);

  // get the component map names
  const names = useMemo(() => {
    return componentMaps
      .map((cm) => cm.name)
      .sort((a, b) => a.localeCompare(b));
  }, [componentMaps]);

  // store the selected component name
  const handleChangeComponent = (name) => {
    setName(name);
  };

  // create a new component
  const handleNew = () => {
    setComponentMap({
      id: null,
      name: "",
      atssComponentName: "",
      queryBuild: "AT,CAMS",
      querySelected: "CAMS_FACILITY_PINPKG,AT,CAMS",
      queryPra: "RECENT,BOM,CAMS_FACILITY_PINPKG,AT,CAMS",
      querySuffix: "SUFFIX",
      pgsMethod: "",
      pgsRefObjectType: "",
      pgsAttrToAtssComponentValue: "",
      pgsAttrToDisplayNameTranslateMethod: "",
      pgsAttrToDisplayName: "",
      pgsItemsType: "",
      pgsItemsAttributeKey: "",
      pgsAttributeName: "",
      allowFreeformTextForComponentChoice: "NO",
      engineeringAtssComponentName: "",
      scnIncludeInTraveler: "",
      componentChangeAlsoChangesSelection: "YES",
      selectionChangeAlsoChangesComponent: "YES",
      associatedComponentName: "",
      allowMultipleInstances: "YES",
      allowMultiplePriorities: "YES",
      requiredForSubmission: "NO",
      alternativeFillSource: "",
      displayOnPra: "NO",
      praPgsValidator: "NO",
      praArmarcValidator: "NO",
      praAtssGlobalValidator: "NO",
      praAtssAtValidator: "NO",
      praAtssBomValidator: "NO",
      praDieValidator: "NO",
      praDiagramValidator: "NO",
      valueShouldBe74: "NO",
      componentUniqueness: "MULTIPLE_VALUE",
      praBomQualifiedValidator: "NO",
      praPavvComponentValidator: "NO",
    });
  };

  // save the component
  const handleSave = (form) => {
    let promise;
    if (componentMap.id == null) {
      promise = create(form);
    } else {
      promise = update(componentMap.id, form);
    }

    return promise
      .then((updatedComponentMap) => {
        // append or update the component map

        if (componentMap.id == null) {
          // newly added - add to the end
          setComponentMaps((maps) => [...maps, updatedComponentMap]);
        } else {
          // updated - replace it in the array
          setComponentMaps((maps) =>
            maps.map((map) =>
              map.name === componentMap.name ? updatedComponentMap : map
            )
          );
        }

        return updatedComponentMap;
      })
      .then((map) => {
        setName(map.name);
        return map;
      });
  };

  return (
    <div>
      <h2>Component Map</h2>

      <div className={classes.split}>
        <ComponentMapSelector
          names={names}
          name={name}
          onChange={handleChangeComponent}
        />

        <ComponentMapForm
          componentMap={componentMap}
          onNew={handleNew}
          onSave={handleSave}
        />
      </div>
    </div>
  );
};

/////////////////////////////////////////////////////////////////////////////////////////////////////////////
