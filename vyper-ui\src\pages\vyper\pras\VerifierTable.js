import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@material-ui/core";
import React from "react";
import { MessageRow } from "src/pages/vyper/pras/MessageRow";

/**
 * Display the verifier messages as a table.
 *
 * @param verifier
 * @returns {JSX.Element}
 * @function
 */
export const VerifierTable = ({ verifier }) => {
  // only show the value2 column when there are rightName values in the messages
  const showValue2 = verifier.messages.some(
    (message) => message.rightName != null
  );

  return (
    <Table size="small">
      <TableHead>
        <TableRow hover>
          <TableCell>Status</TableCell>
          <TableCell>Text</TableCell>
          <TableCell>{verifier.leftTitle || "Value 1"}</TableCell>
          {showValue2 && (
            <TableCell>{verifier.rightTitle || "Value 2"}</TableCell>
          )}
        </TableRow>
      </TableHead>
      <TableBody>
        {verifier.messages.map((message, n) => (
          <MessageRow key={n} message={message} />
        ))}
      </TableBody>
    </Table>
  );
};
