import Button from "@material-ui/core/Button";
import Grid from "@material-ui/core/Grid";
import makeStyles from "@material-ui/core/styles/makeStyles";
import PropTypes from "prop-types";
import React, { useContext } from "react";
import { HelperContext } from "src/component/helper/Helpers";
import { NewBuildDialogContext } from "src/component/newBuild/NewBuildDialog";
import VyperFilter from "../../vyper/filter/VyperFilter";
import VyperPraModeSwitch from "../../vyper/pras/VyperPraModeSwitch";
import { Owners } from "../owners/Owners";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles((theme) => ({
  ProjectBox: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "stretch",
    padding: "0.5rem",
    marginBottom: "1rem",
    borderRadius: "1rem",
    paddingLeft: "1rem",
  },
  header: {
    marginTop: theme.spacing(3),
    fontWeight: "bold",
  },
  actions: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-evenly",
    alignItems: "flex-start",
    gap: "5px",
  },
}));

/**
 * Show the header on the Vyper Project Page
 *
 * @param vyper
 * @param canDeleteVyper
 * @param onDeleteVyper
 * @param currentFilters
 * @param onFilterChange
 * @param getFilterVyperData
 * @param onAddNewBuild
 * @param totalFilteredBuilds
 * @param totalBuilds
 * @returns {JSX.Element|null}
 * @constructor
 */
export const VyperHeaderBox = ({
  vyper,
  canDeleteVyper = false,
  onDeleteVyper,
  currentFilters,
  onFilterChange,
  getFilterVyperData,
  onAddNewBuild,
  totalFilteredBuilds,
  totalBuilds,
}) => {
  const { canEditOwners } = useContext(HelperContext);
  const { openNewBuildDialog } = useContext(NewBuildDialogContext);

  // can the current user edit this vyper?
  const canEdit = canEditOwners(vyper);

  const classes = useStyles();

  if (vyper == null) return null;

  // Link to download Excel
  let vyperExcelDownloadLink = `/vyper/v1/vyper/downloadVyperExcel/${vyper.vyperNumber}`;

  const handleClickAddBuild = () => {
    openNewBuildDialog({
      vyperNumber: vyper.vyperNumber,
      buildNumber: undefined,
      device: undefined,
      facility: undefined,
      multiBuild: false,
      specDevice: undefined,
      description: undefined,
      buildType: undefined,
      symbolChoice: undefined,
      onSave: (
        mode,
        vyperNumber,
        buildNumber,
        material,
        facility,
        multiBuild,
        specDevice,
        description,
        buildType,
        copyBuildNumber,
        symbolChoice
      ) => {
        return onAddNewBuild(
          mode,
          vyperNumber,
          buildNumber,
          material,
          facility,
          multiBuild,
          specDevice,
          description,
          buildType,
          copyBuildNumber,
          symbolChoice
        );
      },
    });
  };

  return (
    <div>
      <Grid container className={classes.ProjectBox + " header-box"}>
        <Grid item xs={4}>
          <div className={classes.header}>Owners</div>
          <Owners />
        </Grid>

        <Grid item xs={8}>
          <div className={classes.actions}>
            {canDeleteVyper && canEdit ? (
              <Button
                onClick={onDeleteVyper}
                variant="contained"
                color="secondary"
                size="small"
              >
                Delete Project
              </Button>
            ) : null}
            {canEdit && !externalUse ? (
              <Button
                onClick={handleClickAddBuild}
                variant="contained"
                color="secondary"
                size="small"
              >
                Add Build
              </Button>
            ) : null}
            <Button
              variant="contained"
              color="primary"
              size="small"
              href={vyperExcelDownloadLink}
            >
              Download To Excel
            </Button>
            <VyperPraModeSwitch vyperNumber={vyper.vyperNumber} />
          </div>
        </Grid>

        <Grid item xs={12}>
          <div className={classes.header}>Filters</div>
          <VyperFilter
            currentFilters={currentFilters}
            onChange={onFilterChange}
            getFilterVyperData={getFilterVyperData}
            totalFilteredBuilds={totalFilteredBuilds}
            totalBuilds={totalBuilds}
          />
        </Grid>
      </Grid>
    </div>
  );
};

VyperHeaderBox.propTypes = {
  vyper: PropTypes.object.isRequired,
  canDeleteVyper: PropTypes.bool.isRequired,
  onDeleteVyper: PropTypes.func.isRequired,
  currentFilters: PropTypes.object.isRequired,
  onFilterChange: PropTypes.func.isRequired,
  getFilterVyperData: PropTypes.func.isRequired,
  onAddNewBuild: PropTypes.func.isRequired,
  totalFilteredBuilds: PropTypes.number.isRequired,
  totalBuilds: PropTypes.number.isRequired,
};
