import React, { useEffect, useState, useContext } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { DataCell } from "src/component/datacell/DataCell";
import PraTaskSvcActions from "./Buttons/PraTaskSvcActions";
import PraDeleteButton from "./Buttons/PraDeleteButton";
import { AuthContext } from "src/component/common/auth";
import {
  processTasks,
  approveOrRejectTheTask,
} from "src/pages/mockup/workflow/approvalHelpers";
import { DataModelsContext } from "src/DataModel";
import { logError } from "../../../functions/logError";
import { WorkFlowButton } from "../WorkFlowButton";
import TaskStatusDialog from "../TaskStatusDialog";

const useStyles = makeStyles({
  root: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    gap: "5px",
  },
});

export const PraWorkFlow = ({
  vyper,
  pra,
  onChangePra,
  onDeletePra,
  reqCheckedGroups,
  praTaskAssignments,
}) => {
  const classes = useStyles();
  const { authUser } = useContext(AuthContext);
  const { praDao } = useContext(DataModelsContext);

  const [tasks, setTasks] = useState([]);
  const [unApprovedGroups, setUnApprovedGroups] = useState([]);
  const [allGroups, setAllGroups] = useState([]);
  const [isTaskStatusDialogOpen, setIsTaskStatusDialogOpen] = useState(false);

  const getActiveTaskAssignments = () => {
    const { taskAssignments, unApprovedGroups, allGroups } = processTasks(
      praTaskAssignments,
      authUser
    );

    setTasks(taskAssignments);
    setUnApprovedGroups(unApprovedGroups);
    setAllGroups(allGroups);
  };

  //Retrieving assignments for current pra once user context is loaded
  useEffect(() => {
    if (!authUser.uid) {
      return;
    }
    getActiveTaskAssignments();
  }, [authUser.uid, praTaskAssignments]);

  const handleTaskButtonClick = (action, comment, reason, group) => {
    approveOrRejectTheTask(tasks, authUser, action, comment, reason, group)
      .then(() => {
        if (group === "BU_APPROVERS") {
          if (action === "Approve") {
            return praDao.approvePra(vyper.vyperNumber, pra.praNumber);
          }
          return praDao.buReworkPra(vyper.vyperNumber, pra.praNumber);
        }
        return pra;
      })
      .then((pra) => {
        onChangePra(pra);
      })
      .catch(logError);
  };

  const handleOpenStatus = () => {
    setIsTaskStatusDialogOpen(true);
  };

  return (
    <DataCell source={null}>
      <div className={classes.root}>
        {allGroups.length > 0 && (
          <>
            <PraTaskSvcActions
              validatedComponents={pra?.validatedComponents}
              reqCheckedGroups={reqCheckedGroups}
              buttonLabel={"Approve"}
              userAssignments={unApprovedGroups}
              handleTaskButtonClick={handleTaskButtonClick}
              pra={pra}
            />
            <PraTaskSvcActions
              validatedComponents={pra?.validatedComponents}
              reqCheckedGroups={reqCheckedGroups}
              buttonLabel={"Rework"}
              userAssignments={unApprovedGroups}
              handleTaskButtonClick={handleTaskButtonClick}
              pra={pra}
            />
          </>
        )}

        {/* <PraSubmitButton vyper={vyper} pra={pra} onChangePra={onChangePra}/> */}
        <PraDeleteButton vyper={vyper} pra={pra} onDeletePra={onDeletePra} />
        <WorkFlowButton
          visible={true}
          blockers={[]}
          value={"Status"}
          onClick={handleOpenStatus}
        />
        <TaskStatusDialog
          contextKey={`Pra Approval Flow~praNumber~${pra.praNumber}`}
          open={isTaskStatusDialogOpen}
          handleClose={() => setIsTaskStatusDialogOpen(false)}
        />
      </div>
    </DataCell>
  );
};
