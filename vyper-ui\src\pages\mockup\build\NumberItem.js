import makeStyles from "@material-ui/core/styles/makeStyles";
import PropTypes from "prop-types";
import React from "react";
import { NumberDisplay } from "./NumberDisplay";
import { VyperMenu } from "./VyperMenu";
import { StateDisplay } from "./StateDisplay";

const useStyles = makeStyles({
  menu: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    whiteSpace: "nowrap",
  },
  element: {
    paddingRight: ".5rem",
  },
  elementMenu: {
    paddingRight: ".5rem",
    paddingTop: 6, // moves the icon down just a little
  },
});

/**
 * Display the build number items - the build number, state and menu.
 *
 * @param config
 * @param pra
 * @param onMenu
 * @returns {JSX.Element}
 * @constructor
 */
export const NumberItem = ({ config, onMenu }) => {
  const classes = useStyles();

  return (
    <div className={classes.menu}>
      <div className={classes.element}>
        <NumberDisplay number={config.number} />
      </div>
      <div className={classes.element}>
        <StateDisplay state={config.state} />
      </div>
      <div className={classes.elementMenu}>
        <VyperMenu items={config.menuItems} onMenu={onMenu} />
      </div>
    </div>
  );
};

NumberItem.propTypes = {
  config: PropTypes.object.isRequired,
  onMenu: PropTypes.func.isRequired,
};
