import { TableCell, TableRow } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";
import { Help } from "../../../component/help/Help";
import { ReferenceCell2 } from "./ReferenceCell2";

/**
 * Display a row of reference link(s).
 *
 * @param {*[] }items - The list of items for the row
 * @param {*} as - The component to display in the cell.
 * @return {JSX.Element}
 * @constructor
 */
export function ReferenceRow2({ items, as }) {
  return (
    <TableRow hover>
      <TableCell variant="head">
        <Help name="reference" /> Reference
      </TableCell>
      {items.map((item, n) => (
        <TableCell key={n}>
          <ReferenceCell2 item={item} as={as} />
        </TableCell>
      ))}
    </TableRow>
  );
}

ReferenceRow2.propTypes = {
  items: PropTypes.array.isRequired,
  as: PropTypes.any.isRequired,
};
