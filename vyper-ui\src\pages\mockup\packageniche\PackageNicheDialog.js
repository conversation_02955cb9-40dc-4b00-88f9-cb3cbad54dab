import React, { useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { Dialog } from "@material-ui/core";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import PackageNicheSurvey from "../packageniche/PackageNicheSurvey";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  gridItem: {
    marginBottom: theme.spacing(2),
  },
}));

export const PackageNicheDialog = ({ children }) => {
  const [open, setOpen] = useState(false);

  // the material
  const [pkgGroup, setPkgGroup] = useState("");

  const [onSave, setOnSave] = useState();

  const handleOpen = ({ pkgGroup, onSave }) => {
    setPkgGroup(pkgGroup);
    setOnSave(() => onSave);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const onPackageNicheSurveySelected = (packageNiche) => {
    onSave(packageNiche);
    handleClose();
  };

  const classes = useStyles();

  return (
    <PackageNicheDialogContext.Provider
      value={{
        showDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle>Package Niche Selector</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <PackageNicheSurvey
            onPkgNicheSelected={onPackageNicheSurveySelected}
            pkgGroup={pkgGroup}
          />
        </DialogContent>
      </Dialog>
      {children}
    </PackageNicheDialogContext.Provider>
  );
};

export const PackageNicheDialogContext = React.createContext(null);
