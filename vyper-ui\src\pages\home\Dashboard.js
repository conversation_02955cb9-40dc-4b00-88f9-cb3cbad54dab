import React from "react";
import { DashboardCard } from "./DashboardCard";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  root: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
  },
});

export const Dashboard = ({ cards, dashboard }) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      {cards.map((card, n) => (
        <DashboardCard
          key={n}
          card={card}
          loading={dashboard == null}
          count={dashboard?.[card.countField]}
        />
      ))}
    </div>
  );
};
