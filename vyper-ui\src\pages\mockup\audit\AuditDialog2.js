import { Dialog, DialogTitle, Paper, Typography } from "@material-ui/core";
import Button from "@material-ui/core/Button";
import DialogActions from "@material-ui/core/DialogActions";
import DialogContent from "@material-ui/core/DialogContent";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import AddIcon from "@material-ui/icons/Add";
import CloseIcon from "@material-ui/icons/Close";
import EditIcon from "@material-ui/icons/Edit";
import FiberNewIcon from "@material-ui/icons/FiberNew";
import LanguageIcon from "@material-ui/icons/Language";
import RemoveIcon from "@material-ui/icons/Remove";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineOppositeContent,
  TimelineSeparator,
} from "@material-ui/lab";
import moment from "moment";
import PropTypes from "prop-types";
import React from "react";
import config from "../../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles((theme) => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    paddingTop: 4,
    paddingBottom: 4,
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: "#ffffff",
  },
  paper: {
    padding: "6px 16px",
  },
  secondaryTail: {
    backgroundColor: theme.palette.secondary.main,
  },
}));

export function AuditDialog2({ open, title, audits, onClose }) {
  const classes = useStyles();

  const determineIcon = (activity) => {
    if (activity.includes("CREATE_")) {
      return <FiberNewIcon />;
    } else if (activity.includes("CHANGE_")) {
      return <EditIcon />;
    } else if (activity.includes("ADD_")) {
      return <AddIcon />;
    } else if (activity.includes("REMOVE_")) {
      return <RemoveIcon />;
    } else {
      return <LanguageIcon />;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl">
      <DialogTitle className={classes.title}>{title}</DialogTitle>

      <IconButton className={classes.closeButton} onClick={onClose}>
        {" "}
        <CloseIcon />{" "}
      </IconButton>

      <DialogContent>
        <Timeline align="alternate">
          {audits.map((audit) => (
            <TimelineItem key={audit.id}>
              <TimelineOppositeContent>
                <Typography variant="body2" color="textSecondary">
                  {moment(audit.when).fromNow()}
                </Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot color="primary" variant="outlined">
                  {determineIcon(audit.activity)}
                </TimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent>
                <Paper elevation={3} className={classes.paper}>
                  <Typography variant="h6" component="h1">
                    {audit.detail}
                  </Typography>
                  <Typography variant="caption">{userInfo(audit)}</Typography>
                </Paper>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined" color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

const userInfo = (audit) => {
  if (externalUse) {
    return `${audit.username}`;
  }

  return `${audit.username} / ${audit.userid}`;
};

AuditDialog2.propTypes = {
  open: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  audits: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
};
