import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessWaferSawMethod } from "../../../component/sameness/sameness";
import { WaferSawMethodCell } from "./WaferSawMethodCell";
import { WaferSawMethodDialogContext } from "./WaferSawMethodDialog";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const WaferSawMethodRow = ({
  vyper,
  builds,
  onChange,
  showSameness,
}) => {
  const { open: openDialog } = useContext(WaferSawMethodDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangeWaferSawMethod = (value, build) => {
    return buildDao
      .changeWaferSawMethod(vyper.vyperNumber, build.buildNumber, value)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    openDialog({
      value: build.waferSawMethod?.object?.value,
      onSave: (value) => handleChangeWaferSawMethod(value, build),
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessWaferSawMethod(builds),
      })}
      hover
    >
      <RowPrefix help="wafersawmethod" title="Wafer Saw Method" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <WaferSawMethodCell
            vyper={vyper}
            build={build}
            onClick={handleClick}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
