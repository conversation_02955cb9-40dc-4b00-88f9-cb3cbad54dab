import React, { useEffect, useState } from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Box
} from "@material-ui/core";
import { makeStyles } from '@material-ui/core/styles';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
  },
  '&$expanded': {
    margin: 'auto',
  },
  attributePanel:{
    flexDirection: 'column',
  },
  compName: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0,
  },
  compValue: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary,
  },
  attrName: {
    fontSize: theme.typography.pxToRem(12),
    paddingLeft: '5px',
    minWidth: '10rem'
  },
  attrValue: {
    fontSize: theme.typography.pxToRem(12),
    paddingLeft: '5px'
  },

}));

export const CamsDiffPopup = ({deviceCamsList, open, handleCloseDialog }) => {

    const classes = useStyles();
    const [expanded, setExpanded] = useState(false);

    const handleChange = (panel) => (event, isExpanded) => {
        setExpanded(isExpanded ? panel : false);
    };

    return (
        <Dialog
              open={open}
              fullWidth
              onClose={handleCloseDialog}
            >
              <DialogTitle >
                Added components or attributes
              </DialogTitle>

              <DialogContent>
                <div className={classes.root}>
                { deviceCamsList && deviceCamsList.map( (deviceCamsObj,n) => {
                    const compNo = "comp"+n;

                    return <Accordion expanded={expanded === compNo} onChange={handleChange(compNo)} key={n}>
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls={compNo+"-content"}
                          id={compNo}
                        >
                          <Typography className={classes.compName}>{deviceCamsObj.name}</Typography>
                          <Typography className={classes.compValue}>{deviceCamsObj.value}</Typography>
                        </AccordionSummary>
                        <AccordionDetails className={classes.attributePanel}>
                            {deviceCamsObj.attributes?.map( attr => {
                                    return <Box sx={{display: "flex", alignItems:"center"}}>
                                        <Typography className={classes.attrName}>{attr.name}</Typography>
                                        <Typography className={classes.attrValue}>: {attr.value}</Typography>
                                    </Box>
                                })
                            }
                        </AccordionDetails>
                    </Accordion>
                })
                }
                </div>

              </DialogContent>

              <DialogActions>
                <Button variant="outlined" color="primary" onClick={handleCloseDialog}>
                  Cancel
                </Button>
              </DialogActions>
            </Dialog>
    );

}
