import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, MenuItem, TextField } from "@material-ui/core";
import React, { useContext, useEffect, useMemo, useState } from "react";
import IconButton from "@material-ui/core/IconButton";
import DeleteIcon from "@material-ui/icons/Delete";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Tooltip from "@material-ui/core/Tooltip";
import { Material } from "./Material";
import { DataModelsContext } from "../../DataModel";
import { TableDialog } from "./TableDialog";
import { FetchContext } from "../fetch/VyperFetch";
import { ConfirmationDialogContext } from "../cornfirmation/ConfirmationDialog";
import { AlertDialogContext } from "../alert/AlertDialog";
import { AtssTravelerDialog } from "src/component/atss/AtssTravelerDialog";
import { BuildNumberDialog } from "src/pages/mockup/fillcomponent/BuildNumberDialog";
import {
  New,
  MinorChange,
  Experimental,
} from "../../pages/mockup/buildtype/BuildTypes";

const useStyles = makeStyles(() => ({
  root: {
    "&:hover": {
      background: "#f1f1f1",
    },
    marginTop: "10px",
  },
  key: {
    fontWeight: "bold",
    textAlign: "right",
    paddingRight: "1em",
    paddingBottom: "1em",
    "&::after": {
      content: '" : "',
    },
  },
  value: {
    paddingBottom: "1em",
  },
  arrowUp: {
    transition: "transform 0.3s ease-in-out",
    // "&:hover": {
    //   transform: "rotate(180deg)",
    // },
  },
  paper: {
    minWidth: "1000px",
  },
}));

export const FlowDataRow = ({
  item,
  index,
  onChangeFlowData,
  onDeleteFlowData,
  showDeleteButton,
  firstFlowTemplateType,
  firstFlowDevice,
  firstFlowHasBomTemplateForPkgNiche,
  buildType,
}) => {
  const classes = useStyles();

  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { open: openAlert } = useContext(AlertDialogContext);
  const { vpost } = useContext(FetchContext);
  const [showCopyFromAtssOrVBuild, setShowCopyFromAtssOrVBuild] = useState(
    firstFlowTemplateType === "ATSS" || firstFlowTemplateType === "VYPER"
  );
  const [showTravelerDialog, setShowTravelerDialog] = useState(false);
  const [showBuildNumberDialog, setShowBuildNumberDialog] = useState(false);

  const [hasBomTemplateForPkgNiche, setHasBomTemplateForPkgNiche] = useState(
    item.hasBomTemplateForPkgNiche
  );
  const { pkgNicheBomTemplateDao } = useContext(DataModelsContext);

  const [openTableDialog, setOpenTableDialog] = useState(false);
  const [data, setData] = useState([]);
  const [requiredProductionTemplate, setRequiredProductionTemplate] =
    useState(false);

  useEffect(() => {
    if (item.flowName !== "" && item.flowName !== undefined && index > 0) {
      if (firstFlowTemplateType === "ATSS") {
        setShowTravelerDialog(true);
      }

      if (firstFlowTemplateType === "VYPER") {
        setShowBuildNumberDialog(true);
      }
    }
  }, [item.flowName]);

  useEffect(() => {
    if (firstFlowDevice !== item.device?.Material || index === 0) {
      setHasBomTemplateForPkgNiche(undefined);
      setShowCopyFromAtssOrVBuild(false);
    }
  }, [item.device?.Material]);

  useEffect(() => {
    if (item.differentPackageNicheExists === true) {
      setHasBomTemplateForPkgNiche(false);
    }
  }, [item.differentPackageNicheExists]);

  const fetchFlowTemplateDetails = useMemo(() => {
    if (!item.device?.PackageNiche && !item.device?.PackageGroup) return;
    return pkgNicheBomTemplateDao.findBomTemplatesSupportedByPkgNiche(
      item.device?.PackageNiche || item.device?.PackageGroup
    );
  }, [item.device?.PackageNiche || item.device?.PackageGroup]);

  useEffect(() => {
    setRequiredProductionTemplate(false);
    if (item.differentPackageNicheExists === true) return;

    if (item.facility != null && item.facility !== "") {
      if (
        index === 0 &&
        (item.device?.PackageNiche || item.device?.PackageGroup)
      ) {
        fetchFlowTemplateDetails.then((jsonArr) => {
          if (jsonArr.length === 0) {
            setHasBomTemplateForPkgNiche(false);
            onChangeFlowData(false, index, "hasBomTemplateForPkgNiche");
          } else {
            // if buildType is New or Minor Change we should only allow Production templates
            if (buildType === New || buildType === MinorChange) {
              const hasProductionAtTemplate = jsonArr.find(
                (template) =>
                  template.flowTemplateType === "AT" &&
                  template.flowPreferenceCode === "A"
              );
              const hasProductionBomTemplate = jsonArr.find(
                (template) =>
                  template.flowTemplateType === "BOM" &&
                  template.flowPreferenceCode === "A"
              );

              // if no valid production templates, mark as not supported
              if (!hasProductionAtTemplate || !hasProductionBomTemplate) {
                setHasBomTemplateForPkgNiche(false);
                onChangeFlowData(false, index, "hasBomTemplateForPkgNiche");
                setRequiredProductionTemplate(true);
                return;
              }
            }
            // if valid templates exist, mark as supported
            setHasBomTemplateForPkgNiche(true);
            onChangeFlowData(true, index, "hasBomTemplateForPkgNiche");
          }
        });
      } else {
        // use the state of the first flow for validation
        onChangeFlowData(
          firstFlowHasBomTemplateForPkgNiche,
          index,
          "hasBomTemplateForPkgNiche"
        );
      }
    }
  }, [item.facility, buildType]);

  const handleClickOpenSupportedBomTemplate = () => {
    setShowCopyFromAtssOrVBuild(false);
    if (data.length === 0) {
      // Fetch supported package niches for selection
      pkgNicheBomTemplateDao
        .fetchPkgNichesSupported(buildType)
        .then((json) => setData(json));
    }
    setOpenTableDialog(true);
  };

  const handleClose = () => {
    setOpenTableDialog(false);
  };

  const hasSimilarPkgNiche =
    item.templateSource?.similarPkgNiche !== undefined &&
    item.templateSource?.similarPkgNiche !== "";

  const hasCopyFromVyper =
    item.templateSource?.vyperBuildNumber !== undefined &&
    item.templateSource?.vyperBuildNumber !== "";

  const hasCopyFromAtss =
    item.templateSource?.atssMaterial !== undefined &&
    item.templateSource?.atssMaterial !== "" &&
    item.templateSource?.atssFacility !== undefined &&
    item.templateSource?.atssFacility !== "" &&
    item.templateSource?.atssStatus !== undefined &&
    item.templateSource?.atssStatus !== "";

  const handleClickRequestReview = () => {
    openConfirmation({
      title: "Request Bill of Process Template Review",
      message: `This action will send an email to the Bill of Process Template team asking them to review Bill of Process Template for the package niche ${item.device.PackageNiche}. Are you sure you want to do this?`,
      yesText: "Yes",
      noText: "No",
      onYes: () =>
        vpost(
          "/vyper/v1/vyper/packageniche/bomtemplate/review",
          {
            material: item.device.Material,
            facility: item.facility.PDBFacility,
            pin: item.device.PackagePin,
            pkg: item.device.PackageDesignator,
            pkgGroup: item.device.PackageGroup,
            packageNiche: item.device.PackageNiche,
          },
          () => {
            openAlert({
              title: "Success",
              message: "The email was successfully sent",
            });
          }
        ),
    });
  };

  const handleFillComponentAtss = (material, facility, status) => {
    onChangeFlowData({ facility, material, status }, index, "copyFromAtss");
    setShowTravelerDialog(false);
  };

  const handleFillComponentVyper = (buildNumber) => {
    onChangeFlowData(buildNumber, index, "copyFromVyper");
    setShowBuildNumberDialog(false);
  };

  return (
    <Grid
      key={index}
      container
      direction="row"
      alignItems="center"
      spacing={1}
      className={classes.root}
    >
      <Grid item xs={2}>
        <TextField
          select
          variant="outlined"
          id={"flow" + index}
          label="Flow"
          value={item.flowName}
          defaultValue=""
          fullWidth
          onChange={(e) => onChangeFlowData(e, index, "flowName")}
        >
          {item.allowedFlows.map((flow, n) => (
            <MenuItem key={flow.id} value={flow.flowName}>
              <Tooltip
                key={n}
                title={flow.flowDescription}
                placement="right"
                arrow
                style={{ width: "100%" }}
              >
                <div> {flow.flowName}</div>
              </Tooltip>
            </MenuItem>
          ))}
        </TextField>
      </Grid>
      <Grid item xs={3}>
        <Material
          defaultMaterial={item.device}
          onSelect={(e) => onChangeFlowData(e, index, "device")}
        />
      </Grid>
      <Grid item xs={2}>
        <TextField
          select
          variant="outlined"
          id="facility"
          label="Facility"
          value={item.facility}
          fullWidth
          onChange={(e) => onChangeFlowData(e, index, "facility")}
        >
          {item.facilities.map((facility, n) => (
            <MenuItem key={n} value={facility}>
              {facility.PDBFacility}
            </MenuItem>
          ))}
        </TextField>
      </Grid>
      <Grid item xs={1}>
        <TextField
          select
          variant="outlined"
          id="multi-build"
          label="Multi Build"
          value={item.isMultiBuild === true ? "True" : "False"}
          fullWidth
          InputLabelProps={{
            style: {
              fontSize: 18,
              textAlign: "start",
            },
            shrink: true,
          }}
          onChange={(e) => onChangeFlowData(e, index, "isMultiBuild")}
        >
          {["True", "False"].map((item, n) => (
            <MenuItem key={n} value={item}>
              {item}
            </MenuItem>
          ))}
        </TextField>
      </Grid>
      <Grid item xs={3}>
        <TextField
          variant="outlined"
          select
          id="specDevices"
          label="Spec Device"
          fullWidth
          value={item.specDevice || ""}
          onChange={(e) => onChangeFlowData(e, index, "specDevice")}
          InputLabelProps={{ shrink: true }}
        >
          {item.specDevices.map((sd) => (
            <MenuItem key={sd} value={sd}>
              {sd}
            </MenuItem>
          ))}
        </TextField>
      </Grid>
      <Grid item xs={1}>
        {showDeleteButton && (
          <div style={{ display: "flex" }}>
            <IconButton
              onClick={() => {
                onDeleteFlowData(index);
              }}
            >
              <DeleteIcon></DeleteIcon>
            </IconButton>
          </div>
        )}
      </Grid>
      {hasBomTemplateForPkgNiche === false &&
      buildType !== "Minor Change" &&
      (index === 0 
        ? true
        : firstFlowHasBomTemplateForPkgNiche !== hasBomTemplateForPkgNiche ||
          firstFlowDevice !== item.device.Material) ? (
        <Grid item xs={12}>
          <p style={{ margin: "0px" }}>
            {requiredProductionTemplate && (
              <p>
                {" "}
                New or Minor Change BuildType requires Production Template to
                create Build{" "}
              </p>
            )}
            Package Niche ({item.device.PackageNiche || "is Empty"}) for
            Material does not have a supported Bill of Process Template
            {"  "}
            <Button
              variant="contained"
              style={{ backgroundColor: "#9B2242", color: "white" }}
              onClick={handleClickRequestReview}
            >
              REQUEST REVIEW
            </Button>
          </p>
        </Grid>
      ) : undefined}
      {hasBomTemplateForPkgNiche === false && 
      buildType !== "Minor Change" &&
      (index === 0
        ? true
        : firstFlowHasBomTemplateForPkgNiche !== hasBomTemplateForPkgNiche ||
          firstFlowDevice !== item.device.Material) ? (
        <Grid item xs={12}>
          <p style={{ margin: "0px", display: "flex", alignItems: "center" }}>
            Would you like to use supported Bill of Process Template from a
            similar Package Niche ?
            <Button
              variant="contained"
              color="secondary"
              onClick={() => handleClickOpenSupportedBomTemplate()}
              style={{ marginRight: "10px", marginLeft: "10px" }}
            >
              YES
            </Button>
            <Button
              variant="contained"
              style={{
                backgroundColor: "#9B2242",
                color: "white",
                marginRight: "10px",
              }}
              onClick={(e) => {
                setShowCopyFromAtssOrVBuild(true);
                onChangeFlowData(e, index, "NO");
              }}
            >
              NO
            </Button>
            {"   "}
            {hasSimilarPkgNiche && (
              <p style={{ margin: "0px" }}>
                Selected Similar Package Niche :{" "}
                <Link onClick={() => setOpenTableDialog(true)}>
                  {item.templateSource.similarPkgNiche}
                </Link>
              </p>
            )}
          </p>
        </Grid>
      ) : undefined}

      {showCopyFromAtssOrVBuild === true ||
        firstFlowTemplateType === "ATSS" ||
        firstFlowTemplateType === "VYPER" ? (
        <Grid item xs={12}>
          <div
            style={{
              marginTop: "0px",
              display: "flex",
              alignItems: "center",
              gap: "1em",
            }}
          >
            Would you like to copy flow structure from ATSS or another VBUILD ?
            <Button
              variant="contained"
              color="secondary"
              onClick={() => setShowTravelerDialog(true)}
            >
              YES - ATSS
            </Button>
            <Button
              variant="contained"
              color="secondary"
              onClick={() => setShowBuildNumberDialog(true)}
            >
              YES - VBUILD
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={(e) => onChangeFlowData(e, index, "NO")}
            >
              NO
            </Button>
            {"   "}
            {hasCopyFromVyper && (
              <div style={{ margin: "0px" }}>
                Selected Copy from VBuild :{" "}
                <Link
                  style={{ cursor: "pointer" }}
                  onClick={() => setShowBuildNumberDialog(true)}
                >
                  {item.templateSource.vyperBuildNumber}
                </Link>
              </div>
            )}
            {hasCopyFromAtss && (
              <div style={{ margin: "0px" }}>
                Selected Copy from ATSS :{" "}
                <Link
                  style={{ cursor: "pointer" }}
                  onClick={() => setShowTravelerDialog(true)}
                >
                  {item.templateSource.atssMaterial}
                  {"  "}
                  {item.templateSource.atssFacility}
                  {"  "}
                  {item.templateSource.atssStatus}
                </Link>
              </div>
            )}
          </div>
        </Grid>
      ) : undefined}

      <AtssTravelerDialog
        open={showTravelerDialog}
        defaultSpecDevice={item.specDevice}
        defaultFacilityAt={item.facility.PDBFacility}
        defaultStatus="ACTIVE"
        onClose={() => setShowTravelerDialog(false)}
        onSelect={handleFillComponentAtss}
        title="Fill Unselected from ATSS"
      />

      <BuildNumberDialog
        open={showBuildNumberDialog}
        onClose={() => setShowBuildNumberDialog(false)}
        onSelect={handleFillComponentVyper}
        title="Fill Unselected from VYPER"
      />

      <TableDialog
        open={openTableDialog}
        handleclose={handleClose}
        data={data}
        handleClose={handleClose}
        handleSelect={(event, ao) => {
          onChangeFlowData(ao.pkgNiche, index, "copyFromSimilarPkgNiche");
          setOpenTableDialog(false);
        }}
        pkgGroup={item.device.PackageGroup}
      />
    </Grid>
  );
};

FlowDataRow.propTypes = {
  item: PropTypes.object,
  index: PropTypes.number,
  onChangeFlowData: PropTypes.func,
  onDeleteFlowData: PropTypes.func,
  showDeleteButton: PropTypes.bool,
  firstFlowTemplateType: PropTypes.string,
  firstFlowDevice: PropTypes.string,
};
