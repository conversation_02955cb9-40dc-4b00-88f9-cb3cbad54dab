import React, { useContext, useMemo } from "react";
import { Button } from "@material-ui/core";
import { PraWorkFlowHelpersContext } from "../PraWorkFlowHelpers";
import { DataModelsContext } from "src/DataModel";
import { noop } from "src/component/vyper/noop";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { List, ListItem, ListItemIcon, ListItemText } from "@material-ui/core";
import ArrowRightAltIcon from "@material-ui/icons/ArrowRightAlt";

export const PraSubmitButton = ({ vyper, pra, onChangePra }) => {
  const { canUpdatePra } = useContext(PraWorkFlowHelpersContext);
  // const [isVisible, setIsVisible] = useState(false);
  const { praDao } = useContext(DataModelsContext);
  const { open: openAlert } = useContext(AlertDialogContext);

  const isVisible = useMemo(() => {
    if (pra == undefined || vyper == undefined) {
      return false;
    }
    return canUpdatePra(pra, vyper);
  }, [pra, vyper]);

  const unVerified = useMemo(() => {
    if (!pra.verifiers) {
      return [];
    }
    let unVerified = new Set();
    pra.verifiers.forEach((verifier) => {
      if (
        verifier.status === "NOT_VERIFIED" ||
        (verifier.source !== "SOURCE_PGS" &&
          verifier.status === "PARTIALLY_VERIFIED")
      ) {
        unVerified.add(verifier.name);
      }
    });
    return [...unVerified];
  }, [pra.verifiers]);

  const handleAlert = () => {
    const message = (
      <div>
        <div>You are unable to Submit. Here are the reasons:</div>
        <br />
        <div>
          {unVerified.length > 1 ? "Verifiers are" : "Verifiers is"} not fully
          approved.
        </div>
        <List>
          {unVerified.map((component) => (
            <ListItem key={component}>
              <ListItemIcon>
                <ArrowRightAltIcon color="primary" />
              </ListItemIcon>
              <ListItemText>
                The component not fully verified - {component}
              </ListItemText>
            </ListItem>
          ))}
        </List>
      </div>
    );

    openAlert({
      title: `Can't Submit`,
      message: message,
    });
  };
  const handleClick = () => {
    if (unVerified.length > 0) {
      handleAlert();
      return;
    }
    praDao
      .approvePra(vyper.vyperNumber, pra.praNumber)
      .then((updatedPra) => {
        onChangePra(updatedPra);
      })
      .catch(noop);
  };

  return (
    isVisible && (
      <Button
        variant="contained"
        color="primary"
        size="small"
        type="button"
        name="action"
        onClick={handleClick}
      >
        Submit
      </Button>
    )
  );
};

export default PraSubmitButton;
