import { Box, Typography } from "@material-ui/core";
import React from "react";

export const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`simple-tabpanel-${index}`}
    aria-labelledby={`simple-tab-${index}`}
    {...other}
  >
    {value === index && (
      <Box component="div">
        <Typography component="div">{children}</Typography>
      </Box>
    )}
  </div>
);
