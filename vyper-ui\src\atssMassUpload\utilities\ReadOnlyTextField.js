import React from "react";
import TextField from "@material-ui/core/TextField";

export const ReadOnlyTextField = ({ text, value, width, className }) => {
  return (
    <TextField
      className={className}
      variant="outlined"
      inputProps={{ readOnly: true }}
      label={text || "Not Found"}
      value={value || ""}
      style={{ minWidth: width }}
      fullWidth
    ></TextField>
  );
};
