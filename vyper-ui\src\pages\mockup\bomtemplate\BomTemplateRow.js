import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React from "react";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessBomTemplate } from "../../../component/sameness/sameness";
import { BomTemplateCell } from "./BomTemplateCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const BomTemplateRow = ({ vyper, builds, showSameness }) => {
  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessBomTemplate(builds),
      })}
      hover
    >
      <RowPrefix help="bomtemplate" title="Template" />
      {builds.map((build, n) => (
        <TableCell key={n} valign="top">
          <BomTemplateCell vyper={vyper} build={build} />
        </TableCell>
      ))}
    </TableRow>
  );
};
