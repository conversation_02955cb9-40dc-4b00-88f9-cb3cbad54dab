import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
} from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import React, { useState } from "react";
import { VerifierTable } from "src/pages/vyper/pras/VerifierTable";

const useStyles = makeStyles(() => ({
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
    paddingTop: 4,
    paddingBottom: 4,
  },
  sourceAndStatus: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 20,
  },
  divider: {
    backgroundColor: "#cc0000",
    marginTop: "1em",
    marginBottom: "1em",
  },
  header: {
    fontWeight: "bold",
    textAlign: "center",
  },
  data: {
    textAlign: "center",
  },
}));

/**
 * Dialog that displays the verifier information
 *
 * @param children
 * @returns {JSX.Element}
 * @function
 */
export const VerifierDialog = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [verifier, setVerifier] = useState({ messages: [] });

  const handleOpen = ({ verifier }) => {
    setVerifier(verifier);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const translateSource = (source) => {
    switch (source) {
      case "SOURCE_PGS":
        return "PGS";
      case "SOURCE_PAVV_COMPONENT":
        return "PAVV";
      case "SOURCE_ARMARC":
        return "ArmArc";
      case "SOURCE_ATSS_GLOBAL":
        return "ATSS Global CAMS";
      case "SOURCE_ATSS_AT":
        return "ATSS A/T CAMS";
      case "SOURCE_ATSS_BOM":
        return "ATSS BOM";
      case "SOURCE_DIE":
        return "Die";
      default:
        return source;
    }
  };

  const translateStatus = (status) => {
    switch (status) {
      case "NOT_VERIFIED":
        return "Not Verified";
      case "PARTIALLY_VERIFIED":
        return "Partially Verified";
      case "FULLY_VERIFIED":
        return "Fully Verified";
      default:
        return status;
    }
  };

  const classes = useStyles();

  return (
    <VerifierDialogContext.Provider
      value={{
        openVerifierDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="xl">
        <DialogTitle className={classes.title}>
          Verifier Results for {verifier.name}: {verifier.value}
        </DialogTitle>
        <DialogContent>
          <div className={classes.sourceAndStatus}>
            <div>
              <div className={classes.header}>Source</div>
              <div className={classes.data}>
                {translateSource(verifier.source)}
              </div>
            </div>
            <div>
              <div className={classes.header}>Status</div>
              <div className={classes.data}>
                {translateStatus(verifier.status)}
              </div>
            </div>
          </div>

          <Divider className={classes.divider} />

          <VerifierTable verifier={verifier} />
        </DialogContent>
        <DialogActions>
          <Button variant="contained" color="primary" onClick={handleClose}>
            Ok
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </VerifierDialogContext.Provider>
  );
};

export const VerifierDialogContext = React.createContext(null);
