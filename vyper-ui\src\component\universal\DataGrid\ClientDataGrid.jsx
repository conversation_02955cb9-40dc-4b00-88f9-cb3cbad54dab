import React from "react";
import PropTypes from "prop-types";
import MaterialTable from "material-table";
import { default as tableIcons } from "./icons";
import { tableConfig, styleOptions } from "./defaults";

const ClientDataGrid = ({
  handleSelect,
  selectable = false,
  options = {},
  ...rest
}) => {
  return (
    <MaterialTable
      minRows={tableConfig.minRows}
      icons={tableIcons}
      onSelectionChange={selectable ? (rows) => handleSelect(rows) : null}
      options={{
        ...styleOptions,
        selection: selectable,
        filtering: tableConfig.filtering,
        pageSize: tableConfig.defaultPageSize,
        ...options,
      }}
      {...rest}
    />
  );
};

ClientDataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  data: PropTypes.array.isRequired,
  detailPanel: PropTypes.any,
  handleSelect: PropTypes.func,
  selectable: PropTypes.bool,
  title: PropTypes.string,
};

export default ClientDataGrid;
