import React, { useState, useEffect } from "react";
import FormSelect from "./Layouts/FormSelect";
import { useParams } from "react-router-dom";

import { BASE_FETCH_DATA_URL, BASE_POST_DATA_URL } from "./FormConstants";

const postData = (data, callBack) => {
  fetch(`${BASE_POST_DATA_URL}/update`, {
    headers: {
      "Content-Type": "application/json",
    },
    method: "POST",
    body: JSON.stringify(data),
  }).then(callBack);
};

const EditForm = () => {
  const [defaultValues, setDefaultValues] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const { vswrID } = useParams();

  const fetchVswr = (vswrID) => {
    setIsLoading(true);
    fetch(`${BASE_FETCH_DATA_URL}/fetchVswr/${vswrID}`)
      .then((response) => response.json())
      .then((json) => {
        setDefaultValues(json);
        setIsLoading(false);
      })
      .catch(() => {
        setIsLoading(false);
        setDefaultValues({});
      });
  };

  useEffect(() => {
    if (vswrID) {
      fetchVswr(vswrID);
    }
  }, []);

  if (isLoading) {
    return <strong>Loading...</strong>;
  }
  return <FormSelect defaultValues={defaultValues} saveData={postData} />;
};

export default EditForm;
