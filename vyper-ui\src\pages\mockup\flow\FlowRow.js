import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React from "react";
import { samenessFlow } from "src/component/sameness/sameness";
import { FlowCell } from "src/pages/mockup/flow/FlowCell";
import { RowPrefix } from "src/pages/mockup/RowPrefix";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const FlowRow = ({ vyper, builds, showSameness }) => {
  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessFlow(builds),
      })}
      hover
    >
      <RowPrefix help="flow" title="Flow" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <FlowCell vyper={vyper} build={build} flow={build.flow} />
        </TableCell>
      ))}
    </TableRow>
  );
};
