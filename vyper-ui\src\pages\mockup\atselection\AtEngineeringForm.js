import React from "react";
import TextField from "@material-ui/core/TextField";
import Button from "@material-ui/core/Button";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  engineering: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  actionButton: {
    margin: "1rem",
    width: "8rem",
  },
});

export const AtEngineeringForm = ({ value, onChange, onAdd }) => {
  const classes = useStyles();

  return (
    <div className={classes.engineering}>
      <TextField
        fullWidth
        label="Eng Component"
        variant="outlined"
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
      />

      <Button
        className={classes.actionButton}
        variant="contained"
        color="primary"
        disabled={value == null || value === ""}
        onClick={onAdd}
      >
        Add Eng
      </Button>
    </div>
  );
};
