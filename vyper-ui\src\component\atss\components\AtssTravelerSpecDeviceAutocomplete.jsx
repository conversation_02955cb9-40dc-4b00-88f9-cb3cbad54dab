import React from "react";
import Autocomplete from "@material-ui/lab/Autocomplete";
import TextField from "@material-ui/core/TextField";
import { useTravelerSpecDevice } from "src/component/atss/hooks/useTravelerSpecDevice";

export const AtssTravelerSpecDeviceAutocomplete = ({
  specDevice = "",
  onChange,
  showSelectedText,
}) => {
  const [value, setValue] = React.useState(specDevice);
  const { options } = useTravelerSpecDevice(value);

  return (
    <Autocomplete
      inputValue={value || ""}
      onInputChange={(e, v) => setValue(v)}
      value={specDevice}
      onChange={(e, v) => onChange(v)}
      style={{ minWidth: "13em" }}
      options={
        options.includes(specDevice) ? options : [...options, specDevice]
      }
      renderInput={(params) => (
        <TextField
          {...params}
          label="Spec Device"
          variant="outlined"
          InputLabelProps={{ shrink: true }}
          helperText={
            showSelectedText
              ? !specDevice
                ? "not selected"
                : `selected: ${specDevice}`
              : ""
          }
          size="small"
        />
      )}
      getOptionLabel={(o) => o || ""}
    />
  );
};
