import React, { useRef, useState, useContext } from "react";

import { FormControl, InputLabel, MenuItem, Select } from "@material-ui/core";
import PageHeader from "../common/PageHeader";
import CircularProgress from "@material-ui/core/CircularProgress";
import { columnDefs, tableOptions } from "./config";
import { useUpdatableSwrsGetter } from "./queries";
import ChangeRequestDialog from "../common/ChangeRequestDialog";
import VswrAgGridReact from "../common/VswrAgGridReact";
import { ForbiddenErrorPage } from "../common/HttpErrorPage";
import { AuthContext, VSWR_ROLE_GROUPS } from "src/component/common/auth";
const { SBE, BTH } = VSWR_ROLE_GROUPS;

const UpdateSwrsPage = () => {
  const gridRef = useRef(null);
  const [selectedTableOption, setSelectedTableOption] = useState(0);
  const updatableSwrsGetter = useUpdatableSwrsGetter(
    tableOptions[selectedTableOption].value
  );
  const { authUser } = useContext(AuthContext);
  let userVswrSecurityRole = authUser?.vswrSecurity?.roleCategory;
  let hasSbeAccess = userVswrSecurityRole === SBE;
  let hasBthAccess = userVswrSecurityRole === BTH;

  if (!(hasSbeAccess || hasBthAccess)) {
    return <ForbiddenErrorPage />;
  }

  return (
    <div
      className={"App BatchContainer"}
      style={{ display: "flex", flexDirection: "column", gap: "16px" }}
    >
      <PageHeader headerTitle={"Update SWRs"} />

      <div
        style={{
          width: "min-content",
          display: "flex",
          gap: "16px",
          flexDirection: "column",
        }}
      >
        <FormControl>
          <InputLabel>Show SWRs From Table</InputLabel>
          <Select
            variant={"outlined"}
            style={{ width: "300px" }}
            value={selectedTableOption}
            onChange={(e) => {
              setSelectedTableOption(e.target.value);
            }}
          >
            {tableOptions.map((option, i) => (
              <MenuItem key={i} value={i}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <ChangeRequestDialog
          triggerButtonLabel={tableOptions[selectedTableOption].label}
          onSuccessfulChange={() => {
            updatableSwrsGetter.refetch();
          }}
          onOpenRequest={() => {
            let swrIds = gridRef.current.api.getSelectedRows().map((row) => {
              return row.SWR_ID;
            });
            if (swrIds.length === 0) {
              return Promise.reject("No Swrs Have been Selected!");
            }
            let table = tableOptions[selectedTableOption].value;
            return Promise.resolve({ swrIds, table });
          }}
        />
      </div>

      {updatableSwrsGetter.isLoading ? (
        <CircularProgress />
      ) : (
        <VswrAgGridReact
          ref={gridRef}
          rowData={updatableSwrsGetter.data}
          columnDefs={columnDefs}
        />
      )}
    </div>
  );
};
export default UpdateSwrsPage;
