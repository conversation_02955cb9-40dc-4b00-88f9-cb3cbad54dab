import React from "react";
import { TableCell, TableSortLabel, TextField } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

/**
 *
 * @param column
 * @param pageable
 * @param filters
 * @param header
 * @param onFilterChange
 * @param overrides
 * @returns {*}
 * @constructor
 */
export const HeaderCell = ({
  column,
  pageable,
  filter,
  header,
  onSortClick,
  onFilterChange,
  overrides,
  visible,
  columnIndex,
  cellLines = 2,
}) => {
  const isVisible = visible(column, columnIndex);
  if (!isVisible) return null;

  // split the header into lines, and add blank lines so there are the same # of rows
  // this way, the filter box will be horizontally aligned on the row

  let h = header.split(" ");
  while (h.length < cellLines) {
    h.push("");
  }

  const classes = makeStyles((theme) => ({
    cell: {
      verticalAlign: "bottom",
    },
  }))();

  return (
    <TableCell className={classes.cell} variant="head">
      <TableSortLabel
        active={pageable.sort === column.id}
        direction={pageable.order}
        onClick={onSortClick}
      >
        {header}
        {/*{h.map((x, n) => <div key={n}>{x}</div>)}*/}
      </TableSortLabel>

      <br />

      {column.enableFilter ? (
        <TextField
          name={column.id}
          value={filter || ""}
          onChange={onFilterChange}
          placeholder="filter"
          error
        />
      ) : (
        <>
          <br />
          <br />
        </>
      )}
    </TableCell>
  );
};
