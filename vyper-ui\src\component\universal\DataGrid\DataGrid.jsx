import React from "react";
import PropTypes from "prop-types";
import ClientDataGrid from "./ClientDataGrid";
import ServerDataGrid from "./ServerDataGrid";
import {
  ClientEditableDataGrid,
  ServerEditableDataGrid,
} from "./EditableDataGrid";

const DataGrid = (props) => {
  const { editable = false, url = null } = props;
  // If a url is provided, assume a remote data setup
  const DataGrid = editable ? (
    url !== undefined && url !== null && url !== "" ? (
      <ServerEditableDataGrid {...props} />
    ) : (
      <ClientEditableDataGrid {...props} />
    )
  ) : url !== undefined && url !== null && url !== "" ? (
    <ServerDataGrid {...props} />
  ) : (
    <ClientDataGrid {...props} />
  );
  return DataGrid;
};

DataGrid.propTypes = {
  actions: PropTypes.array,
  columns: PropTypes.array,
  data: PropTypes.array,
  detailPanel: PropTypes.any,
  editable: PropTypes.bool,
  forUpdate: PropTypes.func,
  handleSelect: PropTypes.func,
  selectable: PropTypes.bool,
  setData: PropTypes.func,
  timestamp: PropTypes.string,
  title: PropTypes.any,
  url: PropTypes.string,
  maxBodyHeight: PropTypes.string,
  pageSize: PropTypes.number,
  paging: PropTypes.bool,
};

export default DataGrid;
