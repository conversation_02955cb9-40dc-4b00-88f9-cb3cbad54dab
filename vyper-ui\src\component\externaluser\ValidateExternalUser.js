import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { AuthContext } from "src/component/common";
import config from "src/buildEnvironment";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  root: {
    minWidth: "50vw",
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
});

let { externalUse } = config;

/**
 * This component shows an error if the user is external, and doesn't have any roles in vyper
 * @param {any} children - description
 * @return {JSX.Element}
 * @constructor
 */
export function ValidateExternalUser({ children }) {
  const [open, setOpen] = useState(false);
  const { authUser } = useContext(AuthContext);

  const classes = useStyles();

  // simulate an external user with no roles
  // externalUse = true
  // authUser.roles = []

  // the userid is valid if it has more than zero characters
  // (meaning that we have successfully loaded the current user)
  const isUseridValid = authUser?.uid?.length > 0;

  // do we have any roles for the user
  const hasRoles = authUser?.roles?.length > 0;

  // invalid means that user is external, the userid is valid, and they have no roles
  const isInvalid = externalUse && isUseridValid && !hasRoles;

  // console.log('###################### externalUse', externalUse)
  // console.log('###################### isUseridValid', isUseridValid)
  // console.log('###################### hasRoles', hasRoles)
  // console.log('###################### isInvalid', isInvalid)

  // if the isInvalid value changes to true, show the dialog
  useEffect(() => {
    if (isInvalid) {
      setOpen(true);
    }
  }, [isInvalid]);

  // on dialog close, close the dialog and navigate to ti.com
  const handleClose = () => {
    setOpen(false);
    window.open(`https://www.ti.com`, "_self");
  };

  return (
    <div>
      {isInvalid ? null : children}

      <Dialog
        className={classes.root}
        open={open}
        maxWidth="lg"
        fullWidth
        onClose={handleClose}
      >
        <DialogTitle classes={{ root: classes.title }}>
          Unauthorized
        </DialogTitle>

        <DialogContent>
          <h3>This account lacks permissions to use this application.</h3>
        </DialogContent>

        <DialogActions>
          <Button variant="contained" color="secondary" onClick={handleClose}>
            Exit
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

ValidateExternalUser.propTypes = {
  children: PropTypes.node.isRequired,
};
