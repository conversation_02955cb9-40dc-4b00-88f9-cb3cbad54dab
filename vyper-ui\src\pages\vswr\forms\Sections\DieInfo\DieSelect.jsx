import React, { useState, useMemo } from "react";
import { FormControl, InputLabel, Select, MenuItem } from "@material-ui/core";
import { BASE_FETCH_DATA_URL, numberFormatter } from "../../FormConstants";
import Grid from "@material-ui/core/Grid";
import { AgGridReact } from "ag-grid-react";

const fetchData = (uri, setFormState, defaultValue) => {
  fetch(`${BASE_FETCH_DATA_URL}${uri}`)
    .then((response) => response.json())
    .then(setFormState)
    .catch(() => {
      setFormState(defaultValue);
    });
};

const dieLotStatusColumns = [
  { headerName: "Material", field: "material", flex: 3 },
  { headerName: "Batch", field: "batch", flex: 3 },
  { headerName: "Plant", field: "plant", flex: 3 },
  {
    headerName: "Unrestricted",
    field: "unrestricted",
    flex: 3,
    valueFormatter: numberFormatter,
    type: "rightAligned",
  },
  {
    headerName: "Stock in TFR",
    field: "stock",
    flex: 3,
    valueFormatter: numberFormatter,
    type: "rightAligned",
  },
  {
    headerName: "Qual Inspection",
    field: "qualInspection",
    flex: 3,
    valueFormatter: numberFormatter,
    type: "rightAligned",
  },
  {
    headerName: "Restricted",
    field: "restricted",
    flex: 3,
    valueFormatter: numberFormatter,
    type: "rightAligned",
  },
  {
    headerName: "Blocked",
    field: "blocked",
    flex: 3,
    valueFormatter: numberFormatter,
    type: "rightAligned",
  },
  {
    headerName: "Return",
    field: "returns",
    flex: 3,
    valueFormatter: numberFormatter,
    type: "rightAligned",
  },
];

const dieLotIntransitColumns = [
  { headerName: "Shipping Plant", field: "shippingPlant" },
  { headerName: "Shipping Facility", field: "shippingFacility" },
  { headerName: "Receiving Plant", field: "receivingPlant" },
  { headerName: "Receiving Facility", field: "receivingFacility" },
  { headerName: "Material", field: "material" },
  { headerName: "Batch", field: "batch" },
  { headerName: "PO Number", field: "poNumber" },
  { headerName: "PO Line Item", field: "poLineItem" },
  { headerName: "Schedule Line", field: "scheduleLine" },
  { headerName: "Waybill", field: "waybill" },
  { headerName: "Delivery Number", field: "deliveryNumber" },
  { headerName: "Delivery Item", field: "deliveryItem" },
  { headerName: "Receipt Date", field: "receiptDate" },
  { headerName: "Ship Date", field: "shipDate" },
  { headerName: "Qty", field: "qty" },
  { headerName: "Wafer Qty", field: "waferQty" },
];

const DieSelect = (props) => {
  const { classes, setDieLotData, material, plant, dieLocationOptions } = props;

  const [selectedLocationOption, setSelectedLocationOption] = useState("");

  const [selectedRow, setSelectedRow] = useState(-1);
  const [dieLotStatusData, setDieLotStatusData] = useState([]);

  const columnDefs = useMemo(() => {
    if (selectedLocationOption === "direct") {
      return dieLotIntransitColumns;
    }
    return dieLotStatusColumns;
  }, [selectedLocationOption]);

  const fetchDieLotStatus = (matShipStatus) => {
    let params = `?plant=${plant}&material=${material}`;
    let url;

    if (matShipStatus === "diebank" || matShipStatus === "normal") {
      params += `&inPlant=${matShipStatus === "diebank"}`;
      url = `/fetchDieLotStatus${params}`;
    } else if (matShipStatus === "direct") {
      url = `/fetchDieLotIntransitStatus${params}`;
    }

    fetchData(url, setDieLotStatusData, []);
  };

  // find column and location of the qty
  const findQtyLoc = (dieLot) => {
    const column = dieLotStatusColumns.find((column) => {
      //ignore the plant numerical value
      //only looking for qty
      if ("plant" == column.field) {
        return false;
      }
      return dieLot[column.field] > 0;
    });
    return {
      qty: dieLot[column.field],
      location: column.headerName,
    };
  };

  const onRowClicked = (evt) => {
    const rowIndex = evt.node.rowIndex;
    if (rowIndex === selectedRow) {
      return;
    }

    setSelectedRow(rowIndex);
    let dieLotData = {};
    const {
      material,
      batch,
      plant,
      shippingPlant,
      deliveryNumber,
      qty,
      shipDate,
      waybill,
    } = dieLotStatusData[rowIndex];

    if (selectedLocationOption === "direct") {
      dieLotData = {
        matlMasterDieName: material,
        dieLot: batch,
        plant: shippingPlant,
        matShipStatus: selectedLocationOption,
        deliveryNote: deliveryNumber,
        qtyToShip: qty.trim(),
        dateShipped: shipDate,
        sapWaybill: waybill,
      };
    } else {
      const qtyLoc = findQtyLoc(dieLotStatusData[rowIndex]);

      dieLotData = {
        ...dieLotData,
        matlMasterDieName: material,
        dieLot: batch,
        plant: plant,
        matShipStatus: selectedLocationOption,
        qtyToShip: qtyLoc.qty,
        location: qtyLoc.location,
      };
    }

    setDieLotData(dieLotData);
  };

  const handleChange = (evt) => {
    const value = evt.target.value;
    fetchDieLotStatus(value);
    setSelectedLocationOption(value);
    setSelectedRow(-1);
  };

  return (
    <Grid container spacing={1}>
      <Grid item xs={12} sm={4}>
        <FormControl
          required
          fullWidth
          error={!selectedLocationOption}
          color="secondary"
          variant="outlined"
        >
          <InputLabel className={classes.textField}>
            Die Lot Location Info
          </InputLabel>
          <Select
            name={"selectedLocationOption"}
            onChange={handleChange}
            required
            value={selectedLocationOption}
          >
            {dieLocationOptions.map((item, i) => (
              <MenuItem key={i} value={item.value}>
                {item.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid container spacing={1}>
        <div
          className={"ti-server-ag-grid ag-theme-alpine"}
          style={{ marginBottom: "10px", width: "100%", height: "100%" }}
        >
          <AgGridReact
            rowData={dieLotStatusData}
            columnDefs={columnDefs}
            domLayout={"autoHeight"}
            suppressCellSelection={true}
            suppressColumnVirtualisation={true}
            rowSelection={"single"}
            enableCellTextSelection={true}
            onRowClicked={onRowClicked}
          />
        </div>
      </Grid>
    </Grid>
  );
};
export default DieSelect;
