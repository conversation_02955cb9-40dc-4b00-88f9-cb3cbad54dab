import { useCallback } from "react";
import { useAxios } from "src/api/useAxios";

export const useWarningApi = () => {
  const { instance } = useAxios();

  const warningRefresh = useCallback(
    (vyperNumber, buildNumber, config = {}) =>
      instance
        .post("/vyper/v1/warning/refresh", { vyperNumber, buildNumber }, config)
        .then((result) => result.data),
    []
  );

  return { warningRefresh };
};
