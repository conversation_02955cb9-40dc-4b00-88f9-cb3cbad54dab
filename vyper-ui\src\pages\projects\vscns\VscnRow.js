import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { Help } from "src/component/help/Help";
import { Required } from "src/component/required/Required";
import { VscnCell } from "./VscnCell";

/**
 * Display PRA components in the row
 *
 * @param name - The component name
 * @param vyper - The vyper object
 * @param pras - Array of PRA objects
 * @param onSave - Callback for saving component value updates
 * @returns {JSX.Element}
 * @function
 */
export const VscnRow = ({ name, vyper, vscns, onChange }) => {
  const getTooltip = () => {
    switch (name) {
      case "Leadframe":
        return "leadframe";

      case "Mount Compound":
        return "mountcompound";

      case "MB Diagram":
        return "mbdiagram";

      case "Wire":
        return "wire";

      case "Mold Compound":
        return "moldcompound";

      case "MSL":
        return "msl";

      default:
        return "component";
    }
  };

  return (
    <TableRow hover>
      <TableCell variant="head">
        <Help name={getTooltip()} /> {name} <Required required={true} />
      </TableCell>
      {vscns.map((vscn, n) => {
        const canEdit = ![
          "Leadframe",
          "MB Diagram",
          "Mount Compound",
          "Wire",
          "Mold Compound",
        ].includes(name);
        return (
          <TableCell key={n}>
            <VscnCell
              vyper={vyper}
              vscn={vscn}
              name={name}
              onChange={onChange}
              canEdit={canEdit}
            />
          </TableCell>
        );
      })}
    </TableRow>
  );
};

VscnRow.propTypes = {
  name: PropTypes.string.isRequired,
  vyper: PropTypes.object.isRequired,
  vscns: PropTypes.array.isRequired,
};
