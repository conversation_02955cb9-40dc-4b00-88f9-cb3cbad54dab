import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import {
  hasFacility,
  hasMaterial,
  hasPackageNiche,
} from "src/pages/vyper/FormStatus";
import { Link } from "react-router-dom";
import { DataCell } from "src/component/datacell/DataCell";
import { convertBuildNumbertoVyperNumber } from "src/component/helper/convertBuildNumbertoVyperNumber";

const useStyles = makeStyles(() => ({
  link: {
    textDecoration: "none",
    color: "rgb(85, 26, 139)",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
      color: "red",
    },
  },
  sameline: {
    display: "inline",
  },
}));

export const BuildFlowCell = ({ build, buildFlow }) => {
  const classes = useStyles();

  const buildNumber = build?.buildNumber;
  const vyperNumber = convertBuildNumbertoVyperNumber(buildNumber);
  const buildflowName = buildFlow?.flowName || "TKY";
  const vyperSystem = { system: { name: "VYPER" } };

  return (
    <DataCell source={vyperSystem}>
      <Link
        className={classes.link}
        to={`/projects/${vyperNumber}/builds/${buildNumber}/flow`}
      >
        <h3 className={classes.sameline}>{buildflowName}</h3>
      </Link>
    </DataCell>
  );
};
