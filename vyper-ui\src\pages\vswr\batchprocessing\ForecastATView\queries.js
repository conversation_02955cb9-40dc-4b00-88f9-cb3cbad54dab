import axios from "axios";
import { useQuery } from "react-query";
import { BASE_URL } from "../common/scswrAPI";
import useSnackbar from "../../../../hooks/Snackbar";

export let useAtViewGetter = (swrId) => {
  let { enqueueErrorSnackbar } = useSnackbar();
  let atViewGetter = useQuery(
    ["getAtView", "swrId", swrId],
    () => {
      return axios
        .get(`${BASE_URL}/getAtView${swrId ? `?swrId=${swrId}` : ``}`)
        .then((res) => res.data);
    },
    {
      onError: () => {
        enqueueErrorSnackbar("An error occured while trying to load data.");
      },
    }
  );

  return atViewGetter;
};
