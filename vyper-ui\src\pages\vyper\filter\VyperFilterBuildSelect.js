import React, { useState } from "react";
import { Autocomplete } from "@material-ui/lab";
import { TextField } from "@material-ui/core";
import FormHelperText from "@material-ui/core/FormHelperText";
import produce from "immer";

const VyperFilterBuildSelect = ({
  filteroptions,
  onChange,
  currentFilters,
}) => {
  const handleBuildSelect = (e, val) => {
    onChange(
      produce(currentFilters, (draft) => {
        draft.buildNumbers = val;
        return draft;
      })
    );
  };
  return (
    <>
      <Autocomplete
        multiple
        options={filteroptions}
        onChange={handleBuildSelect}
        getOptionLabel={(filteroptions) => filteroptions.buildNumber}
        defaultValue={currentFilters.buildNumbers}
        renderInput={(params) => (
          <TextField
            {...params}
            variant="standard"
            placeholder="Build Number(s)"
          />
        )}
      />
      <FormHelperText>Build Number(s)</FormHelperText>
    </>
  );
};

export default VyperFilterBuildSelect;
