import React from "react";
import {
  CancelRecordIcon,
  CopyIcon,
  DeleteRecordIcon,
  DownloadRecordIcon,
  EditRecordIcon,
  ObsoleteRecordIcon,
  PlusRecordIcon,
  RebuildIcon,
  ViewCommentIcon,
  ViewRecordIcon,
  SelectRecordIcon,
} from "../Icons";

export function gridActionEditRecord(callback, tooltip = "Edit Record") {
  return {
    icon: () => <EditRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionDeleteRecord(callback, tooltip = "Delete Record") {
  return {
    icon: () => <DeleteRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionDownloadRecord(
  callback,
  tooltip = "Download Attachment"
) {
  return {
    icon: () => <DownloadRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionObsoleteRecord(
  callback,
  tooltip = "Obsolete Record"
) {
  return {
    icon: () => <ObsoleteRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionCancelRecord(callback, tooltip = "Cancel Record") {
  return {
    icon: () => <CancelRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionViewComment(callback, tooltip = "Comments") {
  return {
    icon: () => <ViewCommentIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionViewRecord(callback, tooltip = "View Record") {
  return {
    icon: () => <ViewRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionAdd(callback, tooltip) {
  return {
    icon: () => <PlusRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionCopyRecord(callback, tooltip = "Copy Record") {
  return {
    icon: () => <CopyIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionRefreshRecord(callback, tooltip = "Refresh Record") {
  return {
    icon: () => <RebuildIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}

export function gridActionSelectRecord(callback, tooltip = "Select Record") {
  return {
    icon: () => <SelectRecordIcon />,
    tooltip: tooltip,
    onClick: (event, rowData) => {
      return typeof callback === "function" ? callback(event, rowData) : null;
    },
  };
}
