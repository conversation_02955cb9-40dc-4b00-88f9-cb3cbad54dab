import React, { useContext, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import DialogContent from "@material-ui/core/DialogContent";
import DialogContentText from "@material-ui/core/DialogContentText";
import TextField from "@material-ui/core/TextField";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import MenuItem from "@material-ui/core/MenuItem";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { noop } from "src/component/vyper/noop";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  required: {
    color: "red",
    fontSize: "1.5rem",
    verticalAlign: "bottom",
    cursor: "default",
  },
}));

export const RejectionDialog = ({ children }) => {
  const { vget } = useContext(FetchContext);
  const [open, setOpen] = useState(false);
  const [parameters, setParameters] = useState({});

  // const useStyles = makeStyles(theme => ({
  //     dialogPaper: {
  //         height: '700'
  //     },
  // }));

  /**
   * @param {object} parameters
   * @param {string} parameters.type text, select
   * @param {string} parameters.type2 text, select
   * @param {string} parameters.title
   * @param {string} parameters.description
   * @param {string} parameters.value
   * @param {string} parameters.value2
   * @param {string} parameters.name
   * @param {string} parameters.textvalue
   * @param {string} parameters.onSave
   * @param {string} parameters.onClose   optional callback for when the dialog is closed with no answer save.
   * @param {string} parameters.multiline
   * @param {string} parameters.rows
   * @param {string} parameters.options
   * @param {string} parameters.bld
   */

  const [values, setValues] = useState([]);

  const handleOpen = (parameters) => {
    parameters.onSave = parameters.onSave || noop;
    parameters.onClose = parameters.onClose || noop;

    // setting default value when state is in bu review since we removed group dropdown
    if (parameters.isStateBuReview) {
      parameters.value2 = "BU_APPROVERS";
    }

    setParameters(parameters);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    parameters.onClose();
  };

  // called when the group changes
  const handleChangeGroup = (e) => {
    setValues([]);

    // only called on rework dialog, and a group has been selected
    if (parameters.title.includes("Rework") && e.target.value != null) {
      //determine the approval group
      let grp = e.target.value;
      if (grp.includes("_TEST_")) {
        grp = "_TEST_";
      } else if (grp === "BU_APPROVERS") {
        //used to ignore changing group
      } else {
        grp = grp.substring(grp.split("_", 2).join("_").length + 1);
      }

      // get the reasons
      vget("/vyper/v1/vyper/searchReasons?grp=" + grp, (json) =>
        setValues(json)
      );
    }

    // parameters.value = ''
    // setParameters({value: '', name: ''});

    setParameters({
      ...parameters,
      value2: e.target.value,
      name: e.target.name,
    });
  };

  // called when the reason changes
  const handleChangeReason = (e) => {
    setParameters({
      ...parameters,
      value: e.target.value,
      name: e.target.name,
    });
  };

  // the optional text has changed
  const handleTextChange = (e) => {
    setParameters({
      ...parameters,
      textvalue: e.target.value,
      name: e.target.name,
    });
  };

  const handleSave = () => {
    // textvalue becomes value when state is in bu review since there is no reason dropdown
    if (parameters.isStateBuReview) {
      parameters.value = parameters.textvalue;
      parameters.textvalue = "";
    }
    // console.log("Rejection Dialog parameters++++", parameters);
    parameters.onSave([
      parameters.value,
      parameters.textvalue,
      parameters.value2,
    ]);

    parameters.onClose();
    handleClose();
  };

  // disable save until we have a value
  const disabled = parameters.isStateBuReview
    ? parameters.textvalue == null || parameters.textvalue === ""
    : parameters.value == null || parameters.value === "";

  // user can select a reason after they select the group
  const firstSelectComplete =
    parameters.value2 != null && parameters.value2 !== "";

  const classes = useStyles();

  // label for the group list
  const groupLabel = parameters.title?.includes("Rework") ? (
    <div>
      <span className={classes.required}>*</span>
      <span>Approval group:</span>
    </div>
  ) : (
    <div>
      <span>Group:</span>
    </div>
  );

  return (
    <RejectionDialogContext.Provider
      value={{
        showRejectionDialog: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>{parameters.title}</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          {parameters.isStateBuReview ? undefined : (
            <div>
              {parameters.type2 === "select" ? groupLabel : ""}
              {parameters.type2 === "select" ? (
                <TextField
                  autoFocus
                  fullWidth
                  select
                  margin="dense"
                  name="value2"
                  value={parameters.value2 || ""}
                  onChange={handleChangeGroup}
                  variant="outlined"
                >
                  {parameters.options2.map((reasons) => (
                    <MenuItem key={reasons} value={reasons}>
                      {reasons}
                    </MenuItem>
                  ))}
                </TextField>
              ) : null}

              {parameters.type === "select" ? (
                <span>
                  <font className={classes.required}>*</font> Pick a reason:
                </span>
              ) : (
                ""
              )}
              {parameters.type === "select" ? (
                <TextField
                  fullWidth
                  select
                  margin="dense"
                  name="value"
                  value={parameters.value || ""}
                  onChange={handleChangeReason}
                  variant="outlined"
                  disabled={!firstSelectComplete}
                >
                  {values.map((reasons) => (
                    <MenuItem key={reasons} value={reasons}>
                      {reasons}
                    </MenuItem>
                  ))}
                </TextField>
              ) : null}
            </div>
          )}
          <DialogContentText>{parameters.description}</DialogContentText>

          <TextField
            fullWidth
            multiline
            rows="5"
            margin="dense"
            name="textvalue"
            value={parameters.textvalue}
            onChange={handleTextChange}
            variant="outlined"
          />
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          {parameters.type === "select" ? (
            <Button
              type="submit"
              onClick={handleSave}
              variant="contained"
              color="primary"
              disabled={disabled}
            >
              Rework
            </Button>
          ) : (
            <Button
              type="submit"
              onClick={handleSave}
              variant="contained"
              color="primary"
              disabled={!firstSelectComplete}
            >
              Approve
            </Button>
          )}
        </DialogActions>
      </Dialog>
      {children}
    </RejectionDialogContext.Provider>
  );
};

export const RejectionDialogContext = React.createContext(null);
