import React, { useContext, useState } from "react";
import { DataCell } from "../../../component/datacell/DataCell";
import { BackgrindDialog } from "../../../component/backgrind/BackgrindDialog";
import {
  hasFacility,
  hasMaterial,
  existsInFlow,
} from "src/pages/vyper/FormStatus";
import { VyperLink } from "src/pages/mockup/VyperLink";
import { HelperContext } from "src/component/helper/Helpers";
import { AtSelectionDialogContext } from "../atselection/AtSelectionDialog";
import { noop } from "src/component/vyper/noop";
import { ComponentMapContext } from "src/component/componentmap/ComponentMap";

export const BackgrindCell = ({ vyper, build, onSave }) => {
  const { canEditBackgrind } = useContext(HelperContext);
  const canEdit = canEditBackgrind(vyper, build);
  const [open, setOpen] = useState(false);
  const { showSelectionDialog } = useContext(AtSelectionDialogContext);
  const { componentMaps } = useContext(ComponentMapContext);
  const [isBackgrindReq, setIsBackgrindReq] = useState(
    build.backgrind?.backgrindVal || ""
  );

  if (
    !hasMaterial(build) ||
    !hasFacility(build) ||
    !existsInFlow(build, "Backgrind")
  )
    return null;

  const backgrindValue =
    build.backgrind?.backgrindSelected?.length === 0 ||
    build.backgrind?.backgrindSelected == null
      ? "select backgrind value"
      : `${build.backgrind.backgrindSelected.map((c) => c.value).toString()}`;

  const text =
    build.backgrind?.backgrindVal == null
      ? "select if backgrind required"
      : `${build.backgrind.backgrindVal}`;

  const handleClick = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // edit a component value
  // noinspection JSUnusedLocalSymbol
  const handleEditComponentValue = () => {
    // if this is an eng component, change to the vyper component name

    let cName = "Backgrind";
    const componentMap = componentMaps.find(
      (cm) => cm.engineeringAtssComponentName === cName
    );
    if (componentMap != null) {
      cName = componentMap.name;
    }

    // get the selected items
    const selection = build.selections.find(
      (s) => s.operation === "Backgrind" && s.name === cName
    );
    const selectedItems = selection == null ? [] : selection.items;
    // get the supplier part number / wire
    let supplierOrWire = build.components
      .find((c) => c.name === cName)
      ?.instances?.[0]?.priorities?.[0]?.object?.name?.replace(
        "Placeholder Supplier #: ",
        ""
      );

    // show the a/t selection dialog
    showSelectionDialog({
      selection: {},
      build: build,
      name: cName,
      selectionItems: selectedItems,
      supplierOrWire: supplierOrWire,
      disableAddEngineering: true,
      onSave: (selection) => {
        return onSave(isBackgrindReq, selection.items).catch(noop);
      },
    });
  };

  return (
    <DataCell source={build.backgrind?.source}>
      <div style={{ display: "flex" }}>
        <div style={{ marginRight: "10px" }}>
          <VyperLink onClick={handleClick} canEdit={canEdit}>
            {text}
          </VyperLink>
        </div>

        {text === "YES" ? (
          <VyperLink
            key="Backgrind Value"
            onClick={handleEditComponentValue}
            canEdit={canEdit}
          >
            <div key="Backgrind Value">{backgrindValue}</div>
          </VyperLink>
        ) : null}
      </div>

      <BackgrindDialog
        build={build}
        open={open}
        onClose={handleClose}
        onSave={onSave}
        isBackgrindReq={isBackgrindReq}
        setIsBackgrindReq={setIsBackgrindReq}
      />
    </DataCell>
  );
};
