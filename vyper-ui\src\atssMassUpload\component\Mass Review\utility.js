const COMPONENT_PRIORITY_SEPERATOR = "_";
const GROUP_ID_SEPERATOR = "=";
const DATAPOINTER_ID_SEPERATOR = "@";
const columnComparisonConfig = [
  {
    headerName: "Operation",
    colId: "operation",
    field: "operation",
    cellStyle: { fontWeight: "bold" },
    filter: "agTextColumnFilter",
    filterParams: {
      filterOptions: ["contains"],
      textMatcher: ({ filterOption, filterText, data }) => {
        if (filterText == null) {
          return false;
        }
        const field = data["dataPointer"];
        const operation = field.split(DATAPOINTER_ID_SEPERATOR)[0];
        switch (filterOption) {
          case "contains":
            return (
              operation.toLowerCase().indexOf(filterText.toLowerCase()) >= 0
            );
          default:
            // should never happen
            console.warn("invalid filter type " + filter);
            return false;
        }
      },
    },
    pinned: "left",
    lockPinned: true,
    lockPosition: true,
  },
  {
    headerName: "Component & Attributes",
    colId: "component",
    field: "component",
    filter: "agTextColumnFilter",
    filterParams: {
      filterOptions: ["contains"],
      textMatcher: ({ filterOption, value, filterText, data }) => {
        if (filterText == null) {
          return false;
        }
        const fields = data["dataPointer"].split(DATAPOINTER_ID_SEPERATOR);
        const operation = fields[0];
        const component = fields[1];
        const attribute = fields[2];
        switch (filterOption) {
          case "contains":
            return (
              (attribute + component).toLowerCase().indexOf(filterText) >= 0
            );
          default:
            // should never happen
            console.warn("invalid filter type " + filter);
            return false;
        }
      },
    },
    pinned: "left",
    lockPinned: true,
    lockPosition: true,
  },
];

const createColumnFromNameValue = (name, fieldId, dataPointer) => {
  var columnDef = {};
  columnDef["colId"] = name;
  columnDef["headerName"] = "";
  columnDef["dataPointer"] = dataPointer;
  columnDef["field"] = fieldId;
  columnDef["tooltipValueGetter"] = (params) => {
    const columnDef = params.colDef;
    return componentParagraphGetter({
      colDef: columnDef,
      data: params.data,
    });
  };
  columnDef["cellStyle"] = cellStyle;
  return columnDef;
};

const padStringOnLeft = (str, times, pattern) => {
  return str.toString().padStart(times, pattern);
};

const padStringOnRight = (str, times, pattern) => {
  return str.toString().padEnd(times, pattern);
};

const createColumnDefWithChildren = (params) => {
  const { name, colId, groupId } = params;
  const columnDef = {};
  columnDef["headerName"] = name;
  columnDef["colId"] = colId;
  columnDef["groupId"] = groupId;
  columnDef["children"] = [];
  return columnDef;
};

const cellStyle = (params) => {
  const colDef = params.colDef;
  const bgColor = getCellDifferenceBackgroundColorStyle(
    params.value,
    params.api.getValue(colDef, params.api.getDisplayedRowAtIndex(0))
  );
  if (bgColor) {
    return { "background-color": bgColor };
  }
};

const componentParagraphGetter = (params) => {
  const fieldId = params.colDef.dataPointer;
  if (!fieldId) return "";
  let data = params.data;
  var arr = fieldId.split(DATAPOINTER_ID_SEPERATOR);
  arr.pop();
  arr.push("paragraph");
  if (!data) return "";
  while (arr.length && (data = data[arr.shift()]));
  if (!data) {
    data = params.data;
    arr = fieldId.split(DATAPOINTER_ID_SEPERATOR);
    arr.pop();
    arr.push("texts");
    if (!data) return "";
    while (arr.length && (data = data[arr.shift()])); 
    return data;
  }
  return data;
};

const attributeValueGetter = (params) => {
  const fieldId = params.colDef.dataPointer;
  if (!fieldId) return "";
  let data = params.data;
  var arr = fieldId.split(DATAPOINTER_ID_SEPERATOR);
  if (!data) return "";
  while (arr.length && (data = data[arr.shift()]));
  return data;
};

const getComponentGroupId = (component) => {
  let groupId = (component["attributes"] ?? [])
    .map((attribute) => attribute["name"])
    .sort()
    .join(GROUP_ID_SEPERATOR);
  return component["name"] + " " + groupId;
};

const getOperationGroupId = (operation) => {
  let groupId = operation["components"]
    .map((component) => component["name"])
    .sort()
    .join(GROUP_ID_SEPERATOR);
  return groupId;
};

/**
 * Column Comparision Config Utilities
 */

const getIncludeSubFlows = (comparisionConfig) => {
  return comparisionConfig.flows || [];
};

const getIncludeOperations = (comparisionConfig) => {
  return comparisionConfig.operations || [];
};

const getIncludeComponents = (comparisionConfig) => {
  return comparisionConfig.components || [];
};

const findFieldToInclude = (includeFields, field) => {
  if (!includeFields || includeFields.length == 0) return true;
  return includeFields
    .map((field) => field.toLowerCase())
    .includes(field.toLowerCase());
};

/**
 * Utility function to dynamically fecth columns from the json traveller.
 *
 */

const createSpecDeviceColumnDef = () => {
  var columnDef = {};
  columnDef["headerName"] = "Spec Device";
  columnDef["colId"] = "specDevice";
  columnDef["field"] = "specDevice";
  columnDef["pinned"] = "left";
  return columnDef;
};

const getColumnDefinitonFromData = (data, fieldId, dataPointer, comparisionConfig) => {
  let columnDefs = [];

  if (data["subFlows"]) {
    columnDefs.push(createSpecDeviceColumnDef());
    for (let i = 0; i < data["subFlows"].length; ++i) {
      const subflow = data["subFlows"][i];
      if (
        !findFieldToInclude(
          getIncludeSubFlows(comparisionConfig),
          subflow["type"]
        )
      )
        continue;

      columnDefs = [
        ...columnDefs,
        ...getColumnDefinitonFromData(subflow, fieldId, fieldId, comparisionConfig),
      ];
    }
    return columnDefs;
  } else if (data["operations"]) {
    for (let i = 0; i < data["operations"].length; ++i) {
      const operation = data["operations"][i];
      if (
        !findFieldToInclude(
          getIncludeOperations(comparisionConfig),
          operation["name"]
        )
      )
        continue;

      const newFieldId = `${fieldId}${operation["name"]}`;
      var columnDef = {};
      columnDef["headerName"] = operation["name"];
      columnDef["colId"] = operation["name"];
      columnDef["dataPointer"] = newFieldId;
      columnDef["groupId"] = getOperationGroupId(operation);
      columnDef["children"] = getColumnDefinitonFromData(
        operation,
        newFieldId,
        newFieldId,
        comparisionConfig
      );
      columnDefs.push(columnDef);
    }
    return columnDefs;
  } else if (data["components"]) {
    for (let i = 0; i < data["components"].length; ++i) {
      const component = data["components"][i];
      if (
        !findFieldToInclude(
          getIncludeComponents(comparisionConfig),
          component["name"]
        )
      )
        continue;
      const newFieldId = `${fieldId}.${
        component["name"]
      }${COMPONENT_PRIORITY_SEPERATOR}${
        component["occurrence"] || component["priority"]
      }`;
      const newDataPointer = `${dataPointer}${DATAPOINTER_ID_SEPERATOR}${
        component["name"]
      }${COMPONENT_PRIORITY_SEPERATOR}${
        component["occurrence"] || component["priority"]
      }`;
      var columnDef = {};
      columnDef["headerName"] =
        padStringOnRight(component["name"], 28, " ") +
        padStringOnLeft(
          component["occurrence"] || component["priority"],
          2,
          "0"
        );
      columnDef["colId"] = component["name"];
      columnDef["groupId"] = getComponentGroupId(component);
      columnDef["dataPointer"] = newDataPointer;
      columnDef["columnGroupShow"] = i > 0 ? "open" : null;
      columnDef["children"] = [
        createColumnFromNameValue(component["name"], newFieldId + '.' + "value", newDataPointer + DATAPOINTER_ID_SEPERATOR + "value"),
        ...getColumnDefinitonFromData(component, newFieldId, newDataPointer, comparisionConfig),
      ];
      columnDefs.push(columnDef);
    }
    return columnDefs;
  } else if (data["attributes"]) {
    for (let i = 0; i < data["attributes"].length; ++i) {
      const attribute = data["attributes"][i];
      const newField = `${fieldId}.${attribute["name"]}.value`;
      const newDataPointer = `${dataPointer}${DATAPOINTER_ID_SEPERATOR}${attribute["name"]}${DATAPOINTER_ID_SEPERATOR}value`;
      var columnDef = {};
      columnDef["headerName"] = attribute["name"];
      columnDef["colId"] = attribute["name"];
      columnDef["dataPointer"] = fieldId;
      columnDef["field"] = newField;
      columnDef["dataPointer"] = newDataPointer;
      columnDef["cellStyle"] = cellStyle;
      columnDef["columnGroupShow"] = "open";
      columnDefs.push(columnDef);
    }
    return columnDefs;
  }
  return columnDefs;
};

export const getGroupNamesFromColumnDefs = (columnDefs) => {
  const groupNames = [];
  for (const operation of columnDefs) {
    if (!operation["groupId"]) continue;
    groupNames.push(operation["groupId"]);
    for (const component of operation["children"]) {
      if (!component["groupId"]) continue;
      groupNames.push(component["groupId"]);
    }
  }
  return groupNames;
};

const getComponentChildrenCountMap = (components) => {
  return components.split(GROUP_ID_SEPERATOR).reduce((countMap, item) => {
    countMap[item] = (countMap[item] || 0) + 1;
    return countMap;
  }, {});
};

const createComonColumns = (def_a, columnsMap) => {
  for (const operation of def_a) {
    // check if it has childrens
    if (!operation["children"]) {
      columnsMap.set(operation["colId"], operation);
      continue;
    }

    // so it has components to iterate over
    if (!columnsMap.has(operation["colId"])) {
      columnsMap.set(operation["colId"], { ...operation, children: new Map() });
    }

    const componentCountMap = getComponentChildrenCountMap(
      operation["groupId"]
    );

    const operationChildrenMap = columnsMap.get(operation["colId"])["children"];
    for (const component of operation["children"]) {
      if (!component["children"]) {
        operationChildrenMap.set(component["colId"], component);
        continue;
      }
      if (!operationChildrenMap.has(component["colId"])) {
        operationChildrenMap.set(component["colId"], {
          ...component,
          children: new Map(),
          count: componentCountMap[component["colId"]],
        });
      } else {
        const a = operationChildrenMap.get(component["colId"])["count"];
        operationChildrenMap.set(component["colId"], {
          ...operationChildrenMap.get(component["colId"]),
          count: Math.max(a, componentCountMap[component["colId"]]),
        });
      }

      // add the attributes into the components children map
      const attributesMap = operationChildrenMap.get(component["colId"])[
        "children"
      ];
      for (const attribute of component["children"]) {
        if (!attributesMap.has(attribute["colId"])) {
          attributesMap.set(attribute["colId"], { ...attribute });
        }
      }
    }
  }
  return columnsMap;
};

export const combineColumnDefinition = (def_a, def_b, columnsMap) => {
  return createComonColumns(def_b, createComonColumns(def_a, columnsMap));
};

export const prepareAgGridColumnsFromColumnMap = (columnMap) => {
  const columnDefs = [];
  for (const operation of columnMap.values()) {
    if (!operation["children"]) {
      columnDefs.push(operation);
      continue;
    }
    const operationColumnDef = createColumnDefWithChildren({
      name: operation["headerName"],
      colId: operation["colId"],
      groupId: operation["groupId"],
    });
    // refresh the componentArr
    for (const component of operation["children"].values()) {
      let iteration = 0;
      while (++iteration <= component["count"]) {
        const componentColumnDef = createColumnDefWithChildren({
          name: component["headerName"],
          colId: component["colId"],
          groupId: component["groupId"],
        });
        for (const attributes of component["children"].values()) {
          // replace the 1 with the iteration number
          const newAttribute = Object.assign({}, attributes);
          newAttribute["field"] = newAttribute["field"].replace(
            "1",
            iteration.toString()
          );
          // add the attributes in the componentColumnDef
          componentColumnDef["children"].push(newAttribute);
        }
        // add the prepared componentsArr into operationArr
        operationColumnDef["children"].push(componentColumnDef);
      }
    }
    // add the prepared operation
    columnDefs.push(operationColumnDef);
  }
  return columnDefs;
};

const columnAttributeValueGetter = (params) => {
  const columnDef = params.data;
  const specDevice = params.colDef.colId;
  return attributeValueGetter({
    colDef: columnDef,
    data: params.context.data.filter(
      (trav) => trav.specDevice === specDevice
    )[0],
  });
};

const changeColumnHeaderBGColor = (api, colId) => {
  const colDef = api.getColumnDef(colId);
  if(!colDef) return ;
  if (colDef.headerClass === "ag-header-diff") {
    return;
  }
  colDef.headerClass = "ag-header-diff";
  api.refreshHeader();
};

const getCellDifferenceBackgroundColorStyle = (valCell, valRef) => {
  if (!valRef) return "yellow";
  if (!valCell) return "#fcc";
  if (valRef !== valCell) return "#cfc";
};

const columnViewCellStyle = (params) => {
  const colDef = params.data;
  const specDevice = params.colDef.colId;
  const data = params.context.data.filter(
    (trav) => trav.specDevice === specDevice
  )[0];
  const reference = params.context.referenceData;
  // if the value if found different update the column header to be marked as red
  const bgColor = getCellDifferenceBackgroundColorStyle(
    attributeValueGetter({ data, colDef }),
    attributeValueGetter({ data: reference, colDef })
  );
  if (bgColor) {
    changeColumnHeaderBGColor(params.api, specDevice);
    return { "background-color": bgColor };
  }
};

const createColumnViewColumnDef = (material, isReference) => {
  const columnDef = {};
  columnDef["headerName"] = material;
  columnDef["colId"] = material;
  columnDef["groupId"] = material;
  columnDef["valueGetter"] = columnAttributeValueGetter;
  columnDef["cellStyle"] = columnViewCellStyle;

  columnDef["tooltipValueGetter"] = (params) => {
    const columnDef = params.data;
    const specDevice = params.colDef.colId;
    return componentParagraphGetter({
      colDef: columnDef,
      data: params.context.data.filter(
        (trav) => trav.specDevice === specDevice
      )[0],
    });
  };
  if (isReference) {
    return {
      ...columnDef,
      pinned: "left",
      lockPinned: true,
      lockPosition: true,
      cellStyle: {
        background: "#B8E2F2",
      },
    };
  }
  return columnDef;
};

export const getColumnComparisionColumnDefinition = (data) => {
  return [
    ...columnComparisonConfig,
    ...data.map((trav) =>
      createColumnViewColumnDef(
        trav["header"]["specDevice"],
        trav["isReference"]
      )
    ),
  ];
};

export const getRowComparisionLegend = (columnDefs) => {
  if (columnDefs && columnDefs.length < 0) return [];
  const rowLegend = [];
  for (let i = 0; i < columnDefs.length; i++) {
    const operation = columnDefs[i];
    if (!operation["children"]) {
      continue;
    }
    for (let j = 0; j < operation["children"].length; ++j) {
      const component = operation["children"][j];
      for (let k = 0; k < component["children"].length; ++k) {
        const attribute = component["children"][k];
        rowLegend.push({
          operation: k + j === 0 ? operation["headerName"] : "",
          component: `- ${
            k == 0 ? component["headerName"] : `    ${attribute["headerName"]}`
          }`,
          field: attribute["field"],
          ...attribute,
        });
      }
    }
  }
  return rowLegend;
};

export const flattenTravelerData = (travelers) => {
  const flattenedData = [];
  for (const traveler of travelers) {
    const flattenedTraveler = {};

    flattenedTraveler["specDevice"] = traveler["header"]["specDevice"];
    flattenedTraveler["isReference"] = traveler["isReference"];
    // then go over each subflows to get operations
    const operationMap = flattenedTraveler;
    for (const subFlow of traveler["subFlows"]) {
      for (const operation of subFlow["operations"]) {
        // check if operation already present
        if (!operationMap[operation["name"]]) {
          // create the operation object
          operationMap[operation["name"]] = {};
        } else {
          // operationMap is duplicated
        }
        const componentsMap = operationMap[operation["name"]];
        if (!operation["components"]) continue;
        for (const component of operation["components"]) {
          if (!component) continue;
          // check if component is already present
          const compKey =
            component["name"] +
            "_" +
            (component["occurrence"] || component["priority"]);
          if (!componentsMap[compKey]) {
            componentsMap[compKey] = {
              value: component["value"],
              paragraph: component["paragraph"] || component["texts"],
            };
          } else {
            // duplicate component is present
          }
          const attributesMap = componentsMap[compKey];
          if (!component["attributes"]) continue;
          for (const attribute of component["attributes"]) {
            if (!attribute) continue;
            const attrKey = attribute["name"];
            if (!attributesMap[attrKey]) {
              attributesMap[attrKey] = {
                value: attribute["value"] + "   " + (attribute["unit"] || ""),
              };
            } else {
              // duplicate attribute present
            }
          }
        }
      }
    }
    flattenedData.push(flattenedTraveler);
  }
  return flattenedData;
};

const getColumnDefiniton = (data, comparisionConfig) => {
  return getColumnDefinitonFromData(data, "", "", comparisionConfig);
}

export default getColumnDefiniton;
