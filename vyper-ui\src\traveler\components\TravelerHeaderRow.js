import PropTypes from "prop-types";
import React from "react";
import { TravelerHeaderCol1 } from "./TravelerHeaderCol1";
import { TravelerHeaderCol2 } from "./TravelerHeaderCol2";
import { TravelerHeaderCol3 } from "./TravelerHeaderCol3";
import { TravelerHeaderCol4 } from "./TravelerHeaderCol4";

/**
 * Returns a header row
 * @param {string} title1 - The column #1 title
 * @param {*} value1 - The column #2 value
 * @param {string} [title2] - The column #3 title
 * @param {*} [value2] - The column #4 value
 * @returns {JSX.Element}
 * @constructor
 */
export function TravelerHeaderRow({ title1, value1, title2, value2 }) {
  const spaces2 = "  ";
  if (title2 != null) {
    return (
      <>
        <TravelerHeaderCol1 title={title1} />:{" "}
        <TravelerHeaderCol2 value={value1} />
        {spaces2}
        <TravelerHeaderCol3 title={title2} />:{" "}
        <TravelerHeaderCol4 value={value2} />
      </>
    );
  } else {
    return (
      <>
        <TravelerHeaderCol1 title={title1} />:{" "}
        <TravelerHeaderCol2 value={value1} />
        {spaces2}
      </>
    );
  }
}

TravelerHeaderRow.propTypes = {
  title1: PropTypes.string,
  value1: PropTypes.any,
  title2: PropTypes.string,
  value2: PropTypes.any,
};
