import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { samenessPackageNiche } from "../../../component/sameness/sameness";
import { PackageNicheCell } from "./PackageNicheCell";
import { PackageNicheDialogContext } from "./PackageNicheDialog";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const PackageNicheRow = ({ vyper, builds, onChange, showSameness }) => {
  const { showDialog } = useContext(PackageNicheDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleChangePkgNiche = (pkgNiche, build) => {
    return buildDao
      .changePkgNiche(vyper.vyperNumber, build.buildNumber, pkgNiche)
      .then((json) => onChange(json))
      .catch(noop);
  };
  const handleClick = (build) => {
    // show the package niche dialog
    showDialog({
      pkgGroup: build?.material?.object?.PackageGroup,
      onSave: (value) => handleChangePkgNiche(value, build),
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessPackageNiche(builds),
      })}
      hover
    >
      <RowPrefix help="pkgniche" title="Package Niche" />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <PackageNicheCell
            vyper={vyper}
            build={build}
            packageNiche={build.packageNiche}
            onClick={handleClick}
          />
        </TableCell>
      ))}
    </TableRow>
  );
};
