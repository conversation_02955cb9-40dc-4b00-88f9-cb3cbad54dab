/* eslint-disable prettier/prettier */
import { fetchGet, fetchPost } from "./FetchUtilities";
import config from "src/buildEnvironment";

const dataSvcUrl = "/rexdata/v1/data/{UUID}";
const uuidUrl = "/rex/api/v1/datasvc/fetch-uuid/{screen}/{field}";
const fetchPostDataSvcUrl = "/rexdata/v1/data/";
const { defaultDatasvcSize } = config;

// --------------------NOT-WORKING-YET-USE-getUUID-AND-getOptions-FOR-NOW--------------------------
// export function fetchDatasvcOptions(screen, field, callback) {
//     let uuid
//     let options
//     Promise.all([getUUID(screen, field)])
//         .then((value) => {
//             uuid = value[0]
//         });

//     console.log(uuid)
//     Promise.all([fetchGet(dataSvcUrl.replace('{UUID}', uuid), (httpStatus, result) => {
//             result.dataFacts[0].response.data.map((e) => {
//                 return {
//                     label: e.LABEL,
//                     value: e.VALUE,
//                 };
//             });
//         })])
//         .then((values) => options = values);
//     console.log(options)
//     return options
// }

export function getUUID(screen, field) {
  return new Promise((resolve, reject) => {
    fetch(uuidUrl.replace("{screen}", screen).replace("{field}", field))
      .then((response) => {
        if (response.status !== 200) {
          throw new Error();
        }
        return response.json();
      })
      .then((json) => {
        resolve(json.uuid);
      })
      .catch(() => {
        reject();
      });
  });
}

export function getOptions(uuid) {
  return new Promise((resolve, reject) => {
    fetch(dataSvcUrl.replace("{UUID}", uuid))
      .then((response) => {
        if (response.status !== 200) {
          throw new Error();
        }
        return response.json();
      })
      .then((json) => {
        const options = json.dataFacts[0].response.data.map((e) => {
          return {
            label: e.LABEL ? e.LABEL : e.VALUE || e.value,
            value: e.ID ? e.id : e.VALUE == 0 ? e.VALUE : e.VALUE || e.value,
          };
        });
        resolve(options);
      })
      .catch(() => {
        reject();
      });
  });

  // const options = fetchGet(dataSvcUrl.replace('{UUID}', uuid), (httpStatus, result) => {
  //     result.dataFacts[0].response.data.map((e) => {
  //         return {
  //             label: e.LABEL,
  //             value: e.VALUE,
  //         };
  //     });
  // });

  // return options
}

/* this is used by react-data-grid */
export function getOptionsIdValue(uuid) {
  return new Promise((resolve, reject) => {
    fetch(dataSvcUrl.replace("{UUID}", uuid))
      .then((response) => {
        if (response.status !== 200) {
          throw new Error();
        }
        return response.json();
      })
      .then((json) => {
        const options = json.dataFacts[0].response.data.map((e) => {
          return {
            value: e.LABEL,
            id: e.VALUE,
          };
        });
        resolve(options);
      })
      .catch(() => {
        reject();
      });
  });
}

/* this is used by react-data-grid autcompleteeditor */
export function getOptionsAutoComplete(uuid) {
  return new Promise((resolve, reject) => {
    fetch(dataSvcUrl.replace("{UUID}", uuid))
      .then((response) => {
        if (response.status !== 200) {
          throw new Error();
        }
        return response.json();
      })
      .then((json) => {
        const options = json.dataFacts[0].response.data.map((e) => {
          return {
            title: e.value,
            id: e.id,
          };
        });
        resolve(options);
      })
      .catch(() => {
        reject();
      });
  });
}

export function formatOptionsForAutocomplete(data) {
  const options = data.map((e) => {
    return {
      title: e.value,
      id: e.id,
    };
  });
  return options;
}

export function convertUUID(uuid) {
  let id = uuid;
  let DASH = "-";
  if (!id.includes(DASH)) {
    id =
      uuid.substring(0, 0 + 8) +
      DASH +
      uuid.substring(8, 8 + 4) +
      DASH +
      uuid.substring(12, 12 + 4) +
      DASH +
      uuid.substring(16, 16 + 4) +
      DASH +
      uuid.substring(20, 20 + 12);

    id = id.toLowerCase();
  }
  return id;
}

export function createDataSvcRequest(
  dataSvcId,
  dataFactFilterType,
  dataFactFilterAttrib,
  dataFactFilterValue,
  sourceFieldType
) {
  let dataFact;
  let datasvcValueFilter;
  if (sourceFieldType == "AUTOCOMPLETE" || sourceFieldType == "TYPEAHEAD") {
    datasvcValueFilter = dataFactFilterValue.value;
    if (datasvcValueFilter != "") {
      dataFact = {
        dataFacts: [
          {
            uuid: dataSvcId,
            dataFactPageable: {
              page: 0,
              size: defaultDatasvcSize,
              dataFactFilter: [
                {
                  type: dataFactFilterType,
                  attribute: dataFactFilterAttrib,
                  value: datasvcValueFilter,
                },
              ],
            },
          },
        ],
      };
    }
  } else if (
    sourceFieldType == "SELECT_MULTI" ||
    sourceFieldType == "AUTOCOMPLETE_MULTI"
  ) {
    let df = [];
    dataFactFilterValue.map((val) => {
      switch (sourceFieldType) {
        case "SELECT_MULTI":
          datasvcValueFilter = val;
          break;
        case "AUTOCOMPLETE_MULTI":
          datasvcValueFilter = val.value;
          break;
      }
      if (datasvcValueFilter != "") {
        df.push({
          uuid: dataSvcId,
          dataFactPageable: {
            page: 0,
            size: defaultDatasvcSize,
            dataFactFilter: [
              {
                type: dataFactFilterType,
                attribute: dataFactFilterAttrib,
                value: datasvcValueFilter,
              },
            ],
          },
        });
      }
    });
    dataFact = {
      dataFacts: df,
    };
  } else if (
    dataFactFilterType == "" &&
    dataFactFilterAttrib == "" &&
    dataFactFilterValue == "" &&
    sourceFieldType == ""
  ) {
    dataFact = {
      dataFacts: [
        {
          uuid: dataSvcId,
          dataFactPageable: {
            page: 0,
            size: defaultDatasvcSize,
            dataFactFilter: [],
          },
        },
      ],
    };
  } else {
    datasvcValueFilter = dataFactFilterValue;
    if (datasvcValueFilter != "") {
      dataFact = {
        dataFacts: [
          {
            uuid: dataSvcId,
            dataFactPageable: {
              page: 0,
              size: defaultDatasvcSize,
              dataFactFilter: [
                {
                  type: dataFactFilterType,
                  attribute: dataFactFilterAttrib,
                  value: datasvcValueFilter,
                },
              ],
            },
          },
        ],
      };
    }
  }
  return dataFact;
}

export function createDataSvcRequestMultiFilter(
  dataSvcId,
  dataFactFilters = []
) {
  let dataFact;
  let datasvcValueFilters =
    dataFactFilters.length <= 0
      ? []
      : dataFactFilters.map((filter) => {
          return {
            type: filter.type,
            attribute: filter.attribute,
            value: filter.value,
          };
        });

  dataFact = {
    dataFacts: [
      {
        uuid: dataSvcId,
        page: 0,
        size: defaultDatasvcSize,
        dataFactPageable: {
          dataFactFilter: datasvcValueFilters,
        },
      },
    ],
  };

  return dataFact;
}

export function setFilteredOptions(dataFact, targetFieldSetFn) {
  (async () => {
    fetchPost(fetchPostDataSvcUrl, dataFact, (httpStatus, response) => {
      if (response.status === "error") {
        alert(`Something went wrong. Please try again`);
      } else {
        let data = response.dataFacts[0].response.data || [];
        if (data.length > 0) {
          let options = [
            {
              title: "----- Clear Selection -----",
              id: "clear",
            },
          ];
          data.map((opt) => {
            let index = options.findIndex((r) => {
              return r.id == opt.id;
            });
            if (index < 0) {
              options.push({
                title: opt.value,
                id: opt.id,
              });
            }
          });
          targetFieldSetFn(options);
        }
      }
    });
  })();
}
