import React, { useContext } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { QuestionDialogContext } from "./QuestionDialog";

const useStyles = makeStyles(() => ({
  link: {
    textDecoration: "underline",
    color: "rgb(85, 26, 139)",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
      color: "red",
    },
  },
}));

export const QuestionLink = ({
  title,
  description,
  value,
  onSave,
  multiline = false,
  defaultValue,
  rows = 10,
  canEdit = true,
}) => {
  const { showQuestionDialog } = useContext(QuestionDialogContext);

  const handleClick = (e) => {
    e.preventDefault();
    showQuestionDialog({
      title,
      description,
      value,
      onSave,
      multiline,
      rows,
    });
  };

  const classes = useStyles();

  return (
    <span>
      {canEdit ? (
        <a className={classes.link} onClick={handleClick}>
          {value || defaultValue}
        </a>
      ) : (
        <span>{value || defaultValue}</span>
      )}
    </span>
  );
};
