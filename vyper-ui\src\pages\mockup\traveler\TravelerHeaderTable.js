import React from "react";
import { Grid } from "@material-ui/core";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { headerItems } from "src/pages/mockup/traveler/headerItems";

const useStyles = makeStyles({
  root: {
    marginTop: "1rem",
  },
  title: {
    textAlign: "right",
    "&::after": {
      content: '":"',
    },
  },
  value: {
    paddingLeft: "1rem",
  },
});

export const TravelerHeaderTable = ({ build }) => {
  const items = headerItems(build);

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Grid container>
        {items.map((item, n) => (
          <React.Fragment key={n}>
            <Grid className={classes.title} xs={3} item>
              {item.title}
            </Grid>
            <Grid className={classes.value} xs={3} item>
              {item.value}
            </Grid>
          </React.Fragment>
        ))}
      </Grid>
    </div>
  );
};
