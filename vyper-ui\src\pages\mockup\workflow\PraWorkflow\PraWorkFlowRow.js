import React from "react";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import { Help } from "../../../../component/help/Help";
import { PraWorkFlow } from "./PraWorkFlow";

export const PraWorkFlowRow = ({
  vyper,
  pras,
  onChangePra,
  onDeletePra,
  reqPrasCheckedGroups,
  taskAssignments,
}) => {
  return (
    <TableRow hover>
      <TableCell variant="head">
        <Help name="workflow" /> WorkFlow
      </TableCell>
      {pras.map((pra, n) => {
        const praTaskAssignments = taskAssignments.filter(
          (assignment) => assignment.taskName === pra.praNumber
        );
        return (
          <TableCell key={n}>
            <PraWorkFlow
              vyper={vyper}
              pra={pra}
              onChangePra={onChangePra}
              onDeletePra={onDeletePra}
              reqCheckedGroups={reqPrasCheckedGroups[n]}
              praTaskAssignments={praTaskAssignments}
            />
          </TableCell>
        );
      })}
    </TableRow>
  );
};

export default PraWorkFlowRow;
