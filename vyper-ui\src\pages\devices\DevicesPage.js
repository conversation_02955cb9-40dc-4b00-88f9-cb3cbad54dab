import React, { useState } from "react";
import { Link } from "react-router-dom";
import useQueryParam from "../../component/queryparams/useQueryParam";

import TiServerAgGrid, { exportGridAsExcel } from "../../lib/TiServerAgGrid";
import IconButton from "@material-ui/core/IconButton";
import VisibilityIcon from "@material-ui/icons/Visibility";
import Button from "@material-ui/core/Button";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";
import { PersistentFilters } from "./PersistentFilters";
import config from "../../../src/buildEnvironment";
const { externalUse } = config;

let body = document.body,
  html = document.documentElement;
let height =
  Math.max(
    body.scrollHeight,
    body.offsetHeight,
    html.clientHeight,
    html.scrollHeight,
    html.offsetHeight
  ) - 200;

const actionRenderer = (node) => {
  const rowData = node.data;
  if (rowData !== undefined) {
    return (
      <IconButton
        onClick={(e) => {
          e.preventDefault();
          location.href = `/vyper/projects/${rowData.vyperNumber}/builds/${rowData.buildNumber}`;
        }}
        color="secondary"
      >
        <VisibilityIcon style={{ fontSize: "1rem" }} />
      </IconButton>
    );
  }
  return <></>;
};

const buildNumberRenderer = (node) => {
  const rowData = node.data;
  if (rowData !== undefined) {
    return (
      <Link
        to={`/projects/${rowData.vyperNumber}/builds/${rowData.buildNumber}/selection`}
      >
        {rowData.buildNumber}
      </Link>
    );
  }
  return <></>;
};

const hideUserId = (node) => {
  const rowData = node.data;
  if (rowData !== undefined) {
    if (externalUse) {
      let resultText = rowData?.submitter?.split("/")[0];
      return <span>{resultText}</span>;
    }

    return <span>{rowData?.submitter}</span>;
  }
  return <></>;
};

const defaultColDef = {
  filterParams: {
    suppressAndOrCondition: true,
    filterOptions: [
      {
        displayKey: "contains",
        displayName: "Contains",
        predicate: () => {}, //needed to use display key and name
      },
      {
        displayKey: "=",
        displayName: "Equal",
        predicate: () => {}, //needed to use display key and name
      },
      {
        displayKey: "<>",
        displayName: "Not Equal",
        predicate: () => {}, //needed to use display key and name
      },
      {
        displayKey: "<",
        displayName: "Less Than",
        predicate: () => {}, //needed to use display key and name
      },
      {
        displayKey: ">",
        displayName: "Greater Than",
        predicate: () => {}, //needed to use display key and name
      },
      {
        displayKey: "<=",
        displayName: "Less Than or Equal",
        predicate: () => {}, //needed to use display key and name
      },
      {
        displayKey: ">=",
        displayName: "Greater Than or Equal",
        predicate: () => {}, //needed to use display key and name
      },
    ],
  },
};

const columns = [
  {
    headerName: "Actions",
    cellRenderer: actionRenderer,
    filter: false,
    sortable: false,
    width: 90,
  },
  { headerName: "Facility", field: "facilityAt" },
  {
    headerName: "Request Date",
    field: "dateSubmitted",
  },
  {
    headerName: "Build Number",
    field: "id",
    cellRenderer: buildNumberRenderer,
    linkCellRendererParams: {
      links: (params) => {
        /** @type {ResultRowData} */
        const data = params.data;
        const label = `${data.buildNumber}`;
        const url =
          location.origin +
          `/vyper/projects/${data.vyperNumber}/builds/${data.buildNumber}/selection`;
        return { label, url };
      },
    },
    cellStyle: {
      fontSize: 14,
    },
  },

  { headerName: "Build Type", field: "buildtype" },
  { headerName: "Flow Type", field: "flowType" },
  { headerName: "State", field: "state" },
  { headerName: "Title", field: "title" },
  {
    headerName: "Description",
    field: "description",
    cellStyle: {
      minWidth: 150,
      maxWidth: 300,
      wordBreak: "break-word",
      fontSize: 14,
    },
  },
  { headerName: "Device", field: "material" },
  { headerName: "SBE", field: "sbe" },
  { headerName: "SBE-1", field: "sbe1" },
  { headerName: "SBE-2", field: "sbe2" },
  { headerName: "PKG GRP", field: "packageGroup" },
  { headerName: "PIN PKG", field: "packagePin" },
  { headerName: "NICHE", field: "niche" },
  {
    headerName: "Requestor",
    field: "submitter",
    cellRenderer: hideUserId,
  },
  { headerName: "Owner names", field: "ownerNames" },
  {
    headerName: "Last Change",
    field: "detail",
    cellStyle: {
      minWidth: 150,
      maxWidth: 300,
      wordBreak: "break-word",
      fontSize: 14,
    },
  },
  { headerName: "Last Change Date", field: "touched" },
];

export const DevicesPage = () => {
  const [filterState, setFilterState] = useLocalStorage(
    "tableFilterStates.allBuildsFilterState",
    {}
  );
  const [sortState, setSortState] = useLocalStorage(
    "tableFilterStates.allBuildsSortState",
    []
  );

  const defaultSortColDef = {
    state: [{ colId: "dateSubmitted", sort: "desc", sortIndex: 0 }],
  };
  const filters = Object.keys(filterState);

  const [hasTableLoaded, setHasTableLoaded] = useState(false);
  const [gridRef, setGridRef] = useState(null);

  const [device] = useQueryParam("device", null);
  const hasPersistentFilters = Object.keys(filterState).length > 0;
  const hasPresistentSort =
    sortState.length > 0 || Object.keys(sortState).length > 0;

  let url = "/vyper/v1/vyper/project/device";
  if (device != null) {
    console.log({ url });
    url += "?filter=" + encodeURI("material|contains|" + device);
  }

  const onFirstDataRendered = (api) => {
    setGridRef(api);
    setHasTableLoaded(true);
  };

  const exportAsExcel = () => {
    const api = gridRef.api;
    const columnApi = gridRef.columnApi;
    exportGridAsExcel(api, columnApi, {
      columnFilter: (column) => {
        return column.getColDef().headerName !== "Actions";
      },
      fileName: "All_Builds_Vyper.xlsx",
    });
  };

  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        All Builds
        <div
          style={{
            fontSize: "1.25rem",
            fontWeight: "bold",
            marginBottom: "5px",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Button
            variant="contained"
            onClick={() => {
              gridRef.api.setFilterModel({});
            }}
            color="secondary"
            style={{ marginRight: "10px" }}
          >
            Clear filters
          </Button>
          <Button variant="contained" onClick={exportAsExcel} color="secondary">
            Export as Excel
          </Button>
        </div>
      </div>
      <div
        style={{ display: "flex", flexDirection: "row", alignItems: "center" }}
      >
        {hasPersistentFilters && (
          <div style={{ marginRight: "5px" }}>Persistent Filters</div>
        )}
        {filters?.map((filter, n) => (
          <PersistentFilters
            key={n}
            filter={filter}
            value={filterState[`${filter}`]["filter"]}
            columns={columns}
          />
        ))}
      </div>

      <TiServerAgGrid
        url={url}
        columnDefs={columns}
        defaultColDef={defaultColDef}
        style={{ height }}
        paginationSelectOptions={[5, 10, 20, 50, 100, 200]}
        defaultPageSize={20}
        getFilterModel={hasTableLoaded && setFilterState}
        getSortModel={hasTableLoaded && setSortState}
        onFirstDataRendered={onFirstDataRendered}
        initialFilterModel={filterState}
        initialSortModel={hasPresistentSort ? sortState : defaultSortColDef}
      />
    </div>
  );
};
