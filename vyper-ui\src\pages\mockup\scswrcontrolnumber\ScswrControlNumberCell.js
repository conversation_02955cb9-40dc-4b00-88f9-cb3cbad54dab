import React, { useContext } from "react";
import { DataCell } from "src/component/datacell/DataCell";
import { QuestionLink } from "src/component/question/QuestionLink";
import { HelperContext } from "src/component/helper/Helpers";

export const ScswrControlNumberCell = ({ vyper, build, onSave }) => {
  const { canEditScswrControlNumber } = useContext(HelperContext);
  const canEdit = canEditScswrControlNumber(vyper, build);

  return (
    <DataCell source={null}>
      <QuestionLink
        title="Edit the SCSWR Control Numer"
        description="Please enter the SCSWR Control Number for this build."
        value={build.scswrcontrolnumber}
        onSave={onSave}
        defaultValue="click to select"
        canEdit={canEdit}
      />
    </DataCell>
  );
};
