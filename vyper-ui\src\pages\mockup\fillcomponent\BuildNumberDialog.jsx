import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import React, { useEffect, useState } from "react";
import DialogContent from "@material-ui/core/DialogContent";
import Dialog from "@material-ui/core/Dialog";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { BuildNumberAutocomplete } from "src/autocomplete/components/BuildNumberAutocomplete";
import { Button, DialogActions } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  form: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    gap: "1em",
  },
}));

export const BuildNumberDialog = ({
  defaultNumber = "",
  open,
  onClose,
  onSelect,
  title = "Fill Unselected from Vyper",
}) => {
  const [buildNumber, setBuildNumber] = useState(defaultNumber);
  const classes = useStyles();

  useEffect(() => {
    if (open) {
      setBuildNumber(defaultNumber);
    }
  }, [open]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg">
      <DialogTitle className={classes.title}>{title}</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent className={classes.form} style={{ marginBottom: 5 }}>
        <BuildNumberAutocomplete
          defaultNumber={defaultNumber}
          onSelect={(number) => setBuildNumber(number)}
          fullWidth
        />
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          color="primary"
          disabled={buildNumber === ""}
          onClick={() => onSelect(buildNumber)}
        >
          Select
        </Button>
      </DialogActions>
    </Dialog>
  );
};
