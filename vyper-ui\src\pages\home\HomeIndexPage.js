import makeStyles from "@material-ui/core/styles/makeStyles";
import AccountTreeIcon from "@material-ui/icons/AccountTree";
import PublishIcon from "@material-ui/icons/Publish";
import AddBoxIcon from "@material-ui/icons/AddBox";
import CompareArrowsIcon from "@material-ui/icons/CompareArrows";
import ThumbUpIcon from "@material-ui/icons/ThumbUp";
import React, { useContext, useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import { AlertDialogContext } from "src/component/alert/AlertDialog";
import { AuthContext } from "src/component/common/auth";
import {
  AlertDialogErrorHandler,
  SpinnerLoadingHandler,
} from "src/component/fetch/DaoBase";
import { convertBuildNumbertoVyperNumber } from "src/component/helper/convertBuildNumbertoVyperNumber";
import { SpinnerContext } from "src/component/Spinner";
import { noop } from "src/component/vyper/noop";
import { DashboardDao } from "src/dao/DashboardDao";
import { DataModelsContext } from "src/DataModel";
import { AnnouncementBoard } from "../../component/announcement/AnnouncementBoard";
import { fetchUserAssignments } from "../../component/api/taskService2";
import { HorizontalTabs } from "../../component/tab/HorizontalTabs";
import { CreateDialogContext } from "./createNewDialog/CreateDialog";
import { Dashboard } from "./Dashboard";
import ReceiptIcon from "@material-ui/icons/Receipt";
import config from "../../../src/buildEnvironment";
const { externalUse } = config;

const useStyles = makeStyles({
  root: {},
});

export const HomeIndexPage = () => {
  const { authUser } = useContext(AuthContext);
  const { open: openAlert } = useContext(AlertDialogContext);
  const { showSpinner, hideSpinner } = useContext(SpinnerContext);
  const { praDao } = useContext(DataModelsContext);
  const { openCreateDialog: openCreateDialog } =
    useContext(CreateDialogContext);
  const { vyperDao } = useContext(DataModelsContext);

  // {myProjects: 1, allBuilds: 2, myApprovals: 3 }
  const [dashboard, setDashboard] = useState();
  const [pendingCount, setPendingCount] = useState();

  const history = useHistory();

  const dashboardDao = new DashboardDao({
    errorHandler: new AlertDialogErrorHandler(openAlert),
    loadingHandler: new SpinnerLoadingHandler(showSpinner, hideSpinner),
  });

  // get the user's # of projects, and # all projects, when the user changes.
  useEffect(() => {
    if (authUser.uid == null || authUser.uid === "") {
      return;
    }
    dashboardDao
      .view(authUser.uid)
      .then((json) => setDashboard(json))
      .catch(noop);
  }, [authUser.uid]);

  // retrieve the pending approvals.
  useEffect(() => {
    fetchUserAssignments("?current=true")
      .then((assignments) =>
        assignments.filter((assignment) => !assignment.fnctSatisfied)
      )
      .then((assignments) => {
        let uniqueTasks = new Set();
        assignments.forEach((task) => {
          uniqueTasks.add(task.taskUuid);
        });
        return uniqueTasks;
      })
      .then((tasks) => setPendingCount(tasks?.size));
  }, []);

  const handleAddPra = (buildNumber) => {
    praDao
      .addPra(convertBuildNumbertoVyperNumber(buildNumber), buildNumber)
      .then((json) => {
        history.push(`/projects/${json.vyperNumber}/pras`);
      })
      .catch(noop);
  };

  const handleAddBuild = (
    mode,
    vyperNumber,
    buildNumber,
    flowData,
    description,
    buildType,
    copyBuildNumber,
    symbolChoice
  ) => {
    return vyperDao
      .addBuild(
        mode,
        vyperNumber,
        buildNumber,
        flowData,
        description,
        buildType,
        copyBuildNumber,
        symbolChoice
      )
      .then(() => history.push(`/projects/${vyperNumber}`))
      .catch(noop);
  };

  let cards = [
    // {
    //     color: 'primary',
    //     variant: 'contained',
    //     text: "My Projects",
    //     label: 'My',
    //     action: {to: "/my/projects"},
    //     iconComponent: AccountTreeIcon,
    //     countField: 'myProjects',
    //     countUnit: 'projects',
    // },
    {
      color: "primary",
      variant: "contained",
      text: "All Builds",
      label: "Builds",
      action: { to: "/device" },
      iconComponent: AccountTreeIcon,
      countField: "allBuilds",
      countUnit: "builds",
      internal: true,
    },
    {
      color: "primary",
      variant: "contained",
      text: "PRA",
      label: "Builds",
      action: { to: "/praBuildsPage" },
      iconComponent: AccountTreeIcon,
      countField: "praBuilds",
      countUnit: "items",
      internal: true,
    },
    {
      color: "primary",
      variant: "contained",
      text: "VSCN",
      label: "VSCNs",
      action: { to: "/list/vscns" },
      iconComponent: AccountTreeIcon,
      countField: "vscnCount",
      countUnit: "items",
    },
    {
      color: "primary",
      variant: "contained",
      text: "Create",
      label: "New",
      action: {
        fn: ({ vpost }) => {
          openCreateDialog({
            onAddPra: (buildNumber) => {
              handleAddPra(buildNumber);
            },
            onAddBuild: (
              mode,
              vyperNumber,
              buildNumber,
              flowData,
              description,
              buildType,
              copyBuildNumber,
              symbolChoice
            ) => {
              vpost("/vyper/v1/vyper/", {}, (json) => {
                handleAddBuild(
                  mode,
                  json.vyperNumber,
                  buildNumber,
                  flowData,
                  description,
                  buildType,
                  copyBuildNumber,
                  symbolChoice
                );
              });
            },
          });
        },
      },
      iconComponent: AddBoxIcon,
    },
    {
      color: "primary",
      variant: "contained",
      text: "Compare Builds and Travelers",
      label: "Compare",
      action: { to: "/compare" },
      iconComponent: CompareArrowsIcon,
      internal: true,
    },
    {
      color: "primary",
      variant: "contained",
      text: "Show Pending Approvals",
      label: "MY Approvals",
      action: { to: "/my/approvals" },
      iconComponent: ThumbUpIcon,
      countField: "myApprovals",
      countUnit: "approvals",
      internal: true,
    },
    {
      color: "primary",
      variant: "contained",
      text: "View Travelers",
      label: "Traveler",
      action: { to: "/traveler" },
      iconComponent: ReceiptIcon,
      internal: true,
    },
    {
      color: "primary",
      variant: "contained",
      text: "ATSS Mass Upload",
      label: "ATSS Mass Upload",
      action: { to: "/atssmassupload/projects" },
      countField: "allProjects",
      countUnit: "projects",
      iconComponent: PublishIcon,
    },
  ];

  if (externalUse) {
    cards = cards.filter((card) => card.internal);
  }

  const tabs = [
    {
      label: "Dashboard",
      control: null,
    },
  ];

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <AnnouncementBoard />
      <HorizontalTabs
        storageKey="home.dashboard"
        tabs={tabs}
        helpUrl="https://confluence.itg.ti.com/display/SimbaInfo/VYPER+Dashboard"
      />
      <Dashboard
        cards={cards}
        dashboard={{ ...dashboard, myApprovals: pendingCount }}
      />
    </div>
  );
};
