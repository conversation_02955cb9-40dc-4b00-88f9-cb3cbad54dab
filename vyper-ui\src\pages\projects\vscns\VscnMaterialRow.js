import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import PropTypes from "prop-types";
import React from "react";
import { RowPrefix } from "../../mockup/RowPrefix";
import { VscnMaterialCell } from "./VscnMaterialCell";

/**
 * Display a row of materials
 * @param {*[]} vscns - The vscns to display
 * @param {MaterialCell2~onGetMaterialObject} onGetMaterial - callback to get the material object
 * @return {JSX.Element}
 * @constructor
 */
export function VscnMaterialRow({ vscns, onGetMaterialObject, onChange }) {
  return (
    <TableRow hover>
      <RowPrefix help="device" title="OPN/Material Name" required />
      {vscns.map((vscn, n) => (
        <TableCell key={n}>
          <VscnMaterialCell
            vscn={vscn}
            onGetMaterialObject={onGetMaterialObject}
            onChange={onChange}
          />
        </TableCell>
      ))}
    </TableRow>
  );
}

VscnMaterialRow.propTypes = {
  vscns: PropTypes.array.isRequired,
  onGetMaterialObject: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
};
