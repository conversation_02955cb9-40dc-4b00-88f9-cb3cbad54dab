import { MenuItem, TextField } from "@material-ui/core";
import PropTypes from "prop-types";
import React from "react";
import {
  Experimental,
  MinorChange,
  New,
} from "../../pages/mockup/buildtype/BuildTypes";

const buildTypes = [Experimental, New, MinorChange];

/**
 * Select the build type
 *
 * @param buildType The current build type
 * @param onChange called when the build type changes
 * @returns {JSX.Element|boolean}
 * @constructor
 */
export const BuildType = ({ buildType, onChange }) => {
  const handleOnChange = (e) => onChange(e.target.value);

  return (
    <TextField
      variant="outlined"
      select
      id="buildType"
      label="Select the Build Type"
      fullWidth
      value={buildType || ""}
      onChange={handleOnChange}
      style={{ marginRight: "50px" }}
    >
      {buildTypes.map((buildType) => (
        <MenuItem key={buildType} value={buildType}>
          {buildType}
        </MenuItem>
      ))}
    </TextField>
  );
};

BuildType.propTypes = {
  buildType: PropTypes.string,
  onChange: PropTypes.func.isRequired,
};
