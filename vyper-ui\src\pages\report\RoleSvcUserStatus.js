import React, { useState } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { Alert } from "@material-ui/lab";
import TiServerAgGrid from "src/lib/TiServerAgGrid";
import { useLocalStorage } from "src/component/hooks/useLocalStorage";

const useStyles = makeStyles({
  header: {
    marginTop: 0,
  },
  description: {
    marginBottom: "2em",
  },
});

let body = document.body;
let html = document.documentElement;

let height =
  Math.max(
    body.scrollHeight,
    body.offsetHeight,
    html.clientHeight,
    html.scrollHeight,
    html.offsetHeight
  ) - 200;

export const RoleSvcUserStatus = () => {
  const classes = useStyles();
  const [hasTableLoaded, setHasTableLoaded] = useState(false);
  const [filterState, setFilterState] = useLocalStorage(
    "report.rolesvc.users.filters",
    {}
  );

  const defaultSortColDef = {
    state: [{ colId: "userName", sort: "asc", sortIndex: 0 }],
  };

  const defaultColDef = {
    filterParams: {
      flex: 1,
      resizable: true,
      sortable: true,
      wrapText: true,
      autoHeight: true,

      suppressAndOrCondition: true,
      filterOptions: [
        {
          displayKey: "contains",
          displayName: "Contains",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "=",
          displayName: "Equal",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "<>",
          displayName: "Not Equal",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "<",
          displayName: "Less Than",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: ">",
          displayName: "Greater Than",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: "<=",
          displayName: "Less Than or Equal",
          predicate: () => {}, //needed to use display key and name
        },
        {
          displayKey: ">=",
          displayName: "Greater Than or Equal",
          predicate: () => {}, //needed to use display key and name
        },
      ],
    },
  };

  const columns = [
    { headerName: "User Name", field: "userName" },
    { headerName: "Badge", field: "userBadge" },
    {
      headerName: "Groups",
      field: "groups",
      cellStyle: {
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
        overflow: "hidden",
        padding: 0,
      },
    },
    { headerName: "Active", field: "active" },
  ];

  const onFirstDataRendered = () => {
    setHasTableLoaded(true);
  };

  return (
    <div>
      <h2 className={classes.header}>Role Service Users</h2>

      <Alert className={classes.description} severity="warning">
        This report shows all of the users in RoleService for the Vyper
        application, and a column that shows if they are in directory services.
      </Alert>

      <TiServerAgGrid
        url="/vyper/v1/rolesvc/validate/users"
        defaultColDef={defaultColDef}
        columnDefs={columns}
        style={{ height }}
        defaultPageSize={20}
        paginationSelectOptions={[5, 10, 20, 50, 100, 200]}
        getFilterModel={hasTableLoaded && setFilterState}
        onFirstDataRendered={onFirstDataRendered}
        initialFilterModel={filterState}
        initialSortModel={defaultSortColDef}
      />
    </div>
  );
};
