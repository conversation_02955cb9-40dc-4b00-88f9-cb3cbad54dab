import makeStyles from "@material-ui/core/styles/makeStyles";
import TableCell from "@material-ui/core/TableCell";
import TableRow from "@material-ui/core/TableRow";
import clsx from "clsx";
import React, { useContext } from "react";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { RowPrefix } from "src/pages/mockup/RowPrefix";
import { QuestionDialogContext } from "../../../component/question/QuestionDialog";
import { samenessDryBake } from "../../../component/sameness/sameness";
import { DryBakeCell } from "./DryBakeCell";

const useStyles = makeStyles({
  difference: {
    backgroundColor: "hsla(0, 100%, 90%, 1)",
  },
});

export const DryBakeRow = ({ vyper, builds, onChange, showSameness }) => {
  const { showQuestionDialog } = useContext(QuestionDialogContext);
  const { buildDao } = useContext(DataModelsContext);

  const handleDryBake = (value, build) => {
    return buildDao
      .changeDryBake(vyper.vyperNumber, build.buildNumber, value)
      .then((json) => onChange(json))
      .catch(noop);
  };

  const handleClick = (build) => {
    showQuestionDialog({
      type: "select",
      title: "Select Dry Bake",
      description: `Does this device need Dry Bake?`,
      value: build.dryBake?.object?.value,
      multiline: false,
      rows: 10,
      options: ["No", "Yes"],
      onSave: (value) => handleDryBake(value, build),
    });
  };

  const classes = useStyles();

  return (
    <TableRow
      className={clsx({
        [classes.difference]: showSameness && !samenessDryBake(builds),
      })}
      hover
    >
      <RowPrefix help="dryBake" title="Dry Bake" required />
      {builds.map((build, n) => (
        <TableCell key={n}>
          <DryBakeCell vyper={vyper} build={build} onClick={handleClick} />
        </TableCell>
      ))}
    </TableRow>
  );
};
