/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState } from "react";
import {
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
} from "@material-ui/core";
import { useLocalStorage } from "../../hooks/useLocalStorage";
import { Pagination } from "./Pagination";
import { HeaderRow } from "./HeaderRow";
import { BodyRow } from "./BodyRow";
import { FetchContext } from "../../fetch/VyperFetch";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  root: {
    width: "100%",
  },

  table: {
    width: "100%",
  },

  Pagination: {},
});

/**
 * id: string - the unique identifier for this table
 * title: string - the text that is a header above the table
 * uri: string - the base url for the search api
 * rowButtons: array of object - buttons that will be added to each row of the table
 *  {
 *      title: string - the button text
 *  }
 *
 * @param id
 * @param title
 * @param description
 * @param uri
 * @param rowButtons
 *  {
 *      title: The button's text
 *      size: the material-ui size: small, medium, large
 *      variant: the material-ui button variant: contained, outline, text
 *      color: the material-ui button color: primary, secondary, default, inheried
 *      onClick: function - callback used when button is clicked
 *          row - the row of data for the row of the table
 *          button - the rowButton oject
 *  }
 * @param rowButtonsTitle
 * @param overrides
 * @param createButton
 * @param refreshInterval
 * @returns {*}
 * @constructor
 */
export const SmartTable = ({
  id,
  title,
  description,
  uri,
  rowButtons = [],
  rowButtonsTitle = "",
  overrides = [],
  createButton = { color: "primary", href: "#" },
  refreshInterval = 600,
}) => {
  const { vget } = useContext(FetchContext);

  /** columns (array of objects) define the columns of the table
   * {
   *     id: string - the database column name
   *     title: string - the table header text
   *     enableFilter:boolean - determines if the column is filterable
   * }
   */
  const [columns, setColumns] = useState([]);

  /**
   * filters is a object
   * the key is the column id the the database column name.
   * the value is the user entered text
   */
  const [filters, setFilters] = useLocalStorage(id + "filters", []);

  /**
   * pageable (object) is an object that holds the pagination information for the table
   * {
   *     page: number - the zero-based page number
   *     size: number - the number of records to be fetched
   *     sort: string - the column that will be sorted
   *     order: string - the sort direction (asc or desc)
   * }
   */
  const [pageable, setPageable] = useLocalStorage(id + "pageable", {
    page: 0,
    size: 10,
    sort: "",
    order: "asc",
  });

  /**
   * results (object) is the data returned from the api - the page of information to be displayed.
   *  columns: [{
   * 	id: the column name
   * 	title: the column text to display im the header of the table
   * }]
   * page: {
   * 	content: array of objects - the data for the page,
   * 		the key is the column id
   * 		the value is the  data for the record
   *
   * 	pageable: {
   * 		sort: {}
   * 		offset: number - the user requsted index of the first record (zero based)
   * 		pageSize: number - the user requested number of records to display
   * 		pageNumber: number - the user requsted page (zero based)
   * 		unpaged: boolean - flag indicating if the data is not paginated
   * 		paged: boolean - flag indicating if the data is paginated
   * 	}
   * 	last: boolean - flag indicating that this is the last page of data.
   * 	totalElements: number - the total number of records that match the filters.
   * 	totalPages: number - the total number of pages that match the filters.
   * 	size: number - the number of records the user requested to display.
   * 	number: number -the current page number (zero based)
   * 	sort: {
   * 		sorted: boolean - flag indicating if the records are sorted
   * 		unsorted: boolean - flag indicating if the records are not sorted
   * 		empty: boolean -
   * 	}
   * 	numberOfElements: number of records on the current page
   * 	first: boolean - flag indicating if this is the first page
   * 	empty: boolean - flag indicating if no records were returned
   * }
   */
  const [result, setResult] = useState({
    content: [],
    totalElements: 0,
    size: 10,
    page: 0,
  });

  /**
   * Refresh the table whenever the user updates the pagination controls
   */
  useEffect(() => {
    refreshTable();
  }, [title, pageable.page, pageable.size, pageable.order, pageable.sort]);

  const refreshTable = (f) => {
    if (uri == null) return;

    // build the url parameters

    let url = uri;
    url += url.indexOf("?") === -1 ? "?" : "&";
    url +=
      "page=" +
      pageable.page +
      "&size=" +
      pageable.size +
      "&sort=" +
      pageable.sort +
      "," +
      pageable.order;

    // add the filters, and default to the "like" matcher
    Object.keys(filters).forEach((a, b) => {
      url += "&filter=" + encodeURI(a + "|contains|" + filters[a]);
    });

    // call the api

    vget(url, (json) => {
      setResult(
        json.page || { content: [], totalElements: 0, size: 10, page: 0 }
      );
      setColumns(json.columns || []);
    });
  };

  const handleChangePage = (page) => setPageable({ ...pageable, page: page });

  const handChangeRowsPerPage = (size) =>
    setPageable({ ...pageable, size: size, page: 0 });

  const handleSort = (column) => {
    if (column.id === pageable.sort) {
      // the column is already sorted - toggle the direction
      setPageable({
        ...pageable,
        order: "asc" === pageable.order ? "desc" : "asc",
      });
    } else {
      // the column is not sorted - sort it asc
      setPageable({ ...pageable, sort: column.id, order: "asc" });
    }
  };

  const handleFilterChange = (e) => {
    setFilters({ ...filters, [e.target.name]: e.target.value });
  };

  // store a timer-id so we can de-bounce the filter textboxes / table refreshes
  const [timer, setTimer] = useState();

  // debounce changes to the filters - only refresh the table after xxx miliseconds

  useEffect(() => {
    if (timer != null) {
      clearTimeout(timer);
    }

    setTimer(
      window.setTimeout(() => {
        // when the filters change, always go to the 1st page
        setPageable({ ...pageable, page: 0 });

        refreshTable(filters);
      }, refreshInterval)
    );

    // eslint-disable-next-line
  }, [filters]);

  const getVisible = (column, columnIndex) => {
    const override = overrides.find((override) =>
      override.matcher(column, columnIndex)
    );
    return override?.visible == null ? true : override.visible;
  };

  const getHeader = (column, columnIndex) => {
    const override = overrides.find(
      (override) => override.matcher && override.matcher(column, columnIndex)
    );

    if (override && override.header) {
      return override.header(column, columnIndex);
    } else {
      return column.title;
    }
  };

  const getContent = (column, columnIndex, row, rowIndex) => {
    const override = overrides.find(
      (override) =>
        override.matcher && override.matcher(column, columnIndex, row, rowIndex)
    );

    if (override && override.content) {
      return override.content(column, columnIndex, row, rowIndex);
    }

    let value = row[column.id];
    if (value === true) {
      value = "Y";
    } else if (value === false) {
      value = "N";
    }

    return value;
  };

  const handleClearFilters = () => setFilters({});

  const classes = useStyles();

  return (
    <div className="smart-table">
      <div className={classes.root}>
        {title == null ? null : <h1>{title}</h1>}

        {description == null ? null : <p>{description}</p>}

        <br />

        <Pagination
          className={classes.Pagination}
          result={result}
          onChangePage={(e, page) => handleChangePage(page)}
          onChangeRowsPerPage={(e) => handChangeRowsPerPage(e.target.value)}
          onClearFilters={handleClearFilters}
        />

        <TableContainer component={Paper} className={classes.tableContainer}>
          <Table id={id} className={classes.table} size="small" stickyHeader>
            <TableHead>
              <HeaderRow
                rowButtons={rowButtons}
                rowButtonsTitle={rowButtonsTitle}
                columns={columns}
                pageable={pageable}
                filters={filters}
                header={getHeader}
                onSortClick={handleSort}
                onFilterChange={handleFilterChange}
                overrides={overrides}
                visible={getVisible}
              />
            </TableHead>

            <TableBody>
              {result.content.map((row, rowIndex) => (
                <BodyRow
                  key={row.id}
                  rowButtons={rowButtons}
                  columns={columns}
                  row={row}
                  rowIndex={rowIndex}
                  content={getContent}
                  overrides={overrides}
                  visible={getVisible}
                />
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </div>
  );
};
