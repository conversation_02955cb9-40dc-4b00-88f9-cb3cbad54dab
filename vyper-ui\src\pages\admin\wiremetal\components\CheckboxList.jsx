import React from "react";
import { Typography } from "@material-ui/core";

export const CheckboxList = ({ title, items, selected, onChangeSelected }) => {
  //
  const handleChange = (e) => {
    if (e.target.checked) {
      onChangeSelected([...selected, e.target.value]);
    } else {
      onChangeSelected(selected.filter((item) => item !== e.target.value));
    }
  };

  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "baseline",
          marginRight: "0.25em",
        }}
      >
        <h4 style={{ marginBottom: 0 }}>{title}</h4>
        {selected.length !== 0 && (
          <Typography variant="caption">{selected.length}</Typography>
        )}
      </div>

      <div
        style={{
          maxHeight: "10rem",
          overflowY: "scroll",
          border: "1px solid #cccccc",
          padding: "0.25rem",
        }}
      >
        {items.map((item) => (
          <label key={item}>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: "0.5em",
              }}
            >
              <input
                type="checkbox"
                name="item"
                value={item}
                checked={selected.includes(item)}
                onChange={handleChange}
              />
              {item}
            </div>
          </label>
        ))}
      </div>
    </div>
  );
};
