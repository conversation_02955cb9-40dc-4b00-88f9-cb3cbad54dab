import { <PERSON><PERSON>, <PERSON> } from "@material-ui/core";
import React, { useState, useEffect, useContext, useRef } from "react";
import { DataSheetGrid } from "react-datasheet-grid";
//import "react-datasheet-grid/dist/style.css";
import { SpecChangeColumns } from "./SpecChangeColumns";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import { AlertDialogContext } from "../../../component/alert/AlertDialog";
import { withRouter } from "react-router-dom";
import { ProjectHeader } from "../newProject/ProjectHeader";
import { AuthContext } from "src/component/common/auth";
import { useHistory, useParams } from "react-router-dom";
import { submitForApproval } from "../../../pages/functions/submitForApproval";
import { DataModelsContext } from "../../../DataModel";
import useSnackbar from "../../../hooks/Snackbar";
import makeStyles from "@material-ui/core/styles/makeStyles";
import "./SpecChange.css";
import { ProjectHeaderLinks } from "./ProjectHeaderLinks";
import { SpecTemplatePopup } from "./SpecTemplatePopup";
import { LoadingDialog } from "../../utilities/LoadingDialog";
import { ErrorDialogContext } from "../../../component/error/ErrorDialog";
import "regenerator-runtime/runtime";
import { submitBlockers } from "../../../pages/vyper/FormStatus";

const useStyles = makeStyles((theme) => ({
  actions: {
    display: "flex",
    marginTop: "0.5rem",
    marginBottom: "1rem",
    gap: "1rem",
  },
}));

export const SpecChange = () => {
  const [projectHeaderInfo, setProjectHeaderInfo] = useState({});
  const history = useHistory();
  const { projNumber } = useParams();
  const [specChange, setSpecChange] = useState([]);
  const [savedSpecChange, setSavedSpecChange] = useState([]);
  const { open: openConfirmation } = useContext(ConfirmationDialogContext);
  const { open: openAlert } = useContext(AlertDialogContext);
  const [templateOpen, setTemplateOpen] = useState(false);
  const { vpost, vget } = useContext(FetchContext);
  const [specChangeRule, setSpecChangeRule] = useState();
  const [buildInfo, setBuildInfo] = useState();
  const classes = useStyles();
  const [pgsData, setPgsData] = useState();
  const [sheetLoading, setSheetLoading] = useState(false);
  const [autoCompleteDies, setAutoCompleteDies] = useState();
  let diesFromBuildInfo = [];
  let specSaveBlockerFlag = false;

  const specChangeFlowType = ["ASSEMBLY", "TEST", "PACK"];
  let gridmaterials = [];
  let griddies = [];

  let tsmComponentNames = [];
  let tsmComponentValues = [];
  let pgsMaterials = [];
  let invalidMaterials = [];
  const [tsmData, setTsmData] = useState();
  const [materialsFacility, setMaterialsFacility] = useState();
  const [custMaxLen, setCustMaxLen] = useState();
  var materialsInfo = {
    attrs: [],
  };

  const [showLoadingMessage, setShowLoadingMessage] = useState(undefined);
  let { enqueueErrorSnackbar } = useSnackbar();
  const { open: openError } = useContext(ErrorDialogContext);
  const { buildDao } = useContext(DataModelsContext);
  const [build, setBuild] = useState();
  const [vyper, setVyper] = useState();
  const { authUser } = useContext(AuthContext);
  let dataSheetErrorFlag = false;
  const [isTsmData, setIsTsmData] = useState(false);
  const [isAnyUnsavedData, setIsAnyUnsavedData] = useState(false);

  const isSubmitForApprovalNeeded =
    projectHeaderInfo.buildState === "DRAFT" &&
    projectHeaderInfo.facilityAt === projectHeaderInfo.refFacilityAt &&
    projectHeaderInfo.refStatus === "W";

  const isPraCreationForReferenceTraveler =
    projectHeaderInfo.praState == null &&
    projectHeaderInfo.buildState === "FINAL_APPROVED";

  const getProjectHeaderInfo = () => {
    vget(
      `/vyper/v1/atssmassupload/project/projNumber/${projNumber}`,
      (json) => {
        setProjectHeaderInfo(json);
      }
    );
  }

  useEffect(() => {
    getProjectHeaderInfo();
    getSpecChangeData();
    getSpecChangeRule();
  }, []);

  const saveHeader = (dataInError) => {
    let newProjectStatus = projectHeaderInfo?.projStatus;
    if(dataInError){
      newProjectStatus = "DRAFT";
    }
    else if(projectHeaderInfo?.projStatus === "DRAFT"){
      newProjectStatus = projectHeaderInfo?.praState === "PRA_APPROVED" ? "REF VALIDATED" : "VALIDATED";
    }

    vpost(`/vyper/v1/atssmassupload/projects/${projNumber}`,
      {
        projName: projectHeaderInfo?.projName,
        projType: projectHeaderInfo?.projType,
        cmsNumber: projectHeaderInfo?.cmsNumber,
        status: newProjectStatus,
      },
      () => {
        getProjectHeaderInfo();
      }
    );
  };

  const getPgsMaterialsInfo = async (...materials) => {
    const materialList = materials.join(" ").replaceAll(",", " ");
    try {
      const response = await fetch(
        `/pgs/v1/pgs/objects/Device?limit=500&expandFromRel=MaterialDie&expandToRel=MaterialDevice&q=Material%3A(${materialList})`
      );
      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }
      const data = await response.json();
      setPgsData(data);
    } catch (error) {
      if (error instanceof Error) {
        console.error(" Error:", error.message);
      } else {
        console.error("Error:", error);
      }
    }
  };

  const getPgsDiesInfo = async (...dies) => {
    const dieList = dies.join(" ").replaceAll(",", " ");
    try {
      const response = await fetch(
        `/pgs/v1/pgs/objects/Die?limit=500&q=Die%3A(${dieList})`
      );
      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }
      const data = await response.json();
      setAutoCompleteDies(data);
    } catch (error) {
      if (error instanceof Error) {
        console.error(" Error:", error.message);
      } else {
        console.error("Error:", error);
      }
    }
  };

  const geTSMDataInfo = async (...tmsComponentValues) => {
    try {
      await fetch(
        `/vyper/v1/atssmassupload/validation/testprograms?programNames=${tmsComponentValues}`
      )
        .then((response) => response.json())
        .then((data) => setTsmData(data));
    } catch (error) {
      throw new Error(error);
    }
  };

  const getMaterialsPlanFactor = (...materials) => {
    vget(
      `/vyper/v1/atssmassupload/validation/materialsplanfactor?materials=${materials}`,
      setMaterialsFacility
    );
  };

  const getBuildInfo = () => {
    let buildNumber = projectHeaderInfo.refVyperBuildNumber;
    if (buildNumber == undefined || buildInfo != undefined) return;
    vget(`/vyper/v1/vyper/findBuildByBuildNumber/${buildNumber}`, setBuildInfo);
  };

  const getSpecChangeRule = () => {
    vget(
      `/vyper/v1/atssmassupload/validation/specchangerule`,
      setSpecChangeRule
    );
  };

  const getCustMaxLen = (facility, pkg, symbol_name, pin_count) => {
    vget(
      `/vyper/v1/topsidecust/findCustMaxLen?facility=${facility}&pkg=${pkg}&symbol_name=${symbol_name}&pin_count=${pin_count}`,
      setCustMaxLen
    );
  };

  const validateTSMInfo = (
    componentValue,
    attributeName,
    attributeValue,
    specChangeRowData
  ) => {
    if (tsmData) {
      const matchedTsmComponentValues = tsmData.some(
        (element) => element.programName === componentValue
      );
      if (!matchedTsmComponentValues) {
        return `Invalid Test Program Name in TSM`;
      } else {
        if (attributeName === "Revision" && attributeValue) {
          let matchedAttributeValues = tsmData.filter(
            (element) =>
              element.programName === componentValue &&
              element.programRev === attributeValue
          );

          let matchedAttributeValuesWithPad = tsmData.filter(
            (element) =>
              element.programName === componentValue &&
              element.programRev === attributeValue.padStart(2, "0")
          );
          if (
            matchedAttributeValues.length === 0 &&
            matchedAttributeValuesWithPad.length > 0
          ) {
            specChangeRowData.attributeValue = attributeValue.padStart(2, "0");
          }
          if (
            !matchedAttributeValues.length > 0 &&
            !matchedAttributeValuesWithPad.length > 0
          )
            return `Invalid Test Program Revision in TSM`;
        }
      }
    }
  };

  const validateComponent = (
    componentName,
    componentValue,
    attributeName,
    attributeValue,
    componentOccurrance,
    specChangeRowData
  ) => {
    const matchedComponents = specChangeRule.some(
      (element) => element.componentName === componentName
    );
    if (!matchedComponents) {
      return `Component Name ${componentName} is not allowed as per configuration`;
    } else {
      let matchedComponentList = specChangeRule.filter(
        (element) => element.componentName === componentName
      );
      if (!componentValue) {
        return `Missing component value for ${componentName}`;
      }
      if (
        matchedComponentList.some((element) => element.componentName === "Die")
      ) {
        if (!diesFromBuildInfo.includes(componentValue)) {
          let validComponentValue = autoCompleteDies.items.some(
            (element) => element.attrs.Die === componentValue
          );
          if (!validComponentValue)
            return `Invalid component value for ${componentName}`;
          if (
            componentOccurrance &&
            componentOccurrance !== "" &&
            !Number.isInteger(componentOccurrance)
          ) {
            return `Invalid componentOccurrance ${componentOccurrance}`;
          }
        }
      }

      if (
        matchedComponentList.some((element) =>
          element.componentName.startsWith("Test Program")
        )
      ) {
        return validateTSMInfo(
          componentValue,
          attributeName,
          attributeValue,
          specChangeRowData
        );
      }

      if (attributeName) {
        if (
          !matchedComponentList.some(
            (element) => element.attributeName === attributeName
          )
        ) {
          return `Attribute Name ${attributeName} is not allowed as per reference`;
        } else {
          if (!attributeValue)
            return `Missing attribute value for attribute name ${attributeName}`;
        }
      }
    }
  };

  const validateMaterials = (
    invalidMaterials,
    material,
    buildInfo,
    refObjectsIds
  ) => {
    if (material) {
      if (invalidMaterials.some((element) => element.includes(material))) {
        return "Invalid material:".concat(material);
      } else {
        let refMaterialAttr = buildInfo.material.object;
        let sbeValidationMessage;
        refObjectsIds.forEach((element) => {
          if (
            material === pgsData.metadata.refObjects[element].attrs.Material
          ) {
            if (
              refMaterialAttr.SBE !=
                pgsData.metadata.refObjects[element].attrs.SBE ||
              refMaterialAttr.SBE1 !=
                pgsData.metadata.refObjects[element].attrs.SBE1 ||
              refMaterialAttr.SBE2 !=
                pgsData.metadata.refObjects[element].attrs.SBE2 ||
              refMaterialAttr.PackagePin !=
                pgsData.metadata.refObjects[element].attrs.PackagePin ||
              refMaterialAttr.PackageGroup !=
                pgsData.metadata.refObjects[element].attrs.PackageGroup
            ) {
              sbeValidationMessage =
                "Different SBE/PIN/PKG than reference material";
            }
          }
        });
        return sbeValidationMessage;
      }
    }
  };

  const validateOperation = (flowType, operationName, operations) => {
    if (!specChangeFlowType.includes(flowType)) {
      return `Invalid flow Type ${flowType}`;
    }

    if (operationName && operations.length !== 0) {
      const matchedOperation = operations.filter((element) => {
        return (
          element.subflowType === flowType && element.name === operationName
        );
      });
      if (matchedOperation.length !== 0) return;
      else {
        return `Operation ${operationName} is not allowed as per reference`;
      }
    }
  };

  const setMaterialSpecDevice = (refObjectsIds, specChangeRowData) => {
    refObjectsIds.forEach((element) => {
      if (
        specChangeRowData.material ===
        pgsData.metadata.refObjects[element].attrs.Material
      ) {
        specChangeRowData.oldMaterial =
          pgsData.metadata.refObjects[element].attrs.OldMaterial;

        if (!specChangeRowData.specDevice) {
          specChangeRowData.specDevice =
            pgsData.metadata.refObjects[element].attrs.OldMaterial;
        }
      }
    });
  };

  const setGridMaterials = (specChangeRowData) => {
    if (specChangeRowData.material !== "") {
      gridmaterials.push(specChangeRowData.material);
    }
  };
  const setGridComponentValues = (specChangeRowData) => {
    if (specChangeRowData.componentName === "Die") {
      griddies.push(specChangeRowData.componentValue);
    }
  };
  const validateCustXLength = (
    operationName,
    attributeName,
    attributeValue
  ) => {
    if (custMaxLen === undefined || custMaxLen.length == 0) {
      return;
    } else {
      let matchedCustFieldNames = custMaxLen.filter(
        (element) => element.custFieldName === attributeName
      );

      if (operationName === "Symbol") {
        if (!matchedCustFieldNames.length > 0) {
          return `Invalid attribute name ${attributeName} `;
        } else {
          if (!attributeValue) return `Missing attribute value`;

          let attributeValueLength = attributeValue.length;

          if (
            matchedCustFieldNames.some(
              (element) => attributeValueLength > element.maxLen
            )
          ) {
            return `Invalid cust max length ${attributeValue}`;
          }
        }
      } else return;
    }
  };

  const validateTopSideSymbol = (buildInfo, componentName, symbolValue) => {
    if (buildInfo.traveler != undefined) {
      const buildSymbols = buildInfo.traveler.operations
        .filter((oprName) => oprName.name == "Symbol")
        .reduce((oprComponents, operation) => {
          return (oprComponents = oprComponents.concat(operation.components));
        }, [])
        .filter((comp) => comp.name.includes("Symbol"))
        .map((comp) => comp.value);

      if (buildSymbols != undefined) {
        if (!buildSymbols.includes(symbolValue)) {
          return "Symbol " + symbolValue + " is not in Reference Build ";
        }
      }
    }

    return;
  };

  const validateFacility = (material) => {
    if (projectHeaderInfo.facilityAt !== projectHeaderInfo.refFacilityAt) {
      if (
        materialsFacility.length > 0 &&
        materialsFacility.some(
          (element) =>
            element.material === material &&
            element.location !== projectHeaderInfo.facilityAt
        )
      ) {
        return (
          "Material not setup in plan factors for facility:" +
          projectHeaderInfo.facilityAt
        );
      }
    } else return;
  };

  const collectPgsMaterials = (refObjectsIds) => {
    for (let index = 0; index < refObjectsIds.length; index++) {
      pgsMaterials.push(
        pgsData.metadata.refObjects[refObjectsIds[index]].attrs.Material
      );
      materialsInfo.attrs.push({
        material:
          pgsData.metadata.refObjects[refObjectsIds[index]].attrs.Material,
        pkg: pgsData.metadata.refObjects[refObjectsIds[index]].attrs
          .PackageDesignator,
        pinCount:
          pgsData.metadata.refObjects[refObjectsIds[index]].attrs.PackagePin,
        facility: projectHeaderInfo.facilityAt,
      });
    }
  };

  const setInvalidMaterials = (gridmaterials, pgsMaterials) => {
    invalidMaterials.push(
      gridmaterials.filter((material) => !pgsMaterials.includes(material))
    );
  };

  const getMaterialInfoForCustXValiation = (operationName, currentMaterial) => {
    let materialInfo;
    if (operationName && operationName.length !== 0) {
      if (operationName.startsWith("Symbol")) {
        materialInfo = materialsInfo.attrs.filter(
          (element) => element.material === currentMaterial
        );

        return materialInfo;
      }
      return;
    }
  };

  const setTsmComponents = (componentName, componentValue) => {
    if (componentName && componentName.startsWith("Test Program")) {
      tsmComponentNames.push(componentName);

      if (componentValue) {
        tsmComponentValues.push(componentValue);
      }
    }
  };

  const validateSpecChanges = (
    specChangeRowData,
    operations,
    invalidMaterials,
    currentMaterial,
    buildInfo,
    refObjectsIds
  ) => {
    let validationMessageArray = [];
    let validationMessage;

    validationMessage = validateMaterials(
      invalidMaterials,
      currentMaterial,
      buildInfo,
      refObjectsIds
    );
    if (validationMessage) {
      validationMessageArray.push(validationMessage);
    }
    validationMessage = validateComponent(
      specChangeRowData.componentName,
      specChangeRowData.componentValue,
      specChangeRowData.attributeName,
      specChangeRowData.attributeValue,
      specChangeRowData.componentOccurrance,
      specChangeRowData
    );

    if (validationMessage) {
      validationMessageArray.push(validationMessage);
    } else {
      if (specChangeRowData.componentName.includes("Symbol")) {
        validationMessage = validateTopSideSymbol(
          buildInfo,
          specChangeRowData.componentName,
          specChangeRowData.componentValue
        );
        if (validationMessage) {
          validationMessageArray.push(validationMessage);
        }
      }
    }

    validationMessage = validateOperation(
      specChangeRowData.flowType,
      specChangeRowData.operationName,
      operations
    );
    if (validationMessage) {
      validationMessageArray.push(validationMessage);
    }

    validationMessage = validateCustXLength(
      specChangeRowData.operationName,
      specChangeRowData.attributeName,
      specChangeRowData.attributeValue
    );

    if (validationMessage) {
      validationMessageArray.push(validationMessage);
    }

    validationMessage = validateFacility(specChangeRowData.material);
    if (validationMessage) {
      validationMessageArray.push(validationMessage);
    }

    return validationMessageArray;
  };

  const checkSeqForRepetedMaterial = (
    currentRowData,
    othersRowData,
    specChange
  ) => {
    let index = specChange.indexOf(othersRowData);
    let isInSequence = true;

    for (let i = specChange.indexOf(currentRowData) + 1; i < index; i++) {
      if (
        specChange[i].material &&
        specChange[i].material != currentRowData.material
      ) {
        isInSequence = false;
      }
    }

    return isInSequence;
  };

  const checkSpecSaveBlocker = (specChange) => {
    let specSaveBlockers = [];
    if (specChange.length == 0 && savedSpecChange.length == 0) {
      specSaveBlockers.push("empty sheet");
    }
    if (specChange.length > 0) {
      if (specChange.at(0).material === null) {
        specSaveBlockers.push("first row material value can not be empty");
      }

      specChange.forEach((specChangeRowData) => {
        if (specChangeRowData.componentName == null) {
          specSaveBlockers.push(
            "component name can not be empty at row number:".concat(
              specChange.indexOf(specChangeRowData) + 1
            )
          );
        }

        if (specChangeRowData.material) {
          specChange.forEach((rowData) => {
            if (
              rowData.material &&
              specChange.indexOf(rowData) >
                specChange.indexOf(specChangeRowData) &&
              rowData.specDevice == specChangeRowData.specDevice
            ) {
              if (rowData.material == specChangeRowData.material) {
                let isInSequence = checkSeqForRepetedMaterial(
                  specChangeRowData,
                  rowData,
                  specChange
                );
                if (!isInSequence) {
                  specSaveBlockers.push(
                    `same materails are not in sequence at row number:${
                      specChange.indexOf(specChangeRowData) + 1
                    } and at row number:${specChange.indexOf(rowData) + 1} `
                  );
                }
              } else {
                specSaveBlockers.push(
                  `Duplicate specdevice at row number:${
                    specChange.indexOf(specChangeRowData) + 1
                  } and at row number:${
                    specChange.indexOf(rowData) + 1
                  } for different material`
                );
              }
            }
          });
        }
      });
    }

    return specSaveBlockers;
  };

  const saveSpecChange = () => {
    let blockers = checkSpecSaveBlocker(specChange);
    if (blockers.length > 0) specSaveBlockerFlag = true;
    if (specSaveBlockerFlag) {
      openAlert({
        title: "Can't Perform Action: Save sheet data",
        blockers,
      });
    } else {
      const dieInstances = buildInfo.dies.dieInstances;
      const dies = dieInstances.filter((element) => element.type === "Die");
      dies.forEach((element) =>
        element.dies.forEach((element) =>
          diesFromBuildInfo.push(element.object.Die)
        )
      );

      setSheetLoading(true);

      specChange.map((specChangeRowData) => {
        setGridMaterials(specChangeRowData);
        setGridComponentValues(specChangeRowData);
        setTsmComponents(
          specChangeRowData.componentName,
          specChangeRowData.componentValue
        );
      });

      var uniqueNonNullTestPrograms = tsmComponentValues
        .filter((tsmComponentValue) => tsmComponentValue !== null)
        .filter((val, ind, arr) => arr.indexOf(val) == ind);

      if (uniqueNonNullTestPrograms.length > 0) {
        setIsTsmData(true);
        geTSMDataInfo(uniqueNonNullTestPrograms);
      }

      getPgsMaterialsInfo(gridmaterials);
      getPgsDiesInfo(griddies);

      if (gridmaterials.length > 0) getMaterialsPlanFactor(gridmaterials);
    }
  };

  useEffect(() => {
    if (isSubmitForApprovalNeeded === true) {
      const buildNumber = projectHeaderInfo?.refVyperBuildNumber;
      if (buildNumber == null) return;
      vget(`/vyper/v1/vyper/findBuildByBuildNumber/${buildNumber}`, setBuild);
      vget(`/vyper/v1/vyper/findByBuildNumber/${buildNumber}`, setVyper);
    }
  }, [isSubmitForApprovalNeeded]);

  useEffect(() => {
    getBuildInfo();
  }, [projectHeaderInfo]);

  const preSaveSpecChangeProcess = () => {
    let refObjectsIds;

    if (pgsData === undefined) return;

    let metadata = pgsData.metadata;
    refObjectsIds = Object.keys(metadata.refObjects);
    collectPgsMaterials(refObjectsIds);
    specChange.map((specChangeRowData) => {
      setGridMaterials(specChangeRowData);
      setGridComponentValues(specChangeRowData);
    });
    const operations = buildInfo.traveler.operations;
    setInvalidMaterials(gridmaterials, pgsMaterials);
    for (let rowidx = 0; rowidx < specChange.length; rowidx++) {
      let specChangeRowData = specChange[rowidx];
      let currentMaterial;
      if (specChangeRowData.material) {
        currentMaterial = specChangeRowData.material;
      }

      let materialInfo = getMaterialInfoForCustXValiation(
        specChangeRowData.operationName,
        currentMaterial
      );

      if (materialInfo != null && materialInfo.length > 0) {
        let length = materialInfo.length;
        getCustMaxLen(
          projectHeaderInfo.facilityAt,
          materialInfo[length - 1].pkg,
          specChangeRowData.componentValue,
          materialInfo[length - 1].pinCount
        );
      }

      setMaterialSpecDevice(refObjectsIds, specChangeRowData);

      let validationMessageArray = validateSpecChanges(
        specChangeRowData,
        operations,
        invalidMaterials,
        currentMaterial,
        buildInfo,
        refObjectsIds
      );
      specChangeRowData.validationStatus = validationMessageArray.join(",");
      if (validationMessageArray.length > 0) {
        if (!dataSheetErrorFlag) {
          dataSheetErrorFlag = true;
        }
      }
    }

    setSpecChange(specChange);

    openConfirmation({
      title: "Save",
      message: dataSheetErrorFlag
        ? `There are some Validation errors. Would you like to proceed to save ?`
        : `Validation successful. Save changes?`,
      yesText: "Yes",
      noText: "No",
      onYes: () => {
        setSpecChange(specChange);
        saveHeader(dataSheetErrorFlag);

        vpost(
          `/vyper/v1/atssmassupload/projects/${projNumber}/savespecchanges`,
          specChange,
          () => {
            openAlert({
              title: "Success",
              message: "Changes Saved",
            });
            setIsAnyUnsavedData(false);
            getSpecChangeData();
          }
        );
      },
    });
  };

  useEffect(() => {
    getBuildInfo();
    preSaveSpecChangeProcess();
  }, [tsmData, pgsData]);

  const redirectToMassReview = (e) => {
    history.push(`/atssmassupload/projects/review/${projectHeaderInfo.projId}`);
  };

  const getSpecChangeData = () => {
    vget(
      `/vyper/v1/atssmassupload/projects/${projNumber}/specchanges`,
      (json) => {
        setSpecChange(json);
        setSavedSpecChange(json);
      }
    );
  };

  const openProcessActions = () => {
    if (!isAnyUnsavedData) {
      openAtssActions();
    } else {
      openConfirmation({
        title: "Process",
        message:
          "There is unsaved data in sheet.Do you want to continue action process without saving?",
        yesText: "Yes",
        noText: "No",
        onYes: () => {
          openAtssActions();
        },
      });
    }
  };

  const openAtssActions = () => {
    if (
      isSubmitForApprovalNeeded === true &&
      build != undefined &&
      vyper != undefined
    ) {
      const blockers = submitBlockers(build, vyper, authUser);

      if (blockers.length === 0) {
        setShowLoadingMessage(
          "Submitting the Reference Traveler Build to AT REVIEW. Please wait..."
        );
        submitForApproval(vyper, build, "Submit", buildDao)
          .then((json) => {
            setShowLoadingMessage(undefined);
            history.push(
              `/atssmassupload/projects/actions/${projectHeaderInfo.projId}`
            );
          })
          .catch((error) => {
            setShowLoadingMessage(undefined);
            enqueueErrorSnackbar("There are no approver groups defined");
            openError({ error, title: "Submit for AT REVIEW Error" });
          });
      } else {
        openAlert({
          title:
            "Can't Perform Action: Submitting the Reference Traveler Build to AT REVIEW",
          blockers,
          childComponent: (
            <div>
              {`Link to populate missing build information :`}{" "}
              <Link
                href={`/vyper/projects/${vyper.vyperNumber}/builds/${build.buildNumber}`}
              >
                {build.buildNumber}
              </Link>{" "}
            </div>
          ),
        });
      }
    } else if (isPraCreationForReferenceTraveler === true) {
      setShowLoadingMessage(
        "Creating the PRA for the Reference Traveler. Please wait..."
      );
      vpost(
        `/vyper/v1/atssmassupload/project/createPra`,
        projectHeaderInfo,
        () => {
          setShowLoadingMessage(undefined);
          history.push(
            `/atssmassupload/projects/actions/${projectHeaderInfo.projId}`
          );
        },
        () => {
          setShowLoadingMessage(undefined);
          enqueueErrorSnackbar("PRA creation has failed");
          openError({
            error,
            title:
              "PRA creation has failed due to unknown error. Please contact support",
          });
        }
      );
    } else {
      history.push(
        `/atssmassupload/projects/actions/${projectHeaderInfo.projId}`
      );
    }
  };

  /**
   * To add rows from Template
   * @param {*} dataToAdd
   */
  const addRowsToSheet = (dataToAdd) => {
    const withNewRecords = specChange.concat(dataToAdd);
    setSpecChange(withNewRecords);
  };

  const closeTemplatePopup = () => {
    setTemplateOpen(false);
  };

  const handleSpecChange = (value) => {
    setIsAnyUnsavedData(true);

    setSpecChange(value);
  };

  const hasValidationErrors = specChange.some(
    (change) => change.validationStatus != null
  );
  const isOwner = projectHeaderInfo?.ownerId === authUser.uid;
  const enableSave =
    isOwner &&
    (projectHeaderInfo?.projStatus === "DRAFT" ||
      projectHeaderInfo?.projStatus === "VALIDATED" ||
      projectHeaderInfo?.projStatus === "REF VALIDATED");
  const enableProcess =
    isOwner && projectHeaderInfo?.projStatus !== "DRAFT";

  return (
    <div>
      <ProjectHeaderLinks
        projectHeaderInfo={projectHeaderInfo}
        currentPage={"Upload changes"}
      />
      <ProjectHeader
        projectHeaderInfo={projectHeaderInfo}
        setProjectHeaderInfo={setProjectHeaderInfo}
      />
      <div className={classes.actions}>
        <Button
          variant="contained"
          color="primary"
          onClick={saveSpecChange}
          disabled={!enableSave}
        >
          Save
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={openProcessActions}
          disabled={!enableProcess}
        >
          {projectHeaderInfo?.praState === "PRA_APPROVED" ? "Next" : "Process"}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={redirectToMassReview}
          disabled={projectHeaderInfo.projId === undefined || projectHeaderInfo?.projStatus === "DRAFT"}
        >
          Mass Review
        </Button>
        <Button
          variant="outlined"
          color="primary"
          onClick={() => {
            setTemplateOpen(true);
          }}
          disabled={projectHeaderInfo.projId === undefined}
        >
          Show Template
        </Button>
      </div>
      {!pgsData && sheetLoading ? <p>Loading... Please wait..</p> : ""}
      <DataSheetGrid
        height={700}
        value={specChange}
        onChange={(value) => handleSpecChange(value)}
        columns={SpecChangeColumns}
        autoAddRow={true}
        rowHeight={25}
        headerRowHeight={30}
      />
      {buildInfo != undefined &&
        specChangeRule != undefined &&
        templateOpen && (
          <SpecTemplatePopup
            open={templateOpen}
            handleCloseDialog={closeTemplatePopup}
            vbuild={buildInfo}
            specConfig={specChangeRule}
            handleAddRows={addRowsToSheet}
          />
        )}
      <LoadingDialog
        open={showLoadingMessage != undefined}
        message={showLoadingMessage}
      />
    </div>
  );
};

export default withRouter(SpecChange);
