import React from "react";
import PropTypes from "prop-types";
import { MaterialAutocomplete } from "../../autocomplete/components/MaterialAutocomplete";

export const Material = ({ defaultMaterial, onSelect, label }) => {
  return (
    <MaterialAutocomplete
      variant="outlined"
      label={label}
      defaultMaterial={defaultMaterial}
      onSelect={onSelect}
    />
  );
};

Material.propTypes = {
  defaultMaterial: PropTypes.object.isRequired,
  onSelect: PropTypes.func.isRequired,
  label: PropTypes.string,
};
