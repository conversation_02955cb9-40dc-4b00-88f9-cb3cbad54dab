import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";

const useStyles = makeStyles({
  VyperButton: {
    padding: 0,
    margin: 0,
    border: 0,
    backgroundColor: "inherit",
    color: "hsla(271, 68%, 32%, 1)",
    textDecoration: "underline",
    cursor: "pointer",
    "&:hover": {
      color: "hsla(271, 68%, 50%, 1)",
    },
  },
  VyperDiabledButton: {
    padding: 0,
    margin: 0,
    border: 0,
    backgroundColor: "inherit",
  },
});

/**
 * a button that looks like a hyperlink
 */
const VyperButton = ({ disabled, children, ...rest }) => {
  const classes = useStyles();

  if (disabled) {
    return <button className={classes.VyperDiabledButton}>{children}</button>;
  } else {
    return (
      <button {...rest} className={classes.VyperButton}>
        {children}
      </button>
    );
  }
};

export default VyperButton;
