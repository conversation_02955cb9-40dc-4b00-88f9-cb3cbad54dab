import React from "react";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import { PriorityRow } from "./PriorityRow";
import makeStyles from "@material-ui/core/styles/makeStyles";

export const InstanceRow = ({
  component,
  instance,
  index,
  selected,
  onSelect,
  onRemovePriority,
  onChangePriority,
}) => {
  const styles = makeStyles((theme) => ({
    componentName: {
      whiteSpace: "nowrap",
      cursor: "pointer",
      "&:hover": {
        fontWeight: "bold",
      },
    },
    componentNameSelected: {
      whiteSpace: "nowrap",
      color: "white",
      backgroundColor: "#1E90FF",
      cursor: "pointer",
    },
  }));

  const handleSelect = (i) => {
    if (selected) {
      onSelect(undefined);
    } else {
      onSelect(i);
    }
  };

  const classes = styles();

  return (
    <TableRow hover>
      <TableCell
        className={
          selected ? classes.componentNameSelected : classes.componentName
        }
        onClick={() => handleSelect(index)}
      >
        {component.name} {index}
      </TableCell>
      {instance.priorities.map((priority, n) => (
        <PriorityRow
          key={n}
          instance={instance}
          priority={priority}
          index={n}
          onRemovePriority={onRemovePriority}
          onChangePriority={(from, to) => onChangePriority(index, from, to)}
        />
      ))}
    </TableRow>
  );
};
