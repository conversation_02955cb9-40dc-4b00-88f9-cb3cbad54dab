import React from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { ResponsiveGrid } from "src/pages/admin/sandbox/bomtemplate/ResponsiveGrid";
import { DataGrid } from "src/component/universal";
import { RuleContext } from "src/pages/admin/sandbox/bomtemplate/RuleContext";

const useStyles = makeStyles(() => ({
  root: {},
  pass: {
    backgroundColor: "hsla(128, 100%, 95%, 1)",
  },
  fail: {
    backgroundColor: "hsla(0, 100%, 95%, 1)",
  },
  comment: {
    backgroundColor: "hsla(52, 100%, 80%, 1)",
  },
  cell: {
    color: "black",
  },
  border: {
    border: "1px solid #ccc",
    fontSize: "0.75rem",
    padding: "2px",
  },
  datagrid: {
    maxWidth: "70vw",
    overflowX: "scroll",
  },
}));

export const FlowTemplateDetailPanel = ({
  flowTemplateDetail,
  ruleContext,
}) => {
  if (flowTemplateDetail == null) return null;

  // build the items for the Responsive Grid (header)

  const keys = [
    { title: "Name", field: "templateName" },
    { title: "Type", field: "templateType" },
    { title: "Revision", field: "templateRevision" },
    { title: "Description", field: "templateDesc" },
    { title: "Status", field: "status" },
    { title: "Preference Code", field: "preferenceCode" },
    { title: "Comments", field: "comments" },
    { title: "AT Site", field: "atSite" },
    { title: "Json Name", field: "templateJsonName" },
  ];

  const items = keys.map((key) => ({
    title: key.title,
    value: flowTemplateDetail[key.field],
  }));

  // build the items for the table

  const columns = [
    {
      title: "Vyper - Include",
      field: "vyperInclude",
      render: (rowData) => (
        <div
          className={rowData.vyperInclude === "Y" ? classes.pass : classes.fail}
        >
          {rowData.vyperInclude}
        </div>
      ),
    },
    { title: "Vyper - Comments", field: "vyperComments" },
    { title: "templateType", field: "templateType" },
    { title: "template", field: "template" },
    { title: "atSite", field: "atSite" },
    { title: "sequenceProcess", field: "sequenceProcess" },
    { title: "pkgNiche", field: "pkgNiche" },
    { title: "pkgGroup", field: "pkgGroup" },
    { title: "section", field: "section" },
    { title: "process", field: "process" },
    { title: "processRes", field: "processRes" },
    { title: "sequenceComponent", field: "sequenceComponent" },
    { title: "component", field: "component" },
    { title: "componentAttribute", field: "componentAttribute" },
    { title: "componentRes", field: "componentRes" },
    { title: "flow", field: "flow" },
    { title: "source", field: "source" },
    { title: "global", field: "global" },
    { title: "comments", field: "comments" },
    { title: "opt1", field: "opt1" },
    { title: "opt2", field: "opt2" },
    { title: "opt3", field: "opt3" },
    { title: "opt4", field: "opt4" },
    { title: "opt5", field: "opt5" },
    { title: "pgsSource", field: "pgsSource" },
    { title: "logic", field: "logic" },
    { title: "mynewfield", field: "mynewfield" },
    { title: "s1", field: "s1" },
    { title: "type1", field: "type1" },
    { title: "left1", field: "left1" },
    { title: "comparison1", field: "comparison1" },
    { title: "right1", field: "right1" },
    { title: "s2", field: "s2" },
    { title: "type2", field: "type2" },
    { title: "left2", field: "left2" },
    { title: "comparison2", field: "comparison2" },
    { title: "right2", field: "right2" },
    { title: "s3", field: "s3" },
    { title: "type3", field: "type3" },
    { title: "left3", field: "left3" },
    { title: "comparison3", field: "comparison3" },
    { title: "right3", field: "right3" },
    { title: "s4", field: "s4" },
    { title: "type4", field: "type4" },
    { title: "left4", field: "left4" },
    { title: "comparison4", field: "comparison4" },
    { title: "right4", field: "right4" },
    { title: "s5", field: "s5" },
    { title: "type5", field: "type5" },
    { title: "left5", field: "left5" },
    { title: "comparison5", field: "comparison5" },
    { title: "right5", field: "right5" },
  ];

  const tableData = flowTemplateDetail.templateJsons.map((record) => {
    return { ...record, vyperInclude: record.vyperInclude ? "Y" : "N" };
  });

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <h3>Template Information</h3>

      <ResponsiveGrid items={items} />

      <h3>Rule Information</h3>

      <RuleContext ruleContext={ruleContext} />

      <div className={classes.datagrid}>
        <DataGrid
          title={`${flowTemplateDetail.templateName} (${flowTemplateDetail.templateType})`}
          data={tableData}
          columns={columns}
          pageSize={20}
          options={{ pageSize: 100, pageSizeOptions: [20, 100, 200] }}
        />
      </div>
    </div>
  );
};
