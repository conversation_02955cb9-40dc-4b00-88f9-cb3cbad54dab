// noinspection JSUnresolvedVariable,JSCheckFunctionSignatures

import makeStyles from "@material-ui/core/styles/makeStyles";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import React, { useContext, useEffect, useState } from "react";
import useSnackbar from "../../../hooks/Snackbar";
import { useHistory } from "react-router-dom";
import { noop } from "src/component/vyper/noop";
import { DataModelsContext } from "src/DataModel";
import { DescriptionRow } from "src/pages/mockup/description/DescriptionRow";
import { ScswrControlNumberRow } from "src/pages/mockup/scswrcontrolnumber/ScswrControlNumberRow";
import { filterTheBuilds } from "src/pages/vyper/filter/filteredBuilds";
import { ComponentRow } from "../../../component/build-component/ComponentRow";
import { ConfirmationDialogContext } from "../../../component/cornfirmation/ConfirmationDialog";
import { submitForApproval } from "../../functions/submitForApproval";
import { BackgrindRow } from "../../mockup/backgrind/BackgrindRow";
import { BomTemplateRow } from "../../mockup/bomtemplate/BomTemplateRow";
import { BuildNumberRow } from "../../mockup/build/BuildNumberRow";
import { BuildTypeRow } from "../../mockup/buildtype/BuildTypeRow";
import { ChangeRow } from "../../mockup/changelink/ChangeRow";
import { PcnRow } from "../../mockup/changelink/PcnRow";
import { BuildCommentRow } from "../../mockup/comment/BuildCommentRow";
import { CopyFromRow } from "../../mockup/copyfrom/CopyFromRow";
import { DieRow } from "../../mockup/die/DieRow";
import { DryBakeRow } from "../../mockup/drybake/DryBakeRow";
import { EslRow } from "../../mockup/esl/EslRow";
import { FacilityRow } from "../../mockup/facility/FacilityRow";
import { BuildFlowRow } from "../../mockup/flow/BuildFlowRow";
import { MaterialRow } from "../../mockup/material/MaterialRow";
import { PackageNicheRow } from "../../mockup/packageniche/PackageNicheRow";
import { PackConfigRow } from "../../mockup/packconfig/PackConfigRow";
import { ScswrRow } from "../../mockup/scswr/ScswrRow";
import { SymbolizationRow } from "../../mockup/symbol/SymbolizationRow";
import { TestRow } from "../../mockup/test/TestRow";
import { TurnkeyRow } from "../../mockup/turnkey/TurnkeyRow";
import { VyperHeader } from "../../mockup/vyper/VyperHeader";
import { VyperHeaderBox } from "../../mockup/vyper/VyperHeaderBox";
import { WaferSawMethodRow } from "../../mockup/wafersawmethod/WaferSawMethodRow";
import { WorkFlowRow } from "../../mockup/workflow/WorkFlowRow";
import { ErrorDialogContext } from "src/component/error/ErrorDialog";
import { rejectTask } from "src/pages/mockup/workflow/approvalHelpers";

const useStyles = makeStyles(() => ({
  root: {},
  table: {},
}));

export const VyperProjectPage = ({ vyperNumber }) => {
  const { open: openConfirm } = useContext(ConfirmationDialogContext);
  const { vyperDao, vyper, buildDao, praDao } = useContext(DataModelsContext);
  const { enqueueErrorSnackbar } = useSnackbar();
  const history = useHistory();
  const [builds, setBuilds] = useState([]);
  const [buildsWithPras, setBuildsWithPras] = useState([]);
  const classes = useStyles();
  const { open: openError } = useContext(ErrorDialogContext);

  // if vyper number changes, updates the builds
  useEffect(() => {
    reloadBuilds();
    reloadBuildsWithPras();
  }, [vyperNumber]);

  const reloadBuilds = () => {
    buildDao
      .findAllByVyperNumber(vyperNumber)
      .then((json) => setBuilds(json))
      .catch((error) => {
        openError({ error, title: "Loading Build Error" });
      });
  };

  const reloadBuildsWithPras = () => {
    praDao
      .findAllBuildNumbersByVyperNumber(vyperNumber)
      .then((buildNumbers) => {
        setBuildsWithPras(buildNumbers);
      })
      .catch((error) => {
        openError({ error, title: "Loading Build Error" });
      });
  };

  const handleChangeBuild = (updatedBuild) => {
    setBuilds((builds) =>
      builds.map((b) =>
        b.buildNumber === updatedBuild.buildNumber ? updatedBuild : b
      )
    );
    return updatedBuild;
  };

  /////////////////////////////////////////////////////////////////////////////////////

  const handleChangeTitle = (title) => {
    return vyperDao.updateTitle(vyper.vyperNumber, title).catch((error) => {
      enqueueErrorSnackbar("An error occurred.");
      openError({ error, title: "Change Title Error" });
    });
  };

  /////////////////////////////////////////////////////////////////////////////////////

  const handleAddNewBuild = (
    mode,
    vyperNumber,
    buildNumber,
    flowData,
    description,
    buildType,
    copyBuildNumber,
    symbolChoice
) => {
    return vyperDao
        .addBuild(
            mode,
            vyperNumber,
            buildNumber,
            flowData,
            description,
            buildType,
            copyBuildNumber,
            symbolChoice
        )
        .then((newBuilds) => {
          if (Array.isArray(newBuilds)){
            const updatedBuilds = [...newBuilds, ...builds];
            setBuilds(updatedBuilds); }
            else {
              const updatedBuilds = [newBuilds, ...builds];
            setBuilds(updatedBuilds); }
        })
        .catch((error) => {
            console.error("Error adding build:", error);
        }); 
};


  /////////////////////////////////////////////////////////////////////////////////////

  const handleSubmitForApproval = (action, build) => {
    return submitForApproval(vyper, build, action, buildDao)
      .then(handleChangeBuild)
      .catch((error) => {
        enqueueErrorSnackbar("An error occurred.");
        openError({ error, title: "Submit for Approval Error" });
      });
  };

  const handleDeleteBuild = (action, build) =>
    openConfirm({
      title: "Delete Build",
      message: `Are you sure you want to delete ${build.buildNumber}?`,
      onYes: () => deleteTheBuild(action, build),
    });

  const deleteTheBuild = (action, build) => {
    // notify the api of the deletion
    buildDao
      .changeWorkflow(
        vyper.vyperNumber,
        build.buildNumber,
        action.toLowerCase(),
        "BU_OWNER"
      )
      .then((deletedBuild) => {
        // remove the build from the array of builds
        setBuilds((builds) =>
          builds.filter((b) => b.buildNumber !== deletedBuild.buildNumber)
        );
        return deletedBuild;
      })
      .catch((err) => openError({ title: "Delete Build Error", error: err }));
  };

  const handleChangeWorkflow = (action, build, context) => {
    switch (action.toLowerCase()) {
      case "rework":
        return handleAtRework(build, context);

      case "bu rework":
        return handleBuRework(build, context);

      default:
        return buildDao
          .changeWorkflow(
            vyper.vyperNumber,
            build.buildNumber,
            action.toLowerCase(),
            "BU_OWNER"
          )
          .then(handleChangeBuild)
          .catch((err) => openError({ title: `${action} Error`, error: err }));
    }
  };

  const handleAtRework = (build, context) => {
    // rework the task
    return rejectTask(
      context.tasks,
      context.authUser,
      context.comment,
      context.reason,
      context.group
    )
      .then(() => {
        // update the workflow
        return buildDao.changeWorkflow(
          vyper.vyperNumber,
          build.buildNumber,
          "rework",
          context.group,
          context.reason
        );
      })
      .then(handleChangeBuild)
      .catch((err) => openError({ title: `Rework Error`, error: err }));
  };

  const handleBuRework = (build, context) => {
    // rework the task
    return buildDao
      .changeWorkflow(
        vyper.vyperNumber,
        build.buildNumber,
        "bu rework",
        context.group,
        context.reason
      )
      .then(handleChangeBuild)
      .catch((err) => openError({ title: `Rework Error`, error: err }));
  };

  const handleDescriptionChange = (description, build) => {
    return buildDao
      .changeDescription(vyper.vyperNumber, build.buildNumber, description)
      .then((json) => handleChangeBuild(json))
      .catch((error) => {
        enqueueErrorSnackbar("An error occurred.");
        openError({ error, title: "Change Description Error" });
      });
  };

  const handleScswrControlNumberChange = (scswrcontrolnumber, build) => {
    return buildDao
      .changeScswrControlNumber(
        vyper.vyperNumber,
        build.buildNumber,
        scswrcontrolnumber
      )
      .then((json) => handleChangeBuild(json))
      .catch((error) => {
        openError({ error, title: "Change SCSWR Number Error" });
      });
  };

  const handleAddComment = (comment, build) => {
    return buildDao
      .addComment(
        vyper.vyperNumber,
        build.buildNumber,
        comment.who.username,
        comment.who.userid,
        comment.when,
        comment.text,
        comment.operation
      )
      .then((json) => handleChangeBuild(json))
      .catch((error) => {
        openError({ error, title: "Change Comment Error" });
      });
  };

  /////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////

  // Applying build filters
  const defaultFilterOptions = {
    buildtype: "All Buildtypes",
    order: ["Newest -> Oldest", "Oldest -> Newest"],
    buildState: "All Build States",
    facility: "All Facilities",
    material: "All Materials",
  };

  const [currentFilters, setCurrentFilters] = useState({
    buildNumbers: [],
    order: defaultFilterOptions.order[0],
    buildtype: defaultFilterOptions.buildtype,
    buildState: defaultFilterOptions.buildState,
    facility: defaultFilterOptions.facility,
    material: defaultFilterOptions.material,
    totalBuilds: 0,
    totalFilteredBuilds: 0,
  });

  const handleFilterChange = (filters) => {
    setCurrentFilters(filters);
  };

  // Returns the list of builds
  const getFilterVyperData = () => {
    let vyperData = {
      buildNumbers: [],
      facilities: [defaultFilterOptions.facility],
      buildState: [defaultFilterOptions.buildState],
      buildtype: [defaultFilterOptions.buildtype],
      order: ["Newest -> Oldest", "Oldest -> Newest"],
      materials: [defaultFilterOptions.material],
    };

    builds?.map((build) => {
      // Get All the Facility Options
      let facilityName = build.facility?.object?.PDBFacility;

      if (
        !vyperData.facilities.includes(facilityName) &&
        facilityName != null
      ) {
        vyperData.facilities.push(facilityName);
      }

      // Add All the Build Number Options
      vyperData.buildNumbers.push({ buildNumber: build.buildNumber });

      // Adding All the build States
      if (!vyperData.buildState.includes(build.state) && build.state != null) {
        vyperData.buildState.push(build.state);
      }

      // Adding All the Build types
      if (
        !vyperData.buildtype.includes(build.buildtype) &&
        build.buildtype != null
      ) {
        vyperData.buildtype.push(build.buildtype);
      }

      // Adding All the material or device
      if (
        !vyperData.materials.includes(build?.material?.object?.Material) &&
        build?.material?.object?.Material != null
      ) {
        vyperData.materials.push(build.material.object.Material);
      }
    });

    return vyperData;
  };

  const filteredBuilds = filterTheBuilds(builds, currentFilters);

  /**
   * You can delete a vyper only when there are no builds.
   * @type {boolean}
   */
  const canDeleteVyper = builds.length === 0;

  /**
   * Confirm that we can delete the vyper project.
   */
  const handleDeleteVyper = () => {
    openConfirm({
      title: "Delete Vyper Project",
      message: "Are you sure you want to delete this Vyper project?",
      onYes: () => {
        vyperDao
          .delete(vyperNumber)
          .then(() => history.push("/"))
          .catch((error) => {
            openError({ error, title: "Delete Vyper Error" });
          });
      },
    });
  };

  const showSameness = false;

  if (vyper == null) {
    return null;
  }

  return (
    <div className={classes.root}>
      <h1>
        <VyperHeader vyper={vyper} onChangeTitle={handleChangeTitle} />
      </h1>
      <VyperHeaderBox
        vyper={vyper}
        canDeleteVyper={canDeleteVyper}
        onDeleteVyper={handleDeleteVyper}
        onAddNewBuild={handleAddNewBuild}
        getFilterVyperData={getFilterVyperData}
        currentFilters={currentFilters}
        onFilterChange={handleFilterChange}
        totalFilteredBuilds={filteredBuilds.length}
        totalBuilds={builds.length}
      />

      <Table className={classes.table} size="small">
        <TableBody>
          <BuildNumberRow
            vyper={vyper}
            vyperNumber={vyperNumber}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            buildsWithPras={buildsWithPras}
          />
          <BuildFlowRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <WorkFlowRow
            vyper={vyper}
            builds={filteredBuilds}
            setBuilds={setBuilds}
            onChange={handleChangeWorkflow}
            onSubmit={handleSubmitForApproval}
            onDelete={handleDeleteBuild}
          />
          <BuildTypeRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
            onAddNewBuild={handleAddNewBuild}
          />
          <DescriptionRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
            handleRowChange={handleDescriptionChange}
          />
          <ScswrControlNumberRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
            handleRowChange={handleScswrControlNumberChange}
          />
          <BuildCommentRow
            vyper={vyper}
            builds={filteredBuilds}
            handleAddComment={handleAddComment}
            showSameness={showSameness}
          />
          <MaterialRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <FacilityRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <BackgrindRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <CopyFromRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <PackageNicheRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <BomTemplateRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
          />
          <DieRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <WaferSawMethodRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <ComponentRow
            title="Leadframe"
            vyper={vyper}
            builds={filteredBuilds}
            onSave={handleChangeBuild}
            showSameness={showSameness}
          />
          <ComponentRow
            title="Mount Compound"
            vyper={vyper}
            builds={filteredBuilds}
            onSave={handleChangeBuild}
            showSameness={showSameness}
          />
          <ComponentRow
            title="MB Diagram"
            vyper={vyper}
            builds={filteredBuilds}
            onSave={handleChangeBuild}
            showSameness={showSameness}
          />
          <ComponentRow
            title="Wire"
            vyper={vyper}
            builds={filteredBuilds}
            onSave={handleChangeBuild}
            showSameness={showSameness}
          />
          <ComponentRow
            title="Mold Compound"
            vyper={vyper}
            builds={filteredBuilds}
            onSave={handleChangeBuild}
            showSameness={showSameness}
          />
          <SymbolizationRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <TestRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <PackConfigRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <ComponentRow
            title="MSL"
            vyper={vyper}
            builds={filteredBuilds}
            onSave={handleChangeBuild}
            showSameness={showSameness}
          />
          <DryBakeRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <EslRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <TurnkeyRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <ChangeRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <PcnRow
            vyper={vyper}
            builds={filteredBuilds}
            onChange={handleChangeBuild}
            showSameness={showSameness}
          />
          <ScswrRow
            vyper={vyper}
            builds={filteredBuilds}
            showSameness={showSameness}
          />
        </TableBody>
      </Table>
    </div>
  );
};
