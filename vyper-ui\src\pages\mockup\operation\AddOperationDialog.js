import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import makeStyles from "@material-ui/core/styles/makeStyles";
import CloseIcon from "@material-ui/icons/Close";
import React, { useContext, useState } from "react";
import { HelperContext } from "src/component/helper/Helpers";

const MODE_NORMAL = "normal";
const MODE_ENGINEERING = "engineering";

const useStyles = makeStyles({
  root: {},
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  closeButton: {
    color: "white",
  },
  layout: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
});

export const AddOperationDialog = ({ children }) => {
  const { currentUserIsSCP } = useContext(HelperContext);

  const [open, setOpen] = useState(false);
  const [build, setBuild] = useState();
  const [operations, setOperations] = useState([]);
  const [operationName, setOperationName] = useState();
  const [mode, setMode] = useState(MODE_NORMAL);
  const [onAdd, setOnAdd] = useState();
  const [beforeOrAfterOperation, setBeforeOrAfterOperation] = useState("After");
  const positions = ["Before", "After"];

  /**
   * Open the dialog
   * @param build The build object
   * @param operations The list of operations
   * @param onAdd callback {name:'operation name', engineering:'Y' or 'N'}
   */
  const handleOpen = ({ build, operations, onAdd }) => {
    setOpen(true);
    setBuild(build);
    setOperations(operations);
    setOnAdd(() => onAdd);
    setMode(MODE_NORMAL);
  };

  // user clicked the close button
  const handleClickClose = () => {
    setOpen(false);
    setBeforeOrAfterOperation("After");
    setOperationName();
  };

  // user clicked the add button
  const handleClickAdd = () => {
    onAdd({
      name: operationName,
      engineering: mode === MODE_NORMAL ? "N" : "Y",
      beforeOrAfter: beforeOrAfterOperation,
    });
    handleClickClose();
  };

  // toggle the mode between normal and engineering
  const handleClickMode = () => {
    setMode(mode === MODE_NORMAL ? MODE_ENGINEERING : MODE_NORMAL);
  };

  // user selected an operation, or typed in the operation textbox
  const handleChangeOperationName = (e) => {
    setOperationName(e.target.value);
  };

  // user selected where the new operation to be added before or after current operation
  const handleChangeBeforeOrAfterOperation = (e) => {
    setBeforeOrAfterOperation(e.target.value);
  };

  // we can save when the user has selected an operation
  const canAdd = operationName != null && operationName !== "";

  // determine if the current user SCP for the build's site
  const isSCP = currentUserIsSCP(null, build);

  // determine if the current build is experimental
  const isExperimental = build?.buildtype
    ?.toUpperCase()
    ?.includes("EXPERIMENTAL");

  // determine if the experimental operation is allowed
  const canBeExperimental = isSCP && isExperimental;

  const classes = useStyles();

  return (
    <AddOperationDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog className={classes.root} open={open} onClose={handleClickClose}>
        <DialogTitle classes={{ root: classes.title }}>
          <div className={classes.layout}>
            <div>Add Operation</div>
            <IconButton
              className={classes.closeButton}
              onClick={handleClickClose}
            >
              <CloseIcon />
            </IconButton>
          </div>
        </DialogTitle>

        <DialogContent>
          <p>
            Use this dialog to choose an operation to add to your build. Choose
            the name in the drop-down list and click save. If you want to add a
            new operation (a operation that doesn't currently exist in ATSS) you
            can click the engineering button to switch to Engineering Operation
            mode. Then, you can type in the name of the new operation.
            Engineering is only enabled when the current user has SCP authority,
            and the build is EXPERIMENTAL.
          </p>

          {mode === MODE_NORMAL ? (
            <TextField
              select
              fullWidth
              id="operation-existing"
              label="Operation"
              margin="normal"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              SelectProps={{ native: true }}
              value={operationName}
              onChange={handleChangeOperationName}
              helperText="Select the operation you wish to add"
            >
              <option value={null} />
              {operations.map((operation) => (
                <option key={operation} value={operation}>
                  {operation}
                </option>
              ))}
            </TextField>
          ) : (
            <TextField
              fullWidth
              id="operation-engineering"
              label="Operation"
              margin="normal"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              SelectProps={{ native: true }}
              value={operationName}
              onChange={handleChangeOperationName}
              helperText="Type in the new, engineering operation name"
            />
          )}

          <TextField
            select
            fullWidth
            id="Location"
            label="Location"
            margin="normal"
            InputLabelProps={{ shrink: true }}
            variant="outlined"
            SelectProps={{ native: true }}
            value={beforeOrAfterOperation}
            onChange={handleChangeBeforeOrAfterOperation}
            helperText="Insert before or after this operation"
          >
            {positions.map((position) => (
              <option key={position} value={position}>
                {position}
              </option>
            ))}
          </TextField>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClickMode}
            variant="contained"
            color="secondary"
            disabled={!canBeExperimental}
          >
            {mode === MODE_NORMAL ? MODE_ENGINEERING : MODE_NORMAL}
          </Button>
          <Button onClick={handleClickClose} variant="outlined" color="primary">
            Close
          </Button>
          <Button
            onClick={handleClickAdd}
            variant="contained"
            color="primary"
            disabled={!canAdd}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>
      {children}
    </AddOperationDialogContext.Provider>
  );
};

export const AddOperationDialogContext = React.createContext(null);
