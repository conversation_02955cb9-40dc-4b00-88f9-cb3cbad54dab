import { DaoBase } from "src/component/fetch/DaoBase";

export class FlowOperationsMapDao extends DaoBase {
  constructor(params) {
    super({
      name: "FlowOperationsMapDao",
      url: "/vyper/v1/config/flowopn",
      ...params,
    });
  }

  list() {
    return this.handleFetch("list", `/`, "GET");
  }

  defaultRows() {
    return this.handleFetch("defaultRows", `/rows`, "GET");
  }

  search(page, size) {
    return this.handleFetch(
      "search",
      `/search?page=${page}&size=${size}`,
      "GET"
    );
  }

  create(flowOperationsMap) {
    return this.handleFetch("create", ``, "POST", flowOperationsMap);
  }

  update(flowOperationsMap) {
    return this.handleFetch("update", ``, "PUT", flowOperationsMap);
  }

  delete(id) {
    return this.handleFetch("delete", `/${id}`, "DELETE");
  }

  delete(flowid, opnid) {
    return this.handleFetch("delete", `/${flowid}/${opnid}`, "DELETE");
  }
}
