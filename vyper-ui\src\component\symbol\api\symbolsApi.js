import axios from "axios";

/**
 * Retrieve the list of symbols for the package and facilityAt.
 *
 * @param {string} pkg - The package designator
 * @param {string} facilityAt - The facility A/T
 * @return {Promise<Symbol[]>} A promise that resolves to a list of Symbols.
 */
export const symbolsApi = (pkg, facilityAt) => {
  return axios
    .get(
      `/vyper/v1/atss/symbolization/symbols?pkg=${pkg}&facilityAt=${facilityAt}`
    )
    .then((response) => response.data);
};

/**
 * Retrieve the list of symbols for the filter, package and facilityAt
 *
 * @param {string} filter - The filter
 * @param {string} pkg - The package designator
 * @param {string} facilityAt - The facility A/T
 * @return {Promise<Symbol[]>} A promise that resolves to a list of Symbols.
 */
export const filteredSymbolsApi = (filter, pkg, facilityAt) => {
  return axios
    .get(
      `/vyper/v1/atss/symbolization/symbols/filtered?filter=${filter}&pkg=${pkg}&facilityAt=${facilityAt}`
    )
    .then((response) => response.data);
};

/**
 * Retrieve the symbolization by name
 *
 * @param {string} value - The filter
 * @return {Promise<>} A promise that resolves to the symbolization
 */
export const findSymbolByValue = (value) => {
  const uri = encodeURI(`/vyper/v1/atss/symbolization/findByValue`);
  return axios
    .get(uri, { params: { value: encodeURIComponent(value) } })
    .then((response) => response.data);
};
