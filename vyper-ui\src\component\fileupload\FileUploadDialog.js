import React, { useState } from "react";
import Dialog from "@material-ui/core/Dialog";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Button from "@material-ui/core/Button";
import DialogActions from "@material-ui/core/DialogActions";
import makeStyles from "@material-ui/core/styles/makeStyles";
import DialogContent from "@material-ui/core/DialogContent";
import FileReaderInput from "react-file-reader-input";
import Grid from "@material-ui/core/Grid";
import { DialogContentText } from "@material-ui/core";

export const FileUploadDialog = ({ children }) => {
  const styles = makeStyles((theme) => ({
    closeButton: {
      position: "absolute",
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    autocomplete: {
      minWidth: "13rem",
    },
    dialog: {
      padding: "3rem",
      margin: "3rem",
      minWidth: "400px",
    },
    preview: {
      backgroundColor: "#ddd",
      border: "1px solid black",
      overflowY: "scroll",
      height: "300px",
      width: "100%",
      fontFamily: "monospace",
    },
  }));

  const [open, setOpen] = useState(false);

  const [parameters, setParameters] = useState();

  const handleOpen = (parameters) => {
    setParameters(parameters);
    setContent(parameters.content);
    setPreviousContent(parameters.content);
    setOpen(true);
  };

  const [previousContent, setPreviousContent] = useState();
  const [content, setContent] = useState();

  // user has selected a file
  const handleSelectFile = (e, results) => {
    results.forEach((result) => {
      const [e, file] = result;
      const reader = new FileReader();
      reader.onload = (event) => setContent(event.target.result);
      reader.readAsText(file);
    });
  };

  const handleClose = () => {
    setParameters(undefined);
    setContent(undefined);
    setOpen(false);
  };

  const handleSave = () => {
    parameters.onSave(content);
    handleClose();
  };

  const classes = styles();

  return (
    <FileUploadDialogContext.Provider
      value={{
        open: handleOpen,
      }}
    >
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
        <DialogTitle>File Upload/Edit Dialog</DialogTitle>

        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>

        <DialogContent>
          <DialogContentText>
            Use this form to upload or edit a test program flow for the build.
          </DialogContentText>
          <textarea
            className={classes.preview}
            rows={7}
            value={content}
            onChange={(e) => setContent(e.target.value)}
          />
        </DialogContent>

        <DialogActions>
          <FileReaderInput as="text" onChange={handleSelectFile}>
            <Button color="secondary" variant="outlined" type="button">
              Load File
            </Button>
          </FileReaderInput>
          <Button onClick={handleClose} variant="outlined" color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            color="primary"
            disabled={content === previousContent}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </FileUploadDialogContext.Provider>
  );
};

export const FileUploadDialogContext = React.createContext(null);
