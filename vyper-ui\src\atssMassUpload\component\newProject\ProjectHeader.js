import React, { useContext, useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import { AuthContext } from "src/component/common/auth";
import { TextField, Paper } from "@material-ui/core";

import { Link } from "react-router-dom";
import {
  convertBuildNumbertoVyperNumber,
  convertPraNumbertoVyperNumber,
} from "../../../component/helper/convertBuildNumbertoVyperNumber";
import { ChangeDialogContext } from "../../../pages/mockup/changelink/ChangeDialog";
import { FetchContext } from "../../../component/fetch/VyperFetch";
import { VyperLink } from "../../../pages/mockup/VyperLink";

const useStyles = makeStyles((theme) => ({
  labelField: {
    display: "flex",
    flexDirection: "row",
    gap: "0.2rem",
    alignItems: "center",
    borderRadius: "0.2rem",
    minWidth: "12rem",
    color: "rgba(0, 0, 0, 0.38)",
  },
  fieldSetCustom: {
    width: "100%",
    borderRadius: "inherit",
  },
  labelFieldLabel: {
    fontSize: "small",
    fontWeight: "400",
    padding: "0 0.3rem",
    borderRadius: "0 0.3rem",
  },
  flexRow: {
    display: "flex",
    flexDirection: "row",
    gap: "0.8rem",
    flexWrap: "wrap",
  },
  flexElem: {
    flexGrow: 0,
    flexShrink: 0,
    flexBasis: "12rem",
  },

  paper: {
    padding: "0.5rem",
    textAlign: "center",
    margin: "0.2rem 0.2rem 0 0.2rem",
  },

  label: {
    position: "absolute",
    top: "0.2ex",
    zIndex: 1,
    left: "2em",
    padding: "0 5px",
  },
}));

export const MuTextField = (props) => {
  const classes = useStyles();
  return (
    <TextField
      variant="outlined"
      className={classes.flexElem}
      InputLabelProps={{ shrink: true }}
      size="small"
      {...props}
    />
  );
};

/**
 *
 * @param {*} props
 * @returns
 */
export const LabelField = ({ name, children }) => {
  const classes = useStyles();
  return (
    <div className={classes.labelField}>
      <fieldset className={classes.fieldSetCustom}>
        <legend className={classes.labelFieldLabel}> {name}</legend>
        {children}
      </fieldset>
    </div>
  );
};

export const ProjectHeader = ({ projectHeaderInfo, setProjectHeaderInfo }) => {
  const classes = useStyles();
  const { authUser: currentUser } = useContext(AuthContext);
  const { vget, vpost } = useContext(FetchContext);

  const buildLink = `/projects/${convertBuildNumbertoVyperNumber(
    projectHeaderInfo.refVyperBuildNumber
  )}/builds/${projectHeaderInfo.refVyperBuildNumber}/selection`;
  const praLink = `/projects/${convertPraNumbertoVyperNumber(
    projectHeaderInfo.refVyperPraNumber
  )}/pras/${projectHeaderInfo.refVyperPraNumber}`;

  const [changeObj, setChangeObj] = useState([]);
  const saveCMSNumber = (cmsNumber) => {
    vpost(`/vyper/v1/atssmassupload/projects/${projectHeaderInfo.projNumber}`, {
      projName: projectHeaderInfo?.projName,
      projType: projectHeaderInfo?.projType,
      cmsNumber: cmsNumber,
    });
  };

  useEffect(() => {
    if (
      projectHeaderInfo?.cmsNumber == undefined ||
      projectHeaderInfo?.cmsNumber === ""
    ) {
      return undefined;
    }

    vget(
      `/vyper/v1/changelink/change/findByChangeNumber?changeNumber=${projectHeaderInfo?.cmsNumber}`,
      (json) => {
        setChangeObj([json]);
      }
    );
  }, [projectHeaderInfo?.cmsNumber]);

  const { open } = useContext(ChangeDialogContext);

  const handleChangeLinkChange = (changes) => {
    let cmsNumber = changes[0].changeNumber;
    saveCMSNumber(cmsNumber);
    setProjectHeaderInfo({
      ...projectHeaderInfo,
      cmsNumber: cmsNumber !== undefined || null ? cmsNumber : undefined,
    });
  };

  const handleOpen = () => {
    open({
      multiSelect: false,
      rows:
        projectHeaderInfo?.cmsNumber == undefined ||
        projectHeaderInfo?.cmsNumber === ""
          ? []
          : changeObj == undefined
          ? []
          : changeObj,
      onSave: (rows) => handleChangeLinkChange(rows),
    });
  };

  return (
    <div>
      <div className={classes.flexRow}>
        <MuTextField
          label="Project Number"
          disabled
          value={projectHeaderInfo?.projNumber}
        />
        <MuTextField
          label="Project Name"
          disabled={projectHeaderInfo?.ownerId !== currentUser.uid}
          value={projectHeaderInfo?.projName}
          onChange={(e) => {
            setProjectHeaderInfo({
              ...projectHeaderInfo,
              projName: e.target.value,
            });
          }}
          InputLabelProps={{ shrink: true }}
        />
        <MuTextField
          label="Project Type"
          disabled
          value={projectHeaderInfo?.projType}
        />
        <MuTextField
          label="Project Status"
          disabled
          value={projectHeaderInfo?.projStatus}
        />
        <MuTextField
          label="Project Owner"
          disabled
          value={projectHeaderInfo?.ownerId}
        />
        <MuTextField
          label="Target Facility"
          disabled
          value={projectHeaderInfo?.facilityAt}
        />
        <MuTextField
          label="Spec Device"
          disabled
          value={projectHeaderInfo?.refSpecDevice}
        />
        <MuTextField
          label="Ref Facility"
          disabled
          value={projectHeaderInfo?.refFacilityAt}
        />
        <MuTextField
          label="Status"
          disabled
          value={projectHeaderInfo?.refStatus}
        />

        <LabelField name="CMS Number">
          <span>{projectHeaderInfo?.cmsNumber} </span>
          <VyperLink
            onClick={() => handleOpen()}
            canEdit={projectHeaderInfo?.ownerId == currentUser.uid}
          >
            Select
          </VyperLink>
        </LabelField>

        <LabelField name="Ref Build">
          <Link to={buildLink}>{projectHeaderInfo?.refVyperBuildNumber} </Link>
        </LabelField>

        <LabelField name="Build Status">
          <span>{projectHeaderInfo?.buildState} </span>
        </LabelField>

        <LabelField name="Ref PRA">
          {projectHeaderInfo?.refVyperPraNumber ? (
            <Link to={praLink}>{projectHeaderInfo?.refVyperPraNumber}</Link>
          ) : (
            <span>-</span>
          )}
        </LabelField>

        <LabelField name="PRA Status">
          <span>{projectHeaderInfo?.praState || "-"} </span>
        </LabelField>
      </div>
    </div>
  );
};
