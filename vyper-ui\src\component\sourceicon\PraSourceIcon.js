import makeStyles from "@material-ui/core/styles/makeStyles";
import AttachMoneyIcon from "@material-ui/icons/AttachMoney";
import FireplaceIcon from "@material-ui/icons/Fireplace";
import LibraryBooksIcon from "@material-ui/icons/LibraryBooks";
import MemoryIcon from "@material-ui/icons/Memory";
import VpnLockIcon from "@material-ui/icons/VpnLock";
import PlaylistAddCheckIcon from "@material-ui/icons/PlaylistAddCheck";

import React from "react";
import pgs160 from "./crown_160.png";
import BuildIcon from "@material-ui/icons/Build";

const useStyles = makeStyles(() => ({
  root: {
    display: "inline-block",
  },
  notVerified: {
    color: "white",
    backgroundColor: "#cc0000",
  },
  partialVerified: {
    color: "white",
    backgroundColor: "#2222FF",
  },
  fullyVerified: {
    color: "white",
    backgroundColor: "#005500",
  },
  icon: {
    padding: 2,
    marginLeft: 1,
    marginRight: 1,
  },
  pgsIcon: {
    padding: "3px",
  },
  pgsContainer: {
    paddingRight: 3,
  },
}));

// maps the verifier to its icon
export const SOURCE_TO_ICON_MAP = {
  SOURCE_PGS: AttachMoneyIcon,
  SOURCE_PAVV_COMPONENT: PlaylistAddCheckIcon,
  SOURCE_ARMARC: BuildIcon,
  SOURCE_ATSS_GLOBAL: VpnLockIcon,
  SOURCE_ATSS_AT: LibraryBooksIcon,
  SOURCE_ATSS_BOM: FireplaceIcon,
  SOURCE_DIE: MemoryIcon,
};

export const VERIFIER_STATUS_TO_ICON_COLOR = {
  NOT_VERIFIED: "notVerified",
  PARTIALLY_VERIFIED: "partialVerified",
  FULLY_VERIFIED: "fullyVerified",
};

export const SOURCE_NAME_TO_TITLE = {
  SOURCE_PGS: "PGS",
  SOURCE_PAVV_COMPONENT: "PAVV Components",
  SOURCE_ARMARC: "ARMARC",
  SOURCE_ATSS_GLOBAL: "ATSS CAMS Global",
  SOURCE_ATSS_AT: "ATSS CAMS AT",
  SOURCE_ATSS_BOM: "ATSS BOM",
  SOURCE_DIE: "Die",
};

export const PraSourceIcon = ({ title, verifiers, sourceName }) => {
  const classes = useStyles();

  // get the icon
  const Icon = SOURCE_TO_ICON_MAP[sourceName];

  // get the verifier
  const verifier = verifiers.find(
    (verifier) => verifier.verifierSource === sourceName
  ) || { sourceName: sourceName, verifiedStatus: "NOT_VERIFIED" };

  const names =
    classes.icon +
    " " +
    classes[VERIFIER_STATUS_TO_ICON_COLOR[verifier.verifiedStatus]];

  const pgsNames =
    classes.pgsIcon +
    " " +
    classes[VERIFIER_STATUS_TO_ICON_COLOR[verifier.verifiedStatus]];

  let rootClasses = classes.root;
  if (sourceName === "SOURCE_PGS") {
    rootClasses += " " + classes.pgsContainer;
  }

  // hack to adjust pgs icon size for firefox browser
  const isFirefox = typeof InstallTrigger !== "undefined";
  const size = isFirefox ? 36 : 24;

  return (
    <div
      className={rootClasses}
      title={`${SOURCE_NAME_TO_TITLE[sourceName]} - ${title}\n${verifier.verifiedStatus}`}
    >
      {Icon != null && sourceName !== "SOURCE_PGS" && (
        <Icon className={names} />
      )}
      {Icon != null && sourceName === "SOURCE_PGS" && (
        <img
          className={pgsNames}
          src={pgs160}
          alt="pgs"
          height={size}
          width={size}
        />
      )}
    </div>
  );
};
