import { DaoBase } from "src/component/fetch/DaoBase";

export class CompareDao extends DaoBase {
  constructor(params) {
    super({ name: "CompareDao", url: "/vyper/v1/vyper/compare", ...params });
  }

  atss(buildNumber, material, facility, status) {
    return this.handleFetch(
      "atss",
      `/atss?buildNumber=${buildNumber}&material=${material}&facility=${facility}&status=${status}`,
      "GET"
    );
  }

  traveler(buildNumber1, buildNumber2, date1 = "null", date2 = "null") {
    return this.handleFetch(
      "atss",
      `/traveler?buildNumber1=${buildNumber1}&buildNumber2=${buildNumber2}&date1=${date1}&date2=${date2}`,
      "GET"
    );
  }
}
