import React, { useMemo, useState, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import TextField from "@material-ui/core/TextField";
import { FormControl, InputLabel, Select, MenuItem } from "@material-ui/core";
import Autocomplete from "@material-ui/lab/Autocomplete";
import { BASE_FETCH_OPTIONS_URL } from "../../FormConstants";

const fetchData = (uri, setFormState, defaultValue) => {
  fetch(`${BASE_FETCH_OPTIONS_URL}${uri}`)
    .then((response) => response.json())
    .then(setFormState)
    .catch(() => {
      setFormState(defaultValue);
    });
};

const ShippingInfoFields = (props) => {
  const { classes, formState, setFormState } = props;
  const [plantOptions, setPlantOptions] = useState({});
  const [stateOfFinishOptions, setStateOfFinishOptions] = useState([]);

  useEffect(() => {
    fetchData("/shippingPlantOptions", setPlantOptions, {});
    fetchData("/stateOfFinishOptions", setStateOfFinishOptions, []);
  }, []);

  const handleChange = (evt) => {
    const { name, value } = evt.target;
    setFormState((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const handlePlantOptionChange = (evt, value) => {
    setFormState((prevState) => {
      return {
        ...prevState,
        plant: plantOptions[value]?.plant || "",
        address: plantOptions[value]?.plantAddress || "",
      };
    });
  };

  const defaultFieldProps = useMemo(() => {
    return {
      InputLabelProps: {
        className: classes.textField,
      },
      variant: "outlined",
      color: "secondary",
      onChange: handleChange,
      fullWidth: true,
      required: true,
    };
  }, []);
  return (
    <Grid style={{ marginTop: "10px" }}>
      <Grid container spacing={1}>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.attention}
            name={"attention"}
            label="Attention"
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.mailStation}
            name={"mailStation"}
            label="Mail Station"
          />
        </Grid>
        <Grid item xs>
          <Autocomplete
            {...defaultFieldProps}
            style={{}}
            value={formState.plant}
            onChange={handlePlantOptionChange}
            options={Object.keys(plantOptions)}
            getOptionLabel={(option) => {
              if (plantOptions[option]) {
                return `${plantOptions[option].plant} - ${plantOptions[option].plantDesc}`;
              }
              return "";
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                {...defaultFieldProps}
                label="Plant"
                variant="outlined"
              />
            )}
          />
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.address}
            name={"address"}
            label="Address"
          />
        </Grid>
      </Grid>
      <Grid container spacing={1}>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.quantity}
            name={"quantity"}
            label="Quantity"
          />
        </Grid>
        <Grid item xs>
          <FormControl fullWidth color="secondary" variant="outlined">
            <InputLabel className={classes.textField}>
              State of Finish'
            </InputLabel>
            <Select
              {...defaultFieldProps}
              name={"stateOfFinish"}
              value={formState.stateOfFinish}
            >
              {stateOfFinishOptions.map((item, i) => (
                <MenuItem key={i} value={item.value}>
                  {item.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs>
          <TextField
            {...defaultFieldProps}
            value={formState.shipDeviceName}
            name={"shipDeviceName"}
            label="Ship Device Name"
          />
        </Grid>
      </Grid>
    </Grid>
  );
};
export default ShippingInfoFields;
