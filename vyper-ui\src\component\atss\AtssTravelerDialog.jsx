import React, { useEffect, useState } from "react";
import makeStyles from "@material-ui/core/styles/makeStyles";
import DialogTitle from "@material-ui/core/DialogTitle";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import { AtssTravelerSelectButton } from "src/component/atss/components/AtssTravelerSelectButton";
import { AtssTravelerStatusSelect } from "src/component/atss/components/AtssTravelerStatusSelect";
import { AtssTravelerFacilitySelect } from "src/component/atss/components/AtssTravelerFacilitySelect";
import { AtssTravelerSpecDeviceAutocomplete } from "src/component/atss/components/AtssTravelerSpecDeviceAutocomplete";
import { DialogActions } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
  title: {
    backgroundColor: "#cc0000",
    color: "#ffffff",
  },
  form: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    gap: "1em",
  },
}));

export const AtssTravelerDialog = ({
  open = true,
  onClose,
  onSelect,
  title = "ATSS Device Dialog",
  defaultSpecDevice = "",
  defaultFacilityAt = "",
  defaultStatus = "",
  showSelectedText = false,
}) => {
  const [specDevice, setSpecDevice] = React.useState("");
  const [facilityAt, setFacilityAt] = useState("");
  const [status, setStatus] = useState("");
  const classes = useStyles();

  useEffect(() => {
    if (open) {
      setSpecDevice(defaultSpecDevice);
      setFacilityAt(defaultFacilityAt);
      setStatus(defaultStatus);
    }
  }, [open]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg">
      <DialogTitle className={classes.title}>{title}</DialogTitle>

      <IconButton
        aria-label="close"
        className={classes.closeButton}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent className={classes.form}>
        <AtssTravelerSpecDeviceAutocomplete
          specDevice={specDevice}
          onChange={(v) => {
            setSpecDevice(v);
            setFacilityAt("");
            setStatus("");
          }}
          showSelectedText={showSelectedText}
        />

        <AtssTravelerFacilitySelect
          specDevice={specDevice}
          facilityAt={facilityAt}
          onChange={(v) => {
            setFacilityAt(v);
            setStatus("");
          }}
          showSelectedText={showSelectedText}
        />

        <AtssTravelerStatusSelect
          specDevice={specDevice}
          facilityAt={facilityAt}
          status={status}
          onChange={setStatus}
          showSelectedText={showSelectedText}
        />
      </DialogContent>

      <DialogActions>
        <AtssTravelerSelectButton
          specDevice={specDevice}
          facilityAt={facilityAt}
          status={status}
          onClick={() => onSelect(specDevice, facilityAt, status)}
        />
      </DialogActions>
    </Dialog>
  );
};
